"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { toast } from "@/components/ui/use-toast"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, Check, Copy, ExternalLink, Loader2, Plus, RefreshCw, Trash2, Webhook, Eye } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"

interface WebhookConfig {
  id: string
  name: string
  url: string
  events: string[]
  active: boolean
  secret?: string
  lastTriggered?: string
  createdAt: string
}

interface WebhookEvent {
  id: string
  name: string
  description: string
  category: string
}

export function WebhookManager() {
  const [webhooks, setWebhooks] = useState<WebhookConfig[]>([
    {
      id: "webhook-1",
      name: "Content Published Notification",
      url: "https://example.com/webhooks/content-published",
      events: ["content.published", "content.updated"],
      active: true,
      secret: "whsec_8f7h3j2k1l0p9o8i7u6y5t4r3e2w1q",
      lastTriggered: "2023-05-15T14:30:00Z",
      createdAt: "2023-01-10T09:15:00Z",
    },
    {
      id: "webhook-2",
      name: "Product Analytics",
      url: "https://analytics.example.com/webhooks/product-data",
      events: ["product.viewed", "product.purchased"],
      active: false,
      secret: "whsec_9g8f7e6d5c4b3a2z1y2x3c4v5b6n7m8",
      createdAt: "2023-02-22T11:45:00Z",
    },
    {
      id: "webhook-3",
      name: "System Status Updates",
      url: "https://status.example.com/webhooks/system-status",
      events: ["system.check.completed", "system.issue.detected"],
      active: true,
      secret: "whsec_1a2s3d4f5g6h7j8k9l0p1o2i3u4y5t",
      lastTriggered: "2023-05-16T08:45:00Z",
      createdAt: "2023-03-05T16:20:00Z",
    },
  ])

  const [isCreatingWebhook, setIsCreatingWebhook] = useState(false)
  const [newWebhook, setNewWebhook] = useState<Omit<WebhookConfig, "id" | "createdAt">>({
    name: "",
    url: "",
    events: [],
    active: true,
    secret: generateSecret(),
  })
  const [isTestingWebhook, setIsTestingWebhook] = useState<string | null>(null)
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null)
  const [activeTab, setActiveTab] = useState("webhooks")
  const [showSecretDialog, setShowSecretDialog] = useState<string | null>(null)
  const [isRegeneratingSecret, setIsRegeneratingSecret] = useState(false)

  const availableEvents: WebhookEvent[] = [
    {
      id: "content.published",
      name: "Content Published",
      description: "Triggered when new content is published to any platform",
      category: "Content",
    },
    {
      id: "content.updated",
      name: "Content Updated",
      description: "Triggered when existing content is updated",
      category: "Content",
    },
    {
      id: "content.scheduled",
      name: "Content Scheduled",
      description: "Triggered when content is scheduled for future publishing",
      category: "Content",
    },
    {
      id: "product.viewed",
      name: "Product Viewed",
      description: "Triggered when a product is viewed in content",
      category: "Products",
    },
    {
      id: "product.purchased",
      name: "Product Purchased",
      description: "Triggered when a product is purchased through a link",
      category: "Products",
    },
    {
      id: "product.added",
      name: "Product Added",
      description: "Triggered when a new product is added to the system",
      category: "Products",
    },
    {
      id: "system.check.completed",
      name: "System Check Completed",
      description: "Triggered when a system check is completed",
      category: "System",
    },
    {
      id: "system.issue.detected",
      name: "System Issue Detected",
      description: "Triggered when a system issue is detected",
      category: "System",
    },
    {
      id: "system.issue.resolved",
      name: "System Issue Resolved",
      description: "Triggered when a system issue is resolved",
      category: "System",
    },
  ]

  function generateSecret() {
    const characters = "abcdefghijklmnopqrstuvwxyz0123456789"
    const prefix = "whsec_"
    let result = prefix
    for (let i = 0; i < 32; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length))
    }
    return result
  }

  const handleCreateWebhook = () => {
    if (!newWebhook.name || !newWebhook.url || newWebhook.events.length === 0) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields and select at least one event.",
        variant: "destructive",
      })
      return
    }

    const webhook: WebhookConfig = {
      id: `webhook-${Date.now()}`,
      ...newWebhook,
      createdAt: new Date().toISOString(),
    }

    setWebhooks([...webhooks, webhook])
    setIsCreatingWebhook(false)
    setNewWebhook({
      name: "",
      url: "",
      events: [],
      active: true,
      secret: generateSecret(),
    })

    toast({
      title: "Webhook Created",
      description: "Your webhook has been created successfully.",
    })
  }

  const handleDeleteWebhook = (id: string) => {
    setWebhooks(webhooks.filter((webhook) => webhook.id !== id))
    toast({
      title: "Webhook Deleted",
      description: "The webhook has been deleted.",
    })
  }

  const handleToggleWebhook = (id: string, active: boolean) => {
    setWebhooks(webhooks.map((webhook) => (webhook.id === id ? { ...webhook, active } : webhook)))

    toast({
      title: active ? "Webhook Enabled" : "Webhook Disabled",
      description: active
        ? "The webhook has been enabled and will receive events."
        : "The webhook has been disabled and will not receive events.",
    })
  }

  const handleTestWebhook = async (id: string) => {
    const webhook = webhooks.find((w) => w.id === id)
    if (!webhook) return

    setIsTestingWebhook(id)
    setTestResult(null)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Simulate success/failure (80% success rate)
    const success = Math.random() < 0.8
    setTestResult({
      success,
      message: success
        ? "Webhook test successful. The endpoint responded with a 200 OK status."
        : "Webhook test failed. The endpoint returned a 404 Not Found status.",
    })

    setIsTestingWebhook(null)
  }

  const handleRegenerateSecret = (id: string) => {
    setIsRegeneratingSecret(true)

    // Simulate API call
    setTimeout(() => {
      const newSecret = generateSecret()
      setWebhooks(webhooks.map((webhook) => (webhook.id === id ? { ...webhook, secret: newSecret } : webhook)))
      setIsRegeneratingSecret(false)
      setShowSecretDialog(null)

      toast({
        title: "Secret Regenerated",
        description: "Your webhook secret has been regenerated. Make sure to update it in your systems.",
      })
    }, 1500)
  }

  const handleCopySecret = (secret: string) => {
    navigator.clipboard.writeText(secret)
    toast({
      title: "Secret Copied",
      description: "The webhook secret has been copied to your clipboard.",
    })
  }

  const handleEventToggle = (eventId: string) => {
    setNewWebhook({
      ...newWebhook,
      events: newWebhook.events.includes(eventId)
        ? newWebhook.events.filter((id) => id !== eventId)
        : [...newWebhook.events, eventId],
    })
  }

  const getEventName = (eventId: string) => {
    const event = availableEvents.find((e) => e.id === eventId)
    return event ? event.name : eventId
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Webhook Connections</CardTitle>
            <CardDescription>
              Configure webhooks to integrate with external services and receive real-time updates
            </CardDescription>
          </div>
          <Button onClick={() => setIsCreatingWebhook(true)} disabled={isCreatingWebhook}>
            <Plus className="mr-2 h-4 w-4" />
            Add Webhook
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
            <TabsTrigger value="events">Available Events</TabsTrigger>
            <TabsTrigger value="logs">Delivery Logs</TabsTrigger>
          </TabsList>

          <TabsContent value="webhooks" className="space-y-4">
            {isCreatingWebhook ? (
              <Card>
                <CardHeader>
                  <CardTitle>Create Webhook</CardTitle>
                  <CardDescription>Configure a new webhook to receive events from the Command Center</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="webhook-name">Webhook Name</Label>
                    <Input
                      id="webhook-name"
                      placeholder="e.g., Content Publishing Notification"
                      value={newWebhook.name}
                      onChange={(e) => setNewWebhook({ ...newWebhook, name: e.target.value })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="webhook-url">Endpoint URL</Label>
                    <Input
                      id="webhook-url"
                      placeholder="https://example.com/webhooks/endpoint"
                      value={newWebhook.url}
                      onChange={(e) => setNewWebhook({ ...newWebhook, url: e.target.value })}
                    />
                    <p className="text-sm text-muted-foreground">The URL where webhook payloads will be sent</p>
                  </div>

                  <div className="space-y-2">
                    <Label>Events to Subscribe</Label>
                    <div className="border rounded-md p-4 space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {availableEvents.map((event) => (
                          <div key={event.id} className="flex items-start space-x-2">
                            <Switch
                              id={`event-${event.id}`}
                              checked={newWebhook.events.includes(event.id)}
                              onCheckedChange={() => handleEventToggle(event.id)}
                            />
                            <div className="space-y-1">
                              <Label htmlFor={`event-${event.id}`} className="text-sm font-medium cursor-pointer">
                                {event.name}
                              </Label>
                              <p className="text-xs text-muted-foreground">{event.description}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="webhook-secret">Webhook Secret</Label>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setNewWebhook({ ...newWebhook, secret: generateSecret() })}
                      >
                        <RefreshCw className="mr-2 h-3 w-3" />
                        Regenerate
                      </Button>
                    </div>
                    <div className="flex space-x-2">
                      <Input id="webhook-secret" value={newWebhook.secret} readOnly type="password" />
                      <Button variant="outline" size="icon" onClick={() => handleCopySecret(newWebhook.secret || "")}>
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Used to verify that requests are coming from the Command Center
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="webhook-active"
                      checked={newWebhook.active}
                      onCheckedChange={(active) => setNewWebhook({ ...newWebhook, active })}
                    />
                    <Label htmlFor="webhook-active">Enable webhook immediately</Label>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={() => setIsCreatingWebhook(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateWebhook}>Create Webhook</Button>
                </CardFooter>
              </Card>
            ) : webhooks.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <Webhook className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-xl font-medium">No Webhooks Configured</h3>
                <p className="text-muted-foreground mt-2 max-w-md">
                  You haven't set up any webhooks yet. Webhooks allow external services to receive real-time updates
                  from the Command Center.
                </p>
                <Button className="mt-4" onClick={() => setIsCreatingWebhook(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Your First Webhook
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {webhooks.map((webhook) => (
                  <Card key={webhook.id} className="overflow-hidden">
                    <div className="flex border-b">
                      <div className={`w-2 ${webhook.active ? "bg-green-600" : "bg-gray-300"}`}></div>
                      <div className="flex-1 p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{webhook.name}</h3>
                            <Badge
                              variant={webhook.active ? "default" : "outline"}
                              className={webhook.active ? "bg-green-100 text-green-800 hover:bg-green-200" : ""}
                            >
                              {webhook.active ? "Active" : "Inactive"}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2">
                            <Switch
                              checked={webhook.active}
                              onCheckedChange={(checked) => handleToggleWebhook(webhook.id, checked)}
                              aria-label="Toggle webhook"
                            />
                            <Button variant="ghost" size="icon" onClick={() => handleDeleteWebhook(webhook.id)}>
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Delete</span>
                            </Button>
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                          <div>
                            <p className="text-sm text-muted-foreground">Endpoint URL</p>
                            <div className="flex items-center gap-1">
                              <p className="text-sm font-medium truncate max-w-[250px]">{webhook.url}</p>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={() => window.open(webhook.url, "_blank")}
                              >
                                <ExternalLink className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Events</p>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {webhook.events.map((event) => (
                                <Badge key={event} variant="outline" className="text-xs">
                                  {getEventName(event)}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Secret</p>
                            <div className="flex items-center gap-1">
                              <p className="text-sm font-medium">••••••••••••••••</p>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={() => setShowSecretDialog(webhook.id)}
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Last Triggered</p>
                            <p className="text-sm font-medium">
                              {webhook.lastTriggered ? new Date(webhook.lastTriggered).toLocaleString() : "Never"}
                            </p>
                          </div>
                        </div>
                        <div className="flex justify-end mt-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTestWebhook(webhook.id)}
                            disabled={isTestingWebhook === webhook.id}
                          >
                            {isTestingWebhook === webhook.id ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Testing...
                              </>
                            ) : (
                              <>
                                <RefreshCw className="mr-2 h-4 w-4" />
                                Test Webhook
                              </>
                            )}
                          </Button>
                        </div>
                        {isTestingWebhook === webhook.id && testResult && (
                          <div
                            className={`mt-2 p-2 text-sm rounded-md ${
                              testResult.success
                                ? "bg-green-50 text-green-800 border border-green-200"
                                : "bg-red-50 text-red-800 border border-red-200"
                            }`}
                          >
                            <div className="flex items-center gap-2">
                              {testResult.success ? <Check className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                              <span>{testResult.message}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="events" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Available Webhook Events</CardTitle>
                <CardDescription>These are all the events you can subscribe to with your webhooks</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {["Content", "Products", "System"].map((category) => (
                    <div key={category} className="space-y-2">
                      <h3 className="text-lg font-medium">{category} Events</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {availableEvents
                          .filter((event) => event.category === category)
                          .map((event) => (
                            <div key={event.id} className="border rounded-md p-3 space-y-1">
                              <div className="flex items-center justify-between">
                                <h4 className="font-medium">{event.name}</h4>
                                <Badge variant="outline" className="text-xs">
                                  {event.id}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">{event.description}</p>
                            </div>
                          ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="logs" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Webhook Delivery Logs</CardTitle>
                <CardDescription>View the history of webhook deliveries and their status</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px] pr-4">
                  <div className="space-y-4">
                    {[...Array(10)].map((_, i) => {
                      const success = Math.random() > 0.3
                      const webhook = webhooks[Math.floor(Math.random() * webhooks.length)]
                      const event = availableEvents[Math.floor(Math.random() * availableEvents.length)]
                      const date = new Date()
                      date.setHours(date.getHours() - i)

                      return (
                        <div key={i} className="border rounded-md p-3 space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Badge
                                variant="outline"
                                className={`${success ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                              >
                                {success ? "Success" : "Failed"}
                              </Badge>
                              <span className="font-medium">{webhook?.name || "Webhook"}</span>
                            </div>
                            <span className="text-sm text-muted-foreground">{date.toLocaleString()}</span>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                            <div>
                              <p className="text-muted-foreground">Event</p>
                              <p>{event?.name || "Unknown Event"}</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">URL</p>
                              <p className="truncate max-w-[250px]">{webhook?.url || "https://example.com/webhook"}</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Response</p>
                              <p>{success ? "200 OK" : "404 Not Found"}</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Duration</p>
                              <p>{Math.floor(Math.random() * 1000)}ms</p>
                            </div>
                          </div>
                          {!success && (
                            <div className="bg-red-50 p-2 rounded-md text-sm text-red-800">
                              Error: Could not connect to the webhook URL. Please check the endpoint and try again.
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* Secret Dialog */}
      <Dialog open={showSecretDialog !== null} onOpenChange={() => setShowSecretDialog(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Webhook Secret</DialogTitle>
            <DialogDescription>
              This secret is used to verify that requests are coming from the Command Center.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>Secret Key</Label>
              <div className="flex space-x-2">
                <Input value={webhooks.find((w) => w.id === showSecretDialog)?.secret || ""} readOnly />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handleCopySecret(webhooks.find((w) => w.id === showSecretDialog)?.secret || "")}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Keep this secret secure. It will be used to sign webhook payloads.
              </p>
            </div>
          </div>
          <DialogFooter className="flex space-x-2 sm:justify-between">
            <Button variant="outline" onClick={() => setShowSecretDialog(null)}>
              Close
            </Button>
            <Button
              variant="default"
              onClick={() => handleRegenerateSecret(showSecretDialog || "")}
              disabled={isRegeneratingSecret}
            >
              {isRegeneratingSecret ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Regenerating...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Regenerate Secret
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}

"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { XAxis, YAxis, CartesianGrid, Legend, ResponsiveContainer, LineChart, Line } from "recharts"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowUpRight, DollarSign, Film, CreditCard, Zap, ExternalLink } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"

// Sample data for the budget analytics
const subscriptionData = {
  currentPlan: "Professional",
  price: 49.99,
  billingCycle: "monthly",
  nextBillingDate: "2023-06-15",
  creditsTotal: 500,
  creditsUsed: 320,
  estimatedVideos: 25,
  autoRenew: true,
}

const productionForecastData = [
  { month: "Jan", current: 25, upgraded: 40 },
  { month: "Feb", current: 28, upgraded: 45 },
  { month: "Mar", current: 30, upgraded: 48 },
  { month: "Apr", current: 32, upgraded: 52 },
  { month: "May", current: 35, upgraded: 56 },
  { month: "Jun", current: 38, upgraded: 60 },
]

const planComparisonData = [
  {
    name: "Basic",
    price: 29.99,
    credits: 250,
    estimatedVideos: 15,
    features: ["Basic AI repurposing", "3 social platforms", "Standard templates"],
  },
  {
    name: "Professional",
    price: 49.99,
    credits: 500,
    estimatedVideos: 25,
    features: ["Advanced AI repurposing", "All social platforms", "Premium templates", "Complementary products"],
  },
  {
    name: "Enterprise",
    price: 99.99,
    credits: 1200,
    estimatedVideos: 60,
    features: [
      "Advanced AI repurposing",
      "All social platforms",
      "Premium templates",
      "Complementary products",
      "API access",
      "Priority support",
    ],
  },
]

const recommendedApps = [
  {
    name: "VideoEditor Pro",
    category: "Video Editing",
    price: "$19.99/mo",
    integration: "Direct",
    description: "Professional video editing with AI-powered features",
    roi: "High",
  },
  {
    name: "ContentScheduler",
    category: "Scheduling",
    price: "$12.99/mo",
    integration: "API",
    description: "Advanced scheduling for all your social media content",
    roi: "Medium",
  },
  {
    name: "AnalyticsMaster",
    category: "Analytics",
    price: "$24.99/mo",
    integration: "Direct",
    description: "Deep insights into content performance across platforms",
    roi: "High",
  },
  {
    name: "ThumbnailCreator",
    category: "Graphics",
    price: "$9.99/mo",
    integration: "Manual",
    description: "AI-powered thumbnail generation for maximum engagement",
    roi: "Medium",
  },
]

export function BudgetAnalytics() {
  const [selectedPlan, setSelectedPlan] = useState("Professional")

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Subscription Cost</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${subscriptionData.price}/mo</div>
            <p className="text-xs text-muted-foreground">
              {subscriptionData.currentPlan} Plan • Next billing:{" "}
              {new Date(subscriptionData.nextBillingDate).toLocaleDateString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Credits Available</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{subscriptionData.creditsTotal - subscriptionData.creditsUsed}</div>
            <div className="mt-2">
              <Progress value={(subscriptionData.creditsUsed / subscriptionData.creditsTotal) * 100} />
              <p className="text-xs text-muted-foreground mt-1">
                {subscriptionData.creditsUsed} used of {subscriptionData.creditsTotal} total
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Estimated Videos</CardTitle>
            <Film className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{subscriptionData.estimatedVideos}</div>
            <p className="text-xs text-muted-foreground">Based on your current credit usage patterns</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Production Efficiency</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">85%</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
              <span className="text-green-500">+12%</span> from last month
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="forecast" className="space-y-4">
        <TabsList>
          <TabsTrigger value="forecast">Production Forecast</TabsTrigger>
          <TabsTrigger value="plans">Plan Comparison</TabsTrigger>
          <TabsTrigger value="apps">Recommended Apps</TabsTrigger>
        </TabsList>

        <TabsContent value="forecast" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Production Forecast</CardTitle>
              <CardDescription>Projected video production capacity over the next 6 months</CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              <ChartContainer
                config={{
                  current: {
                    label: "Current Plan",
                    color: "hsl(var(--chart-1))",
                  },
                  upgraded: {
                    label: "Enterprise Plan",
                    color: "hsl(var(--chart-2))",
                  },
                }}
                className="h-[300px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={productionForecastData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="current"
                      stroke="var(--color-current)"
                      strokeWidth={2}
                      name="Current Plan"
                    />
                    <Line
                      type="monotone"
                      dataKey="upgraded"
                      stroke="var(--color-upgraded)"
                      strokeWidth={2}
                      name="Enterprise Plan"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
            <CardFooter>
              <p className="text-sm text-muted-foreground">
                Upgrading to the Enterprise plan could increase your production capacity by up to 60%.
              </p>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="plans" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Plan Comparison</CardTitle>
              <CardDescription>
                Compare different subscription plans to find the best fit for your needs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Plan</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Credits</TableHead>
                    <TableHead>Est. Videos</TableHead>
                    <TableHead>Features</TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {planComparisonData.map((plan) => (
                    <TableRow key={plan.name} className={plan.name === selectedPlan ? "bg-muted/50" : ""}>
                      <TableCell className="font-medium">
                        {plan.name}
                        {plan.name === selectedPlan && (
                          <Badge variant="outline" className="ml-2">
                            Current
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>${plan.price}/mo</TableCell>
                      <TableCell>{plan.credits}</TableCell>
                      <TableCell>{plan.estimatedVideos}</TableCell>
                      <TableCell>
                        <ul className="list-disc pl-5 text-sm">
                          {plan.features.map((feature, index) => (
                            <li key={index}>{feature}</li>
                          ))}
                        </ul>
                      </TableCell>
                      <TableCell>
                        {plan.name !== selectedPlan ? (
                          <Button variant="outline" size="sm">
                            Upgrade
                          </Button>
                        ) : (
                          <Button variant="outline" size="sm" disabled>
                            Current
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="apps" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recommended Apps</CardTitle>
              <CardDescription>Suggested applications to increase your production efficiency</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>App</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Integration</TableHead>
                    <TableHead>ROI</TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recommendedApps.map((app) => (
                    <TableRow key={app.name}>
                      <TableCell className="font-medium">{app.name}</TableCell>
                      <TableCell>{app.category}</TableCell>
                      <TableCell>{app.price}</TableCell>
                      <TableCell>{app.integration}</TableCell>
                      <TableCell>
                        <Badge variant={app.roi === "High" ? "default" : "secondary"}>{app.roi}</Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="icon">
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter>
              <p className="text-sm text-muted-foreground">
                These apps integrate with our platform to enhance your content production workflow.
              </p>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

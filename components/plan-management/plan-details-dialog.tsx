"use client"

import React from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Crown,
  Star,
  DollarSign,
  Users,
  Package,
  BarChart,
  Zap,
  Shield,
  Settings,
  Video,
  Image,
  TrendingUp,
  Check,
  X,
} from 'lucide-react'
import type { SubscriptionPlan } from '@/lib/api/subscription-plans'

interface PlanDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  plan: SubscriptionPlan | null
}

export function PlanDetailsDialog({ open, onOpenChange, plan }: PlanDetailsDialogProps) {
  if (!plan) return null

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default">Active</Badge>
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>
      case 'draft':
        return <Badge variant="outline">Draft</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const FeatureItem = ({ 
    icon, 
    label, 
    value, 
    type = 'boolean' 
  }: { 
    icon: React.ReactNode
    label: string
    value: boolean | number
    type?: 'boolean' | 'number'
  }) => (
    <div className="flex items-center justify-between py-2">
      <div className="flex items-center space-x-2">
        {icon}
        <span className="text-sm">{label}</span>
      </div>
      <div>
        {type === 'boolean' ? (
          value ? (
            <Check className="h-4 w-4 text-green-500" />
          ) : (
            <X className="h-4 w-4 text-red-500" />
          )
        ) : (
          <Badge variant="outline">{value}</Badge>
        )}
      </div>
    </div>
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Crown className="h-5 w-5" />
            <span>{plan.name}</span>
            {plan.is_popular && (
              <Star className="h-4 w-4 text-yellow-500 fill-current" />
            )}
          </DialogTitle>
          <DialogDescription>
            Detailed view of the subscription plan configuration
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[70vh] pr-4">
          <div className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Plan Name</p>
                    <p className="text-lg font-semibold">{plan.name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Slug</p>
                    <p className="font-mono text-sm bg-muted px-2 py-1 rounded">{plan.slug}</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium text-muted-foreground">Description</p>
                  <p className="text-sm mt-1">{plan.description}</p>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Status</p>
                    <div className="mt-1">{getStatusBadge(plan.status)}</div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Sort Order</p>
                    <Badge variant="outline" className="mt-1">{plan.sort_order}</Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Popular Plan</p>
                    <div className="mt-1">
                      {plan.is_popular ? (
                        <Badge variant="default">Yes</Badge>
                      ) : (
                        <Badge variant="outline">No</Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Pricing */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <DollarSign className="h-5 w-5" />
                  <span>Pricing</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center p-4 border rounded-lg">
                    <p className="text-sm font-medium text-muted-foreground">Monthly</p>
                    <p className="text-3xl font-bold">{formatPrice(plan.price_monthly)}</p>
                    <p className="text-sm text-muted-foreground">per month</p>
                    {plan.stripe_price_monthly_id && (
                      <p className="text-xs font-mono text-muted-foreground mt-1">
                        {plan.stripe_price_monthly_id}
                      </p>
                    )}
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <p className="text-sm font-medium text-muted-foreground">Annual</p>
                    <p className="text-3xl font-bold">{formatPrice(plan.price_annual)}</p>
                    <p className="text-sm text-muted-foreground">per year</p>
                    {plan.price_annual > 0 && plan.price_monthly > 0 && (
                      <p className="text-xs text-green-600 mt-1">
                        Save {Math.round((1 - (plan.price_annual / 12) / plan.price_monthly) * 100)}%
                      </p>
                    )}
                    {plan.stripe_price_annual_id && (
                      <p className="text-xs font-mono text-muted-foreground mt-1">
                        {plan.stripe_price_annual_id}
                      </p>
                    )}
                  </div>
                </div>

                {/* Stripe Integration Status */}
                <div className="pt-2 border-t">
                  <p className="text-sm font-medium text-muted-foreground mb-2">Stripe Integration</p>
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${plan.stripe_price_monthly_id ? 'bg-green-500' : 'bg-gray-300'}`} />
                      <span>Monthly Price ID</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${plan.stripe_price_annual_id ? 'bg-green-500' : 'bg-gray-300'}`} />
                      <span>Annual Price ID</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Features */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Features & Access</CardTitle>
                <CardDescription>What's included in this plan</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-3 flex items-center space-x-2">
                    <Shield className="h-4 w-4" />
                    <span>Access Features</span>
                  </h4>
                  <div className="space-y-1">
                    <FeatureItem
                      icon={<Zap className="h-4 w-4" />}
                      label="API Access"
                      value={plan.features.api_access}
                    />
                    <FeatureItem
                      icon={<Package className="h-4 w-4" />}
                      label="Product Management"
                      value={plan.features.product_manage}
                    />
                    <FeatureItem
                      icon={<Users className="h-4 w-4" />}
                      label="AI Agents Access"
                      value={plan.features.ai_agents_access}
                    />
                    <FeatureItem
                      icon={<BarChart className="h-4 w-4" />}
                      label="Analytics Access"
                      value={plan.features.analytics_access}
                    />
                    <FeatureItem
                      icon={<Image className="h-4 w-4" />}
                      label="Image Generation"
                      value={plan.features.image_generation}
                    />
                    <FeatureItem
                      icon={<Video className="h-4 w-4" />}
                      label="Video Generation"
                      value={plan.features.video_generation}
                    />
                    <FeatureItem
                      icon={<Shield className="h-4 w-4" />}
                      label="Priority Support"
                      value={plan.features.priority_support}
                    />
                    <FeatureItem
                      icon={<TrendingUp className="h-4 w-4" />}
                      label="Product Analysis"
                      value={plan.features.product_analysis}
                    />
                    <FeatureItem
                      icon={<BarChart className="h-4 w-4" />}
                      label="Advanced Analytics"
                      value={plan.features.advanced_analytics}
                    />
                    <FeatureItem
                      icon={<Settings className="h-4 w-4" />}
                      label="Prototype Pipeline"
                      value={plan.features.prototype_pipeline}
                    />
                    <FeatureItem
                      icon={<Settings className="h-4 w-4" />}
                      label="Production Pipeline"
                      value={plan.features.production_pipeline}
                    />
                    <FeatureItem
                      icon={<Package className="h-4 w-4" />}
                      label="Blog Management"
                      value={plan.features.brog_management}
                    />
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium mb-3 flex items-center space-x-2">
                    <BarChart className="h-4 w-4" />
                    <span>Limits & Quotas</span>
                  </h4>
                  <div className="space-y-1">
                    <FeatureItem
                      icon={<Package className="h-4 w-4" />}
                      label="Products Created"
                      value={plan.features.limits.products_created}
                      type="number"
                    />
                    <FeatureItem
                      icon={<TrendingUp className="h-4 w-4" />}
                      label="Analysis Requests"
                      value={plan.features.limits.analysis_requests}
                      type="number"
                    />
                    <FeatureItem
                      icon={<Image className="h-4 w-4" />}
                      label="Image Generations"
                      value={plan.features.limits.image_generations}
                      type="number"
                    />
                    <FeatureItem
                      icon={<Video className="h-4 w-4" />}
                      label="Video Generations"
                      value={plan.features.limits.video_generations}
                      type="number"
                    />
                    <FeatureItem
                      icon={<Image className="h-4 w-4" />}
                      label="Prototype Image Generations"
                      value={plan.features.limits.prototype_image_generations}
                      type="number"
                    />
                    <FeatureItem
                      icon={<Video className="h-4 w-4" />}
                      label="Prototype Video Generations"
                      value={plan.features.limits.prototype_video_generations}
                      type="number"
                    />
                    <FeatureItem
                      icon={<Package className="h-4 w-4" />}
                      label="Blogs Created"
                      value={plan.features.limits.brogs_created}
                      type="number"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Metadata */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Metadata</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium text-muted-foreground">Plan ID</p>
                    <p className="font-mono text-xs bg-muted px-2 py-1 rounded mt-1">{plan.id}</p>
                  </div>
                  <div>
                    <p className="font-medium text-muted-foreground">Created</p>
                    <p className="mt-1">{formatDate(plan.created_at)}</p>
                  </div>
                  <div className="col-span-2">
                    <p className="font-medium text-muted-foreground">Last Updated</p>
                    <p className="mt-1">{formatDate(plan.updated_at)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}

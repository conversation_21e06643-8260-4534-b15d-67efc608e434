"use client"

import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import type { SubscriptionPlan, CreatePlanData, PlanFeatures, PlanLimits } from '@/lib/api/subscription-plans'

interface PlanFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: CreatePlanData) => void
  plan?: SubscriptionPlan | null
  title: string
}

const defaultFeatures: PlanFeatures = {
  api_access: false,
  product_manage: true,
  ai_agents_access: false,
  analytics_access: false,
  image_generation: true,
  video_generation: true,
  priority_support: false,
  product_analysis: false,
  advanced_analytics: false,
  prototype_pipeline: false,
  production_pipeline: false,
  brog_management: false,
  limits: {
    products_created: 10,
    analysis_requests: 5,
    image_generations: 20,
    video_generations: 5,
    prototype_image_generations: 10,
    prototype_video_generations: 3,
    brogs_created: 5,
  },
}

export function PlanFormDialog({ open, onOpenChange, onSubmit, plan, title }: PlanFormDialogProps) {
  const [formData, setFormData] = useState<CreatePlanData>({
    name: '',
    slug: '',
    description: '',
    price_monthly: 0,
    price_annual: 0,
    stripe_price_monthly_id: null,
    stripe_price_annual_id: null,
    status: 'inactive',
    features: defaultFeatures,
    sort_order: 0,
    is_popular: false,
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (plan) {
      setFormData({
        name: plan.name,
        slug: plan.slug,
        description: plan.description,
        price_monthly: plan.price_monthly,
        price_annual: plan.price_annual,
        stripe_price_monthly_id: plan.stripe_price_monthly_id || null,
        stripe_price_annual_id: plan.stripe_price_annual_id || null,
        status: plan.status,
        features: plan.features,
        sort_order: plan.sort_order,
        is_popular: plan.is_popular,
      })
    } else {
      setFormData({
        name: '',
        slug: '',
        description: '',
        price_monthly: 0,
        price_annual: 0,
        stripe_price_monthly_id: null,
        stripe_price_annual_id: null,
        status: 'inactive',
        features: defaultFeatures,
        sort_order: 0,
        is_popular: false,
      })
    }
  }, [plan, open])

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleNameChange = (name: string) => {
    setFormData(prev => ({
      ...prev,
      name,
      slug: generateSlug(name),
    }))
  }

  const handleFeatureChange = (key: keyof PlanFeatures, value: boolean | number) => {
    setFormData(prev => ({
      ...prev,
      features: {
        ...prev.features,
        [key]: value,
      },
    }))
  }

  const handleLimitChange = (key: keyof PlanLimits, value: number) => {
    setFormData(prev => ({
      ...prev,
      features: {
        ...prev.features,
        limits: {
          ...prev.features.limits,
          [key]: value,
        },
      },
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim() || !formData.description.trim()) {
      return
    }

    setIsSubmitting(true)
    try {
      await onSubmit(formData)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>
            Configure the subscription plan details and features
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[70vh] pr-4">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Basic Information</CardTitle>
                <CardDescription>Plan name, description, and pricing</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Plan Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleNameChange(e.target.value)}
                      placeholder="e.g., Starter Plan"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="slug">Slug</Label>
                    <Input
                      id="slug"
                      value={formData.slug}
                      onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                      placeholder="e.g., starter"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe what this plan offers..."
                    rows={3}
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price_monthly">Monthly Price ($)</Label>
                    <Input
                      id="price_monthly"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.price_monthly}
                      onChange={(e) => setFormData(prev => ({ ...prev, price_monthly: parseFloat(e.target.value) || 0 }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="price_annual">Annual Price ($)</Label>
                    <Input
                      id="price_annual"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.price_annual}
                      onChange={(e) => setFormData(prev => ({ ...prev, price_annual: parseFloat(e.target.value) || 0 }))}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="stripe_price_monthly_id">Stripe Monthly Price ID</Label>
                    <Input
                      id="stripe_price_monthly_id"
                      value={formData.stripe_price_monthly_id || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, stripe_price_monthly_id: e.target.value || null }))}
                      placeholder="price_1234567890"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="stripe_price_annual_id">Stripe Annual Price ID</Label>
                    <Input
                      id="stripe_price_annual_id"
                      value={formData.stripe_price_annual_id || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, stripe_price_annual_id: e.target.value || null }))}
                      placeholder="price_0987654321"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sort_order">Sort Order</Label>
                  <Input
                    id="sort_order"
                    type="number"
                    min="0"
                    value={formData.sort_order}
                    onChange={(e) => setFormData(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value: 'active' | 'inactive' | 'draft') => 
                        setFormData(prev => ({ ...prev, status: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="inactive">Inactive</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="deprecated">Deprecated</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center space-x-2 pt-6">
                    <Switch
                      id="is_popular"
                      checked={formData.is_popular}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_popular: checked }))}
                    />
                    <Label htmlFor="is_popular">Mark as Popular</Label>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Features Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Features & Limits</CardTitle>
                <CardDescription>Configure what this plan includes</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Access Features */}
                <div>
                  <h4 className="font-medium mb-3">Access Features</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="api_access"
                        checked={formData.features.api_access}
                        onCheckedChange={(checked) => handleFeatureChange('api_access', checked)}
                      />
                      <Label htmlFor="api_access">API Access</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="product_manage"
                        checked={formData.features.product_manage}
                        onCheckedChange={(checked) => handleFeatureChange('product_manage', checked)}
                      />
                      <Label htmlFor="product_manage">Product Management</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="ai_agents_access"
                        checked={formData.features.ai_agents_access}
                        onCheckedChange={(checked) => handleFeatureChange('ai_agents_access', checked)}
                      />
                      <Label htmlFor="ai_agents_access">AI Agents Access</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="analytics_access"
                        checked={formData.features.analytics_access}
                        onCheckedChange={(checked) => handleFeatureChange('analytics_access', checked)}
                      />
                      <Label htmlFor="analytics_access">Analytics Access</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="image_generation"
                        checked={formData.features.image_generation}
                        onCheckedChange={(checked) => handleFeatureChange('image_generation', checked)}
                      />
                      <Label htmlFor="image_generation">Image Generation</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="video_generation"
                        checked={formData.features.video_generation}
                        onCheckedChange={(checked) => handleFeatureChange('video_generation', checked)}
                      />
                      <Label htmlFor="video_generation">Video Generation</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="priority_support"
                        checked={formData.features.priority_support}
                        onCheckedChange={(checked) => handleFeatureChange('priority_support', checked)}
                      />
                      <Label htmlFor="priority_support">Priority Support</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="product_analysis"
                        checked={formData.features.product_analysis}
                        onCheckedChange={(checked) => handleFeatureChange('product_analysis', checked)}
                      />
                      <Label htmlFor="product_analysis">Product Analysis</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="advanced_analytics"
                        checked={formData.features.advanced_analytics}
                        onCheckedChange={(checked) => handleFeatureChange('advanced_analytics', checked)}
                      />
                      <Label htmlFor="advanced_analytics">Advanced Analytics</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="prototype_pipeline"
                        checked={formData.features.prototype_pipeline}
                        onCheckedChange={(checked) => handleFeatureChange('prototype_pipeline', checked)}
                      />
                      <Label htmlFor="prototype_pipeline">Prototype Pipeline</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="production_pipeline"
                        checked={formData.features.production_pipeline}
                        onCheckedChange={(checked) => handleFeatureChange('production_pipeline', checked)}
                      />
                      <Label htmlFor="production_pipeline">Production Pipeline</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="brog_management"
                        checked={formData.features.brog_management}
                        onCheckedChange={(checked) => handleFeatureChange('brog_management', checked)}
                      />
                      <Label htmlFor="brog_management">Blog Management</Label>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Numeric Limits */}
                <div>
                  <h4 className="font-medium mb-3">Limits & Quotas</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="products_created">Products Created</Label>
                      <Input
                        id="products_created"
                        type="number"
                        min="0"
                        value={formData.features.limits.products_created}
                        onChange={(e) => handleLimitChange('products_created', parseInt(e.target.value) || 0)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="analysis_requests">Analysis Requests</Label>
                      <Input
                        id="analysis_requests"
                        type="number"
                        min="0"
                        value={formData.features.limits.analysis_requests}
                        onChange={(e) => handleLimitChange('analysis_requests', parseInt(e.target.value) || 0)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="image_generations">Image Generations</Label>
                      <Input
                        id="image_generations"
                        type="number"
                        min="0"
                        value={formData.features.limits.image_generations}
                        onChange={(e) => handleLimitChange('image_generations', parseInt(e.target.value) || 0)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="video_generations">Video Generations</Label>
                      <Input
                        id="video_generations"
                        type="number"
                        min="0"
                        value={formData.features.limits.video_generations}
                        onChange={(e) => handleLimitChange('video_generations', parseInt(e.target.value) || 0)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="prototype_image_generations">Prototype Image Generations</Label>
                      <Input
                        id="prototype_image_generations"
                        type="number"
                        min="0"
                        value={formData.features.limits.prototype_image_generations}
                        onChange={(e) => handleLimitChange('prototype_image_generations', parseInt(e.target.value) || 0)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="prototype_video_generations">Prototype Video Generations</Label>
                      <Input
                        id="prototype_video_generations"
                        type="number"
                        min="0"
                        value={formData.features.limits.prototype_video_generations}
                        onChange={(e) => handleLimitChange('prototype_video_generations', parseInt(e.target.value) || 0)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="brogs_created">Blogs Created</Label>
                      <Input
                        id="brogs_created"
                        type="number"
                        min="0"
                        value={formData.features.limits.brogs_created}
                        onChange={(e) => handleLimitChange('brogs_created', parseInt(e.target.value) || 0)}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </form>
        </ScrollArea>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button type="submit" onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : (plan ? 'Update Plan' : 'Create Plan')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogT<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "@/components/ui/use-toast"
import { Check, Link2, Loader2 } from "lucide-react"

export function APIConnectionDialog({ children }: { children?: React.ReactNode }) {
  const [open, setOpen] = useState(false)
  const [connecting, setConnecting] = useState(false)
  const [poppyApiKey, setPoppyApiKey] = useState("")
  const [ltxApiKey, setLtxApiKey] = useState("")
  const [opusClipsApi<PERSON><PERSON>, setOpus<PERSON>lips<PERSON><PERSON><PERSON><PERSON>] = useState("")
  const [poppyConnected, setPoppyConnected] = useState(false)
  const [ltxConnected, setLtxConnected] = useState(false)
  const [opusConnected, setOpusConnected] = useState(false)

  const handleConnect = async (service: string) => {
    setConnecting(true)

    // Simulate API connection
    await new Promise((resolve) => setTimeout(resolve, 1500))

    setConnecting(false)

    if (service === "poppy" && poppyApiKey) {
      setPoppyConnected(true)
      toast({
        title: "Connected to Poppy AI",
        description: "Your Poppy AI account has been successfully connected.",
      })
    } else if (service === "ltx" && ltxApiKey) {
      setLtxConnected(true)
      toast({
        title: "Connected to LTX Studio",
        description: "Your LTX Studio account has been successfully connected.",
      })
    } else if (service === "opus" && opusClipsApiKey) {
      setOpusConnected(true)
      toast({
        title: "Connected to OpusClips",
        description: "Your OpusClips account has been successfully connected.",
      })
    } else {
      toast({
        title: "Connection Failed",
        description: "Please enter a valid API key.",
        variant: "destructive",
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children || <Button>Connect APIs</Button>}</DialogTrigger>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Connect API Services</DialogTitle>
          <DialogDescription>
            Connect your AI and content creation services to enable seamless workflow.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="poppy" className="mt-4">
          <TabsList className="grid grid-cols-3">
            <TabsTrigger value="poppy">Poppy AI</TabsTrigger>
            <TabsTrigger value="ltx">LTX Studio</TabsTrigger>
            <TabsTrigger value="opus">OpusClips</TabsTrigger>
          </TabsList>

          <TabsContent value="poppy" className="space-y-4 mt-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="poppy-api-key">Poppy AI API Key</Label>
                {poppyConnected && (
                  <Badge variant="outline" className="text-green-500 border-green-500 flex items-center gap-1">
                    <Check className="h-3 w-3" /> Connected
                  </Badge>
                )}
              </div>
              <div className="flex space-x-2">
                <Input
                  id="poppy-api-key"
                  type="password"
                  placeholder="Enter your Poppy AI API key"
                  value={poppyApiKey}
                  onChange={(e) => setPoppyApiKey(e.target.value)}
                  disabled={poppyConnected}
                />
                <Button onClick={() => handleConnect("poppy")} disabled={connecting || poppyConnected || !poppyApiKey}>
                  {connecting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : poppyConnected ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Link2 className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Connect to Poppy AI for scriptwriting suggestions based on trending topics.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="ltx" className="space-y-4 mt-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="ltx-api-key">LTX Studio API Key</Label>
                {ltxConnected && (
                  <Badge variant="outline" className="text-green-500 border-green-500 flex items-center gap-1">
                    <Check className="h-3 w-3" /> Connected
                  </Badge>
                )}
              </div>
              <div className="flex space-x-2">
                <Input
                  id="ltx-api-key"
                  type="password"
                  placeholder="Enter your LTX Studio API key"
                  value={ltxApiKey}
                  onChange={(e) => setLtxApiKey(e.target.value)}
                  disabled={ltxConnected}
                />
                <Button onClick={() => handleConnect("ltx")} disabled={connecting || ltxConnected || !ltxApiKey}>
                  {connecting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : ltxConnected ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Link2 className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Connect to LTX Studio to automatically create videos from your scripts.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="opus" className="space-y-4 mt-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="opus-api-key">OpusClips API Key</Label>
                {opusConnected && (
                  <Badge variant="outline" className="text-green-500 border-green-500 flex items-center gap-1">
                    <Check className="h-3 w-3" /> Connected
                  </Badge>
                )}
              </div>
              <div className="flex space-x-2">
                <Input
                  id="opus-api-key"
                  type="password"
                  placeholder="Enter your OpusClips API key"
                  value={opusClipsApiKey}
                  onChange={(e) => setOpusClipsApiKey(e.target.value)}
                  disabled={opusConnected}
                />
                <Button
                  onClick={() => handleConnect("opus")}
                  disabled={connecting || opusConnected || !opusClipsApiKey}
                >
                  {connecting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : opusConnected ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Link2 className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Connect to OpusClips to repurpose your videos for different platforms.
              </p>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Close
          </Button>
          <Button
            onClick={() => {
              toast({
                title: "API Connections Saved",
                description: "Your API connections have been saved successfully.",
              })
              setOpen(false)
            }}
            disabled={!poppyConnected && !ltxConnected && !opusConnected}
          >
            Save Connections
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

function Badge({ children, className, variant }: { children: React.ReactNode; className?: string; variant?: string }) {
  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium ${className} ${variant === "outline" ? "border" : "bg-primary text-primary-foreground"}`}
    >
      {children}
    </span>
  )
}

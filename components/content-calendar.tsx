"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar } from "@/components/ui/calendar"
import { ChevronLeft, ChevronRight, Facebook, Instagram, Plus, Twitter, Youtube } from "lucide-react"
import { format, addDays, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay, addWeeks, subWeeks } from "date-fns"
import { CreateEventDialog } from "./create-event-dialog"
import { toast } from "@/components/ui/use-toast"

export interface ContentEvent {
  title: string
  date: string
  category: string
  avatar: string
  avatarName: string
  avatarInitials: string
  platforms: string[]
  status: "published" | "scheduled" | "archived"
  description?: string
}

export function ContentCalendar() {
  const [date, setDate] = useState<Date>(new Date())
  const [view, setView] = useState<"day" | "week" | "month">("week")
  const [currentWeek, setCurrentWeek] = useState<Date>(new Date())
  const [events, setEvents] = useState<ContentEvent[]>(contentEvents)
  const [avatarFilter, setAvatarFilter] = useState<string>("all")

  const handlePrevious = () => {
    if (view === "week") {
      setCurrentWeek(subWeeks(currentWeek, 1))
    }
  }

  const handleNext = () => {
    if (view === "week") {
      setCurrentWeek(addWeeks(currentWeek, 1))
    }
  }

  const handleEventCreated = (newEvent: ContentEvent) => {
    setEvents((prevEvents) => [...prevEvents, newEvent])
    toast({
      title: "Event Added",
      description: "Your new content event has been added to the calendar.",
    })
  }

  const filteredEvents =
    avatarFilter === "all"
      ? events
      : events.filter((event) => {
          const avatarCode = event.avatar.split("/").pop()?.split(".")[0] || ""
          return avatarCode === avatarFilter || event.avatar === avatarFilter
        })

  const weekStart = startOfWeek(currentWeek, { weekStartsOn: 1 })
  const weekEnd = endOfWeek(currentWeek, { weekStartsOn: 1 })
  const days = eachDayOfInterval({ start: weekStart, end: weekEnd })

  return (
    <Card className="col-span-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Content Calendar</CardTitle>
        <div className="flex items-center space-x-2">
          <Tabs defaultValue="week" onValueChange={(value) => setView(value as "day" | "week" | "month")}>
            <TabsList>
              <TabsTrigger value="day">Day</TabsTrigger>
              <TabsTrigger value="week">Week</TabsTrigger>
              <TabsTrigger value="month">Month</TabsTrigger>
            </TabsList>

            <TabsContent value="day" className="mt-0">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">{format(date, "MMMM d, yyyy")}</h3>
                <div className="flex space-x-2">
                  <Button variant="outline" size="icon" onClick={() => setDate(addDays(date, -1))}>
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon" onClick={() => setDate(addDays(date, 1))}>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="space-y-4">
                {filteredEvents
                  .filter((event) => isSameDay(new Date(event.date), date))
                  .map((event, index) => (
                    <ContentEvent key={index} event={event} />
                  ))}
              </div>
            </TabsContent>

            <TabsContent value="week" className="mt-0">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">
                  {format(weekStart, "MMM d")} - {format(weekEnd, "MMM d, yyyy")}
                </h3>
                <div className="flex space-x-2">
                  <Button variant="outline" size="icon" onClick={handlePrevious}>
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon" onClick={handleNext}>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-7 gap-4">
                {days.map((day, i) => (
                  <div key={i} className="border rounded-lg overflow-hidden">
                    <div
                      className={`p-2 text-center ${
                        isSameDay(day, new Date()) ? "bg-primary text-primary-foreground" : "bg-muted"
                      }`}
                    >
                      <div className="font-medium">{format(day, "EEE")}</div>
                      <div className="text-sm">{format(day, "MMM d")}</div>
                    </div>
                    <div className="p-2 space-y-2 h-[300px] overflow-y-auto">
                      {filteredEvents
                        .filter((event) => isSameDay(new Date(event.date), day))
                        .map((event, index) => (
                          <ContentEvent key={index} event={event} compact />
                        ))}
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="month" className="mt-0">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">{format(date, "MMMM yyyy")}</h3>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => {
                      const newDate = new Date(date)
                      newDate.setMonth(date.getMonth() - 1)
                      setDate(newDate)
                    }}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => {
                      const newDate = new Date(date)
                      newDate.setMonth(date.getMonth() + 1)
                      setDate(newDate)
                    }}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <Calendar
                mode="single"
                selected={date}
                onSelect={(newDate) => newDate && setDate(newDate)}
                className="rounded-md border"
                components={{
                  DayContent: (props) => {
                    const eventsForDay = filteredEvents.filter((event) => isSameDay(new Date(event.date), props.date))
                    return (
                      <div className="relative h-full w-full p-2">
                        <div className="absolute top-0 left-0 right-0 text-center">{props.date.getDate()}</div>
                        <div className="mt-4 flex flex-wrap gap-1 justify-center">
                          {eventsForDay.length > 0 &&
                            eventsForDay
                              .slice(0, 3)
                              .map((event, i) => (
                                <div
                                  key={i}
                                  className="w-2 h-2 rounded-full"
                                  style={{ backgroundColor: getCategoryColor(event.category) }}
                                ></div>
                              ))}
                          {eventsForDay.length > 3 && (
                            <div className="text-xs text-muted-foreground">+{eventsForDay.length - 3}</div>
                          )}
                        </div>
                      </div>
                    )
                  },
                }}
              />
            </TabsContent>
          </Tabs>
          <Select defaultValue="all" onValueChange={setAvatarFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Filter by avatar" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Avatars</SelectItem>
              <SelectItem value="travel">Travel</SelectItem>
              <SelectItem value="home">Home</SelectItem>
              <SelectItem value="health">Health</SelectItem>
              <SelectItem value="fitness">Fitness</SelectItem>
              <SelectItem value="entrepreneur">Entrepreneur</SelectItem>
            </SelectContent>
          </Select>
          <CreateEventDialog onEventCreated={handleEventCreated}>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-1" />
              Add Event
            </Button>
          </CreateEventDialog>
        </div>
      </CardHeader>
      <CardContent></CardContent>
    </Card>
  )
}

interface ContentEventProps {
  event: ContentEvent
  compact?: boolean
}

function ContentEvent({ event, compact = false }: ContentEventProps) {
  if (compact) {
    return (
      <div className="p-1 rounded text-xs border-l-2" style={{ borderLeftColor: getCategoryColor(event.category) }}>
        <div className="font-medium truncate">{event.title}</div>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            <Avatar className="h-4 w-4">
              <AvatarImage src={event.avatar || "/placeholder.svg"} alt={event.avatarName} />
              <AvatarFallback>{event.avatarInitials}</AvatarFallback>
            </Avatar>
            <span>{format(new Date(event.date), "h:mm a")}</span>
          </div>
          <div className="flex space-x-1">
            {event.platforms.map((platform, i) => (
              <PlatformIcon key={i} platform={platform} size="xs" />
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex items-center p-3 border rounded-lg">
      <div className="flex-shrink-0 mr-4">
        <Avatar className="h-10 w-10">
          <AvatarImage src={event.avatar || "/placeholder.svg"} alt={event.avatarName} />
          <AvatarFallback>{event.avatarInitials}</AvatarFallback>
        </Avatar>
      </div>
      <div className="flex-grow">
        <div className="flex items-center">
          <h4 className="font-medium">{event.title}</h4>
          <Badge
            variant="outline"
            className="ml-2"
            style={{ borderColor: getCategoryColor(event.category), color: getCategoryColor(event.category) }}
          >
            {event.category}
          </Badge>
          {event.status === "published" && <Badge className="ml-2">Published</Badge>}
          {event.status === "scheduled" && (
            <Badge variant="outline" className="ml-2">
              Scheduled
            </Badge>
          )}
          {event.status === "archived" && (
            <Badge variant="secondary" className="ml-2">
              Archived
            </Badge>
          )}
        </div>
        <div className="flex items-center text-sm text-muted-foreground mt-1">
          <span>{format(new Date(event.date), "MMMM d, yyyy • h:mm a")}</span>
        </div>
        {event.description && (
          <div className="text-sm text-muted-foreground mt-1 line-clamp-2">{event.description}</div>
        )}
      </div>
      <div className="flex items-center space-x-2">
        {event.platforms.map((platform, i) => (
          <PlatformIcon key={i} platform={platform} />
        ))}
      </div>
    </div>
  )
}

interface PlatformIconProps {
  platform: string
  size?: "xs" | "sm" | "md"
}

function PlatformIcon({ platform, size = "md" }: PlatformIconProps) {
  const sizeClass = {
    xs: "h-3 w-3",
    sm: "h-4 w-4",
    md: "h-5 w-5",
  }

  const iconClass = sizeClass[size]

  switch (platform) {
    case "youtube":
      return <Youtube className={iconClass} />
    case "instagram":
      return <Instagram className={iconClass} />
    case "twitter":
      return <Twitter className={iconClass} />
    case "facebook":
      return <Facebook className={iconClass} />
    default:
      return null
  }
}

function getCategoryColor(category: string): string {
  switch (category) {
    case "Travel":
      return "#adfa1d"
    case "Home":
      return "#f97316"
    case "Health":
      return "#06b6d4"
    case "Fitness":
      return "#8b5cf6"
    case "Entrepreneurship":
      return "#ec4899"
    default:
      return "#888888"
  }
}

// Sample data
const contentEvents: ContentEvent[] = [
  {
    title: "Top 10 Travel Destinations for 2023",
    date: "2023-05-06T09:00:00",
    category: "Travel",
    avatar: "/placeholder-user.jpg",
    avatarName: "Travel Avatar",
    avatarInitials: "TR",
    platforms: ["youtube", "instagram", "twitter"],
    status: "published",
  },
  {
    title: "Minimalist Home Office Setup Guide",
    date: "2023-05-07T10:30:00",
    category: "Home",
    avatar: "/placeholder-user.jpg",
    avatarName: "Home Avatar",
    avatarInitials: "HM",
    platforms: ["youtube", "instagram", "facebook"],
    status: "published",
  },
  {
    title: "5 Superfoods You Should Eat Daily",
    date: "2023-05-08T08:15:00",
    category: "Health",
    avatar: "/placeholder-user.jpg",
    avatarName: "Health Avatar",
    avatarInitials: "HE",
    platforms: ["youtube", "instagram", "twitter"],
    status: "published",
  },
  {
    title: "15-Minute Full Body Workout",
    date: "2023-05-09T07:00:00",
    category: "Fitness",
    avatar: "/placeholder-user.jpg",
    avatarName: "Fitness Avatar",
    avatarInitials: "FI",
    platforms: ["youtube", "instagram", "facebook"],
    status: "published",
  },
  {
    title: "How to Build a Side Hustle",
    date: "2023-05-10T11:00:00",
    category: "Entrepreneurship",
    avatar: "/placeholder-user.jpg",
    avatarName: "Entrepreneur Avatar",
    avatarInitials: "EN",
    platforms: ["youtube", "twitter", "facebook"],
    status: "published",
  },
  {
    title: "Budget Travel Tips for Europe",
    date: new Date().toISOString(),
    category: "Travel",
    avatar: "/placeholder-user.jpg",
    avatarName: "Travel Avatar",
    avatarInitials: "TR",
    platforms: ["youtube", "instagram"],
    status: "scheduled",
  },
  {
    title: "Smart Home Automation on a Budget",
    date: new Date(new Date().setDate(new Date().getDate() + 1)).toISOString(),
    category: "Home",
    avatar: "/placeholder-user.jpg",
    avatarName: "Home Avatar",
    avatarInitials: "HM",
    platforms: ["youtube", "facebook"],
    status: "scheduled",
  },
  {
    title: "Meal Prep for Busy Professionals",
    date: new Date(new Date().setDate(new Date().getDate() + 2)).toISOString(),
    category: "Health",
    avatar: "/placeholder-user.jpg",
    avatarName: "Health Avatar",
    avatarInitials: "HE",
    platforms: ["youtube", "instagram", "twitter"],
    status: "scheduled",
  },
  {
    title: "HIIT vs. Traditional Cardio",
    date: new Date(new Date().setDate(new Date().getDate() + 3)).toISOString(),
    category: "Fitness",
    avatar: "/placeholder-user.jpg",
    avatarName: "Fitness Avatar",
    avatarInitials: "FI",
    platforms: ["youtube", "instagram"],
    status: "scheduled",
  },
  {
    title: "Passive Income Strategies",
    date: new Date(new Date().setDate(new Date().getDate() + 4)).toISOString(),
    category: "Entrepreneurship",
    avatar: "/placeholder-user.jpg",
    avatarName: "Entrepreneur Avatar",
    avatarInitials: "EN",
    platforms: ["youtube", "twitter"],
    status: "scheduled",
  },
  {
    title: "Hidden Gems in Southeast Asia",
    date: new Date(new Date().setMonth(new Date().getMonth() - 4)).toISOString(),
    category: "Travel",
    avatar: "/placeholder-user.jpg",
    avatarName: "Travel Avatar",
    avatarInitials: "TR",
    platforms: ["youtube", "instagram", "twitter"],
    status: "archived",
  },
  {
    title: "DIY Home Renovation Tips",
    date: new Date(new Date().setMonth(new Date().getMonth() - 5)).toISOString(),
    category: "Home",
    avatar: "/placeholder-user.jpg",
    avatarName: "Home Avatar",
    avatarInitials: "HM",
    platforms: ["youtube", "facebook"],
    status: "archived",
  },
]

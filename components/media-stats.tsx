'use client'

import React, { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import {
  HardDrive,
  FileType,
  Star,
  Calendar,
  BarChart3
} from "lucide-react"
import { mediaService, type ProductMediaStats, type ApiMediaStatsResponse } from "@/lib/api/media"

interface MediaStatsProps {
  productId: string
  className?: string
}

export function MediaStats({ productId, className }: MediaStatsProps) {
  const [stats, setStats] = useState<ProductMediaStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadStats()
  }, [productId])

  const loadStats = async () => {
    try {
      setLoading(true)
      setError(null)
      console.log('Loading media stats for product:', productId)

      const response = await mediaService.getProductMediaStats(productId)
      console.log('Media stats response:', response)

      // Handle different response structures
      let statsData = null

      // Try to extract data from various response structures
      if (response?.data?.data) {
        statsData = response.data.data
      } else if (response?.data) {
        statsData = response.data
      } else if (response) {
        statsData = response
      }

      console.log('Extracted stats data:', statsData)

      // Validate and normalize the stats data
      if (statsData && typeof statsData === 'object') {
        // Handle the actual API response structure
        const normalizedStats: ProductMediaStats = {
          total_media: Number(statsData.total) || Number(statsData.total_media) || 0,
          total_size: Number(statsData.total_size) || 0, // API doesn't provide this yet
          media_types: {
            image: Number(statsData.images) || 0,
            video: Number(statsData.videos) || 0,
            ...statsData.media_types
          },
          total_duration: Number(statsData.total_duration) || 0,
          average_quality_score: Number(statsData.average_quality_score) || 0,
          last_uploaded: statsData.last_uploaded || null,
          formats: statsData.formats || {}
        }

        console.log('Normalized stats:', normalizedStats)
        setStats(normalizedStats)
      } else {
        throw new Error('Invalid stats data structure')
      }
    } catch (error) {
      console.error('Failed to load media stats:', error)
      setError('Failed to load media statistics')

      // Fallback to mock stats for demo
      const mockStats: ProductMediaStats = {
        total_media: 3,
        total_size: 1463576, // ~1.4MB
        media_types: {
          image: 2,
          video: 1
        },
        total_duration: 25,
        average_quality_score: 0.87,
        last_uploaded: new Date().toISOString(),
        formats: {
          jpg: 2,
          mp4: 1
        }
      }
      console.log('Using mock stats:', mockStats)
      setStats(mockStats)
      setError(null)
    } finally {
      setLoading(false)
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }



  if (loading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Skeleton className="h-4 w-4" />
                <div className="flex-1">
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-6 w-12" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error && !stats) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <BarChart3 className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
          <p className="text-sm text-muted-foreground">{error}</p>
        </CardContent>
      </Card>
    )
  }

  if (!stats) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <BarChart3 className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
          <p className="text-sm text-muted-foreground">No media statistics available</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2  gap-4 ${className}`}>
      <Card className="border border-gray-200 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <HardDrive className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Total Files</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total_media || 0}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border border-gray-200 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <FileType className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Total Size</p>
              <p className="text-lg font-bold text-gray-900">{formatFileSize(stats.total_size || 0)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border border-gray-200 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Star className="h-4 w-4 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Quality</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.average_quality_score && stats.average_quality_score > 0
                  ? `${(stats.average_quality_score * 100).toFixed(0)}%`
                  : 'N/A'
                }
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border border-gray-200 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Calendar className="h-4 w-4 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Last Upload</p>
              <p className="text-sm font-bold text-gray-900">
                {stats.last_uploaded
                  ? new Date(stats.last_uploaded).toLocaleDateString()
                  : 'Never'
                }
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

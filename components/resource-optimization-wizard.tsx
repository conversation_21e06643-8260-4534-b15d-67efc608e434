"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { <PERSON>lide<PERSON> } from "@/components/ui/slider"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from "recharts"
import { ArrowRight, ArrowLeft, Check, Zap, DollarSign, Clock, Users, Bot, BarChart3, PieChartIcon } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"]

// Initial resource allocation data
const initialAllocation = [
  { name: "Content Creation", human: 65, ai: 35 },
  { name: "Social Media", human: 40, ai: 60 },
  { name: "Email Marketing", human: 55, ai: 45 },
  { name: "Video Production", human: 80, ai: 20 },
  { name: "Analytics", human: 30, ai: 70 },
]

// Optimized resource allocation based on goals
const getOptimizedAllocation = (goals) => {
  // This would normally be a complex algorithm based on the goals
  // For this demo, we'll use a simplified approach

  const optimized = [
    { name: "Content Creation", human: goals.focus === "content" ? 40 : 50, ai: goals.focus === "content" ? 60 : 50 },
    { name: "Social Media", human: goals.focus === "growth" ? 30 : 35, ai: goals.focus === "growth" ? 70 : 65 },
    { name: "Email Marketing", human: goals.focus === "revenue" ? 25 : 40, ai: goals.focus === "revenue" ? 75 : 60 },
    { name: "Video Production", human: goals.costReduction > 7 ? 50 : 70, ai: goals.costReduction > 7 ? 50 : 30 },
    { name: "Analytics", human: goals.dataFocus ? 15 : 25, ai: goals.dataFocus ? 85 : 75 },
  ]

  return optimized
}

// Format data for charts
const formatForComparison = (current, optimized) => {
  return current.map((item, index) => ({
    name: item.name,
    "Current Human": item.human,
    "Current AI": item.ai,
    "Optimized Human": optimized[index].human,
    "Optimized AI": optimized[index].ai,
  }))
}

// Calculate savings and improvements
const calculateImprovements = (current, optimized, goals) => {
  // Calculate human hours saved
  const currentHumanHours = current.reduce((sum, item) => sum + item.human, 0) * 1.6 // Assuming each percentage point is ~1.6 hours
  const optimizedHumanHours = optimized.reduce((sum, item) => sum + item.human, 0) * 1.6
  const hoursSaved = currentHumanHours - optimizedHumanHours

  // Calculate cost savings (rough estimate)
  const humanHourlyCost = 50 // Assumed average hourly cost
  const aiHourlyCost = 15 // Assumed average hourly cost
  const currentCost =
    currentHumanHours * humanHourlyCost + current.reduce((sum, item) => sum + item.ai, 0) * 1.6 * aiHourlyCost
  const optimizedCost =
    optimizedHumanHours * humanHourlyCost + optimized.reduce((sum, item) => sum + item.ai, 0) * 1.6 * aiHourlyCost
  const costSavings = currentCost - optimizedCost

  // Calculate productivity improvement (simplified)
  const productivityImprovement = goals.focus === "content" ? 18 : goals.focus === "growth" ? 22 : 15

  return {
    hoursSaved: Math.round(hoursSaved),
    costSavings: Math.round(costSavings),
    productivityImprovement,
    timeToImplement: Math.round(hoursSaved * 0.3), // Rough estimate
  }
}

export function ResourceOptimizationWizard() {
  const { toast } = useToast()
  const [step, setStep] = useState(1)
  const [goals, setGoals] = useState({
    focus: "growth", // growth, revenue, content
    costReduction: 5, // 1-10 scale
    timeframe: "3months", // 1month, 3months, 6months
    dataFocus: false,
    aiComfort: "high", // low, medium, high
  })
  const [optimized, setOptimized] = useState(null)
  const [improvements, setImprovements] = useState(null)
  const [comparisonData, setComparisonData] = useState(null)

  const totalSteps = 4
  const progress = (step / totalSteps) * 100

  const handleNext = () => {
    if (step === 3) {
      // Generate optimization on the last step
      const optimizedAllocation = getOptimizedAllocation(goals)
      setOptimized(optimizedAllocation)
      setComparisonData(formatForComparison(initialAllocation, optimizedAllocation))
      setImprovements(calculateImprovements(initialAllocation, optimizedAllocation, goals))
    }
    setStep(step + 1)
  }

  const handleBack = () => {
    setStep(step - 1)
  }

  const handleSaveOptimization = () => {
    toast({
      title: "Optimization Plan Saved",
      description: "Your resource optimization plan has been saved and can be accessed from the dashboard.",
    })
  }

  const handleImplementNow = () => {
    toast({
      title: "Implementation Started",
      description: "The system has begun adjusting resource allocations based on your optimization plan.",
    })
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl">Resource Optimization Wizard</CardTitle>
        <CardDescription>Optimize your human and AI resource allocation based on your business goals</CardDescription>
        <Progress value={progress} className="h-2 mt-2" />
      </CardHeader>
      <CardContent>
        {step === 1 && (
          <div className="space-y-6">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">What is your primary business focus right now?</h3>
              <RadioGroup
                value={goals.focus}
                onValueChange={(value) => setGoals({ ...goals, focus: value })}
                className="grid grid-cols-1 gap-4 pt-2 md:grid-cols-3"
              >
                <div>
                  <RadioGroupItem value="growth" id="growth" className="peer sr-only" />
                  <Label
                    htmlFor="growth"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                  >
                    <Zap className="mb-3 h-6 w-6" />
                    <span className="text-sm font-medium">Growth & Audience</span>
                  </Label>
                </div>
                <div>
                  <RadioGroupItem value="revenue" id="revenue" className="peer sr-only" />
                  <Label
                    htmlFor="revenue"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                  >
                    <DollarSign className="mb-3 h-6 w-6" />
                    <span className="text-sm font-medium">Revenue & Sales</span>
                  </Label>
                </div>
                <div>
                  <RadioGroupItem value="content" id="content" className="peer sr-only" />
                  <Label
                    htmlFor="content"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                  >
                    <BarChart3 className="mb-3 h-6 w-6" />
                    <span className="text-sm font-medium">Content Quality</span>
                  </Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">How important is cost reduction?</h3>
              <p className="text-sm text-muted-foreground">Drag the slider to indicate priority level (1-10)</p>
              <div className="pt-4">
                <Slider
                  value={[goals.costReduction]}
                  min={1}
                  max={10}
                  step={1}
                  onValueChange={(value) => setGoals({ ...goals, costReduction: value[0] })}
                />
                <div className="flex justify-between mt-2">
                  <span className="text-xs">Low Priority</span>
                  <span className="text-xs font-medium">{goals.costReduction}/10</span>
                  <span className="text-xs">High Priority</span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">Implementation timeframe</h3>
              <Select value={goals.timeframe} onValueChange={(value) => setGoals({ ...goals, timeframe: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select timeframe" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1month">1 Month (Aggressive)</SelectItem>
                  <SelectItem value="3months">3 Months (Balanced)</SelectItem>
                  <SelectItem value="6months">6 Months (Conservative)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {step === 2 && (
          <div className="space-y-6">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Current Resource Allocation</h3>
              <p className="text-sm text-muted-foreground">This is your current allocation of human vs AI resources</p>

              <div className="h-[300px] mt-6">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={initialAllocation}
                    layout="vertical"
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" domain={[0, 100]} />
                    <YAxis dataKey="name" type="category" />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="human" name="Human %" fill="#8884d8" stackId="a" />
                    <Bar dataKey="ai" name="AI %" fill="#82ca9d" stackId="a" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">Additional Preferences</h3>

              <div className="flex items-center space-x-2 pt-2">
                <Checkbox
                  id="dataFocus"
                  checked={goals.dataFocus}
                  onCheckedChange={(checked) => setGoals({ ...goals, dataFocus: checked === true })}
                />
                <label
                  htmlFor="dataFocus"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Prioritize data-driven decision making
                </label>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">AI Comfort Level</h3>
              <p className="text-sm text-muted-foreground">How comfortable are you with AI handling critical tasks?</p>

              <RadioGroup
                value={goals.aiComfort}
                onValueChange={(value) => setGoals({ ...goals, aiComfort: value })}
                className="grid grid-cols-3 gap-4 pt-2"
              >
                <div>
                  <RadioGroupItem value="low" id="low" className="peer sr-only" />
                  <Label
                    htmlFor="low"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                  >
                    <span className="text-sm font-medium">Low</span>
                  </Label>
                </div>
                <div>
                  <RadioGroupItem value="medium" id="medium" className="peer sr-only" />
                  <Label
                    htmlFor="medium"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                  >
                    <span className="text-sm font-medium">Medium</span>
                  </Label>
                </div>
                <div>
                  <RadioGroupItem value="high" id="high" className="peer sr-only" />
                  <Label
                    htmlFor="high"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                  >
                    <span className="text-sm font-medium">High</span>
                  </Label>
                </div>
              </RadioGroup>
            </div>
          </div>
        )}

        {step === 3 && (
          <div className="space-y-6">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Review Your Goals</h3>
              <p className="text-sm text-muted-foreground">
                Please review your optimization goals before generating recommendations
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Primary Focus</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center">
                      {goals.focus === "growth" && <Zap className="mr-2 h-4 w-4" />}
                      {goals.focus === "revenue" && <DollarSign className="mr-2 h-4 w-4" />}
                      {goals.focus === "content" && <BarChart3 className="mr-2 h-4 w-4" />}
                      <span className="capitalize">
                        {goals.focus === "growth"
                          ? "Growth & Audience"
                          : goals.focus === "revenue"
                            ? "Revenue & Sales"
                            : "Content Quality"}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Cost Reduction Priority</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center">
                      <DollarSign className="mr-2 h-4 w-4" />
                      <span>{goals.costReduction}/10</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Implementation Timeframe</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center">
                      <Clock className="mr-2 h-4 w-4" />
                      <span>
                        {goals.timeframe === "1month"
                          ? "1 Month (Aggressive)"
                          : goals.timeframe === "3months"
                            ? "3 Months (Balanced)"
                            : "6 Months (Conservative)"}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Additional Preferences</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <PieChartIcon className="mr-2 h-4 w-4" />
                        <span>Data-driven focus: {goals.dataFocus ? "Yes" : "No"}</span>
                      </div>
                      <div className="flex items-center">
                        <Bot className="mr-2 h-4 w-4" />
                        <span>
                          AI comfort level: {goals.aiComfort.charAt(0).toUpperCase() + goals.aiComfort.slice(1)}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )}

        {step === 4 && optimized && (
          <div className="space-y-6">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Recommended Resource Optimization</h3>
              <p className="text-sm text-muted-foreground">
                Based on your goals, here's our recommended resource allocation
              </p>

              <Tabs defaultValue="chart" className="mt-4">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="chart">Comparison Chart</TabsTrigger>
                  <TabsTrigger value="breakdown">Detailed Breakdown</TabsTrigger>
                </TabsList>
                <TabsContent value="chart" className="pt-4">
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={comparisonData}
                        layout="vertical"
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" domain={[0, 100]} />
                        <YAxis dataKey="name" type="category" />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="Current Human" stackId="current" fill="#8884d8" />
                        <Bar dataKey="Current AI" stackId="current" fill="#82ca9d" />
                        <Bar dataKey="Optimized Human" stackId="optimized" fill="#8884d8" fillOpacity={0.5} />
                        <Bar dataKey="Optimized AI" stackId="optimized" fill="#82ca9d" fillOpacity={0.5} />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </TabsContent>
                <TabsContent value="breakdown" className="pt-4">
                  <div className="space-y-4">
                    {comparisonData.map((item, index) => (
                      <Card key={index}>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium">{item.name}</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-xs text-muted-foreground">Current Allocation</p>
                              <div className="flex items-center mt-1">
                                <Users className="mr-2 h-4 w-4" />
                                <span className="text-sm">{item["Current Human"]}% Human</span>
                              </div>
                              <div className="flex items-center mt-1">
                                <Bot className="mr-2 h-4 w-4" />
                                <span className="text-sm">{item["Current AI"]}% AI</span>
                              </div>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Recommended Allocation</p>
                              <div className="flex items-center mt-1">
                                <Users className="mr-2 h-4 w-4" />
                                <span className="text-sm">{item["Optimized Human"]}% Human</span>
                              </div>
                              <div className="flex items-center mt-1">
                                <Bot className="mr-2 h-4 w-4" />
                                <span className="text-sm">{item["Optimized AI"]}% AI</span>
                              </div>
                            </div>
                          </div>
                          <div className="mt-3">
                            <p className="text-xs text-muted-foreground">Change Impact</p>
                            <p className="text-sm mt-1">
                              {item["Optimized Human"] < item["Current Human"]
                                ? `Reduce human involvement by ${item["Current Human"] - item["Optimized Human"]}% and increase AI utilization.`
                                : `Increase human involvement by ${item["Optimized Human"] - item["Current Human"]}% and decrease AI utilization.`}
                            </p>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">Expected Improvements</h3>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Human Hours Saved</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{improvements.hoursSaved}</div>
                    <p className="text-xs text-muted-foreground">hours/month</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Cost Savings</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">${improvements.costSavings}</div>
                    <p className="text-xs text-muted-foreground">per month</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Productivity Gain</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{improvements.productivityImprovement}%</div>
                    <p className="text-xs text-muted-foreground">estimated increase</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Implementation Time</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{improvements.timeToImplement}</div>
                    <p className="text-xs text-muted-foreground">hours</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        {step > 1 ? (
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" /> Back
          </Button>
        ) : (
          <Button variant="outline" disabled>
            <ArrowLeft className="mr-2 h-4 w-4" /> Back
          </Button>
        )}

        {step < totalSteps ? (
          <Button onClick={handleNext}>
            Next <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        ) : (
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleSaveOptimization}>
              Save Plan
            </Button>
            <Button onClick={handleImplementNow}>
              <Check className="mr-2 h-4 w-4" /> Implement Now
            </Button>
          </div>
        )}
      </CardFooter>
    </Card>
  )
}

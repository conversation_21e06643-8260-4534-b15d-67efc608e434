"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  AlertTriangle,
  CheckCircle2,
  Clock,
  FileText,
  Lock,
  Shield,
  ShieldCheck,
  AlertCircle,
  Calendar,
} from "lucide-react"

export function ComplianceDashboard() {
  const [activeTab, setActiveTab] = useState("overview")

  // Sample compliance scores
  const complianceScores = {
    overall: 87,
    cookies: 92,
    disclosures: 78,
    agents: 95,
    retention: 85,
  }

  // Sample recent compliance events
  const recentEvents = [
    {
      id: 1,
      type: "warning",
      title: "Missing Affiliate Disclosure",
      description: "Travel avatar content missing proper FTC disclosure on 3 recent posts",
      timestamp: "2 hours ago",
      area: "disclosures",
    },
    {
      id: 2,
      type: "success",
      title: "<PERSON>ie Consent Updated",
      description: "Cookie consent banner updated to comply with latest GDPR requirements",
      timestamp: "1 day ago",
      area: "cookies",
    },
    {
      id: 3,
      type: "info",
      title: "Agent Action Review",
      description: "Scheduled review of Content Scout agent actions completed",
      timestamp: "2 days ago",
      area: "agents",
    },
    {
      id: 4,
      type: "warning",
      title: "Data Retention Alert",
      description: "User analytics data approaching 2-year retention limit",
      timestamp: "3 days ago",
      area: "retention",
    },
    {
      id: 5,
      type: "success",
      title: "Compliance Audit Completed",
      description: "Quarterly compliance audit completed with 2 minor issues identified",
      timestamp: "1 week ago",
      area: "overall",
    },
  ]

  // Sample upcoming compliance tasks
  const upcomingTasks = [
    {
      id: 1,
      title: "Quarterly GDPR Compliance Review",
      dueDate: "June 30, 2023",
      priority: "high",
    },
    {
      id: 2,
      title: "Update Affiliate Disclosure Templates",
      dueDate: "July 15, 2023",
      priority: "medium",
    },
    {
      id: 3,
      title: "Agent Decision Logic Audit",
      dueDate: "July 22, 2023",
      priority: "medium",
    },
    {
      id: 4,
      title: "Data Retention Policy Review",
      dueDate: "August 5, 2023",
      priority: "low",
    },
  ]

  // Get icon for event type
  const getEventIcon = (type: string) => {
    switch (type) {
      case "warning":
        return <AlertTriangle className="h-5 w-5 text-amber-500" />
      case "success":
        return <CheckCircle2 className="h-5 w-5 text-green-500" />
      case "info":
        return <AlertCircle className="h-5 w-5 text-blue-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  // Get badge for task priority
  const getTaskPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">High</Badge>
      case "medium":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">Medium</Badge>
      case "low":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Low</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overall Compliance</CardTitle>
            <ShieldCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{complianceScores.overall}%</div>
                <Badge
                  className={
                    complianceScores.overall >= 90
                      ? "bg-green-100 text-green-800"
                      : complianceScores.overall >= 70
                        ? "bg-amber-100 text-amber-800"
                        : "bg-red-100 text-red-800"
                  }
                >
                  {complianceScores.overall >= 90
                    ? "Excellent"
                    : complianceScores.overall >= 70
                      ? "Good"
                      : "Needs Attention"}
                </Badge>
              </div>
              <Progress value={complianceScores.overall} className="h-2" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cookie & Consent</CardTitle>
            <Lock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{complianceScores.cookies}%</div>
                <Badge
                  className={
                    complianceScores.cookies >= 90
                      ? "bg-green-100 text-green-800"
                      : complianceScores.cookies >= 70
                        ? "bg-amber-100 text-amber-800"
                        : "bg-red-100 text-red-800"
                  }
                >
                  {complianceScores.cookies >= 90
                    ? "Excellent"
                    : complianceScores.cookies >= 70
                      ? "Good"
                      : "Needs Attention"}
                </Badge>
              </div>
              <Progress value={complianceScores.cookies} className="h-2" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Affiliate Disclosures</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{complianceScores.disclosures}%</div>
                <Badge
                  className={
                    complianceScores.disclosures >= 90
                      ? "bg-green-100 text-green-800"
                      : complianceScores.disclosures >= 70
                        ? "bg-amber-100 text-amber-800"
                        : "bg-red-100 text-red-800"
                  }
                >
                  {complianceScores.disclosures >= 90
                    ? "Excellent"
                    : complianceScores.disclosures >= 70
                      ? "Good"
                      : "Needs Attention"}
                </Badge>
              </div>
              <Progress value={complianceScores.disclosures} className="h-2" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Data Retention</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{complianceScores.retention}%</div>
                <Badge
                  className={
                    complianceScores.retention >= 90
                      ? "bg-green-100 text-green-800"
                      : complianceScores.retention >= 70
                        ? "bg-amber-100 text-amber-800"
                        : "bg-red-100 text-red-800"
                  }
                >
                  {complianceScores.retention >= 90
                    ? "Excellent"
                    : complianceScores.retention >= 70
                      ? "Good"
                      : "Needs Attention"}
                </Badge>
              </div>
              <Progress value={complianceScores.retention} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="events">Recent Events</TabsTrigger>
          <TabsTrigger value="tasks">Upcoming Tasks</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Compliance Summary</CardTitle>
                <CardDescription>Current compliance status across all areas</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Lock className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span>Cookie & Consent Compliance</span>
                      </div>
                      <span className="font-medium">{complianceScores.cookies}%</span>
                    </div>
                    <Progress value={complianceScores.cookies} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <FileText className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span>Affiliate Disclosure Compliance</span>
                      </div>
                      <span className="font-medium">{complianceScores.disclosures}%</span>
                    </div>
                    <Progress value={complianceScores.disclosures} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <ShieldCheck className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span>Agent Action Audit Compliance</span>
                      </div>
                      <span className="font-medium">{complianceScores.agents}%</span>
                    </div>
                    <Progress value={complianceScores.agents} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Shield className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span>Data Retention Compliance</span>
                      </div>
                      <span className="font-medium">{complianceScores.retention}%</span>
                    </div>
                    <Progress value={complianceScores.retention} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Recent Compliance Events</CardTitle>
                <CardDescription>Latest compliance-related activities</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentEvents.slice(0, 4).map((event) => (
                    <div key={event.id} className="flex items-start space-x-3 border-b pb-3 last:border-0 last:pb-0">
                      <div className="mt-0.5">{getEventIcon(event.type)}</div>
                      <div>
                        <div className="font-medium">{event.title}</div>
                        <div className="text-sm text-muted-foreground">{event.description}</div>
                        <div className="mt-1 flex items-center text-xs text-muted-foreground">
                          <Clock className="mr-1 h-3 w-3" />
                          <span>{event.timestamp}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Compliance Improvement Recommendations</CardTitle>
              <CardDescription>Suggested actions to improve compliance scores</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="rounded-md bg-amber-50 p-4">
                  <div className="flex items-start">
                    <div className="mr-3 mt-0.5">
                      <AlertTriangle className="h-5 w-5 text-amber-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-amber-800">Update Affiliate Disclosures</h4>
                      <p className="mt-1 text-sm text-amber-700">
                        Add proper FTC disclosures to recent Travel avatar content. 3 posts were identified as missing
                        required disclosures.
                      </p>
                      <div className="mt-3">
                        <Button variant="outline" size="sm" className="border-amber-600 text-amber-700">
                          View Affected Content
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="rounded-md bg-blue-50 p-4">
                  <div className="flex items-start">
                    <div className="mr-3 mt-0.5">
                      <AlertCircle className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-blue-800">Schedule Agent Action Audit</h4>
                      <p className="mt-1 text-sm text-blue-700">
                        It's been 30 days since your last agent action audit. Schedule a new audit to maintain
                        compliance.
                      </p>
                      <div className="mt-3">
                        <Button variant="outline" size="sm" className="border-blue-600 text-blue-700">
                          Schedule Audit
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="rounded-md bg-blue-50 p-4">
                  <div className="flex items-start">
                    <div className="mr-3 mt-0.5">
                      <AlertCircle className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-blue-800">Review Data Retention Policies</h4>
                      <p className="mt-1 text-sm text-blue-700">
                        Some user analytics data is approaching the 2-year retention limit. Review and update your data
                        retention policies.
                      </p>
                      <div className="mt-3">
                        <Button variant="outline" size="sm" className="border-blue-600 text-blue-700">
                          Review Policies
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="events">
          <Card>
            <CardHeader>
              <CardTitle>Recent Compliance Events</CardTitle>
              <CardDescription>All compliance-related activities in the past 30 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentEvents.map((event) => (
                  <div key={event.id} className="flex items-start space-x-3 border-b pb-4 last:border-0 last:pb-0">
                    <div className="mt-0.5">{getEventIcon(event.type)}</div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div className="font-medium">{event.title}</div>
                        <Badge
                          variant="outline"
                          className={
                            event.area === "cookies"
                              ? "bg-blue-50 text-blue-800"
                              : event.area === "disclosures"
                                ? "bg-amber-50 text-amber-800"
                                : event.area === "agents"
                                  ? "bg-green-50 text-green-800"
                                  : event.area === "retention"
                                    ? "bg-purple-50 text-purple-800"
                                    : "bg-gray-50 text-gray-800"
                          }
                        >
                          {event.area === "cookies"
                            ? "Cookie & Consent"
                            : event.area === "disclosures"
                              ? "Affiliate Disclosures"
                              : event.area === "agents"
                                ? "Agent Actions"
                                : event.area === "retention"
                                  ? "Data Retention"
                                  : "General"}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">{event.description}</div>
                      <div className="mt-1 flex items-center text-xs text-muted-foreground">
                        <Clock className="mr-1 h-3 w-3" />
                        <span>{event.timestamp}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tasks">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Compliance Tasks</CardTitle>
              <CardDescription>Scheduled compliance tasks and deadlines</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingTasks.map((task) => (
                  <div key={task.id} className="flex items-start justify-between border-b pb-4 last:border-0 last:pb-0">
                    <div className="flex items-start space-x-3">
                      <div className="mt-0.5">
                        <Calendar className="h-5 w-5 text-muted-foreground" />
                      </div>
                      <div>
                        <div className="font-medium">{task.title}</div>
                        <div className="mt-1 flex items-center text-sm text-muted-foreground">
                          <Clock className="mr-1 h-4 w-4" />
                          <span>Due: {task.dueDate}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getTaskPriorityBadge(task.priority)}
                      <Button variant="outline" size="sm">
                        Complete
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

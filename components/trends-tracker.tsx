"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Bell, ChevronUp, Facebook, Instagram, Lightbulb, Twitter, Youtube } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export function TrendsTracker() {
  const [selectedPlatform, setSelectedPlatform] = useState<string>("all")

  const filteredTrends =
    selectedPlatform === "all"
      ? trendingTopics
      : trendingTopics.filter((trend) => trend.platforms.includes(selectedPlatform))

  return (
    <Card className="col-span-full">
      <CardHeader>
        <div className="flex w-full flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <div>
            <CardTitle className="text-xl sm:text-2xl">Trends Tracker</CardTitle>
            <CardDescription className="text-sm sm:text-base">Monitor trending topics and content opportunities</CardDescription>
          </div>
          <Tabs defaultValue="all" onValueChange={setSelectedPlatform}>
            <TabsList className="w-full md:w-auto overflow-x-auto whitespace-nowrap">
              <TabsTrigger value="all">All Platforms</TabsTrigger>
              <TabsTrigger value="youtube">YouTube</TabsTrigger>
              <TabsTrigger value="instagram">Instagram</TabsTrigger>
              <TabsTrigger value="twitter">Twitter</TabsTrigger>
              <TabsTrigger value="facebook">Facebook</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Trends</CardTitle>
                <Bell className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{filteredTrends.length}</div>
                <p className="text-xs text-muted-foreground">+3 new in the last 24h</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Highest Volume</CardTitle>
                <ChevronUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {filteredTrends.length > 0
                    ? filteredTrends.reduce((prev, current) =>
                        prev.volumePerDay > current.volumePerDay ? prev : current,
                      ).keyword
                    : "N/A"}
                </div>
                <p className="text-xs text-muted-foreground">
                  {filteredTrends.length > 0
                    ? `${filteredTrends
                        .reduce((prev, current) => (prev.volumePerDay > current.volumePerDay ? prev : current))
                        .volumePerDay.toLocaleString()}/day`
                    : "No data"}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Fastest Growing</CardTitle>
                <ChevronUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {filteredTrends.length > 0
                    ? filteredTrends.reduce((prev, current) => (prev.growthRate > current.growthRate ? prev : current))
                        .keyword
                    : "N/A"}
                </div>
                <p className="text-xs text-muted-foreground">
                  {filteredTrends.length > 0
                    ? `+${filteredTrends
                        .reduce((prev, current) => (prev.growthRate > current.growthRate ? prev : current))
                        .growthRate.toFixed(1)}%`
                    : "No data"}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Affiliate Matches</CardTitle>
                <Lightbulb className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {filteredTrends.reduce((count, trend) => count + (trend.affiliateMatches?.length || 0), 0)}
                </div>
                <p className="text-xs text-muted-foreground">Across all trends</p>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Current Trending Topics</h3>
            <div className="space-y-4">
              {filteredTrends.map((trend, index) => (
                <TrendCard key={index} trend={trend} />
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

interface TrendCardProps {
  trend: TrendingTopic
}

function TrendCard({ trend }: TrendCardProps) {
  return (
    <Card>
      <CardContent className="p-4 sm:p-6">
        <div className="flex flex-col md:flex-row md:items-start gap-4">
          <div className="flex-grow">
            <div className="flex items-center flex-wrap gap-y-2 mb-2">
              <h4 className="font-semibold text-base sm:text-lg">{trend.keyword}</h4>
              <Badge variant="outline" className="ml-2">
                {trend.category}
              </Badge>
              {trend.isHot && (
                <Badge className="ml-2 bg-red-500 hover:bg-red-600">
                  <span className="animate-pulse mr-1">●</span> Hot
                </Badge>
              )}
            </div>

            <div className="flex flex-wrap items-center gap-2 mb-4">
              {trend.platforms.includes("youtube") && (
                <Badge variant="outline" className="flex items-center space-x-1">
                  <Youtube className="h-3 w-3" />
                  <span>YouTube</span>
                </Badge>
              )}
              {trend.platforms.includes("instagram") && (
                <Badge variant="outline" className="flex items-center space-x-1">
                  <Instagram className="h-3 w-3" />
                  <span>Instagram</span>
                </Badge>
              )}
              {trend.platforms.includes("twitter") && (
                <Badge variant="outline" className="flex items-center space-x-1">
                  <Twitter className="h-3 w-3" />
                  <span>Twitter</span>
                </Badge>
              )}
              {trend.platforms.includes("facebook") && (
                <Badge variant="outline" className="flex items-center space-x-1">
                  <Facebook className="h-3 w-3" />
                  <span>Facebook</span>
                </Badge>
              )}
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
              <div>
                <div className="text-sm text-muted-foreground mb-1">Volume</div>
                <div className="font-medium">{trend.volumePerDay.toLocaleString()}/day</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground mb-1">Growth</div>
                <div className="font-medium text-green-500">+{trend.growthRate.toFixed(1)}%</div>
              </div>
            </div>

            <div className="mb-4">
              <div className="text-sm text-muted-foreground mb-1">Trend Strength</div>
              <div className="flex items-center">
                <Progress value={trend.trendStrength} className="h-2" />
                <span className="ml-2 text-sm">{trend.trendStrength}%</span>
              </div>
            </div>
          </div>

          <div className="mt-4 w-full md:mt-0 md:ml-6 md:w-1/3 border-t md:border-t-0 md:border-l pt-4 md:pt-0 md:pl-6">
            <div className="text-sm font-medium mb-2">Affiliate Opportunities</div>
            {trend.affiliateMatches && trend.affiliateMatches.length > 0 ? (
              <div className="space-y-3">
                {trend.affiliateMatches.map((match, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Avatar className="h-8 w-8 mr-2">
                      <AvatarImage src="/placeholder.svg" alt={match.name} />
                      <AvatarFallback>AF</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="text-sm font-medium">{match.name}</div>
                      <div className="text-xs text-muted-foreground">{match.commission}</div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="ml-auto h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <Lightbulb className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem>Create Content</DropdownMenuItem>
                        <DropdownMenuItem>View Product Details</DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>Dismiss Suggestion</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">No affiliate matches found</div>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col sm:flex-row justify-between gap-2">
        <Button variant="outline" size="sm" className="w-full sm:w-auto">
          View Details
        </Button>
        <Button size="sm" className="w-full sm:w-auto">Create Content</Button>
      </CardFooter>
    </Card>
  )
}

interface TrendingTopic {
  keyword: string
  category: string
  platforms: string[]
  volumePerDay: number
  growthRate: number
  trendStrength: number
  isHot: boolean
  affiliateMatches?: {
    name: string
    commission: string
  }[]
}

// Sample data
const trendingTopics: TrendingTopic[] = [
  {
    keyword: "Sustainable Travel",
    category: "Travel",
    platforms: ["youtube", "instagram", "twitter"],
    volumePerDay: 45000,
    growthRate: 32.5,
    trendStrength: 85,
    isHot: true,
    affiliateMatches: [
      {
        name: "EcoTravel Backpack",
        commission: "15% commission",
      },
      {
        name: "Solar Power Bank",
        commission: "12% commission",
      },
    ],
  },
  {
    keyword: "Minimalist Home Office",
    category: "Home",
    platforms: ["youtube", "instagram", "facebook"],
    volumePerDay: 28000,
    growthRate: 18.7,
    trendStrength: 72,
    isHot: false,
    affiliateMatches: [
      {
        name: "Adjustable Standing Desk",
        commission: "10% commission",
      },
      {
        name: "Ergonomic Chair",
        commission: "8% commission",
      },
    ],
  },
  {
    keyword: "Gut Health Foods",
    category: "Health",
    platforms: ["youtube", "instagram", "facebook"],
    volumePerDay: 35000,
    growthRate: 25.3,
    trendStrength: 78,
    isHot: true,
    affiliateMatches: [
      {
        name: "Probiotic Supplement",
        commission: "20% commission",
      },
      {
        name: "Fermentation Kit",
        commission: "15% commission",
      },
    ],
  },
  {
    keyword: "HIIT Workouts at Home",
    category: "Fitness",
    platforms: ["youtube", "instagram", "twitter"],
    volumePerDay: 52000,
    growthRate: 41.2,
    trendStrength: 92,
    isHot: true,
    affiliateMatches: [
      {
        name: "Adjustable Dumbbells",
        commission: "12% commission",
      },
      {
        name: "Fitness Tracker",
        commission: "10% commission",
      },
    ],
  },
  {
    keyword: "Passive Income Strategies",
    category: "Entrepreneurship",
    platforms: ["youtube", "twitter", "facebook"],
    volumePerDay: 38000,
    growthRate: 22.8,
    trendStrength: 75,
    isHot: false,
    affiliateMatches: [
      {
        name: "Digital Marketing Course",
        commission: "30% commission",
      },
      {
        name: "Investment Platform",
        commission: "25% commission",
      },
    ],
  },
]

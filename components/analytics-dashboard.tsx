"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  ArrowUpRight,
  BarChart3,
  ExternalLink,
  Filter,
  QrCode,
  RefreshCw,
  TrendingUp,
  LinkIcon,
  ChevronDown,
  ChevronUp,
  CreditCard,
  Layers,
} from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area"
import { DataExportOptions } from "@/components/data-export-options"

export function AnalyticsDashboard() {
  const [activeTab, setActiveTab] = useState("products")
  const [timeRange, setTimeRange] = useState("30d")
  const [viewMode, setViewMode] = useState<"table" | "chart">("table")
  const [planTier, setPlanTier] = useState("pro")
  const [estimatedVideos, setEstimatedVideos] = useState(50)

  // Calculate estimated videos based on plan tier
  const updateEstimatedVideos = (tier: string) => {
    switch (tier) {
      case "starter":
        setEstimatedVideos(20)
        break
      case "pro":
        setEstimatedVideos(50)
        break
      case "business":
        setEstimatedVideos(120)
        break
      case "enterprise":
        setEstimatedVideos(300)
        break
      default:
        setEstimatedVideos(50)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h2 className="text-2xl font-semibold tracking-tight">Analytics Dashboard</h2>
          <p className="text-sm text-muted-foreground">
            Track performance metrics, conversions, and identify growth opportunities
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="12m">Last 12 months</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <RefreshCw className="h-4 w-4" />
          </Button>
          <DataExportOptions dataType={activeTab as any} />
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-sage-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <ArrowUpRight className="h-4 w-4 text-sage-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-sage-900">$45,231.89</div>
            <div className="flex items-center text-xs text-sage-600">
              <ArrowUpRight className="mr-1 h-3 w-3" />
              <span>+20.1% from last period</span>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-sage-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <QrCode className="h-4 w-4 text-sage-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-sage-900">4.3%</div>
            <div className="flex items-center text-xs text-sage-600">
              <ArrowUpRight className="mr-1 h-3 w-3" />
              <span>+0.8% from last period</span>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-sage-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Avatar</CardTitle>
            <BarChart3 className="h-4 w-4 text-sage-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-sage-900">Fitness</div>
            <div className="flex items-center text-xs text-sage-600">
              <span>$12,450 in revenue</span>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-sage-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Trending Products</CardTitle>
            <TrendingUp className="h-4 w-4 text-sage-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-sage-900">8</div>
            <div className="flex items-center text-xs text-sage-600">
              <span>3 new opportunities</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="bg-sage-100">
          <TabsTrigger value="products" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
            Products
          </TabsTrigger>
          <TabsTrigger value="avatars" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
            Avatars
          </TabsTrigger>
          <TabsTrigger value="categories" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
            Categories
          </TabsTrigger>
          <TabsTrigger
            value="projections"
            className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900"
          >
            Projections
          </TabsTrigger>
          <TabsTrigger
            value="conversions"
            className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900"
          >
            Conversions
          </TabsTrigger>
          <TabsTrigger
            value="suggestions"
            className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900"
          >
            Suggestions
          </TabsTrigger>
          <TabsTrigger value="automation" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
            Automation
          </TabsTrigger>
          <TabsTrigger value="budget" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
            Budget
          </TabsTrigger>
        </TabsList>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="h-8 gap-1">
              <Filter className="h-3.5 w-3.5" />
              <span>Filters</span>
            </Button>
            <Select defaultValue="all">
              <SelectTrigger className="h-8 w-[150px]">
                <SelectValue placeholder="All Avatars" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Avatars</SelectItem>
                <SelectItem value="travel">Travel</SelectItem>
                <SelectItem value="home">Home</SelectItem>
                <SelectItem value="health">Health</SelectItem>
                <SelectItem value="fitness">Fitness</SelectItem>
                <SelectItem value="entrepreneur">Entrepreneur</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex items-center space-x-1 rounded-md border px-3 h-8">
              <Label htmlFor="search" className="text-xs">
                Search:
              </Label>
              <Input
                id="search"
                placeholder="Search products..."
                className="h-7 border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-0 pl-1 text-sm"
              />
            </div>
          </div>
          <div className="flex items-center border rounded-md p-1">
            <Button
              variant={viewMode === "table" ? "secondary" : "ghost"}
              size="sm"
              className="h-8 px-2"
              onClick={() => setViewMode("table")}
            >
              <BarChart3 className="h-4 w-4" />
              <span className="sr-only">Table View</span>
            </Button>
            <Button
              variant={viewMode === "chart" ? "secondary" : "ghost"}
              size="sm"
              className="h-8 px-2"
              onClick={() => setViewMode("chart")}
            >
              <TrendingUp className="h-4 w-4" />
              <span className="sr-only">Chart View</span>
            </Button>
          </div>
        </div>

        {/* Products Tab Content */}
        <TabsContent value="products" className="space-y-4">
          <Card className="bg-white">
            <CardHeader className="pb-2">
              <CardTitle>Product Performance</CardTitle>
              <CardDescription>Track revenue, conversions, and growth by product</CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[450px]">
                <Table>
                  <TableHeader className="bg-sage-50">
                    <TableRow>
                      <TableHead className="w-[250px]">Product</TableHead>
                      <TableHead>Avatar</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Revenue</TableHead>
                      <TableHead className="text-right">Units Sold</TableHead>
                      <TableHead className="text-right">QR Conversions</TableHead>
                      <TableHead className="text-right">Link Conversions</TableHead>
                      <TableHead className="text-right">Growth</TableHead>
                      <TableHead>Complementary</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {productData.map((product, index) => (
                      <TableRow key={index} className="hover:bg-sage-50">
                        <TableCell className="font-medium">{product.name}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src="/placeholder-user.jpg" alt={product.avatar} />
                              <AvatarFallback>{product.avatarInitials}</AvatarFallback>
                            </Avatar>
                            <span>{product.avatar}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="bg-sage-50 text-sage-700">
                            {product.category}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right font-medium">${product.revenue.toLocaleString()}</TableCell>
                        <TableCell className="text-right">{product.unitsSold}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <span>{product.qrConversions}%</span>
                            <QrCode className="h-3.5 w-3.5 text-sage-600" />
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <span>{product.linkConversions}%</span>
                            <LinkIcon className="h-3.5 w-3.5 text-sage-600" />
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div
                            className={`flex items-center justify-end gap-1 ${
                              product.growth > 0 ? "text-green-600" : "text-red-600"
                            }`}
                          >
                            <span>
                              {product.growth > 0 ? "+" : ""}
                              {product.growth}%
                            </span>
                            {product.growth > 0 ? (
                              <ChevronUp className="h-3.5 w-3.5" />
                            ) : (
                              <ChevronDown className="h-3.5 w-3.5" />
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Button variant="outline" size="sm" className="h-7 text-xs">
                            <Layers className="mr-1 h-3.5 w-3.5" />
                            View Bundle
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Other existing tabs content... */}
        <TabsContent value="avatars" className="space-y-4">
          {/* Existing avatars content */}
        </TabsContent>

        <TabsContent value="projections" className="space-y-4">
          {/* Existing projections content */}
        </TabsContent>

        <TabsContent value="conversions" className="space-y-4">
          {/* Existing conversions content */}
        </TabsContent>

        <TabsContent value="suggestions" className="space-y-4">
          {/* Existing suggestions content */}
        </TabsContent>

        <TabsContent value="automation" className="space-y-4">
          {/* Existing automation content */}
        </TabsContent>

        {/* New Budget Tab Content */}
        <TabsContent value="budget" className="space-y-4">
          <Card className="bg-white">
            <CardHeader className="pb-2">
              <CardTitle>Budget Overview</CardTitle>
              <CardDescription>Track subscription costs, credits, and production capacity</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-6">
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Current Plan</h3>
                    <Card className="bg-sage-50 border-sage-200">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">
                            {planTier === "starter"
                              ? "Starter"
                              : planTier === "pro"
                                ? "Professional"
                                : planTier === "business"
                                  ? "Business"
                                  : "Enterprise"}{" "}
                            Plan
                          </CardTitle>
                          <Badge className="bg-sage-700">Current</Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="pb-2">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-muted-foreground">Monthly Cost</span>
                          <span className="font-medium">
                            $
                            {planTier === "starter"
                              ? "49"
                              : planTier === "pro"
                                ? "99"
                                : planTier === "business"
                                  ? "249"
                                  : "499"}
                            /month
                          </span>
                        </div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-muted-foreground">Credits</span>
                          <span className="font-medium">
                            {planTier === "starter"
                              ? "500"
                              : planTier === "pro"
                                ? "1,500"
                                : planTier === "business"
                                  ? "5,000"
                                  : "15,000"}{" "}
                            / month
                          </span>
                        </div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-muted-foreground">Credits Used</span>
                          <span className="font-medium">
                            {planTier === "starter"
                              ? "342"
                              : planTier === "pro"
                                ? "876"
                                : planTier === "business"
                                  ? "2,450"
                                  : "8,750"}
                          </span>
                        </div>
                        <div className="space-y-1 mb-2">
                          <div className="flex justify-between text-xs">
                            <span>Credit Usage</span>
                            <span>
                              {planTier === "starter"
                                ? "68%"
                                : planTier === "pro"
                                  ? "58%"
                                  : planTier === "business"
                                    ? "49%"
                                    : "58%"}
                            </span>
                          </div>
                          <Progress
                            value={
                              planTier === "starter" ? 68 : planTier === "pro" ? 58 : planTier === "business" ? 49 : 58
                            }
                            className="h-2"
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Renewal Date</span>
                          <span className="font-medium">May 15, 2025</span>
                        </div>
                      </CardContent>
                      <CardFooter>
                        <Button variant="outline" size="sm" className="w-full">
                          <CreditCard className="mr-2 h-4 w-4" />
                          Manage Subscription
                        </Button>
                      </CardFooter>
                    </Card>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Plan Comparison</h3>
                    <div className="space-y-2">
                      <Label>Select Plan to Compare</Label>
                      <Select
                        value={planTier}
                        onValueChange={(value) => {
                          setPlanTier(value)
                          updateEstimatedVideos(value)
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select plan" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="starter">Starter ($49/month)</SelectItem>
                          <SelectItem value="pro">Professional ($99/month)</SelectItem>
                          <SelectItem value="business">Business ($249/month)</SelectItem>
                          <SelectItem value="enterprise">Enterprise ($499/month)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="border rounded-md p-3 space-y-3">
                      <h4 className="text-sm font-medium">Plan Features</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center justify-between">
                          <span>Monthly Credits</span>
                          <span className="font-medium">
                            {planTier === "starter"
                              ? "500"
                              : planTier === "pro"
                                ? "1,500"
                                : planTier === "business"
                                  ? "5,000"
                                  : "15,000"}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Max Video Length</span>
                          <span className="font-medium">
                            {planTier === "starter"
                              ? "10 min"
                              : planTier === "pro"
                                ? "30 min"
                                : planTier === "business"
                                  ? "60 min"
                                  : "Unlimited"}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Avatars</span>
                          <span className="font-medium">
                            {planTier === "starter"
                              ? "2"
                              : planTier === "pro"
                                ? "5"
                                : planTier === "business"
                                  ? "10"
                                  : "Unlimited"}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>AI Script Generation</span>
                          <span className="font-medium">
                            {planTier === "starter" ? "Basic" : planTier === "pro" ? "Advanced" : "Premium"}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Repurposing Options</span>
                          <span className="font-medium">
                            {planTier === "starter"
                              ? "3 platforms"
                              : planTier === "pro"
                                ? "All platforms"
                                : "All platforms + custom"}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="md:col-span-2 space-y-6">
                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Production Capacity</h3>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium">Estimated Monthly Videos</h4>
                              <p className="text-sm text-muted-foreground">Based on your current plan and usage</p>
                            </div>
                            <div className="text-right">
                              <div className="text-3xl font-bold">{estimatedVideos}</div>
                              <div className="text-sm text-muted-foreground">videos/month</div>
                            </div>
                          </div>

                          <div className="space-y-1">
                            <div className="flex justify-between text-sm">
                              <span>Current Production Efficiency</span>
                              <span>72%</span>
                            </div>
                            <Progress value={72} className="h-2" />
                            <p className="text-xs text-muted-foreground">
                              Efficiency is calculated based on credit usage, workflow automation, and content reuse
                            </p>
                          </div>

                          <div className="grid grid-cols-3 gap-4 pt-2">
                            <div className="border rounded-md p-3 text-center">
                              <div className="text-2xl font-bold">30</div>
                              <div className="text-xs text-muted-foreground">Credits per video</div>
                            </div>
                            <div className="border rounded-md p-3 text-center">
                              <div className="text-2xl font-bold">15</div>
                              <div className="text-xs text-muted-foreground">Repurposed assets</div>
                            </div>
                            <div className="border rounded-md p-3 text-center">
                              <div className="text-2xl font-bold">85%</div>
                              <div className="text-xs text-muted-foreground">Automation rate</div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Production Scaling</h3>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">Plan Comparison</h4>
                            <Badge variant="outline">Estimated Production</Badge>
                          </div>

                          <Table>
                            <TableHeader className="bg-sage-50">
                              <TableRow>
                                <TableHead>Plan</TableHead>
                                <TableHead>Monthly Cost</TableHead>
                                <TableHead>Credits</TableHead>
                                <TableHead>Est. Videos</TableHead>
                                <TableHead>Cost per Video</TableHead>
                                <TableHead></TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              <TableRow className={planTier === "starter" ? "bg-sage-50" : ""}>
                                <TableCell className="font-medium">Starter</TableCell>
                                <TableCell>$49/month</TableCell>
                                <TableCell>500</TableCell>
                                <TableCell>20</TableCell>
                                <TableCell>$2.45</TableCell>
                                <TableCell>
                                  {planTier === "starter" ? (
                                    <Badge>Current</Badge>
                                  ) : (
                                    <Button variant="outline" size="sm" className="h-7 text-xs">
                                      Downgrade
                                    </Button>
                                  )}
                                </TableCell>
                              </TableRow>
                              <TableRow className={planTier === "pro" ? "bg-sage-50" : ""}>
                                <TableCell className="font-medium">Professional</TableCell>
                                <TableCell>$99/month</TableCell>
                                <TableCell>1,500</TableCell>
                                <TableCell>50</TableCell>
                                <TableCell>$1.98</TableCell>
                                <TableCell>
                                  {planTier === "pro" ? (
                                    <Badge>Current</Badge>
                                  ) : planTier === "starter" ? (
                                    <Button variant="outline" size="sm" className="h-7 text-xs">
                                      Upgrade
                                    </Button>
                                  ) : (
                                    <Button variant="outline" size="sm" className="h-7 text-xs">
                                      Downgrade
                                    </Button>
                                  )}
                                </TableCell>
                              </TableRow>
                              <TableRow className={planTier === "business" ? "bg-sage-50" : ""}>
                                <TableCell className="font-medium">Business</TableCell>
                                <TableCell>$249/month</TableCell>
                                <TableCell>5,000</TableCell>
                                <TableCell>120</TableCell>
                                <TableCell>$2.08</TableCell>
                                <TableCell>
                                  {planTier === "business" ? (
                                    <Badge>Current</Badge>
                                  ) : planTier === "enterprise" ? (
                                    <Button variant="outline" size="sm" className="h-7 text-xs">
                                      Downgrade
                                    </Button>
                                  ) : (
                                    <Button variant="outline" size="sm" className="h-7 text-xs">
                                      Upgrade
                                    </Button>
                                  )}
                                </TableCell>
                              </TableRow>
                              <TableRow className={planTier === "enterprise" ? "bg-sage-50" : ""}>
                                <TableCell className="font-medium">Enterprise</TableCell>
                                <TableCell>$499/month</TableCell>
                                <TableCell>15,000</TableCell>
                                <TableCell>300</TableCell>
                                <TableCell>$1.66</TableCell>
                                <TableCell>
                                  {planTier === "enterprise" ? (
                                    <Badge>Current</Badge>
                                  ) : (
                                    <Button variant="outline" size="sm" className="h-7 text-xs">
                                      Upgrade
                                    </Button>
                                  )}
                                </TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-lg font-medium">Recommended Efficiency Apps</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card className="border-sage-200">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base">Content Calendar Pro</CardTitle>
                            <Badge variant="outline" className="bg-green-100 text-green-800">
                              Recommended
                            </Badge>
                          </div>
                          <CardDescription>Advanced scheduling and content planning</CardDescription>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-muted-foreground">Monthly Cost</span>
                            <span className="font-medium">$29/month</span>
                          </div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-muted-foreground">Efficiency Boost</span>
                            <div className="flex items-center">
                              <span className="font-medium mr-1">+18%</span>
                              <TrendingUp className="h-4 w-4 text-green-600" />
                            </div>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Streamlines content planning and scheduling across all platforms with AI-powered
                            recommendations for optimal posting times.
                          </p>
                        </CardContent>
                        <CardFooter>
                          <Button variant="outline" size="sm" className="w-full">
                            <ExternalLink className="mr-2 h-4 w-4" />
                            Learn More
                          </Button>
                        </CardFooter>
                      </Card>

                      <Card className="border-sage-200">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base">BatchMaster AI</CardTitle>
                            <Badge variant="outline" className="bg-green-100 text-green-800">
                              High ROI
                            </Badge>
                          </div>
                          <CardDescription>Batch content creation and processing</CardDescription>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-muted-foreground">Monthly Cost</span>
                            <span className="font-medium">$39/month</span>
                          </div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-muted-foreground">Efficiency Boost</span>
                            <div className="flex items-center">
                              <span className="font-medium mr-1">+25%</span>
                              <TrendingUp className="h-4 w-4 text-green-600" />
                            </div>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Process multiple videos simultaneously and create content batches for consistent publishing
                            schedules.
                          </p>
                        </CardContent>
                        <CardFooter>
                          <Button variant="outline" size="sm" className="w-full">
                            <ExternalLink className="mr-2 h-4 w-4" />
                            Learn More
                          </Button>
                        </CardFooter>
                      </Card>

                      <Card className="border-sage-200">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base">Analytics Optimizer</CardTitle>
                            <Badge variant="outline" className="bg-amber-100 text-amber-800">
                              Data-Driven
                            </Badge>
                          </div>
                          <CardDescription>Performance tracking and optimization</CardDescription>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-muted-foreground">Monthly Cost</span>
                            <span className="font-medium">$35/month</span>
                          </div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-muted-foreground">Efficiency Boost</span>
                            <div className="flex items-center">
                              <span className="font-medium mr-1">+15%</span>
                              <TrendingUp className="h-4 w-4 text-green-600" />
                            </div>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Advanced analytics to identify top-performing content and optimize future production based
                            on data insights.
                          </p>
                        </CardContent>
                        <CardFooter>
                          <Button variant="outline" size="sm" className="w-full">
                            <ExternalLink className="mr-2 h-4 w-4" />
                            Learn More
                          </Button>
                        </CardFooter>
                      </Card>

                      <Card className="border-sage-200">
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-base">Template Library Pro</CardTitle>
                            <Badge variant="outline" className="bg-blue-100 text-blue-800">
                              Time-Saver
                            </Badge>
                          </div>
                          <CardDescription>Extensive template collection</CardDescription>
                        </CardHeader>
                        <CardContent className="pb-2">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-muted-foreground">Monthly Cost</span>
                            <span className="font-medium">$25/month</span>
                          </div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-muted-foreground">Efficiency Boost</span>
                            <div className="flex items-center">
                              <span className="font-medium mr-1">+20%</span>
                              <TrendingUp className="h-4 w-4 text-green-600" />
                            </div>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Access to 500+ professional templates for scripts, videos, and social media posts to
                            accelerate content creation.
                          </p>
                        </CardContent>
                        <CardFooter>
                          <Button variant="outline" size="sm" className="w-full">
                            <ExternalLink className="mr-2 h-4 w-4" />
                            Learn More
                          </Button>
                        </CardFooter>
                      </Card>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Sample data for the tables
const productData = [
  {
    name: "Premium Travel Backpack",
    avatar: "Travel Avatar",
    avatarInitials: "TR",
    category: "Travel Gear",
    revenue: 4250,
    unitsSold: 85,
    qrConversions: 4.8,
    linkConversions: 3.6,
    growth: 15.2,
  },
  {
    name: "Compact Travel Adapter",
    avatar: "Travel Avatar",
    avatarInitials: "TR",
    category: "Travel Accessories",
    revenue: 2890,
    unitsSold: 145,
    qrConversions: 3.2,
    linkConversions: 4.4,
    growth: 8.7,
  },
  {
    name: "Minimalist Desk Organizer",
    avatar: "Home Avatar",
    avatarInitials: "HM",
    category: "Home Office",
    revenue: 3450,
    unitsSold: 172,
    qrConversions: 5.1,
    linkConversions: 4.5,
    growth: 12.3,
  },
  {
    name: "Smart Home Lighting Kit",
    avatar: "Home Avatar",
    avatarInitials: "HM",
    category: "Smart Home",
    revenue: 6780,
    unitsSold: 113,
    qrConversions: 3.8,
    linkConversions: 6.6,
    growth: 24.5,
  },
  {
    name: "Organic Superfood Blend",
    avatar: "Health Avatar",
    avatarInitials: "HE",
    category: "Nutrition",
    revenue: 4120,
    unitsSold: 206,
    qrConversions: 4.3,
    linkConversions: 4.7,
    growth: 9.8,
  },
  {
    name: "Sleep Tracking Device",
    avatar: "Health Avatar",
    avatarInitials: "HE",
    category: "Wellness",
    revenue: 5340,
    unitsSold: 89,
    qrConversions: 5.7,
    linkConversions: 3.1,
    growth: -2.4,
  },
  {
    name: "Adjustable Kettlebell Set",
    avatar: "Fitness Avatar",
    avatarInitials: "FI",
    category: "Home Fitness",
    revenue: 7890,
    unitsSold: 157,
    qrConversions: 6.2,
    linkConversions: 5.2,
    growth: 31.5,
  },
  {
    name: "Smart Fitness Tracker",
    avatar: "Fitness Avatar",
    avatarInitials: "FI",
    category: "Wearables",
    revenue: 9450,
    unitsSold: 210,
    qrConversions: 5.8,
    linkConversions: 6.6,
    growth: 18.9,
  },
  {
    name: "Business Productivity Course",
    avatar: "Entrepreneur Avatar",
    avatarInitials: "EN",
    category: "Online Education",
    revenue: 12780,
    unitsSold: 85,
    qrConversions: 7.4,
    linkConversions: 8.2,
    growth: 42.3,
  },
  {
    name: "Premium Planner System",
    avatar: "Entrepreneur Avatar",
    avatarInitials: "EN",
    category: "Productivity",
    revenue: 5670,
    unitsSold: 189,
    qrConversions: 4.1,
    linkConversions: 5.7,
    growth: 7.8,
  },
]

const avatarData = [
  {
    name: "Travel Avatar",
    initials: "TR",
    revenue: 12450,
    products: 5,
    avgConversion: 4.2,
    qrConversions: 4.5,
    linkConversions: 3.9,
    growth: 14.8,
    topPlatform: "Instagram",
  },
  {
    name: "Home Avatar",
    initials: "HM",
    revenue: 15780,
    products: 6,
    avgConversion: 4.8,
    qrConversions: 4.2,
    linkConversions: 5.4,
    growth: 18.2,
    topPlatform: "YouTube",
  },
  {
    name: "Health Avatar",
    initials: "HE",
    revenue: 18920,
    products: 7,
    avgConversion: 4.5,
    qrConversions: 5.1,
    linkConversions: 3.9,
    growth: 7.6,
    topPlatform: "Instagram",
  },
  {
    name: "Fitness Avatar",
    initials: "FI",
    revenue: 24560,
    products: 8,
    avgConversion: 5.9,
    qrConversions: 6.3,
    linkConversions: 5.5,
    growth: 26.4,
    topPlatform: "YouTube",
  },
  {
    name: "Entrepreneur Avatar",
    initials: "EN",
    revenue: 21340,
    products: 4,
    avgConversion: 6.8,
    qrConversions: 5.7,
    linkConversions: 7.9,
    growth: 22.1,
    topPlatform: "LinkedIn",
  },
]

const projectionData = [
  {
    name: "Premium Travel Backpack",
    avatar: "Travel Avatar",
    avatarInitials: "TR",
    currentRevenue: 4250,
    projection30d: 4890,
    projection90d: 6120,
    trendStrength: 78,
    confidence: "High",
  },
  {
    name: "Smart Home Lighting Kit",
    avatar: "Home Avatar",
    avatarInitials: "HM",
    currentRevenue: 6780,
    projection30d: 7450,
    projection90d: 9200,
    trendStrength: 85,
    confidence: "High",
  },
  {
    name: "Organic Superfood Blend",
    avatar: "Health Avatar",
    avatarInitials: "HE",
    currentRevenue: 4120,
    projection30d: 4350,
    projection90d: 4780,
    trendStrength: 62,
    confidence: "Medium",
  },
  {
    name: "Adjustable Kettlebell Set",
    avatar: "Fitness Avatar",
    avatarInitials: "FI",
    currentRevenue: 7890,
    projection30d: 9450,
    projection90d: 12800,
    trendStrength: 92,
    confidence: "High",
  },
  {
    name: "Business Productivity Course",
    avatar: "Entrepreneur Avatar",
    avatarInitials: "EN",
    currentRevenue: 12780,
    projection30d: 15400,
    projection90d: 22600,
    trendStrength: 88,
    confidence: "High",
  },
  {
    name: "Travel Category",
    avatar: "Travel Avatar",
    avatarInitials: "TR",
    currentRevenue: 12450,
    projection30d: 14200,
    projection90d: 18500,
    trendStrength: 75,
    confidence: "Medium",
  },
  {
    name: "Fitness Category",
    avatar: "Fitness Avatar",
    avatarInitials: "FI",
    currentRevenue: 24560,
    projection30d: 28900,
    projection90d: 36700,
    trendStrength: 90,
    confidence: "High",
  },
]

const conversionData = [
  {
    product: "Premium Travel Backpack",
    platform: "Instagram",
    qrScans: 1250,
    qrConversionRate: 4.8,
    qrRevenue: 2890,
    linkClicks: 3450,
    linkConversionRate: 3.6,
    linkRevenue: 1360,
    bestMethod: "QR Code",
  },
  {
    product: "Premium Travel Backpack",
    platform: "YouTube",
    qrScans: 780,
    qrConversionRate: 3.9,
    qrRevenue: 1460,
    linkClicks: 5670,
    linkConversionRate: 4.2,
    linkRevenue: 2790,
    bestMethod: "Link",
  },
  {
    product: "Smart Home Lighting Kit",
    platform: "Instagram",
    qrScans: 920,
    qrConversionRate: 3.8,
    qrRevenue: 1680,
    linkClicks: 2450,
    linkConversionRate: 6.6,
    linkRevenue: 5100,
    bestMethod: "Link",
  },
  {
    product: "Smart Home Lighting Kit",
    platform: "YouTube",
    qrScans: 1560,
    qrConversionRate: 5.2,
    qrRevenue: 3900,
    linkClicks: 1890,
    linkConversionRate: 4.8,
    linkRevenue: 2880,
    bestMethod: "QR Code",
  },
  {
    product: "Adjustable Kettlebell Set",
    platform: "Instagram",
    qrScans: 2340,
    qrConversionRate: 6.2,
    qrRevenue: 4680,
    linkClicks: 1780,
    linkConversionRate: 5.2,
    linkRevenue: 3210,
    bestMethod: "QR Code",
  },
  {
    product: "Adjustable Kettlebell Set",
    platform: "TikTok",
    qrScans: 3450,
    qrConversionRate: 5.8,
    qrRevenue: 5790,
    linkClicks: 4560,
    linkConversionRate: 4.9,
    linkRevenue: 4230,
    bestMethod: "QR Code",
  },
  {
    product: "Business Productivity Course",
    platform: "LinkedIn",
    qrScans: 890,
    qrConversionRate: 7.4,
    qrRevenue: 4560,
    linkClicks: 3450,
    linkConversionRate: 8.2,
    linkRevenue: 8220,
    bestMethod: "Link",
  },
]

const productSuggestions = [
  {
    name: "Eco-Friendly Travel Essentials Kit",
    category: "Travel Gear",
    recommendedAvatar: "Travel Avatar",
    opportunity: "High",
    marketTrend: "Sustainable Travel",
    estimatedRevenue: 7800,
    competitionLevel: "Medium",
    rationale:
      "The sustainable travel market is growing at 23% annually. Your travel avatar's audience shows high engagement with eco-friendly content. This product bundle would complement your existing travel backpack.",
  },
  {
    name: "Smart Home Security Bundle",
    category: "Smart Home",
    recommendedAvatar: "Home Avatar",
    opportunity: "Medium",
    marketTrend: "Home Security",
    estimatedRevenue: 9200,
    competitionLevel: "High",
    rationale:
      "Your home avatar audience has shown strong interest in home security content. This bundle would create a natural upsell opportunity from your existing smart lighting products.",
  },
  {
    name: "Personalized Fitness Program Subscription",
    category: "Digital Product",
    recommendedAvatar: "Fitness Avatar",
    opportunity: "High",
    marketTrend: "Personalized Fitness",
    estimatedRevenue: 15600,
    competitionLevel: "Medium",
    rationale:
      "Your fitness avatar has the highest conversion rate among all avatars. A digital subscription product would provide recurring revenue and complement your physical fitness products.",
  },
]

const emergingMarkets = [
  {
    name: "Sustainable Home Products",
    growthRate: 28.5,
    opportunity: "High",
    recommendedProducts: "Eco-friendly cleaning kit, Water conservation system",
  },
  {
    name: "Mental Wellness Tech",
    growthRate: 32.7,
    opportunity: "High",
    recommendedProducts: "Meditation app subscription, Sleep optimization device",
  },
  {
    name: "Remote Work Productivity",
    growthRate: 24.3,
    opportunity: "Medium",
    recommendedProducts: "Ergonomic home office bundle, Digital productivity course",
  },
  {
    name: "Plant-Based Nutrition",
    growthRate: 19.8,
    opportunity: "Medium",
    recommendedProducts: "Plant protein blend, Meal planning subscription",
  },
]

const automationEvents = [
  {
    name: "Poppy AI Script Generation",
    source: "Trends Tracker",
    destination: "Poppy AI",
    frequency: "Daily",
    successRate: 98,
    status: "Active",
    lastRun: "Today, 6:00 AM",
  },
  {
    name: "LTX Studio Video Production",
    source: "Poppy AI",
    destination: "LTX Studio",
    frequency: "Daily",
    successRate: 92,
    status: "Active",
    lastRun: "Today, 7:30 AM",
  },
  {
    name: "Video Repurposing",
    source: "LTX Studio",
    destination: "OpusClips",
    frequency: "Daily",
    successRate: 88,
    status: "Active",
    lastRun: "Today, 9:15 AM",
  },
  {
    name: "Social Media Scheduling",
    source: "OpusClips",
    destination: "Social Platforms",
    frequency: "Daily",
    successRate: 76,
    status: "Warning",
    lastRun: "Today, 10:30 AM",
  },
  {
    name: "Analytics Data Collection",
    source: "Social Platforms",
    destination: "Command Center",
    frequency: "Hourly",
    successRate: 94,
    status: "Active",
    lastRun: "Today, 11:00 AM",
  },
  {
    name: "Product Performance Tracking",
    source: "E-commerce Platform",
    destination: "Command Center",
    frequency: "Hourly",
    successRate: 65,
    status: "Error",
    lastRun: "Yesterday, 11:00 PM",
  },
]

const automationSuggestions = [
  {
    title: "Optimize Social Media Scheduling",
    description:
      "The current social media scheduling automation has a 76% success rate. Implementing retry logic and platform-specific error handling could improve this to 95%+.",
    impact: "High",
    category: "Workflow Optimization",
    difficulty: "Medium",
  },
  {
    title: "Fix Product Performance Tracking",
    description:
      "The e-commerce platform integration is failing due to API rate limits. Implementing batch processing and scheduled intervals would resolve this issue.",
    impact: "High",
    category: "Error Resolution",
    difficulty: "Medium",
  },
  {
    title: "Implement Cross-Platform Analytics",
    description:
      "Create a new automation that combines data from all platforms to generate comprehensive cross-platform performance reports automatically.",
    impact: "Medium",
    category: "New Automation",
    difficulty: "High",
  },
]

'use client'

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Image as ImageIcon,
  Video,
  File,
  Download,
  Star,
  Calendar,
  HardDrive,
  FileType,
  Edit,
  Save,
  X,
  ExternalLink,
  Copy,
  Play,
  Link,
  FileText
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { mediaService, type Media } from "@/lib/api/media"
import { getImageUrl } from "@/lib/url-utils"

interface MediaDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  media: Media | null
  onMediaUpdate?: (updatedMedia: Media) => void
}

export function MediaDetailDialog({ 
  open, 
  onOpenChange, 
  media,
  onMediaUpdate 
}: MediaDetailDialogProps) {
  const [loading, setLoading] = useState(false)
  const [editing, setEditing] = useState(false)
  const [saving, setSaving] = useState(false)
  const [mediaData, setMediaData] = useState<Media | null>(null)
  const [editForm, setEditForm] = useState({
    alt_text: '',
    description: '',
    is_primary: false
  })
  const { toast } = useToast()

  const copyToClipboard = async (text: string, label: string = 'URL') => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard.`,
      })
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
      toast({
        title: "Copy Failed",
        description: "Could not copy to clipboard. Please copy manually.",
        variant: "destructive",
      })
    }
  }

  const openInNewTab = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  useEffect(() => {
    if (open && media) {
      loadMediaDetails()
    }
  }, [open, media])

  useEffect(() => {
    if (mediaData) {
      setEditForm({
        alt_text: mediaData.alt_text || '',
        description: mediaData.description || '',
        is_primary: mediaData.is_primary
      })
    }
  }, [mediaData])

  const loadMediaDetails = async () => {
    if (!media) return

    try {
      setLoading(true)
      const response = await mediaService.getMediaById(media.id)
      setMediaData(response.data)
    } catch (error) {
      console.error('Failed to load media details:', error)
      
      // Fallback to the provided media data
      setMediaData(media)
      
      toast({
        title: "Demo Mode",
        description: "Using provided media data - backend not available.",
        variant: "default",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSaveChanges = async () => {
    if (!mediaData) return

    try {
      setSaving(true)
      const response = await mediaService.updateMedia(mediaData.id, editForm)
      const updatedMedia = response.data
      setMediaData(updatedMedia)
      onMediaUpdate?.(updatedMedia)
      setEditing(false)
      
      toast({
        title: "Media Updated",
        description: "Media information has been updated successfully.",
      })
    } catch (error) {
      console.error('Failed to update media:', error)
      
      // For demo mode, update local state
      const updatedMedia = {
        ...mediaData,
        ...editForm,
        updated_at: new Date().toISOString()
      }
      setMediaData(updatedMedia)
      onMediaUpdate?.(updatedMedia)
      setEditing(false)
      
      toast({
        title: "Media Updated (Demo Mode)",
        description: "Media information has been updated locally.",
      })
    } finally {
      setSaving(false)
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getMediaIcon = (mediaType: string) => {
    switch (mediaType) {
      case 'image':
        return <ImageIcon className="h-5 w-5" />
      case 'video':
        return <Video className="h-5 w-5" />
      default:
        return <File className="h-5 w-5" />
    }
  }

  const getMediaPreview = (mediaItem: Media) => {
    const mediaUrl = mediaItem.media_url

    if (mediaItem.media_type === 'image') {
      return (
        <div className="aspect-video bg-muted rounded-lg overflow-hidden relative group">
          <img
            src={mediaUrl || '/placeholder.jpg'}
            alt={`${mediaItem.media_type} - ${mediaItem.format || 'Unknown'}`}
            className="w-full h-full object-contain cursor-pointer"
            onClick={() => mediaUrl && openInNewTab(mediaUrl)}
            onError={(e) => {
              (e.target as HTMLImageElement).src = '/placeholder.jpg'
            }}
          />
          {/* Click overlay hint */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="bg-black/70 text-white px-3 py-1 rounded text-sm">
              Click to open full size
            </div>
          </div>
        </div>
      )
    } else if (mediaItem.media_type === 'video') {
      return (
        <div className="aspect-video bg-black rounded-lg relative overflow-hidden">
          {mediaUrl && mediaUrl !== '/placeholder.jpg' ? (
            <video
              className="w-full h-full object-contain"
              controls
              preload="metadata"
              poster={mediaItem.thumbnail_url}
            >
              <source src={mediaUrl} type={`video/${mediaItem.format || 'mp4'}`} />
              Your browser does not support the video tag.
            </video>
          ) : (
            <div className="w-full h-full flex flex-col items-center justify-center text-white">
              <Video className="h-16 w-16 text-white/50 mb-2" />
              <p className="text-sm text-white/70">Video not available</p>
            </div>
          )}
        </div>
      )
    } else {
      return (
        <div className="aspect-video bg-muted rounded-lg flex flex-col items-center justify-center cursor-pointer group" onClick={() => openInNewTab(mediaUrl)}>
          <File className="h-16 w-16 text-muted-foreground mb-2" />
          <p className="text-sm text-muted-foreground group-hover:text-foreground transition-colors">
            Click to open file
          </p>
        </div>
      )
    }
  }

  if (!media) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getMediaIcon(media.media_type)}
              <span>{media.format ? media.format.toUpperCase() : 'Unknown'} {media.media_type}</span>
              <Badge variant="outline" className="text-xs">
                {media.status}
              </Badge>
            </div>
            {/* <div className="flex items-center space-x-2">
              {!editing && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setEditing(true)}
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
              )}
              {editing && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setEditing(false)
                      setEditForm({
                        alt_text: mediaData?.alt_text || '',
                        description: mediaData?.description || '',
                        is_primary: mediaData?.is_primary || false
                      })
                    }}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSaveChanges}
                    disabled={saving}
                  >
                    <Save className="h-4 w-4 mr-1" />
                    {saving ? 'Saving...' : 'Save'}
                  </Button>
                </>
              )}
            </div> */}
          </DialogTitle>
          <DialogDescription>
            View and edit media file details
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="space-y-6">
            <Skeleton className="aspect-video w-full" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
              </div>
              <div className="space-y-4">
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
              </div>
            </div>
          </div>
        ) : mediaData ? (
          <div className="space-y-6">
            {/* Media Preview */}
            <div className="relative">
              {getMediaPreview(mediaData)}
              <div className="absolute top-2 right-2 flex space-x-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => mediaData.media_url && copyToClipboard(mediaData.media_url, 'Media URL')}
                  disabled={!mediaData.media_url}
                >
                  <Copy className="h-4 w-4 mr-1" />
                  Copy URL
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => mediaData.media_url && openInNewTab(mediaData.media_url)}
                  disabled={!mediaData.media_url}
                >
                  <ExternalLink className="h-4 w-4 mr-1" />
                  View Full
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  asChild
                >
                  <a
                    href={mediaData.media_url || '#'}
                    download={`${mediaData.media_type}.${mediaData.format || 'unknown'}`}
                    onClick={(e) => !mediaData.media_url && e.preventDefault()}
                    style={{ pointerEvents: mediaData.media_url ? 'auto' : 'none', opacity: mediaData.media_url ? 1 : 0.5 }}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </a>
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* File Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <FileType className="h-5 w-5" />
                    <span>File Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Media Type</p>
                        <p className="font-medium capitalize">{mediaData.media_type}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Format</p>
                        <p className="font-medium uppercase">{mediaData.format}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">File Size</p>
                        <p className="font-medium">{formatFileSize(mediaData.file_size)}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Status</p>
                        <p className="font-medium capitalize">{mediaData.status}</p>
                      </div>
                      {mediaData.width && mediaData.height && (
                        <div>
                          <p className="text-muted-foreground">Dimensions</p>
                          <p className="font-medium">{mediaData.width} × {mediaData.height}</p>
                        </div>
                      )}
                      {mediaData.duration && (
                        <div>
                          <p className="text-muted-foreground">Duration</p>
                          <p className="font-medium">{mediaData.duration}s</p>
                        </div>
                      )}
                      <div>
                        <p className="text-muted-foreground">Quality Score</p>
                        <p className="font-medium">{((mediaData.metadata?.quality_score || 0) * 100).toFixed(0)}%</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Created</p>
                        <p className="font-medium">
                          {new Date(mediaData.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    {/* File URL Section */}
                    <div className="pt-2 border-t">
                      <p className="text-muted-foreground text-sm mb-2">Media URL</p>
                      <div className="flex items-center space-x-2 p-2 bg-muted rounded-md">
                        <Link className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                        <code className="text-xs flex-1 break-all">
                          {mediaData.media_url || 'No URL available'}
                        </code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => mediaData.media_url && copyToClipboard(mediaData.media_url, 'Media URL')}
                          disabled={!mediaData.media_url}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                      {mediaData.thumbnail_url && (
                        <div className="mt-2">
                          <p className="text-muted-foreground text-sm mb-2">Thumbnail URL</p>
                          <div className="flex items-center space-x-2 p-2 bg-muted rounded-md">
                            <Link className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                            <code className="text-xs flex-1 break-all">
                              {mediaData.thumbnail_url}
                            </code>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(mediaData.thumbnail_url, 'Thumbnail URL')}
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Editable Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Edit className="h-5 w-5" />
                    <span>Media Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {editing ? (
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="alt_text">Alt Text</Label>
                        <Input
                          id="alt_text"
                          value={editForm.alt_text}
                          onChange={(e) => setEditForm(prev => ({ ...prev, alt_text: e.target.value }))}
                          placeholder="Descriptive text for accessibility"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="description">Description</Label>
                        <Textarea
                          id="description"
                          value={editForm.description}
                          onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
                          placeholder="Detailed description of the media"
                          rows={3}
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="is_primary"
                          checked={editForm.is_primary}
                          onCheckedChange={(checked) => 
                            setEditForm(prev => ({ ...prev, is_primary: checked as boolean }))
                          }
                        />
                        <Label htmlFor="is_primary">Set as primary media</Label>
                      </div>
                    </>
                  ) : (
                    <>
                      <div>
                        <p className="text-sm text-muted-foreground">Alt Text</p>
                        <p className="font-medium">{mediaData.alt_text || 'Not set'}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Description</p>
                        <p className="font-medium">{mediaData.description || 'No description'}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Primary Media</p>
                        <Badge variant={mediaData.is_primary ? 'default' : 'outline'}>
                          {mediaData.is_primary ? 'Yes' : 'No'}
                        </Badge>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Last Updated</p>
                        <p className="font-medium">
                          {new Date(mediaData.updated_at).toLocaleString()}
                        </p>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>

              {/* Content Details */}
              {(mediaData.content_type || mediaData.video_type || mediaData.style ||
                mediaData.tone || mediaData.voice_tone || mediaData.ai_generation_type ||
                mediaData.content_description || mediaData.custom_ai_prompt ||
                mediaData.target_audience) && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center space-x-2">
                      <FileText className="h-5 w-5" />
                      <span>Content Details</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {mediaData.content_type && (
                      <div>
                        <p className="text-sm text-muted-foreground">Content Type</p>
                        <Badge variant="outline">{mediaData.content_type}</Badge>
                      </div>
                    )}

                    {mediaData.video_type && (
                      <div>
                        <p className="text-sm text-muted-foreground">Video Type</p>
                        <Badge variant="outline">{mediaData.video_type}</Badge>
                      </div>
                    )}

                    {mediaData.style && (
                      <div>
                        <p className="text-sm text-muted-foreground">Style</p>
                        <p className="font-medium">{mediaData.style}</p>
                      </div>
                    )}

                    {mediaData.tone && (
                      <div>
                        <p className="text-sm text-muted-foreground">Tone</p>
                        <p className="font-medium">{mediaData.tone}</p>
                      </div>
                    )}

                    {mediaData.voice_tone && (
                      <div>
                        <p className="text-sm text-muted-foreground">Voice Tone</p>
                        <p className="font-medium">{mediaData.voice_tone}</p>
                      </div>
                    )}

                    {mediaData.ai_generation_type && (
                      <div>
                        <p className="text-sm text-muted-foreground">AI Generation Type</p>
                        <Badge variant="secondary">{mediaData.ai_generation_type}</Badge>
                      </div>
                    )}

                    {mediaData.target_audience && (
                      <div>
                        <p className="text-sm text-muted-foreground">Target Audience</p>
                        <p className="font-medium">{mediaData.target_audience}</p>
                      </div>
                    )}

                    {mediaData.content_description && (
                      <div>
                        <p className="text-sm text-muted-foreground">Content Description</p>
                        <p className="font-medium text-sm leading-relaxed">{mediaData.content_description}</p>
                      </div>
                    )}

                    {mediaData.custom_ai_prompt && (
                      <div>
                        <p className="text-sm text-muted-foreground">Custom AI Prompt</p>
                        <div className="bg-muted p-3 rounded-md">
                          <p className="text-sm font-mono leading-relaxed">{mediaData.custom_ai_prompt}</p>
                        </div>
                      </div>
                    )}

                    {mediaData.content_options && Object.keys(mediaData.content_options).length > 0 && (
                      <div>
                        <p className="text-sm text-muted-foreground">Content Options</p>
                        <div className="bg-muted p-3 rounded-md">
                          <pre className="text-xs overflow-x-auto">
                            {JSON.stringify(mediaData.content_options, null, 2)}
                          </pre>
                        </div>
                      </div>
                    )}

                    {mediaData.platform_distribution && Object.keys(mediaData.platform_distribution).length > 0 && (
                      <div>
                        <p className="text-sm text-muted-foreground">Platform Distribution</p>
                        <div className="bg-muted p-3 rounded-md">
                          <pre className="text-xs overflow-x-auto">
                            {JSON.stringify(mediaData.platform_distribution, null, 2)}
                          </pre>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <File className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-semibold">Media not found</h3>
            <p className="text-muted-foreground">
              The requested media file could not be loaded.
            </p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

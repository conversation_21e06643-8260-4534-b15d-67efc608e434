"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from "recharts"
import { DollarSign, TrendingDown, CheckCircle2, ArrowRight, Download, Scissors } from "lucide-react"

// Sample data for cost analysis
const costCategories = [
  {
    id: 1,
    category: "Software Subscriptions",
    currentCost: 4200,
    potentialSavings: 1250,
    impactRisk: "low",
    recommendations: [
      {
        id: 1,
        title: "Consolidate redundant tools",
        description: "You have 3 similar design tools. Standardize on one platform.",
        savings: 650,
        effort: "medium",
        timeframe: "1-2 weeks",
      },
      {
        id: 2,
        title: "Downgrade unused premium features",
        description: "Several tools have premium features that usage data shows are rarely accessed.",
        savings: 350,
        effort: "low",
        timeframe: "immediate",
      },
      {
        id: 3,
        title: "Negotiate annual contracts",
        description: "Convert monthly subscriptions to annual billing for additional discounts.",
        savings: 250,
        effort: "low",
        timeframe: "at renewal",
      },
    ],
  },
  {
    id: 2,
    category: "AI Services",
    currentCost: 3800,
    potentialSavings: 950,
    impactRisk: "medium",
    recommendations: [
      {
        id: 4,
        title: "Optimize prompt engineering",
        description: "Reduce token usage by refining prompts and implementing caching strategies.",
        savings: 450,
        effort: "medium",
        timeframe: "2-3 weeks",
      },
      {
        id: 5,
        title: "Implement usage quotas",
        description: "Set department-specific usage limits to prevent unnecessary API calls.",
        savings: 300,
        effort: "medium",
        timeframe: "1 week",
      },
      {
        id: 6,
        title: "Migrate non-critical tasks to cheaper models",
        description: "Use smaller, more cost-effective models for tasks that don't require advanced capabilities.",
        savings: 200,
        effort: "medium",
        timeframe: "2-4 weeks",
      },
    ],
  },
  {
    id: 3,
    category: "Content Production",
    currentCost: 5600,
    potentialSavings: 1400,
    impactRisk: "medium",
    recommendations: [
      {
        id: 7,
        title: "Batch content creation",
        description: "Group similar content creation tasks to reduce setup and context-switching time.",
        savings: 600,
        effort: "medium",
        timeframe: "immediate",
      },
      {
        id: 8,
        title: "Implement content templates",
        description: "Create standardized templates for recurring content types.",
        savings: 450,
        effort: "medium",
        timeframe: "2-3 weeks",
      },
      {
        id: 9,
        title: "Optimize approval workflows",
        description: "Streamline the review process to reduce bottlenecks and revision cycles.",
        savings: 350,
        effort: "high",
        timeframe: "1 month",
      },
    ],
  },
  {
    id: 4,
    category: "Outsourced Services",
    currentCost: 6200,
    potentialSavings: 1850,
    impactRisk: "high",
    recommendations: [
      {
        id: 10,
        title: "Renegotiate vendor contracts",
        description: "Several contracts are above market rate and due for renewal.",
        savings: 800,
        effort: "high",
        timeframe: "at renewal",
      },
      {
        id: 11,
        title: "Bring select services in-house",
        description: "Some outsourced tasks could be handled by existing team with minimal training.",
        savings: 650,
        effort: "high",
        timeframe: "2-3 months",
      },
      {
        id: 12,
        title: "Implement performance-based pricing",
        description: "Convert fixed-fee arrangements to performance-based compensation.",
        savings: 400,
        effort: "medium",
        timeframe: "next contract cycle",
      },
    ],
  },
  {
    id: 5,
    category: "Infrastructure & Hosting",
    currentCost: 3200,
    potentialSavings: 850,
    impactRisk: "low",
    recommendations: [
      {
        id: 13,
        title: "Right-size cloud resources",
        description: "Several instances are over-provisioned based on actual usage patterns.",
        savings: 400,
        effort: "medium",
        timeframe: "2 weeks",
      },
      {
        id: 14,
        title: "Implement auto-scaling",
        description: "Configure resources to scale down during low-usage periods.",
        savings: 250,
        effort: "medium",
        timeframe: "3-4 weeks",
      },
      {
        id: 15,
        title: "Reserved instance commitments",
        description: "Convert on-demand resources to reserved instances for predictable workloads.",
        savings: 200,
        effort: "low",
        timeframe: "immediate",
      },
    ],
  },
]

// Historical cost data
const historicalCostData = [
  { month: "Jan", cost: 22500 },
  { month: "Feb", cost: 23100 },
  { month: "Mar", cost: 23800 },
  { month: "Apr", cost: 24200 },
  { month: "May", cost: 23900 },
  { month: "Jun", cost: 23000 },
]

// Cost breakdown data
const costBreakdownData = [
  { name: "Software Subscriptions", value: 4200, fill: "#0088FE" },
  { name: "AI Services", value: 3800, fill: "#00C49F" },
  { name: "Content Production", value: 5600, fill: "#FFBB28" },
  { name: "Outsourced Services", value: 6200, fill: "#FF8042" },
  { name: "Infrastructure & Hosting", value: 3200, fill: "#8884D8" },
]

// Projected savings data
const projectedSavingsData = [
  { month: "Current", cost: 23000 },
  { month: "Month 1", cost: 22100 },
  { month: "Month 2", cost: 21300 },
  { month: "Month 3", cost: 20500 },
  { month: "Month 4", cost: 19800 },
  { month: "Month 5", cost: 19200 },
  { month: "Month 6", cost: 18700 },
]

// Implementation plan data
const implementationPlanData = [
  {
    phase: "Phase 1: Quick Wins",
    timeframe: "Weeks 1-2",
    tasks: [
      "Downgrade unused premium features",
      "Implement usage quotas for AI services",
      "Reserved instance commitments",
      "Batch content creation",
    ],
    expectedSavings: 1100,
  },
  {
    phase: "Phase 2: Optimization",
    timeframe: "Weeks 3-6",
    tasks: [
      "Consolidate redundant tools",
      "Optimize prompt engineering",
      "Right-size cloud resources",
      "Implement content templates",
    ],
    expectedSavings: 1950,
  },
  {
    phase: "Phase 3: Structural Changes",
    timeframe: "Months 2-3",
    tasks: ["Optimize approval workflows", "Implement auto-scaling", "Migrate non-critical tasks to cheaper models"],
    expectedSavings: 800,
  },
  {
    phase: "Phase 4: Contract Negotiations",
    timeframe: "As contracts renew",
    tasks: [
      "Negotiate annual contracts",
      "Renegotiate vendor contracts",
      "Implement performance-based pricing",
      "Bring select services in-house",
    ],
    expectedSavings: 2100,
  },
]

// Helper function to format currency
const formatCurrency = (value) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value)
}

// Helper function to get badge variant based on impact risk
const getImpactBadgeVariant = (risk) => {
  switch (risk) {
    case "low":
      return "success"
    case "medium":
      return "warning"
    case "high":
      return "destructive"
    default:
      return "outline"
  }
}

// Helper function to get badge variant based on effort
const getEffortBadgeVariant = (effort) => {
  switch (effort) {
    case "low":
      return "success"
    case "medium":
      return "warning"
    case "high":
      return "destructive"
    default:
      return "outline"
  }
}

export function CostReductionAnalyzer() {
  const { toast } = useToast()
  const [selectedCategory, setSelectedCategory] = useState(costCategories[0])
  const [selectedRecommendations, setSelectedRecommendations] = useState([])
  const [implementationPlan, setImplementationPlan] = useState([])
  const [activeTab, setActiveTab] = useState("analysis")

  // Calculate totals
  const totalCurrentCost = costCategories.reduce((sum, category) => sum + category.currentCost, 0)
  const totalPotentialSavings = costCategories.reduce((sum, category) => sum + category.potentialSavings, 0)
  const savingsPercentage = (totalPotentialSavings / totalCurrentCost) * 100

  // Calculate selected savings
  const selectedSavings = selectedRecommendations.reduce((sum, recId) => {
    const recommendation = costCategories
      .flatMap((category) => category.recommendations)
      .find((rec) => rec.id === recId)
    return sum + (recommendation ? recommendation.savings : 0)
  }, 0)

  const handleCategorySelect = (category) => {
    setSelectedCategory(category)
  }

  const handleRecommendationToggle = (recId) => {
    setSelectedRecommendations((prev) => (prev.includes(recId) ? prev.filter((id) => id !== recId) : [...prev, recId]))
  }

  const handleCreatePlan = () => {
    if (selectedRecommendations.length === 0) {
      toast({
        title: "No recommendations selected",
        description: "Please select at least one recommendation to create an implementation plan.",
        variant: "destructive",
      })
      return
    }

    // In a real application, this would generate a custom implementation plan
    // For this demo, we'll just use the pre-defined plan
    setImplementationPlan(implementationPlanData)
    setActiveTab("implementation")

    toast({
      title: "Implementation Plan Created",
      description: `Plan created with ${selectedRecommendations.length} cost-saving initiatives.`,
    })
  }

  const handleExportPlan = () => {
    toast({
      title: "Plan Exported",
      description: "The implementation plan has been exported to CSV.",
    })
  }

  const handleApprove = () => {
    toast({
      title: "Plan Approved",
      description: "The cost reduction plan has been approved and added to your task queue.",
    })
  }

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Monthly Cost</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalCurrentCost)}</div>
            <p className="text-xs text-muted-foreground">Across all categories</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Potential Savings</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalPotentialSavings)}</div>
            <p className="text-xs text-muted-foreground">{savingsPercentage.toFixed(1)}% of total cost</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Selected Savings</CardTitle>
            <Scissors className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(selectedSavings)}</div>
            <p className="text-xs text-muted-foreground">
              {((selectedSavings / totalPotentialSavings) * 100).toFixed(1)}% of potential savings
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recommendations</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {selectedRecommendations.length} / {costCategories.flatMap((c) => c.recommendations).length}
            </div>
            <p className="text-xs text-muted-foreground">Cost-saving opportunities selected</p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="analysis">Cost Analysis</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="implementation">Implementation Plan</TabsTrigger>
        </TabsList>

        <TabsContent value="analysis" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Cost Breakdown</CardTitle>
                <CardDescription>Current monthly costs by category</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={costBreakdownData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {costBreakdownData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => formatCurrency(value)} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Historical Cost Trend</CardTitle>
                <CardDescription>Monthly costs over the past 6 months</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={historicalCostData}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis domain={[15000, 30000]} />
                      <Tooltip formatter={(value) => formatCurrency(value)} />
                      <Line type="monotone" dataKey="cost" stroke="#8884d8" activeDot={{ r: 8 }} />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-2">
              <CardHeader>
                <CardTitle>Savings Opportunities by Category</CardTitle>
                <CardDescription>Potential monthly savings with performance impact risk</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={costCategories}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="category" />
                      <YAxis />
                      <Tooltip formatter={(value) => formatCurrency(value)} />
                      <Legend />
                      <Bar dataKey="currentCost" name="Current Cost" fill="#8884d8" />
                      <Bar dataKey="potentialSavings" name="Potential Savings" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
              <CardFooter>
                <div className="w-full">
                  <div className="grid grid-cols-3 gap-4">
                    {costCategories.map((category) => (
                      <Button
                        key={category.id}
                        variant={selectedCategory.id === category.id ? "default" : "outline"}
                        className="w-full justify-start"
                        onClick={() => handleCategorySelect(category)}
                      >
                        <div className="flex flex-col items-start">
                          <span className="text-sm">{category.category}</span>
                          <div className="flex items-center mt-1">
                            <Badge variant={getImpactBadgeVariant(category.impactRisk)} className="text-xs">
                              {category.impactRisk} risk
                            </Badge>
                            <span className="ml-2 text-xs">{formatCurrency(category.potentialSavings)}</span>
                          </div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>{selectedCategory.category}</CardTitle>
                  <CardDescription>
                    Current cost: {formatCurrency(selectedCategory.currentCost)} | Potential savings:{" "}
                    {formatCurrency(selectedCategory.potentialSavings)} |{" "}
                    <Badge variant={getImpactBadgeVariant(selectedCategory.impactRisk)}>
                      {selectedCategory.impactRisk} performance impact risk
                    </Badge>
                  </CardDescription>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" onClick={() => setActiveTab("analysis")}>
                    Back to Analysis
                  </Button>
                  <Button size="sm" onClick={handleCreatePlan}>
                    Create Implementation Plan
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {selectedCategory.recommendations.map((recommendation) => (
                  <Card key={recommendation.id}>
                    <CardHeader className="pb-2">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <CardTitle className="text-base">{recommendation.title}</CardTitle>
                          <CardDescription>{recommendation.description}</CardDescription>
                        </div>
                        <Checkbox
                          checked={selectedRecommendations.includes(recommendation.id)}
                          onCheckedChange={() => handleRecommendationToggle(recommendation.id)}
                        />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <p className="text-xs text-muted-foreground">Potential Savings</p>
                          <p className="text-sm font-medium">{formatCurrency(recommendation.savings)}/month</p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Implementation Effort</p>
                          <Badge variant={getEffortBadgeVariant(recommendation.effort)}>{recommendation.effort}</Badge>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Timeframe</p>
                          <p className="text-sm">{recommendation.timeframe}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">
                {
                  selectedRecommendations.filter((id) => selectedCategory.recommendations.some((rec) => rec.id === id))
                    .length
                }{" "}
                of {selectedCategory.recommendations.length} recommendations selected
              </div>
              <Button variant="outline" size="sm" onClick={() => setActiveTab("implementation")}>
                Skip to Implementation <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="implementation" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Implementation Plan</CardTitle>
                  <CardDescription>
                    Phased approach to implement {selectedRecommendations.length} cost-saving initiatives
                  </CardDescription>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" onClick={handleExportPlan}>
                    <Download className="mr-2 h-4 w-4" /> Export Plan
                  </Button>
                  <Button size="sm" onClick={handleApprove}>
                    <CheckCircle2 className="mr-2 h-4 w-4" /> Approve Plan
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Projected Cost Reduction</CardTitle>
                    <CardDescription>Estimated monthly costs over the next 6 months</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={projectedSavingsData}
                          margin={{
                            top: 5,
                            right: 30,
                            left: 20,
                            bottom: 5,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis domain={[15000, 25000]} />
                          <Tooltip formatter={(value) => formatCurrency(value)} />
                          <Line type="monotone" dataKey="cost" stroke="#82ca9d" activeDot={{ r: 8 }} />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Implementation Phases</h3>
                  {implementationPlan.map((phase, index) => (
                    <Card key={index}>
                      <CardHeader className="pb-2">
                        <div className="flex items-start justify-between">
                          <div>
                            <CardTitle className="text-base">{phase.phase}</CardTitle>
                            <CardDescription>{phase.timeframe}</CardDescription>
                          </div>
                          <Badge variant="outline">{formatCurrency(phase.expectedSavings)}</Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {phase.tasks.map((task, taskIndex) => (
                            <div key={taskIndex} className="flex items-start space-x-2">
                              <div className="mt-0.5">
                                <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
                              </div>
                              <span className="text-sm">{task}</span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Implementation Notes</h3>
                  <Textarea
                    placeholder="Add any additional notes or considerations for the implementation team..."
                    className="min-h-[100px]"
                  />
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Approval & Notifications</h3>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch id="notify-team" />
                      <Label htmlFor="notify-team">Notify team members of approved plan</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="add-calendar" />
                      <Label htmlFor="add-calendar">Add implementation milestones to calendar</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="progress-reports" defaultChecked />
                      <Label htmlFor="progress-reports">Schedule weekly progress reports</Label>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div>
                <p className="text-sm font-medium">
                  Total Annual Savings: {formatCurrency(totalPotentialSavings * 12)}
                </p>
                <p className="text-xs text-muted-foreground">Based on selected recommendations</p>
              </div>
              <Button onClick={handleApprove}>Approve Implementation Plan</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

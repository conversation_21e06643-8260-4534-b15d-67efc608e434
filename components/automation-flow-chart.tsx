"use client"

import type React from "react"
import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  Upload,
  User,
  Package,
  ArrowRight,
  Brain,
  Target,
  TrendingUp,
  MessageSquare,
  Calendar,
  BarChart3,
  Zap,
  RefreshCw,
  Users,
  ShoppingCart,
  Mail,
  Globe,
  Settings,
  AlertTriangle,
  Clock,
  GitBranch,
} from "lucide-react"
import { FlowOptimizationSuggestions } from "@/components/flow-optimization-suggestions"

interface FlowNode {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  type: "start" | "process" | "decision" | "end" | "system"
  status: "active" | "pending" | "completed" | "error"
  connections: string[]
  position: { x: number; y: number }
  category: "upload" | "analysis" | "automation" | "output" | "feedback"
}

export function AutomationFlowChart() {
  const [activeTab, setActiveTab] = useState("flowchart")

  const flowNodes: FlowNode[] = [
    // Starting Points
    {
      id: "product-upload",
      title: "Product Upload",
      description: "User uploads new product data",
      icon: <Package className="h-5 w-5" />,
      type: "start",
      status: "active",
      connections: ["product-analysis", "content-generation"],
      position: { x: 50, y: 100 },
      category: "upload",
    },
    {
      id: "avatar-upload",
      title: "Avatar Upload",
      description: "User uploads avatar/persona data",
      icon: <User className="h-5 w-5" />,
      type: "start",
      status: "active",
      connections: ["avatar-analysis", "audience-segmentation"],
      position: { x: 50, y: 300 },
      category: "upload",
    },

    // Analysis Layer
    {
      id: "product-analysis",
      title: "Product Analysis",
      description: "AI analyzes product features, benefits, and market positioning",
      icon: <Brain className="h-5 w-5" />,
      type: "process",
      status: "completed",
      connections: ["market-research", "competitor-analysis"],
      position: { x: 250, y: 50 },
      category: "analysis",
    },
    {
      id: "avatar-analysis",
      title: "Avatar Analysis",
      description: "AI processes persona data and behavioral patterns",
      icon: <Target className="h-5 w-5" />,
      type: "process",
      status: "completed",
      connections: ["audience-segmentation", "content-personalization"],
      position: { x: 250, y: 200 },
      category: "analysis",
    },
    {
      id: "market-research",
      title: "Market Research",
      description: "Automated trend analysis and market validation",
      icon: <TrendingUp className="h-5 w-5" />,
      type: "process",
      status: "active",
      connections: ["opportunity-identification"],
      position: { x: 450, y: 25 },
      category: "analysis",
    },
    {
      id: "competitor-analysis",
      title: "Competitor Analysis",
      description: "Automated competitive landscape assessment",
      icon: <BarChart3 className="h-5 w-5" />,
      type: "process",
      status: "active",
      connections: ["pricing-strategy"],
      position: { x: 450, y: 100 },
      category: "analysis",
    },
    {
      id: "audience-segmentation",
      title: "Audience Segmentation",
      description: "AI segments audience based on avatar data",
      icon: <Users className="h-5 w-5" />,
      type: "process",
      status: "completed",
      connections: ["content-personalization", "campaign-targeting"],
      position: { x: 450, y: 200 },
      category: "analysis",
    },

    // Automation Layer
    {
      id: "content-generation",
      title: "Content Generation",
      description: "AI creates product descriptions, marketing copy, and visuals",
      icon: <MessageSquare className="h-5 w-5" />,
      type: "process",
      status: "active",
      connections: ["content-scheduling", "social-distribution"],
      position: { x: 650, y: 75 },
      category: "automation",
    },
    {
      id: "content-personalization",
      title: "Content Personalization",
      description: "Tailors content for specific avatar segments",
      icon: <Settings className="h-5 w-5" />,
      type: "process",
      status: "active",
      connections: ["email-sequences", "landing-pages"],
      position: { x: 650, y: 175 },
      category: "automation",
    },
    {
      id: "opportunity-identification",
      title: "Opportunity ID",
      description: "Identifies automation and scaling opportunities",
      icon: <Zap className="h-5 w-5" />,
      type: "decision",
      status: "pending",
      connections: ["white-label-check", "affiliate-check"],
      position: { x: 650, y: 25 },
      category: "automation",
    },
    {
      id: "pricing-strategy",
      title: "Pricing Strategy",
      description: "AI optimizes pricing based on market analysis",
      icon: <ShoppingCart className="h-5 w-5" />,
      type: "process",
      status: "active",
      connections: ["campaign-targeting"],
      position: { x: 650, y: 125 },
      category: "automation",
    },

    // Decision Points
    {
      id: "white-label-check",
      title: "White Label Check",
      description: "Evaluates white label opportunity potential",
      icon: <AlertTriangle className="h-5 w-5" />,
      type: "decision",
      status: "pending",
      connections: ["white-label-automation", "standard-flow"],
      position: { x: 850, y: 0 },
      category: "automation",
    },
    {
      id: "affiliate-check",
      title: "Affiliate Check",
      description: "Assesses affiliate marketing potential",
      icon: <AlertTriangle className="h-5 w-5" />,
      type: "decision",
      status: "pending",
      connections: ["affiliate-automation", "standard-flow"],
      position: { x: 850, y: 50 },
      category: "automation",
    },

    // Output Layer
    {
      id: "content-scheduling",
      title: "Content Scheduling",
      description: "Automatically schedules content across platforms",
      icon: <Calendar className="h-5 w-5" />,
      type: "process",
      status: "active",
      connections: ["performance-tracking"],
      position: { x: 850, y: 100 },
      category: "output",
    },
    {
      id: "social-distribution",
      title: "Social Distribution",
      description: "Distributes content across social media platforms",
      icon: <Globe className="h-5 w-5" />,
      type: "process",
      status: "active",
      connections: ["engagement-monitoring"],
      position: { x: 850, y: 150 },
      category: "output",
    },
    {
      id: "email-sequences",
      title: "Email Sequences",
      description: "Automated email marketing campaigns",
      icon: <Mail className="h-5 w-5" />,
      type: "process",
      status: "active",
      connections: ["conversion-tracking"],
      position: { x: 850, y: 200 },
      category: "output",
    },
    {
      id: "landing-pages",
      title: "Landing Pages",
      description: "Auto-generated personalized landing pages",
      icon: <Globe className="h-5 w-5" />,
      type: "process",
      status: "completed",
      connections: ["conversion-tracking"],
      position: { x: 850, y: 250 },
      category: "output",
    },
    {
      id: "campaign-targeting",
      title: "Campaign Targeting",
      description: "Automated ad campaign creation and targeting",
      icon: <Target className="h-5 w-5" />,
      type: "process",
      status: "active",
      connections: ["performance-tracking"],
      position: { x: 850, y: 300 },
      category: "output",
    },

    // Specialized Automation
    {
      id: "white-label-automation",
      title: "White Label Automation",
      description: "Automated white label opportunity processing",
      icon: <Zap className="h-5 w-5" />,
      type: "system",
      status: "active",
      connections: ["manufacturer-outreach"],
      position: { x: 1050, y: 0 },
      category: "automation",
    },
    {
      id: "affiliate-automation",
      title: "Affiliate Automation",
      description: "Automated affiliate network applications",
      icon: <Zap className="h-5 w-5" />,
      type: "system",
      status: "active",
      connections: ["network-applications"],
      position: { x: 1050, y: 50 },
      category: "automation",
    },
    {
      id: "standard-flow",
      title: "Standard Flow",
      description: "Continue with standard automation process",
      icon: <RefreshCw className="h-5 w-5" />,
      type: "process",
      status: "active",
      connections: ["performance-tracking"],
      position: { x: 1050, y: 100 },
      category: "automation",
    },

    // Monitoring & Feedback
    {
      id: "performance-tracking",
      title: "Performance Tracking",
      description: "Real-time performance monitoring and analytics",
      icon: <BarChart3 className="h-5 w-5" />,
      type: "system",
      status: "active",
      connections: ["optimization-engine"],
      position: { x: 1050, y: 150 },
      category: "feedback",
    },
    {
      id: "engagement-monitoring",
      title: "Engagement Monitoring",
      description: "Tracks social media engagement and interactions",
      icon: <Users className="h-5 w-5" />,
      type: "system",
      status: "active",
      connections: ["optimization-engine"],
      position: { x: 1050, y: 200 },
      category: "feedback",
    },
    {
      id: "conversion-tracking",
      title: "Conversion Tracking",
      description: "Monitors conversion rates and sales performance",
      icon: <ShoppingCart className="h-5 w-5" />,
      type: "system",
      status: "active",
      connections: ["optimization-engine"],
      position: { x: 1050, y: 250 },
      category: "feedback",
    },
    {
      id: "manufacturer-outreach",
      title: "Manufacturer Outreach",
      description: "Automated communication with manufacturers",
      icon: <Mail className="h-5 w-5" />,
      type: "process",
      status: "pending",
      connections: ["feedback-loop"],
      position: { x: 1250, y: 0 },
      category: "output",
    },
    {
      id: "network-applications",
      title: "Network Applications",
      description: "Automated affiliate network applications",
      icon: <Upload className="h-5 w-5" />,
      type: "process",
      status: "pending",
      connections: ["feedback-loop"],
      position: { x: 1250, y: 50 },
      category: "output",
    },

    // Final Layer
    {
      id: "optimization-engine",
      title: "Optimization Engine",
      description: "AI-powered continuous optimization and learning",
      icon: <Brain className="h-5 w-5" />,
      type: "system",
      status: "active",
      connections: ["feedback-loop"],
      position: { x: 1250, y: 200 },
      category: "feedback",
    },
    {
      id: "feedback-loop",
      title: "Feedback Loop",
      description: "Continuous learning and strategy adjustment",
      icon: <RefreshCw className="h-5 w-5" />,
      type: "end",
      status: "active",
      connections: [],
      position: { x: 1450, y: 150 },
      category: "feedback",
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200"
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "completed":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "error":
        return "bg-red-100 text-red-800 border-red-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getNodeTypeColor = (type: string) => {
    switch (type) {
      case "start":
        return "border-green-500 bg-green-50"
      case "process":
        return "border-blue-500 bg-blue-50"
      case "decision":
        return "border-yellow-500 bg-yellow-50"
      case "system":
        return "border-purple-500 bg-purple-50"
      case "end":
        return "border-red-500 bg-red-50"
      default:
        return "border-gray-500 bg-gray-50"
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "upload":
        return "text-green-600"
      case "analysis":
        return "text-blue-600"
      case "automation":
        return "text-purple-600"
      case "output":
        return "text-orange-600"
      case "feedback":
        return "text-red-600"
      default:
        return "text-gray-600"
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            Automation Flow Chart & Optimization
          </CardTitle>
          <CardDescription>
            Visual representation of automation processes with AI-powered optimization suggestions
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="flowchart">Flow Chart</TabsTrigger>
          <TabsTrigger value="optimization">
            <Brain className="h-4 w-4 mr-1" />
            AI Optimization
          </TabsTrigger>
        </TabsList>

        <TabsContent value="flowchart" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Automation Flow Chart</CardTitle>
              <CardDescription>
                Visual representation of the automation process from product and avatar uploads through the complete
                workflow
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Legend */}
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Node Types</h4>
                    <div className="space-y-1 text-xs">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded border-2 border-green-500 bg-green-50"></div>
                        <span>Start</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded border-2 border-blue-500 bg-blue-50"></div>
                        <span>Process</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded border-2 border-yellow-500 bg-yellow-50"></div>
                        <span>Decision</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded border-2 border-purple-500 bg-purple-50"></div>
                        <span>System</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded border-2 border-red-500 bg-red-50"></div>
                        <span>End</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Status</h4>
                    <div className="space-y-1 text-xs">
                      <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200 text-xs">
                        Active
                      </Badge>
                      <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200 text-xs">
                        Pending
                      </Badge>
                      <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200 text-xs">
                        Completed
                      </Badge>
                      <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200 text-xs">
                        Error
                      </Badge>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Categories</h4>
                    <div className="space-y-1 text-xs">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-green-600"></div>
                        <span>Upload</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-blue-600"></div>
                        <span>Analysis</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-purple-600"></div>
                        <span>Automation</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-orange-600"></div>
                        <span>Output</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-red-600"></div>
                        <span>Feedback</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Flow Metrics</h4>
                    <div className="space-y-1 text-xs">
                      <div>Total Nodes: {flowNodes.length}</div>
                      <div>Active: {flowNodes.filter((n) => n.status === "active").length}</div>
                      <div>Pending: {flowNodes.filter((n) => n.status === "pending").length}</div>
                      <div>Completed: {flowNodes.filter((n) => n.status === "completed").length}</div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Processing Time</h4>
                    <div className="space-y-1 text-xs">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>Avg: 2.3 min</span>
                      </div>
                      <div>Upload → Analysis: 30s</div>
                      <div>Analysis → Automation: 45s</div>
                      <div>Automation → Output: 60s</div>
                    </div>
                  </div>
                </div>

                {/* Flow Chart */}
                <ScrollArea className="h-[600px] w-full">
                  <div className="relative" style={{ width: "1600px", height: "400px" }}>
                    {/* Render connections first (behind nodes) */}
                    <svg className="absolute inset-0 w-full h-full pointer-events-none" style={{ zIndex: 1 }}>
                      {flowNodes.map((node) =>
                        node.connections.map((connectionId) => {
                          const targetNode = flowNodes.find((n) => n.id === connectionId)
                          if (!targetNode) return null

                          const startX = node.position.x + 100
                          const startY = node.position.y + 40
                          const endX = targetNode.position.x
                          const endY = targetNode.position.y + 40

                          return (
                            <g key={`${node.id}-${connectionId}`}>
                              <line
                                x1={startX}
                                y1={startY}
                                x2={endX}
                                y2={endY}
                                stroke="#94a3b8"
                                strokeWidth="2"
                                markerEnd="url(#arrowhead)"
                              />
                            </g>
                          )
                        }),
                      )}
                      <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                          <polygon points="0 0, 10 3.5, 0 7" fill="#94a3b8" />
                        </marker>
                      </defs>
                    </svg>

                    {/* Render nodes */}
                    {flowNodes.map((node) => (
                      <div
                        key={node.id}
                        className={`absolute border-2 rounded-lg p-3 bg-white shadow-sm hover:shadow-md transition-shadow cursor-pointer ${getNodeTypeColor(
                          node.type,
                        )}`}
                        style={{
                          left: `${node.position.x}px`,
                          top: `${node.position.y}px`,
                          width: "180px",
                          zIndex: 2,
                        }}
                      >
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className={`${getCategoryColor(node.category)}`}>{node.icon}</div>
                            <Badge variant="outline" className={`text-xs ${getStatusColor(node.status)}`}>
                              {node.status}
                            </Badge>
                          </div>
                          <div>
                            <h4 className="font-medium text-sm leading-tight">{node.title}</h4>
                            <p className="text-xs text-muted-foreground mt-1 leading-tight">{node.description}</p>
                          </div>
                          {node.connections.length > 0 && (
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <ArrowRight className="h-3 w-3" />
                              <span>
                                {node.connections.length} connection{node.connections.length !== 1 ? "s" : ""}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </CardContent>
          </Card>

          {/* Process Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Product Upload Flow</CardTitle>
                <CardDescription>Automation triggered by product uploads</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="mt-1 bg-green-100 p-1.5 rounded-full text-green-700">
                      <Package className="h-4 w-4" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Product Analysis</h4>
                      <p className="text-xs text-muted-foreground">
                        AI analyzes product features, benefits, and market positioning to identify opportunities
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="mt-1 bg-blue-100 p-1.5 rounded-full text-blue-700">
                      <TrendingUp className="h-4 w-4" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Market Research</h4>
                      <p className="text-xs text-muted-foreground">
                        Automated trend analysis and competitive landscape assessment
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="mt-1 bg-purple-100 p-1.5 rounded-full text-purple-700">
                      <MessageSquare className="h-4 w-4" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Content Generation</h4>
                      <p className="text-xs text-muted-foreground">
                        AI creates product descriptions, marketing copy, and promotional materials
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="mt-1 bg-orange-100 p-1.5 rounded-full text-orange-700">
                      <Globe className="h-4 w-4" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Multi-Channel Distribution</h4>
                      <p className="text-xs text-muted-foreground">
                        Automated content scheduling and distribution across platforms
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Avatar Upload Flow</CardTitle>
                <CardDescription>Automation triggered by avatar/persona uploads</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="mt-1 bg-green-100 p-1.5 rounded-full text-green-700">
                      <User className="h-4 w-4" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Avatar Analysis</h4>
                      <p className="text-xs text-muted-foreground">
                        AI processes persona data and behavioral patterns to understand target audience
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="mt-1 bg-blue-100 p-1.5 rounded-full text-blue-700">
                      <Users className="h-4 w-4" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Audience Segmentation</h4>
                      <p className="text-xs text-muted-foreground">
                        Automated segmentation based on demographics, interests, and behaviors
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="mt-1 bg-purple-100 p-1.5 rounded-full text-purple-700">
                      <Settings className="h-4 w-4" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Content Personalization</h4>
                      <p className="text-xs text-muted-foreground">
                        Tailors content and messaging for specific avatar segments
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="mt-1 bg-orange-100 p-1.5 rounded-full text-orange-700">
                      <Mail className="h-4 w-4" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Targeted Campaigns</h4>
                      <p className="text-xs text-muted-foreground">
                        Automated email sequences and targeted advertising campaigns
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Key Automation Points */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Key Automation Decision Points</CardTitle>
              <CardDescription>Critical decision nodes that determine automation paths</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 border rounded-lg bg-yellow-50 border-yellow-200">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    <h4 className="font-medium">White Label Check</h4>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Evaluates if the product has white label potential and triggers specialized automation workflows
                  </p>
                </div>
                <div className="p-4 border rounded-lg bg-yellow-50 border-yellow-200">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    <h4 className="font-medium">Affiliate Check</h4>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Assesses affiliate marketing opportunities and initiates network application processes
                  </p>
                </div>
                <div className="p-4 border rounded-lg bg-purple-50 border-purple-200">
                  <div className="flex items-center gap-2 mb-2">
                    <Brain className="h-5 w-5 text-purple-600" />
                    <h4 className="font-medium">Optimization Engine</h4>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Continuously learns from performance data and adjusts automation strategies
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="optimization" className="space-y-4">
          <FlowOptimizationSuggestions />
        </TabsContent>
      </Tabs>
    </div>
  )
}

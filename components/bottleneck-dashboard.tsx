"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/chart"
import { ResponsiveContainer, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Line } from "recharts"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AlertTriangle, Clock, CheckCircle, XCircle } from "lucide-react"

const bottleneckData = [
  {
    process: "Video Editing",
    avgDelay: 48,
    errorRate: 15,
    impact: "High",
    status: "Critical",
    department: "Production",
    lastOccurrence: "2 hours ago",
    trend: "Increasing",
  },
  {
    process: "Content Approval",
    avgDelay: 36,
    errorRate: 8,
    impact: "Medium",
    status: "Warning",
    department: "Management",
    lastOccurrence: "1 day ago",
    trend: "Stable",
  },
  {
    process: "Thumbnail Creation",
    avgDelay: 24,
    errorRate: 12,
    impact: "Medium",
    status: "Warning",
    department: "Design",
    lastOccurrence: "5 hours ago",
    trend: "Decreasing",
  },
  {
    process: "Script Writing",
    avgDelay: 18,
    errorRate: 5,
    impact: "Low",
    status: "Resolved",
    department: "Content",
    lastOccurrence: "3 days ago",
    trend: "Decreasing",
  },
  {
    process: "Social Media Posting",
    avgDelay: 6,
    errorRate: 10,
    impact: "Medium",
    status: "Warning",
    department: "Marketing",
    lastOccurrence: "12 hours ago",
    trend: "Stable",
  },
  {
    process: "Analytics Review",
    avgDelay: 12,
    errorRate: 3,
    impact: "Low",
    status: "Monitoring",
    department: "Analytics",
    lastOccurrence: "2 days ago",
    trend: "Stable",
  },
  {
    process: "Product Listing",
    avgDelay: 30,
    errorRate: 18,
    impact: "High",
    status: "Critical",
    department: "Products",
    lastOccurrence: "6 hours ago",
    trend: "Increasing",
  },
  {
    process: "Email Campaign Setup",
    avgDelay: 10,
    errorRate: 7,
    impact: "Low",
    status: "Monitoring",
    department: "Marketing",
    lastOccurrence: "4 days ago",
    trend: "Stable",
  },
]

const delayTrendData = [
  {
    name: "Week 1",
    "Video Editing": 36,
    "Content Approval": 42,
    "Thumbnail Creation": 30,
    "Product Listing": 24,
  },
  {
    name: "Week 2",
    "Video Editing": 40,
    "Content Approval": 38,
    "Thumbnail Creation": 28,
    "Product Listing": 26,
  },
  {
    name: "Week 3",
    "Video Editing": 42,
    "Content Approval": 36,
    "Thumbnail Creation": 26,
    "Product Listing": 28,
  },
  {
    name: "Week 4",
    "Video Editing": 48,
    "Content Approval": 36,
    "Thumbnail Creation": 24,
    "Product Listing": 30,
  },
]

const errorTrendData = [
  {
    name: "Week 1",
    "Video Editing": 10,
    "Content Approval": 12,
    "Thumbnail Creation": 15,
    "Product Listing": 14,
  },
  {
    name: "Week 2",
    "Video Editing": 12,
    "Content Approval": 10,
    "Thumbnail Creation": 14,
    "Product Listing": 16,
  },
  {
    name: "Week 3",
    "Video Editing": 14,
    "Content Approval": 9,
    "Thumbnail Creation": 13,
    "Product Listing": 17,
  },
  {
    name: "Week 4",
    "Video Editing": 15,
    "Content Approval": 8,
    "Thumbnail Creation": 12,
    "Product Listing": 18,
  },
]

const departmentBottlenecks = [
  {
    name: "Production",
    bottlenecks: 2,
    avgDelay: 42,
  },
  {
    name: "Marketing",
    bottlenecks: 3,
    avgDelay: 18,
  },
  {
    name: "Content",
    bottlenecks: 1,
    avgDelay: 15,
  },
  {
    name: "Design",
    bottlenecks: 2,
    avgDelay: 24,
  },
  {
    name: "Management",
    bottlenecks: 1,
    avgDelay: 36,
  },
  {
    name: "Products",
    bottlenecks: 2,
    avgDelay: 30,
  },
]

export function BottleneckDashboard() {
  const [status, setStatus] = useState("all")
  const [sortBy, setSortBy] = useState("avgDelay")
  const [sortOrder, setSortOrder] = useState("desc")

  const filteredData =
    status === "all"
      ? bottleneckData
      : bottleneckData.filter((item) => item.status.toLowerCase() === status.toLowerCase())

  const sortedData = [...filteredData].sort((a, b) => {
    const factor = sortOrder === "asc" ? 1 : -1
    if (sortBy === "impact") {
      const impactOrder = { High: 3, Medium: 2, Low: 1 }
      return factor * (impactOrder[a.impact] - impactOrder[b.impact])
    }
    if (sortBy === "status") {
      const statusOrder = { Critical: 4, Warning: 3, Monitoring: 2, Resolved: 1 }
      return factor * (statusOrder[a.status] - statusOrder[b.status])
    }
    if (sortBy === "trend") {
      const trendOrder = { Increasing: 3, Stable: 2, Decreasing: 1 }
      return factor * (trendOrder[a.trend] - trendOrder[b.trend])
    }
    return factor * (a[sortBy] - b[sortBy])
  })

  const criticalCount = bottleneckData.filter((item) => item.status === "Critical").length
  const warningCount = bottleneckData.filter((item) => item.status === "Warning").length
  const monitoringCount = bottleneckData.filter((item) => item.status === "Monitoring").length
  const resolvedCount = bottleneckData.filter((item) => item.status === "Resolved").length

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortBy(field)
      setSortOrder("desc")
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case "Critical":
        return "destructive"
      case "Warning":
        return "warning"
      case "Monitoring":
        return "secondary"
      case "Resolved":
        return "success"
      default:
        return "default"
    }
  }

  const getTrendIcon = (trend) => {
    switch (trend) {
      case "Increasing":
        return <XCircle className="h-4 w-4 text-destructive" />
      case "Decreasing":
        return <CheckCircle className="h-4 w-4 text-success" />
      case "Stable":
        return <Clock className="h-4 w-4 text-muted-foreground" />
      default:
        return null
    }
  }

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className={criticalCount > 0 ? "border-red-500" : ""}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Bottlenecks</CardTitle>
            <AlertTriangle className={`h-4 w-4 ${criticalCount > 0 ? "text-red-500" : "text-muted-foreground"}`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${criticalCount > 0 ? "text-red-500" : ""}`}>{criticalCount}</div>
            <p className="text-xs text-muted-foreground">Require immediate attention</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Warning Bottlenecks</CardTitle>
            <AlertTriangle className={`h-4 w-4 ${warningCount > 0 ? "text-amber-500" : "text-muted-foreground"}`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${warningCount > 0 ? "text-amber-500" : ""}`}>{warningCount}</div>
            <p className="text-xs text-muted-foreground">Need monitoring and action</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monitoring</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{monitoringCount}</div>
            <p className="text-xs text-muted-foreground">Under observation</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resolved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">{resolvedCount}</div>
            <p className="text-xs text-muted-foreground">Fixed in the last 30 days</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="current" className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="current">Current Bottlenecks</TabsTrigger>
            <TabsTrigger value="trends">Delay Trends</TabsTrigger>
            <TabsTrigger value="errors">Error Trends</TabsTrigger>
            <TabsTrigger value="departments">By Department</TabsTrigger>
          </TabsList>
          <div className="flex items-center space-x-2">
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger className="w-[180px] h-8">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
                <SelectItem value="warning">Warning</SelectItem>
                <SelectItem value="monitoring">Monitoring</SelectItem>
                <SelectItem value="resolved">Resolved</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              Export Data
            </Button>
          </div>
        </div>

        <TabsContent value="current" className="space-y-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Process</TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("avgDelay")}>
                      Avg Delay (hrs) {sortBy === "avgDelay" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("errorRate")}>
                      Error Rate (%) {sortBy === "errorRate" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("impact")}>
                      Impact {sortBy === "impact" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("status")}>
                      Status {sortBy === "status" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Last Occurrence</TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("trend")}>
                      Trend {sortBy === "trend" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedData.map((bottleneck, i) => (
                    <TableRow key={i}>
                      <TableCell className="font-medium">{bottleneck.process}</TableCell>
                      <TableCell>{bottleneck.avgDelay}</TableCell>
                      <TableCell>{bottleneck.errorRate}%</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={
                            bottleneck.impact === "High"
                              ? "border-red-500 text-red-500"
                              : bottleneck.impact === "Medium"
                                ? "border-amber-500 text-amber-500"
                                : "border-blue-500 text-blue-500"
                          }
                        >
                          {bottleneck.impact}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusColor(bottleneck.status)}>{bottleneck.status}</Badge>
                      </TableCell>
                      <TableCell>{bottleneck.department}</TableCell>
                      <TableCell>{bottleneck.lastOccurrence}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {getTrendIcon(bottleneck.trend)}
                          <span>{bottleneck.trend}</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Delay Trends by Process</CardTitle>
              <CardDescription>Average delay in hours over the past 4 weeks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={delayTrendData}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="Video Editing" stroke="#8884d8" activeDot={{ r: 8 }} />
                    <Line type="monotone" dataKey="Content Approval" stroke="#82ca9d" />
                    <Line type="monotone" dataKey="Thumbnail Creation" stroke="#ff7300" />
                    <Line type="monotone" dataKey="Product Listing" stroke="#0088FE" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Error Rate Trends by Process</CardTitle>
              <CardDescription>Error rates (%) over the past 4 weeks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={errorTrendData}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="Video Editing" stroke="#8884d8" activeDot={{ r: 8 }} />
                    <Line type="monotone" dataKey="Content Approval" stroke="#82ca9d" />
                    <Line type="monotone" dataKey="Thumbnail Creation" stroke="#ff7300" />
                    <Line type="monotone" dataKey="Product Listing" stroke="#0088FE" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="departments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bottlenecks by Department</CardTitle>
              <CardDescription>Number of bottlenecks and average delay by department</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={departmentBottlenecks}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Bar yAxisId="left" dataKey="bottlenecks" fill="#8884d8" name="Number of Bottlenecks" />
                    <Bar yAxisId="right" dataKey="avgDelay" fill="#82ca9d" name="Average Delay (hrs)" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

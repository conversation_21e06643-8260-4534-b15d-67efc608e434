"use client"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  Bot,
  FileText,
  MessageSquare,
  Image,
  Video,
  BarChart3,
  Camera,
  Wand2,
  TrendingUp,
  Users,
  ArrowRight,
  Sparkles
} from "lucide-react"

// AI workflow tasks with visual elements
const aiWorkflowTasks = [
  {
    id: 1,
    title: "Automatic Mode",
    description: "Create high-quality product photos and videos on autopilot",
    icon: Wand2,
    buttonText: "Generate",
    buttonVariant: "default" as const,
    status: "active",
    mediaType: "image"
  },
  {
    id: 2,
    title: "AI Photography",
    description: "Generate custom product photos that fit your exact business needs",
    icon: Camera,
    buttonText: "Generate",
    buttonVariant: "default" as const,
    status: "active",
    mediaType: "image"
  },
  {
    id: 3,
    title: "AI Videography",
    description: "Turn any product images into engaging cinematic videos using AI",
    icon: Video,
    buttonText: "Create",
    buttonVariant: "default" as const,
    status: "active",
    mediaType: "video"
  },
  {
    id: 4,
    title: "Upscale Images",
    description: "Make your images sharper and bigger with smart AI technology",
    icon: TrendingUp,
    buttonText: "Upscale",
    buttonVariant: "default" as const,
    status: "active",
    mediaType: "image"
  },
  {
    id: 5,
    title: "Train Models",
    description: "Teach AI to understand your products better for improved results",
    icon: Bot,
    buttonText: "Train Now",
    buttonVariant: "default" as const,
    status: "active",
    mediaType: "image"
  },
  {
    id: 6,
    title: "Create Ads",
    description: "Create eye-catching ads with smart AI help for better marketing",
    icon: Sparkles,
    buttonText: "Coming Soon",
    buttonVariant: "secondary" as const,
    status: "coming-soon",
    mediaType: "image"
  }
]

export function DashboardAIWorkflowsCard() {
  // const { onOpen } = useModal()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Bot className="h-6 w-6 text-primary" />
            AI Workflows
          </h2>
          <p className="text-muted-foreground mt-1">
            Powerful AI tools to automate your creative workflow
          </p>
        </div>
        <Button className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          Create Custom Workflow
        </Button>
      </div>

      {/* AI Workflow Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {aiWorkflowTasks.map((task) => {
          const IconComponent = task.icon
          return (
            <Card
              key={task.id}
              className="group relative overflow-hidden hover:shadow-lg transition-all duration-300 bg-card border border-border"
            >
              <CardContent className="p-6">
                <div className="flex flex-col sm:flex-row justify-between gap-4 pb-4 pt-0">
                  <div className="flex-1 min-w-0">
                    {/* Icon */}
                    <div className="mb-4">
                      <div className="inline-flex p-3 rounded-xl bg-muted text-primary">
                        <IconComponent className="h-6 w-6" aria-hidden="true" />
                      </div>
                    </div>

                    {/* Content */}
                    <div className="space-y-3 mb-6">
                      <h3 className="text-lg font-semibold text-card-foreground leading-tight line-clamp-2">
                        {task.title}
                      </h3>
                      <p className="text-sm text-muted-foreground leading-relaxed line-clamp-3">
                        {task.description}
                      </p>
                    </div>
                  </div>

                  {/* Media Preview Area */}
                  <div className="flex-1 min-w-0 ">
                    {/* <div className="aspect-video bg-muted/50 rounded-lg border-2 border-dashed border-border flex items-center justify-center overflow-hidden"> */}
                    <div className="h-[200px] overflow-hidden flex items-center justify-center rounded-lg border-2 border-dashed border-border">
                      {task.mediaType === 'video' ? (
                        <video
                          className="w-full h-[200px] object-cover rounded-lg"
                          src="https://pollo.ai/v/cmeo24hs007ev2lv24oi9hrqn?source=share"
                          controls
                          autoPlay
                          muted
                          loop
                          playsInline
                          aria-label="Task video preview"
                        />
                      ) : (
                        <div className="text-center p-4">
                          {task.imageUrl ? (
                            <img
                              src={task.imageUrl}
                              alt="Task preview"
                              className="max-w-full h-auto rounded-lg mx-auto"
                            />
                          ) : (
                            <>
                              <Image className="h-8 w-8 text-muted-foreground mx-auto mb-2" aria-hidden="true" />
                              <p className="text-xs text-muted-foreground">No image available</p>
                            </>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Action Button */}
                <Button
                  className="w-full group-hover:shadow-md"
                  variant={task.buttonVariant}
                  disabled={task.status === 'coming-soon'}
                >
                  {task.buttonText}
                  {task.status !== 'coming-soon' && (
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
                  )}
                </Button>

                {/* Status Badge */}
                {task.status === 'coming-soon' && (
                  <div className="absolute top-4 right-4">
                    <Badge variant="secondary">
                      Coming Soon
                    </Badge>
                  </div>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
        <Card className="p-4 text-center bg-card border border-border">
          <div className="text-2xl font-bold text-primary">
            {aiWorkflowTasks.filter(t => t.status === 'active').length}
          </div>
          <div className="text-sm text-muted-foreground">Active Tools</div>
        </Card>
        <Card className="p-4 text-center bg-card border border-border">
          <div className="text-2xl font-bold text-primary">
            {aiWorkflowTasks.filter(t => t.mediaType === 'image').length}
          </div>
          <div className="text-sm text-muted-foreground">Image Tools</div>
        </Card>
        <Card className="p-4 text-center bg-card border border-border">
          <div className="text-2xl font-bold text-primary">
            {aiWorkflowTasks.filter(t => t.mediaType === 'video').length}
          </div>
          <div className="text-sm text-muted-foreground">Video Tools</div>
        </Card>
        <Card className="p-4 text-center bg-card border border-border">
          <div className="text-2xl font-bold text-primary">
            {aiWorkflowTasks.length}
          </div>
          <div className="text-sm text-muted-foreground">Total Tools</div>
        </Card>
      </div>
    </div>
  )
}

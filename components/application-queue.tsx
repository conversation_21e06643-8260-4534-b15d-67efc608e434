"use client"

import { Label } from "@/components/ui/label"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Search, Filter, MoreHorizontal, Globe, Calendar, Send, CheckCircle2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"
import { ScrollArea } from "@/components/ui/scroll-area"

// Sample application queue data
const applications = [
  {
    id: "app-1",
    networkId: "network-1",
    networkName: "ShareASale",
    agentId: "agent-6",
    agentName: "Affiliate Hunter",
    status: "pending",
    createdAt: "2023-05-16T08:45:00Z",
    notes: "Automated application for health products affiliate program",
    avatar: "health",
    priority: "high",
  },
  {
    id: "app-2",
    networkId: "network-3",
    networkName: "CJ Affiliate",
    agentId: "agent-6",
    agentName: "Affiliate Hunter",
    status: "ready",
    createdAt: "2023-05-15T14:30:00Z",
    notes: "Application form prepared for travel niche products",
    avatar: "travel",
    priority: "medium",
  },
  {
    id: "app-3",
    networkId: "network-4",
    networkName: "ClickBank",
    agentId: "agent-6",
    agentName: "Affiliate Hunter",
    status: "in_progress",
    createdAt: "2023-05-14T11:15:00Z",
    notes: "Applying for digital product affiliate program",
    avatar: "entrepreneur",
    priority: "medium",
    submittedAt: "2023-05-14T12:30:00Z",
  },
  {
    id: "app-4",
    networkId: "network-2",
    networkName: "Amazon Associates",
    agentId: "agent-6",
    agentName: "Affiliate Hunter",
    status: "review_needed",
    createdAt: "2023-05-13T16:20:00Z",
    notes: "Application requires manual review of website compliance",
    avatar: "home",
    priority: "high",
  },
  {
    id: "app-5",
    networkId: "network-5",
    networkName: "Awin",
    agentId: "agent-6",
    agentName: "Affiliate Hunter",
    status: "ready",
    createdAt: "2023-05-12T09:30:00Z",
    notes: "Application form prepared for fitness products",
    avatar: "fitness",
    priority: "low",
  },
]

export function ApplicationQueue() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [avatarFilter, setAvatarFilter] = useState("all")
  const [priorityFilter, setPriorityFilter] = useState("all")
  const [isSubmitting, setIsSubmitting] = useState<string | null>(null)
  const [isReviewOpen, setIsReviewOpen] = useState(false)
  const [currentApp, setCurrentApp] = useState<string | null>(null)
  const [reviewNotes, setReviewNotes] = useState("")

  // Filter applications based on search and filters
  const filteredApplications = applications.filter((app) => {
    const matchesSearch =
      app.networkName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.notes.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || app.status === statusFilter
    const matchesAvatar = avatarFilter === "all" || app.avatar === avatarFilter
    const matchesPriority = priorityFilter === "all" || app.priority === priorityFilter

    return matchesSearch && matchesStatus && matchesAvatar && matchesPriority
  })

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
            Pending
          </Badge>
        )
      case "ready":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">Ready to Submit</Badge>
      case "in_progress":
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-800 hover:bg-purple-200">
            In Progress
          </Badge>
        )
      case "review_needed":
        return <Badge variant="destructive">Review Needed</Badge>
      case "approved":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Approved</Badge>
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Get priority badge
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-200">
            High
          </Badge>
        )
      case "medium":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
            Medium
          </Badge>
        )
      case "low":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-200">
            Low
          </Badge>
        )
      default:
        return <Badge variant="outline">Normal</Badge>
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  // Handle submitting an application
  const handleSubmitApplication = (appId: string) => {
    setIsSubmitting(appId)

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(null)

      toast({
        title: "Application Submitted",
        description: "The application has been submitted to the affiliate network.",
      })
    }, 2000)
  }

  // Handle completing a review
  const handleCompleteReview = () => {
    if (!reviewNotes) {
      toast({
        title: "Notes Required",
        description: "Please provide review notes before completing.",
        variant: "destructive",
      })
      return
    }

    // Simulate API call
    setTimeout(() => {
      setIsReviewOpen(false)
      setCurrentApp(null)
      setReviewNotes("")

      toast({
        title: "Review Completed",
        description: "Your review has been saved and the application is ready to submit.",
      })
    }, 1000)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Application Queue</CardTitle>
            <CardDescription>Manage and monitor affiliate network applications</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search applications..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex flex-1 items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="ready">Ready to Submit</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="review_needed">Review Needed</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
              <Select value={avatarFilter} onValueChange={setAvatarFilter}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Filter by avatar" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Avatars</SelectItem>
                  <SelectItem value="travel">Travel</SelectItem>
                  <SelectItem value="fitness">Fitness</SelectItem>
                  <SelectItem value="health">Health</SelectItem>
                  <SelectItem value="home">Home</SelectItem>
                  <SelectItem value="entrepreneur">Entrepreneur</SelectItem>
                </SelectContent>
              </Select>
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Filter by priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <ScrollArea className="h-[500px]">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Network</TableHead>
                    <TableHead>Avatar</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Notes</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredApplications.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        No applications found matching your filters.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredApplications.map((app) => (
                      <TableRow key={app.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Globe className="h-4 w-4 text-muted-foreground" />
                            <span>{app.networkName}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="capitalize">
                            {app.avatar}
                          </Badge>
                        </TableCell>
                        <TableCell>{getStatusBadge(app.status)}</TableCell>
                        <TableCell>{getPriorityBadge(app.priority)}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3 text-muted-foreground" />
                            <span>{formatDate(app.createdAt)}</span>
                          </div>
                        </TableCell>
                        <TableCell className="max-w-[200px] truncate" title={app.notes}>
                          {app.notes}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => (window.location.href = `/affiliate-networks/applications/${app.id}`)}
                              >
                                View Details
                              </DropdownMenuItem>
                              {app.status === "ready" && (
                                <DropdownMenuItem onClick={() => handleSubmitApplication(app.id)}>
                                  <Send className="mr-2 h-4 w-4" />
                                  Submit Application
                                </DropdownMenuItem>
                              )}
                              {app.status === "review_needed" && (
                                <DropdownMenuItem
                                  onClick={() => {
                                    setCurrentApp(app.id)
                                    setIsReviewOpen(true)
                                  }}
                                >
                                  <CheckCircle2 className="mr-2 h-4 w-4" />
                                  Complete Review
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() =>
                                  (window.location.href = `/affiliate-networks/applications/${app.id}/edit`)
                                }
                              >
                                Edit Application
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </ScrollArea>
          </div>
        </div>

        {/* Review Dialog */}
        <Dialog open={isReviewOpen} onOpenChange={setIsReviewOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Complete Application Review</DialogTitle>
              <DialogDescription>
                Review the application and provide any necessary notes before submission
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="review-notes">Review Notes</Label>
                <Textarea
                  id="review-notes"
                  placeholder="Enter your review notes and any changes made..."
                  className="min-h-[150px]"
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsReviewOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCompleteReview}>Complete Review</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}

"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Card<PERSON>ooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import {
  Bot,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Send,
  AlertTriangle,
  CheckCircle2,
  Lightbulb,
  Loader2,
} from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"

// Sample feedback data
const feedbackItems = [
  {
    id: "feedback-1",
    agentId: "agent-1",
    agentName: "Content Scout",
    type: "correction",
    content: "The trending topics for travel should focus more on sustainable tourism rather than luxury destinations.",
    timestamp: "2023-05-16T08:45:00Z",
    status: "pending",
    avatar: "travel",
  },
  {
    id: "feedback-2",
    agentId: "agent-2",
    agentName: "Engagement Bot",
    type: "improvement",
    content: "Responses to negative comments should be more empathetic and offer solutions.",
    timestamp: "2023-05-15T14:30:00Z",
    status: "implemented",
    avatar: "fitness",
  },
  {
    id: "feedback-3",
    agentId: "agent-4",
    agentName: "Content Repurposer",
    type: "correction",
    content: "The repurposed content for Instagram is too text-heavy. Focus more on visual elements.",
    timestamp: "2023-05-14T11:15:00Z",
    status: "rejected",
    reason: "Current format performs better in A/B testing",
    avatar: "entrepreneur",
  },
  {
    id: "feedback-4",
    agentId: "agent-3",
    agentName: "Analytics Miner",
    type: "suggestion",
    content: "Include month-over-month growth percentages in the weekly analytics reports.",
    timestamp: "2023-05-13T16:20:00Z",
    status: "implemented",
    avatar: "all",
  },
  {
    id: "feedback-5",
    agentId: "agent-5",
    agentName: "Hashtag Optimizer",
    type: "correction",
    content: "Some hashtags being used are banned or restricted on Instagram. Implement a verification system.",
    timestamp: "2023-05-12T09:30:00Z",
    status: "in_progress",
    avatar: "home",
  },
  {
    id: "feedback-6",
    agentId: "agent-7",
    agentName: "Scheduler Bot",
    type: "suggestion",
    content: "Consider time zone differences when scheduling posts for international audiences.",
    timestamp: "2023-05-11T13:45:00Z",
    status: "pending",
    avatar: "all",
  },
  {
    id: "feedback-7",
    agentId: "agent-6",
    agentName: "Affiliate Hunter",
    type: "improvement",
    content: "Focus on finding affiliate programs with at least 5% commission rates.",
    timestamp: "2023-05-10T10:20:00Z",
    status: "implemented",
    avatar: "health",
  },
]

export function AgentFeedback() {
  const [activeTab, setActiveTab] = useState("provide")
  const [agentFilter, setAgentFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")
  const [selectedAgent, setSelectedAgent] = useState("")
  const [feedbackType, setFeedbackType] = useState("improvement")
  const [feedbackContent, setFeedbackContent] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isImplementing, setIsImplementing] = useState<string | null>(null)
  const [isRejecting, setIsRejecting] = useState<string | null>(null)
  const [rejectionReason, setRejectionReason] = useState("")
  const [showRejectionDialog, setShowRejectionDialog] = useState(false)

  // Filter feedback items
  const filteredFeedback = feedbackItems.filter((item) => {
    const matchesAgent = agentFilter === "all" || item.agentId === agentFilter
    const matchesStatus = statusFilter === "all" || item.status === statusFilter
    const matchesType = typeFilter === "all" || item.type === typeFilter

    return matchesAgent && matchesStatus && matchesType
  })

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
            Pending
          </Badge>
        )
      case "implemented":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Implemented</Badge>
      case "in_progress":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-200">
            In Progress
          </Badge>
        )
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Handle feedback submission
  const handleSubmitFeedback = () => {
    if (!selectedAgent || !feedbackContent) {
      toast({
        title: "Missing Information",
        description: "Please select an agent and enter your feedback.",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false)
      setSelectedAgent("")
      setFeedbackType("improvement")
      setFeedbackContent("")

      toast({
        title: "Feedback Submitted",
        description: "Your feedback has been submitted for processing.",
      })
    }, 1500)
  }

  // Handle implementing feedback
  const handleImplementFeedback = (feedbackId: string) => {
    setIsImplementing(feedbackId)

    // Simulate API call
    setTimeout(() => {
      setIsImplementing(null)

      toast({
        title: "Feedback Implemented",
        description: "The feedback has been implemented and the agent has been updated.",
      })
    }, 2000)
  }

  // Handle rejecting feedback
  const handleRejectFeedback = (feedbackId: string) => {
    if (!rejectionReason) {
      toast({
        title: "Reason Required",
        description: "Please provide a reason for rejecting this feedback.",
        variant: "destructive",
      })
      return
    }

    setIsRejecting(feedbackId)

    // Simulate API call
    setTimeout(() => {
      setIsRejecting(null)
      setRejectionReason("")
      setShowRejectionDialog(false)

      toast({
        title: "Feedback Rejected",
        description: "The feedback has been rejected with the provided reason.",
      })
    }, 1500)
  }

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="provide">Provide Feedback</TabsTrigger>
          <TabsTrigger value="manage">Manage Feedback</TabsTrigger>
        </TabsList>

        <TabsContent value="provide" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Provide Agent Feedback</CardTitle>
              <CardDescription>Submit feedback to improve agent performance and behavior</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="agent-select">Select Agent</Label>
                <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                  <SelectTrigger id="agent-select">
                    <SelectValue placeholder="Choose an agent" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="agent-1">Content Scout</SelectItem>
                    <SelectItem value="agent-2">Engagement Bot</SelectItem>
                    <SelectItem value="agent-3">Analytics Miner</SelectItem>
                    <SelectItem value="agent-4">Content Repurposer</SelectItem>
                    <SelectItem value="agent-5">Hashtag Optimizer</SelectItem>
                    <SelectItem value="agent-6">Affiliate Hunter</SelectItem>
                    <SelectItem value="agent-7">Scheduler Bot</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="feedback-type">Feedback Type</Label>
                <Select value={feedbackType} onValueChange={setFeedbackType}>
                  <SelectTrigger id="feedback-type">
                    <SelectValue placeholder="Select feedback type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="improvement">Improvement Suggestion</SelectItem>
                    <SelectItem value="correction">Correction</SelectItem>
                    <SelectItem value="suggestion">New Feature Suggestion</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="feedback-content">Feedback Details</Label>
                <Textarea
                  id="feedback-content"
                  placeholder="Describe your feedback in detail..."
                  className="min-h-[150px]"
                  value={feedbackContent}
                  onChange={(e) => setFeedbackContent(e.target.value)}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="priority-feedback" />
                <Label htmlFor="priority-feedback">Mark as high priority</Label>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSubmitFeedback} disabled={isSubmitting} className="ml-auto">
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" />
                    Submit Feedback
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="manage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Manage Agent Feedback</CardTitle>
              <CardDescription>Review, implement, or reject feedback for your AI agents</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2">
                <Select value={agentFilter} onValueChange={setAgentFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by agent" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Agents</SelectItem>
                    <SelectItem value="agent-1">Content Scout</SelectItem>
                    <SelectItem value="agent-2">Engagement Bot</SelectItem>
                    <SelectItem value="agent-3">Analytics Miner</SelectItem>
                    <SelectItem value="agent-4">Content Repurposer</SelectItem>
                    <SelectItem value="agent-5">Hashtag Optimizer</SelectItem>
                    <SelectItem value="agent-6">Affiliate Hunter</SelectItem>
                    <SelectItem value="agent-7">Scheduler Bot</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="implemented">Implemented</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="improvement">Improvement</SelectItem>
                    <SelectItem value="correction">Correction</SelectItem>
                    <SelectItem value="suggestion">Suggestion</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <ScrollArea className="h-[500px] pr-4">
                <div className="space-y-4">
                  {filteredFeedback.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium">No Feedback Found</h3>
                      <p className="text-sm text-muted-foreground mt-1 max-w-md">
                        No feedback items match your current filters.
                      </p>
                    </div>
                  ) : (
                    filteredFeedback.map((item) => (
                      <Card key={item.id} className="overflow-hidden">
                        <div className="flex border-l-4 border-l-primary">
                          <div className="flex-1 p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Bot className="h-4 w-4 text-muted-foreground" />
                                <h3 className="font-medium">{item.agentName}</h3>
                                <Badge variant="outline" className="capitalize">
                                  {item.avatar}
                                </Badge>
                                {getStatusBadge(item.status)}
                              </div>
                              <div className="text-sm text-muted-foreground">{formatDate(item.timestamp)}</div>
                            </div>

                            <div className="mt-2 flex items-start gap-2">
                              {item.type === "improvement" ? (
                                <ThumbsUp className="h-4 w-4 text-blue-500 mt-1" />
                              ) : item.type === "correction" ? (
                                <AlertTriangle className="h-4 w-4 text-amber-500 mt-1" />
                              ) : (
                                <Lightbulb className="h-4 w-4 text-purple-500 mt-1" />
                              )}
                              <div className="flex-1">
                                <div className="text-sm">{item.content}</div>
                                {item.reason && (
                                  <div className="mt-2 text-sm bg-red-50 p-2 rounded-md text-red-800">
                                    <strong>Rejection reason:</strong> {item.reason}
                                  </div>
                                )}
                              </div>
                            </div>

                            {item.status === "pending" && (
                              <div className="mt-4 flex justify-end space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setShowRejectionDialog(true)
                                    setIsRejecting(item.id)
                                  }}
                                >
                                  <ThumbsDown className="mr-2 h-4 w-4" />
                                  Reject
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => handleImplementFeedback(item.id)}
                                  disabled={isImplementing === item.id}
                                >
                                  {isImplementing === item.id ? (
                                    <>
                                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                      Implementing...
                                    </>
                                  ) : (
                                    <>
                                      <CheckCircle2 className="mr-2 h-4 w-4" />
                                      Implement
                                    </>
                                  )}
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      </Card>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Rejection Dialog */}
          {showRejectionDialog && (
            <Card className="border-red-200">
              <CardHeader>
                <CardTitle>Reject Feedback</CardTitle>
                <CardDescription>Please provide a reason for rejecting this feedback</CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  placeholder="Enter rejection reason..."
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  className="min-h-[100px]"
                />
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowRejectionDialog(false)
                    setIsRejecting(null)
                    setRejectionReason("")
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => handleRejectFeedback(isRejecting || "")}
                  disabled={!rejectionReason || isRejecting === null}
                >
                  {isRejecting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Rejecting...
                    </>
                  ) : (
                    "Confirm Rejection"
                  )}
                </Button>
              </CardFooter>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

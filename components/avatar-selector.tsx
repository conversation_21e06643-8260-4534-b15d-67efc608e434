import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Facebook, Instagram, Linkedin, Twitter, Youtube } from "lucide-react"

export function AvatarSelector() {
  return (
    <div className="grid gap-6 lg:grid-cols-5">
      <Card>
        <CardHeader>
          <div className="flex justify-center">
            <Avatar className="h-24 w-24">
              <AvatarImage src="/placeholder-user.jpg" alt="Avatar" />
              <AvatarFallback>TR</AvatarFallback>
            </Avatar>
          </div>
          <CardTitle className="text-center mt-4">Travel Avatar</CardTitle>
          <CardDescription className="text-center">Adventure & Exploration Content</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center space-x-2">
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Instagram className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Youtube className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Twitter className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Facebook className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Linkedin className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
        <CardFooter>
          <Button className="w-full">Manage Content</Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex justify-center">
            <Avatar className="h-24 w-24">
              <AvatarImage src="/placeholder-user.jpg" alt="Avatar" />
              <AvatarFallback>HM</AvatarFallback>
            </Avatar>
          </div>
          <CardTitle className="text-center mt-4">Home Avatar</CardTitle>
          <CardDescription className="text-center">Interior Design & Decor Content</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center space-x-2">
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Instagram className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Youtube className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Twitter className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Facebook className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Linkedin className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
        <CardFooter>
          <Button className="w-full">Manage Content</Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex justify-center">
            <Avatar className="h-24 w-24">
              <AvatarImage src="/placeholder-user.jpg" alt="Avatar" />
              <AvatarFallback>HE</AvatarFallback>
            </Avatar>
          </div>
          <CardTitle className="text-center mt-4">Health Avatar</CardTitle>
          <CardDescription className="text-center">Wellness & Nutrition Content</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center space-x-2">
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Instagram className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Youtube className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Twitter className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Facebook className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Linkedin className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
        <CardFooter>
          <Button className="w-full">Manage Content</Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex justify-center">
            <Avatar className="h-24 w-24">
              <AvatarImage src="/placeholder-user.jpg" alt="Avatar" />
              <AvatarFallback>FI</AvatarFallback>
            </Avatar>
          </div>
          <CardTitle className="text-center mt-4">Fitness Avatar</CardTitle>
          <CardDescription className="text-center">Workout & Training Content</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center space-x-2">
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Instagram className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Youtube className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Twitter className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Facebook className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Linkedin className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
        <CardFooter>
          <Button className="w-full">Manage Content</Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex justify-center">
            <Avatar className="h-24 w-24">
              <AvatarImage src="/placeholder-user.jpg" alt="Avatar" />
              <AvatarFallback>EN</AvatarFallback>
            </Avatar>
          </div>
          <CardTitle className="text-center mt-4">Entrepreneur Avatar</CardTitle>
          <CardDescription className="text-center">Business & Success Content</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center space-x-2">
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Instagram className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Youtube className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Twitter className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Facebook className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
              <Linkedin className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
        <CardFooter>
          <Button className="w-full">Manage Content</Button>
        </CardFooter>
      </Card>
    </div>
  )
}

"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Plus, Search, ArrowRight, CheckCircle, Clock, AlertTriangle } from "lucide-react"

type Product = {
  id: string
  name: string
  category: string
  stage: "Sample" | "Contract" | "Production" | "Shipping" | "Launch"
  supplier: string
  startDate: string
  targetLaunchDate: string
  notes: string
}

export function PrivateLabelTracker() {
  const [products, setProducts] = useState<Product[]>([
    {
      id: "1",
      name: "Premium Yoga Mat",
      category: "Fitness",
      stage: "Production",
      supplier: "EcoFriendly Products Co.",
      startDate: "2023-02-15",
      targetLaunchDate: "2023-06-01",
      notes: "Production started on schedule. First batch of 500 units.",
    },
    {
      id: "2",
      name: "Bamboo Cutting Board",
      category: "Kitchen",
      stage: "Sample",
      supplier: "WoodCraft Supplies",
      startDate: "2023-03-10",
      targetLaunchDate: "2023-07-15",
      notes: "Waiting for second round of samples with logo engraving.",
    },
    {
      id: "3",
      name: "Organic Coffee Beans",
      category: "Food",
      stage: "Contract",
      supplier: "Organic Harvest",
      startDate: "2023-03-25",
      targetLaunchDate: "2023-06-30",
      notes: "Finalizing packaging design and negotiating minimum order quantity.",
    },
    {
      id: "4",
      name: "Silicone Baking Mats",
      category: "Kitchen",
      stage: "Shipping",
      supplier: "SiliTech Kitchen",
      startDate: "2023-01-20",
      targetLaunchDate: "2023-05-01",
      notes: "First shipment of 1000 units en route. Expected arrival in 2 weeks.",
    },
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [newProduct, setNewProduct] = useState<Omit<Product, "id">>({
    name: "",
    category: "",
    stage: "Sample",
    supplier: "",
    startDate: new Date().toISOString().split("T")[0],
    targetLaunchDate: "",
    notes: "",
  })
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const filteredProducts = products.filter(
    (product) => {
      const productName = product.name || ""
      const productCategory = product.category || ""
      const productSupplier = product.supplier || ""
      return productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
             productCategory.toLowerCase().includes(searchTerm.toLowerCase()) ||
             productSupplier.toLowerCase().includes(searchTerm.toLowerCase())
    }
  )

  const handleAddProduct = () => {
    const id = (products.length + 1).toString()
    setProducts([...products, { ...newProduct, id }])
    setNewProduct({
      name: "",
      category: "",
      stage: "Sample",
      supplier: "",
      startDate: new Date().toISOString().split("T")[0],
      targetLaunchDate: "",
      notes: "",
    })
    setIsDialogOpen(false)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date)
  }

  const getStageColor = (stage: Product["stage"]) => {
    switch (stage) {
      case "Sample":
        return "bg-blue-100 text-blue-800"
      case "Contract":
        return "bg-purple-100 text-purple-800"
      case "Production":
        return "bg-amber-100 text-amber-800"
      case "Shipping":
        return "bg-indigo-100 text-indigo-800"
      case "Launch":
        return "bg-green-100 text-green-800"
    }
  }

  const getStageIcon = (stage: Product["stage"]) => {
    switch (stage) {
      case "Sample":
        return <Clock className="h-4 w-4" />
      case "Contract":
        return <AlertTriangle className="h-4 w-4" />
      case "Production":
        return <ArrowRight className="h-4 w-4" />
      case "Shipping":
        return <ArrowRight className="h-4 w-4" />
      case "Launch":
        return <CheckCircle className="h-4 w-4" />
    }
  }

  const advanceStage = (id: string) => {
    setProducts(
      products.map((product) => {
        if (product.id === id) {
          const stageOrder: Product["stage"][] = ["Sample", "Contract", "Production", "Shipping", "Launch"]
          const currentIndex = stageOrder.indexOf(product.stage)
          const nextStage = currentIndex < stageOrder.length - 1 ? stageOrder[currentIndex + 1] : product.stage

          return {
            ...product,
            stage: nextStage,
          }
        }
        return product
      }),
    )
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Private Label Tracker</CardTitle>
            <CardDescription>Monitor product development stages from sample to launch</CardDescription>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Product
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Private Label Product</DialogTitle>
                <DialogDescription>Track a new product through the development process</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Product Name</Label>
                  <Input
                    id="name"
                    value={newProduct.name}
                    onChange={(e) => setNewProduct({ ...newProduct, name: e.target.value })}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="category">Category</Label>
                    <Input
                      id="category"
                      value={newProduct.category}
                      onChange={(e) => setNewProduct({ ...newProduct, category: e.target.value })}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="supplier">Supplier</Label>
                    <Input
                      id="supplier"
                      value={newProduct.supplier}
                      onChange={(e) => setNewProduct({ ...newProduct, supplier: e.target.value })}
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="stage">Current Stage</Label>
                  <select
                    id="stage"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={newProduct.stage}
                    onChange={(e) =>
                      setNewProduct({
                        ...newProduct,
                        stage: e.target.value as "Sample" | "Contract" | "Production" | "Shipping" | "Launch",
                      })
                    }
                  >
                    <option value="Sample">Sample</option>
                    <option value="Contract">Contract</option>
                    <option value="Production">Production</option>
                    <option value="Shipping">Shipping</option>
                    <option value="Launch">Launch</option>
                  </select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="startDate">Start Date</Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={newProduct.startDate}
                      onChange={(e) => setNewProduct({ ...newProduct, startDate: e.target.value })}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="targetLaunchDate">Target Launch Date</Label>
                    <Input
                      id="targetLaunchDate"
                      type="date"
                      value={newProduct.targetLaunchDate}
                      onChange={(e) => setNewProduct({ ...newProduct, targetLaunchDate: e.target.value })}
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    rows={3}
                    value={newProduct.notes}
                    onChange={(e) => setNewProduct({ ...newProduct, notes: e.target.value })}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddProduct}>Add Product</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex items-center gap-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search products..."
            className="h-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Product</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Stage</TableHead>
                <TableHead>Supplier</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>Target Launch</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProducts.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    No products found.
                  </TableCell>
                </TableRow>
              ) : (
                filteredProducts.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">{product.name}</TableCell>
                    <TableCell>{product.category}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Badge variant="outline" className={getStageColor(product.stage)}>
                          {getStageIcon(product.stage)}
                          <span className="ml-1">{product.stage}</span>
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>{product.supplier}</TableCell>
                    <TableCell>{formatDate(product.startDate)}</TableCell>
                    <TableCell>{formatDate(product.targetLaunchDate)}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => advanceStage(product.id)}
                        disabled={product.stage === "Launch"}
                      >
                        <ArrowRight className="mr-2 h-4 w-4" />
                        Advance Stage
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}

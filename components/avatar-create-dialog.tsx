'use client'

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"
import { categoryService, Category } from "@/lib/api/categories"

interface AvatarCreateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: any) => void
}

const PLATFORMS = [
  "instagram", "youtube", "tiktok", "twitter", "facebook", "linkedin", "pinterest", "twitch"
]

const CONTENT_TYPES = [
  "vlogs", "photos", "stories", "tutorials", "reviews", "tips", "workouts", "recipes", "insights", "case studies"
]

const TONES = [
  "professional", "casual", "enthusiastic", "warm", "motivational", "encouraging", "energetic", "inspiring"
]

const STYLES = [
  "casual", "professional", "adventurous", "cozy", "informative", "energetic", "inspiring", "educational"
]

export function AvatarCreateDialog({ open, onOpenChange, onSubmit }: AvatarCreateDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    category_id: "",
    description: "",
    personality_traits: {
      tone: "",
      style: "",
      expertise: [] as string[]
    },
    content_preferences: {
      primaryPlatforms: [] as string[],
      contentTypes: [] as string[],
      hashtagStyle: "",
      postingTimes: [] as string[]
    },
    is_active: true
  })
  const [categories, setCategories] = useState<Category[]>([])
  const [loadingCategories, setLoadingCategories] = useState(false)

  useEffect(() => {
    if (open) {
      loadCategories()
    }
  }, [open])

  const loadCategories = async () => {
    try {
      setLoadingCategories(true)
      const publicCategories = await categoryService.getPublicCategories(1, 100)
      setCategories(publicCategories)
    } catch (error) {
      console.error('Failed to load categories:', error)
      setCategories([])
    } finally {
      setLoadingCategories(false)
    }
  }

  const [newExpertise, setNewExpertise] = useState("")
  const [newPostingTime, setNewPostingTime] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
    // Reset form
    setFormData({
      name: "",
      category_id: "",
      description: "",
      personality_traits: {
        tone: "",
        style: "",
        expertise: []
      },
      content_preferences: {
        primaryPlatforms: [],
        contentTypes: [],
        hashtagStyle: "",
        postingTimes: []
      },
      is_active: true
    })
    setNewExpertise("")
    setNewPostingTime("")
  }

  const addExpertise = () => {
    if (newExpertise.trim() && !formData.personality_traits.expertise.includes(newExpertise.trim())) {
      setFormData(prev => ({
        ...prev,
        personality_traits: {
          ...prev.personality_traits,
          expertise: [...prev.personality_traits.expertise, newExpertise.trim()]
        }
      }))
      setNewExpertise("")
    }
  }

  const removeExpertise = (expertise: string) => {
    setFormData(prev => ({
      ...prev,
      personality_traits: {
        ...prev.personality_traits,
        expertise: prev.personality_traits.expertise.filter(e => e !== expertise)
      }
    }))
  }

  const addPostingTime = () => {
    if (newPostingTime && !formData.content_preferences.postingTimes.includes(newPostingTime)) {
      setFormData(prev => ({
        ...prev,
        content_preferences: {
          ...prev.content_preferences,
          postingTimes: [...prev.content_preferences.postingTimes, newPostingTime]
        }
      }))
      setNewPostingTime("")
    }
  }

  const removePostingTime = (time: string) => {
    setFormData(prev => ({
      ...prev,
      content_preferences: {
        ...prev.content_preferences,
        postingTimes: prev.content_preferences.postingTimes.filter(t => t !== time)
      }
    }))
  }

  const togglePlatform = (platform: string) => {
    setFormData(prev => ({
      ...prev,
      content_preferences: {
        ...prev.content_preferences,
        primaryPlatforms: prev.content_preferences.primaryPlatforms.includes(platform)
          ? prev.content_preferences.primaryPlatforms.filter(p => p !== platform)
          : [...prev.content_preferences.primaryPlatforms, platform]
      }
    }))
  }

  const toggleContentType = (contentType: string) => {
    setFormData(prev => ({
      ...prev,
      content_preferences: {
        ...prev.content_preferences,
        contentTypes: prev.content_preferences.contentTypes.includes(contentType)
          ? prev.content_preferences.contentTypes.filter(c => c !== contentType)
          : [...prev.content_preferences.contentTypes, contentType]
      }
    }))
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-[900px] max-h-[95vh] rounded-2xl overflow-auto border-0 shadow-2xl bg-gradient-to-br from-background via-background to-muted/20 sm:mx-auto">
        <DialogHeader>
          <DialogTitle>Create New Avatar</DialogTitle>
          <DialogDescription>
            Create a new content avatar with specific personality traits and content preferences.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Avatar Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Travel Avatar"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={formData.category_id}
                onValueChange={(value) => setFormData(prev => ({ ...prev, category_id: value }))}
                required
                disabled={loadingCategories}
              >
                <SelectTrigger>
                  <SelectValue placeholder={loadingCategories ? "Loading categories..." : "Select category"} />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: category.color }}
                        />
                        <span>{category.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe the avatar's purpose and content focus..."
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="tone">Tone</Label>
              <Select
                value={formData.personality_traits.tone}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  personality_traits: { ...prev.personality_traits, tone: value }
                }))}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select tone" />
                </SelectTrigger>
                <SelectContent>
                  {TONES.map((tone) => (
                    <SelectItem key={tone} value={tone}>
                      {tone.charAt(0).toUpperCase() + tone.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="style">Style</Label>
              <Select
                value={formData.personality_traits.style}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  personality_traits: { ...prev.personality_traits, style: value }
                }))}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select style" />
                </SelectTrigger>
                <SelectContent>
                  {STYLES.map((style) => (
                    <SelectItem key={style} value={style}>
                      {style.charAt(0).toUpperCase() + style.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Expertise Areas</Label>
            <div className="flex space-x-2">
              <Input
                value={newExpertise}
                onChange={(e) => setNewExpertise(e.target.value)}
                placeholder="Add expertise area"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addExpertise())}
              />
              <Button type="button" onClick={addExpertise}>Add</Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.personality_traits.expertise.map((expertise) => (
                <Badge key={expertise} variant="secondary" className="flex items-center gap-1">
                  {expertise}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => removeExpertise(expertise)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Primary Platforms</Label>
            <div className="grid grid-cols-4 gap-2">
              {PLATFORMS.map((platform) => (
                <div key={platform} className="flex items-center space-x-2">
                  <Checkbox
                    id={platform}
                    checked={formData.content_preferences.primaryPlatforms.includes(platform)}
                    onCheckedChange={() => togglePlatform(platform)}
                  />
                  <Label htmlFor={platform} className="text-sm">
                    {platform.charAt(0).toUpperCase() + platform.slice(1)}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Content Types</Label>
            <div className="grid grid-cols-3 gap-2">
              {CONTENT_TYPES.map((contentType) => (
                <div key={contentType} className="flex items-center space-x-2">
                  <Checkbox
                    id={contentType}
                    checked={formData.content_preferences.contentTypes.includes(contentType)}
                    onCheckedChange={() => toggleContentType(contentType)}
                  />
                  <Label htmlFor={contentType} className="text-sm">
                    {contentType.charAt(0).toUpperCase() + contentType.slice(1)}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="hashtagStyle">Hashtag Style</Label>
            <Input
              id="hashtagStyle"
              value={formData.content_preferences.hashtagStyle}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                content_preferences: { ...prev.content_preferences, hashtagStyle: e.target.value }
              }))}
              placeholder="e.g., travel_focused"
            />
          </div>

          <div className="space-y-2">
            <Label>Posting Times</Label>
            <div className="flex space-x-2">
              <Input
                type="time"
                value={newPostingTime}
                onChange={(e) => setNewPostingTime(e.target.value)}
              />
              <Button type="button" onClick={addPostingTime}>Add</Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.content_preferences.postingTimes.map((time) => (
                <Badge key={time} variant="secondary" className="flex items-center gap-1">
                  {time}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => removePostingTime(time)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: !!checked }))}
            />
            <Label htmlFor="is_active">Active</Label>
          </div>

          <DialogFooter>
            <Button className="mb-2 sm:mb-0" type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button className="mb-2 sm:mb-0" type="submit">Create Avatar</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

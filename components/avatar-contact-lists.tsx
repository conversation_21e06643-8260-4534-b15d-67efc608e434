"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Search, MoreHorizontal, Tag, Mail, Star, StarOff, UserPlus } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"

interface AvatarContactListsProps {
  selectedAvatar: string
}

export function AvatarContactLists({ selectedAvatar }: AvatarContactListsProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedContacts, setSelectedContacts] = useState<string[]>([])
  const [isAddContactOpen, setIsAddContactOpen] = useState(false)
  const [newContact, setNewContact] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    avatar: "travel",
    source: "website",
    tags: "",
  })

  // Filter contacts based on search and selected avatar
  const filteredContacts = contacts.filter((contact) => {
    const matchesSearch =
      contact.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesAvatar = selectedAvatar === "all" || contact.avatar === selectedAvatar

    return matchesSearch && matchesAvatar
  })

  // Handle selecting all contacts
  const handleSelectAll = () => {
    if (selectedContacts.length === filteredContacts.length) {
      setSelectedContacts([])
    } else {
      setSelectedContacts(filteredContacts.map((contact) => contact.id))
    }
  }

  // Handle selecting a single contact
  const handleSelectContact = (contactId: string) => {
    if (selectedContacts.includes(contactId)) {
      setSelectedContacts(selectedContacts.filter((id) => id !== contactId))
    } else {
      setSelectedContacts([...selectedContacts, contactId])
    }
  }

  // Handle adding a new contact
  const handleAddContact = () => {
    if (!newContact.firstName || !newContact.lastName || !newContact.email) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    // In a real app, this would add the contact to the database
    toast({
      title: "Contact Added",
      description: "The new contact has been added successfully.",
    })

    setIsAddContactOpen(false)
    setNewContact({
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      avatar: "travel",
      source: "website",
      tags: "",
    })
  }

  // Get avatar badge color
  const getAvatarBadgeColor = (avatar: string) => {
    switch (avatar) {
      case "travel":
        return "bg-blue-100 text-blue-800"
      case "home":
        return "bg-green-100 text-green-800"
      case "health":
        return "bg-purple-100 text-purple-800"
      case "fitness":
        return "bg-red-100 text-red-800"
      case "entrepreneur":
        return "bg-amber-100 text-amber-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    }).format(date)
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Avatar Contact Lists</CardTitle>
            <CardDescription>Track subscribers, followers, and customers by persona</CardDescription>
          </div>
          <Button onClick={() => setIsAddContactOpen(true)}>
            <UserPlus className="mr-2 h-4 w-4" />
            Add Contact
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search contacts..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button variant="outline" size="sm">
              <Tag className="mr-2 h-4 w-4" />
              Add Tag
            </Button>
            <Button variant="outline" size="sm">
              <Mail className="mr-2 h-4 w-4" />
              Email Selected
            </Button>
          </div>

          <div className="rounded-md border">
            <ScrollArea className="h-[450px]">
              <Table>
                <TableHeader className="bg-sage-50">
                  <TableRow>
                    <TableHead className="w-[40px]">
                      <Checkbox
                        checked={filteredContacts.length > 0 && selectedContacts.length === filteredContacts.length}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all contacts"
                      />
                    </TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Avatar</TableHead>
                    <TableHead>Source</TableHead>
                    <TableHead>Tags</TableHead>
                    <TableHead>Added</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredContacts.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="h-24 text-center">
                        No contacts found matching your search.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredContacts.map((contact) => (
                      <TableRow key={contact.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedContacts.includes(contact.id)}
                            onCheckedChange={() => handleSelectContact(contact.id)}
                            aria-label={`Select ${contact.firstName} ${contact.lastName}`}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage
                                src={contact.avatarUrl || "/placeholder.svg"}
                                alt={`${contact.firstName} ${contact.lastName}`}
                              />
                              <AvatarFallback>
                                {contact.firstName.charAt(0)}
                                {contact.lastName.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">
                                {contact.firstName} {contact.lastName}
                              </div>
                              <div className="text-xs text-muted-foreground">{contact.phone}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{contact.email}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className={`capitalize ${getAvatarBadgeColor(contact.avatar)}`}>
                            {contact.avatar}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="capitalize">
                            {contact.source}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {contact.tags.map((tag, index) => (
                              <Badge key={index} variant="outline" className="bg-sage-50 text-sage-700">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>{formatDate(contact.addedDate)}</TableCell>
                        <TableCell>
                          <Badge
                            className={
                              contact.status === "active"
                                ? "bg-green-100 text-green-800"
                                : contact.status === "inactive"
                                  ? "bg-gray-100 text-gray-800"
                                  : "bg-amber-100 text-amber-800"
                            }
                          >
                            {contact.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>View Profile</DropdownMenuItem>
                              <DropdownMenuItem>Edit Contact</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Send Email</DropdownMenuItem>
                              <DropdownMenuItem>Add to Sequence</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {contact.isVIP ? (
                                <DropdownMenuItem>
                                  <StarOff className="mr-2 h-4 w-4" />
                                  Remove VIP Status
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem>
                                  <Star className="mr-2 h-4 w-4" />
                                  Mark as VIP
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-red-600">Delete Contact</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </ScrollArea>
          </div>
        </div>

        {/* Add Contact Dialog */}
        <Dialog open={isAddContactOpen} onOpenChange={setIsAddContactOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Add New Contact</DialogTitle>
              <DialogDescription>Add a new contact to your CRM database.</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name *</Label>
                  <Input
                    id="firstName"
                    placeholder="John"
                    value={newContact.firstName}
                    onChange={(e) => setNewContact({ ...newContact, firstName: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name *</Label>
                  <Input
                    id="lastName"
                    placeholder="Doe"
                    value={newContact.lastName}
                    onChange={(e) => setNewContact({ ...newContact, lastName: e.target.value })}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={newContact.email}
                  onChange={(e) => setNewContact({ ...newContact, email: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  placeholder="(*************"
                  value={newContact.phone}
                  onChange={(e) => setNewContact({ ...newContact, phone: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="avatar">Avatar</Label>
                  <Select
                    value={newContact.avatar}
                    onValueChange={(value) => setNewContact({ ...newContact, avatar: value })}
                  >
                    <SelectTrigger id="avatar">
                      <SelectValue placeholder="Select avatar" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="travel">Travel</SelectItem>
                      <SelectItem value="home">Home</SelectItem>
                      <SelectItem value="health">Health</SelectItem>
                      <SelectItem value="fitness">Fitness</SelectItem>
                      <SelectItem value="entrepreneur">Entrepreneur</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="source">Source</Label>
                  <Select
                    value={newContact.source}
                    onValueChange={(value) => setNewContact({ ...newContact, source: value })}
                  >
                    <SelectTrigger id="source">
                      <SelectValue placeholder="Select source" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="website">Website</SelectItem>
                      <SelectItem value="instagram">Instagram</SelectItem>
                      <SelectItem value="youtube">YouTube</SelectItem>
                      <SelectItem value="tiktok">TikTok</SelectItem>
                      <SelectItem value="facebook">Facebook</SelectItem>
                      <SelectItem value="linkedin">LinkedIn</SelectItem>
                      <SelectItem value="referral">Referral</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="tags">Tags (comma separated)</Label>
                <Input
                  id="tags"
                  placeholder="subscriber, interested, webinar"
                  value={newContact.tags}
                  onChange={(e) => setNewContact({ ...newContact, tags: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddContactOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddContact}>Add Contact</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}

// Sample contact data
const contacts = [
  {
    id: "contact-1",
    firstName: "Emma",
    lastName: "Wilson",
    email: "<EMAIL>",
    phone: "(*************",
    avatar: "travel",
    avatarUrl: "/placeholder-user.jpg",
    source: "instagram",
    tags: ["subscriber", "lead magnet", "europe"],
    addedDate: "2023-04-15T10:30:00Z",
    status: "active",
    isVIP: true,
  },
  {
    id: "contact-2",
    firstName: "Michael",
    lastName: "Johnson",
    email: "<EMAIL>",
    phone: "(*************",
    avatar: "fitness",
    avatarUrl: "/placeholder-user.jpg",
    source: "youtube",
    tags: ["customer", "premium", "workout plan"],
    addedDate: "2023-03-22T14:45:00Z",
    status: "active",
    isVIP: false,
  },
  {
    id: "contact-3",
    firstName: "Sophia",
    lastName: "Martinez",
    email: "<EMAIL>",
    phone: "(*************",
    avatar: "health",
    avatarUrl: "/placeholder-user.jpg",
    source: "website",
    tags: ["subscriber", "nutrition guide"],
    addedDate: "2023-05-10T09:15:00Z",
    status: "active",
    isVIP: false,
  },
  {
    id: "contact-4",
    firstName: "Daniel",
    lastName: "Taylor",
    email: "<EMAIL>",
    phone: "(*************",
    avatar: "home",
    avatarUrl: "/placeholder-user.jpg",
    source: "facebook",
    tags: ["customer", "interior design"],
    addedDate: "2023-02-18T11:20:00Z",
    status: "inactive",
    isVIP: false,
  },
  {
    id: "contact-5",
    firstName: "Olivia",
    lastName: "Brown",
    email: "<EMAIL>",
    phone: "(*************",
    avatar: "entrepreneur",
    avatarUrl: "/placeholder-user.jpg",
    source: "linkedin",
    tags: ["subscriber", "business course", "startup"],
    addedDate: "2023-04-05T16:30:00Z",
    status: "active",
    isVIP: true,
  },
  {
    id: "contact-6",
    firstName: "James",
    lastName: "Anderson",
    email: "<EMAIL>",
    phone: "(*************",
    avatar: "travel",
    avatarUrl: "/placeholder-user.jpg",
    source: "tiktok",
    tags: ["subscriber", "asia travel"],
    addedDate: "2023-05-01T13:45:00Z",
    status: "pending",
    isVIP: false,
  },
  {
    id: "contact-7",
    firstName: "Isabella",
    lastName: "Garcia",
    email: "<EMAIL>",
    phone: "(*************",
    avatar: "fitness",
    avatarUrl: "/placeholder-user.jpg",
    source: "instagram",
    tags: ["customer", "yoga program"],
    addedDate: "2023-03-12T10:00:00Z",
    status: "active",
    isVIP: false,
  },
  {
    id: "contact-8",
    firstName: "Ethan",
    lastName: "Miller",
    email: "<EMAIL>",
    phone: "(*************",
    avatar: "health",
    avatarUrl: "/placeholder-user.jpg",
    source: "referral",
    tags: ["subscriber", "wellness retreat"],
    addedDate: "2023-04-20T15:15:00Z",
    status: "active",
    isVIP: false,
  },
  {
    id: "contact-9",
    firstName: "Ava",
    lastName: "Davis",
    email: "<EMAIL>",
    phone: "(*************",
    avatar: "home",
    avatarUrl: "/placeholder-user.jpg",
    source: "youtube",
    tags: ["customer", "premium", "home renovation"],
    addedDate: "2023-02-28T09:30:00Z",
    status: "active",
    isVIP: true,
  },
  {
    id: "contact-10",
    firstName: "Noah",
    lastName: "Rodriguez",
    email: "<EMAIL>",
    phone: "(*************",
    avatar: "entrepreneur",
    avatarUrl: "/placeholder-user.jpg",
    source: "linkedin",
    tags: ["subscriber", "business strategy"],
    addedDate: "2023-05-05T14:00:00Z",
    status: "pending",
    isVIP: false,
  },
]

"use client"
import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Brain,
  TrendingUp,
  Zap,
  Clock,
  AlertTriangle,
  CheckCircle2,
  BarChart3,
  Settings,
  Lightbulb,
  Gauge,
  RefreshCw,
  DollarSign,
  Link2,
  ChevronRight,
  Star,
  Activity,
  FlaskConical,
  Play,
  Target,
} from "lucide-react"

interface OptimizationSuggestion {
  id: string
  title: string
  description: string
  category: "performance" | "cost" | "quality" | "automation" | "integration"
  priority: "high" | "medium" | "low"
  impact: "high" | "medium" | "low"
  effort: "low" | "medium" | "high"
  estimatedSavings: {
    time: number // hours per month
    cost: number // dollars per month
    efficiency: number // percentage improvement
  }
  currentMetrics: {
    processingTime: number
    errorRate: number
    throughput: number
    resourceUsage: number
  }
  projectedMetrics: {
    processingTime: number
    errorRate: number
    throughput: number
    resourceUsage: number
  }
  implementation: {
    steps: string[]
    timeline: string
    requirements: string[]
    risks: string[]
  }
  affectedNodes: string[]
  confidence: number // 0-100
  aiReasoning: string
  status: "new" | "in-progress" | "completed" | "dismissed" | "testing"
  abTestId?: string
}

interface FlowMetrics {
  totalProcessingTime: number
  averageLatency: number
  throughputPerHour: number
  errorRate: number
  resourceUtilization: number
  costPerProcess: number
  automationCoverage: number
  userSatisfactionScore: number
}

interface BottleneckAnalysis {
  nodeId: string
  nodeName: string
  bottleneckSeverity: "critical" | "high" | "medium" | "low"
  avgProcessingTime: number
  queueLength: number
  resourceConstraints: string[]
  suggestedOptimizations: string[]
}

export function FlowOptimizationSuggestions() {
  const [suggestions, setSuggestions] = useState<OptimizationSuggestion[]>([])
  const [flowMetrics, setFlowMetrics] = useState<FlowMetrics | null>(null)
  const [bottlenecks, setBottlenecks] = useState<BottleneckAnalysis[]>([])
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisProgress, setAnalysisProgress] = useState(0)
  const [selectedSuggestion, setSelectedSuggestion] = useState<OptimizationSuggestion | null>(null)
  const [activeTab, setActiveTab] = useState("overview")
  const [isCreateTestDialogOpen, setIsCreateTestDialogOpen] = useState(false)

  // Simulate AI analysis
  useEffect(() => {
    const runAnalysis = async () => {
      setIsAnalyzing(true)
      setAnalysisProgress(0)

      // Simulate analysis progress
      for (let i = 0; i <= 100; i += 10) {
        await new Promise((resolve) => setTimeout(resolve, 200))
        setAnalysisProgress(i)
      }

      // Generate mock data
      const mockMetrics: FlowMetrics = {
        totalProcessingTime: 145.6,
        averageLatency: 2.3,
        throughputPerHour: 450,
        errorRate: 3.2,
        resourceUtilization: 78,
        costPerProcess: 0.12,
        automationCoverage: 85,
        userSatisfactionScore: 4.2,
      }

      const mockBottlenecks: BottleneckAnalysis[] = [
        {
          nodeId: "content-generation",
          nodeName: "Content Generation",
          bottleneckSeverity: "high",
          avgProcessingTime: 45.2,
          queueLength: 12,
          resourceConstraints: ["GPU Memory", "API Rate Limits"],
          suggestedOptimizations: ["Implement batch processing", "Add GPU scaling", "Optimize prompt templates"],
        },
        {
          nodeId: "market-research",
          nodeName: "Market Research",
          bottleneckSeverity: "medium",
          avgProcessingTime: 28.7,
          queueLength: 6,
          resourceConstraints: ["External API Limits", "Data Processing"],
          suggestedOptimizations: ["Cache frequent queries", "Parallel API calls", "Implement data preprocessing"],
        },
        {
          nodeId: "email-sequences",
          nodeName: "Email Sequences",
          bottleneckSeverity: "low",
          avgProcessingTime: 12.1,
          queueLength: 2,
          resourceConstraints: ["Template Processing"],
          suggestedOptimizations: ["Pre-compile templates", "Async processing"],
        },
      ]

      const mockSuggestions: OptimizationSuggestion[] = [
        {
          id: "opt-001",
          title: "Implement Parallel Content Generation",
          description: "Process multiple content pieces simultaneously to reduce overall generation time by 40%",
          category: "performance",
          priority: "high",
          impact: "high",
          effort: "medium",
          estimatedSavings: {
            time: 18.5,
            cost: 450,
            efficiency: 40,
          },
          currentMetrics: {
            processingTime: 45.2,
            errorRate: 2.1,
            throughput: 80,
            resourceUsage: 85,
          },
          projectedMetrics: {
            processingTime: 27.1,
            errorRate: 1.8,
            throughput: 133,
            resourceUsage: 78,
          },
          implementation: {
            steps: [
              "Analyze current content generation pipeline",
              "Implement queue-based parallel processing",
              "Add load balancing for AI model requests",
              "Test and optimize batch sizes",
              "Deploy with gradual rollout",
            ],
            timeline: "2-3 weeks",
            requirements: ["Additional GPU resources", "Queue management system", "Load balancer configuration"],
            risks: ["Increased resource usage", "Potential quality variations", "API rate limit issues"],
          },
          affectedNodes: ["content-generation", "content-personalization", "social-distribution"],
          confidence: 92,
          aiReasoning:
            "Analysis shows content generation is the primary bottleneck, consuming 31% of total processing time. Parallel processing can significantly reduce wait times while maintaining quality through proper batch management.",
          status: "testing",
          abTestId: "test-001",
        },
        {
          id: "opt-002",
          title: "Smart Caching for Market Research",
          description: "Implement intelligent caching to reduce redundant API calls and improve response times by 60%",
          category: "performance",
          priority: "high",
          impact: "medium",
          effort: "low",
          estimatedSavings: {
            time: 12.3,
            cost: 280,
            efficiency: 35,
          },
          currentMetrics: {
            processingTime: 28.7,
            errorRate: 1.5,
            throughput: 120,
            resourceUsage: 65,
          },
          projectedMetrics: {
            processingTime: 11.5,
            errorRate: 1.2,
            throughput: 192,
            resourceUsage: 45,
          },
          implementation: {
            steps: [
              "Analyze market research query patterns",
              "Design cache key strategy",
              "Implement Redis-based caching layer",
              "Add cache invalidation logic",
              "Monitor cache hit rates",
            ],
            timeline: "1-2 weeks",
            requirements: ["Redis cache server", "Cache management logic", "Monitoring dashboard"],
            risks: ["Stale data issues", "Cache memory usage", "Complex invalidation scenarios"],
          },
          affectedNodes: ["market-research", "competitor-analysis", "opportunity-identification"],
          confidence: 88,
          aiReasoning:
            "Market research shows 67% query overlap within 24-hour periods. Smart caching with TTL-based invalidation can dramatically reduce external API calls while maintaining data freshness.",
          status: "completed",
          abTestId: "test-002",
        },
        {
          id: "opt-003",
          title: "Automated Quality Scoring",
          description:
            "Implement AI-powered quality scoring to automatically filter and improve content before distribution",
          category: "quality",
          priority: "medium",
          impact: "high",
          effort: "high",
          estimatedSavings: {
            time: 8.7,
            cost: 320,
            efficiency: 25,
          },
          currentMetrics: {
            processingTime: 15.2,
            errorRate: 4.8,
            throughput: 200,
            resourceUsage: 55,
          },
          projectedMetrics: {
            processingTime: 18.1,
            errorRate: 1.2,
            resourceUsage: 62,
            throughput: 185,
          },
          implementation: {
            steps: [
              "Train quality scoring model on historical data",
              "Implement scoring pipeline",
              "Add automatic content filtering",
              "Create quality improvement suggestions",
              "Integrate with content generation workflow",
            ],
            timeline: "4-6 weeks",
            requirements: ["ML model training", "Quality dataset", "Scoring infrastructure"],
            risks: ["Model accuracy issues", "False positive filtering", "Training data bias"],
          },
          affectedNodes: ["content-generation", "content-personalization", "email-sequences"],
          confidence: 78,
          aiReasoning:
            "Current error rate of 4.8% in content quality leads to manual review overhead. AI quality scoring can reduce errors by 75% while maintaining content velocity.",
          status: "testing",
          abTestId: "test-003",
        },
        {
          id: "opt-004",
          title: "Dynamic Resource Scaling",
          description: "Implement auto-scaling based on queue length and processing demand to optimize costs",
          category: "cost",
          priority: "medium",
          impact: "medium",
          effort: "medium",
          estimatedSavings: {
            time: 5.2,
            cost: 680,
            efficiency: 20,
          },
          currentMetrics: {
            processingTime: 22.1,
            errorRate: 2.3,
            throughput: 150,
            resourceUsage: 78,
          },
          projectedMetrics: {
            processingTime: 19.8,
            errorRate: 2.1,
            throughput: 165,
            resourceUsage: 65,
          },
          implementation: {
            steps: [
              "Implement queue monitoring",
              "Create scaling policies",
              "Add resource provisioning automation",
              "Test scaling scenarios",
              "Deploy with monitoring",
            ],
            timeline: "3-4 weeks",
            requirements: ["Auto-scaling infrastructure", "Monitoring system", "Resource orchestration"],
            risks: ["Scaling delays", "Resource over-provisioning", "Cost spikes during scaling"],
          },
          affectedNodes: ["all-processing-nodes"],
          confidence: 85,
          aiReasoning:
            "Resource utilization varies from 45% to 95% throughout the day. Dynamic scaling can reduce costs by 35% while maintaining performance during peak loads.",
          status: "new",
        },
        {
          id: "opt-005",
          title: "Predictive Workflow Routing",
          description: "Use ML to predict optimal workflow paths based on content type and user behavior",
          category: "automation",
          priority: "low",
          impact: "medium",
          effort: "high",
          estimatedSavings: {
            time: 6.8,
            cost: 190,
            efficiency: 15,
          },
          currentMetrics: {
            processingTime: 35.6,
            errorRate: 3.1,
            throughput: 100,
            resourceUsage: 70,
          },
          projectedMetrics: {
            processingTime: 30.2,
            errorRate: 2.6,
            throughput: 115,
            resourceUsage: 68,
          },
          implementation: {
            steps: [
              "Collect workflow performance data",
              "Train routing prediction model",
              "Implement dynamic routing logic",
              "A/B test routing strategies",
              "Deploy predictive routing",
            ],
            timeline: "6-8 weeks",
            requirements: ["ML pipeline", "Historical data analysis", "Routing infrastructure"],
            risks: ["Model prediction accuracy", "Complex routing logic", "Debugging difficulties"],
          },
          affectedNodes: ["workflow-routing", "decision-points"],
          confidence: 72,
          aiReasoning:
            "Analysis of workflow patterns shows 23% of processes could benefit from alternative routing. Predictive routing can optimize path selection based on content characteristics.",
          status: "new",
        },
      ]

      setFlowMetrics(mockMetrics)
      setBottlenecks(mockBottlenecks)
      setSuggestions(mockSuggestions)
      setIsAnalyzing(false)
    }

    runAnalysis()
  }, [])

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800 border-red-200"
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "low":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case "high":
        return "text-green-600"
      case "medium":
        return "text-yellow-600"
      case "low":
        return "text-gray-600"
      default:
        return "text-gray-600"
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "performance":
        return <Gauge className="h-4 w-4" />
      case "cost":
        return <DollarSign className="h-4 w-4" />
      case "quality":
        return <Star className="h-4 w-4" />
      case "automation":
        return <Zap className="h-4 w-4" />
      case "integration":
        return <Link2 className="h-4 w-4" />
      default:
        return <Settings className="h-4 w-4" />
    }
  }

  const getBottleneckSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical":
        return "bg-red-500"
      case "high":
        return "bg-orange-500"
      case "medium":
        return "bg-yellow-500"
      case "low":
        return "bg-green-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "in-progress":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "completed":
        return "bg-green-100 text-green-800 border-green-200"
      case "dismissed":
        return "bg-gray-100 text-gray-800 border-gray-200"
      case "testing":
        return "bg-purple-100 text-purple-800 border-purple-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const implementSuggestion = (suggestionId: string) => {
    setSuggestions((prev) => prev.map((s) => (s.id === suggestionId ? { ...s, status: "in-progress" } : s)))
  }

  const dismissSuggestion = (suggestionId: string) => {
    setSuggestions((prev) => prev.map((s) => (s.id === suggestionId ? { ...s, status: "dismissed" } : s)))
  }

  const createABTest = (suggestion: OptimizationSuggestion) => {
    // This would integrate with the A/B testing framework
    const testId = `test-${Date.now()}`
    setSuggestions((prev) =>
      prev.map((s) => (s.id === suggestion.id ? { ...s, status: "testing", abTestId: testId } : s)),
    )
    setIsCreateTestDialogOpen(false)
    // Here you would actually create the A/B test in the testing framework
  }

  if (isAnalyzing) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 animate-pulse" />
            AI Flow Analysis in Progress
          </CardTitle>
          <CardDescription>Analyzing your automation flows to identify optimization opportunities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Progress value={analysisProgress} className="w-full" />
            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                {analysisProgress < 30 && "Collecting flow performance data..."}
                {analysisProgress >= 30 && analysisProgress < 60 && "Analyzing bottlenecks and inefficiencies..."}
                {analysisProgress >= 60 && analysisProgress < 90 && "Generating optimization suggestions..."}
                {analysisProgress >= 90 && "Finalizing recommendations..."}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                AI Flow Optimization Suggestions
              </CardTitle>
              <CardDescription>AI-powered recommendations to improve your automation flow efficiency</CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Re-analyze
            </Button>
          </div>
        </CardHeader>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="suggestions">Suggestions</TabsTrigger>
          <TabsTrigger value="bottlenecks">Bottlenecks</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {flowMetrics && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Processing Time</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{flowMetrics.totalProcessingTime}s</div>
                  <p className="text-xs text-muted-foreground">Avg latency: {flowMetrics.averageLatency}s</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Throughput</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{flowMetrics.throughputPerHour}/hr</div>
                  <p className="text-xs text-muted-foreground">Processes per hour</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
                  <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{flowMetrics.errorRate}%</div>
                  <p className="text-xs text-muted-foreground">Current error rate</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Automation Coverage</CardTitle>
                  <Zap className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{flowMetrics.automationCoverage}%</div>
                  <p className="text-xs text-muted-foreground">Processes automated</p>
                </CardContent>
              </Card>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Top Optimization Opportunities</CardTitle>
                <CardDescription>Highest impact suggestions for your workflow</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {suggestions
                    .filter((s) => s.status === "new")
                    .sort((a, b) => b.confidence - a.confidence)
                    .slice(0, 3)
                    .map((suggestion) => (
                      <div key={suggestion.id} className="flex items-start gap-3 p-3 border rounded-lg">
                        <div className="mt-1">{getCategoryIcon(suggestion.category)}</div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-sm">{suggestion.title}</h4>
                            <Badge variant="outline" className={getPriorityColor(suggestion.priority)}>
                              {suggestion.priority}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground mb-2">{suggestion.description}</p>
                          <div className="flex items-center gap-4 text-xs">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>{suggestion.estimatedSavings.time}h/mo saved</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-3 w-3" />
                              <span>${suggestion.estimatedSavings.cost}/mo</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-3 w-3" />
                              <span>{suggestion.confidence}% confidence</span>
                            </div>
                          </div>
                        </div>
                        <Button size="sm" variant="outline" onClick={() => setSelectedSuggestion(suggestion)}>
                          View
                        </Button>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">A/B Testing Status</CardTitle>
                <CardDescription>Current optimization tests and results</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {suggestions
                    .filter((s) => s.status === "testing" || s.status === "completed")
                    .map((suggestion) => (
                      <div key={suggestion.id} className="flex items-start gap-3 p-3 border rounded-lg">
                        <div className="mt-1">
                          <FlaskConical className="h-4 w-4 text-purple-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-sm">{suggestion.title}</h4>
                            <Badge variant="outline" className={getStatusColor(suggestion.status)}>
                              {suggestion.status}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground mb-2">
                            {suggestion.status === "testing" ? "Currently running A/B test" : "Test completed"}
                          </p>
                          <div className="flex items-center gap-4 text-xs">
                            <div className="flex items-center gap-1">
                              <Target className="h-3 w-3" />
                              <span>Test ID: {suggestion.abTestId}</span>
                            </div>
                            {suggestion.status === "completed" && (
                              <div className="flex items-center gap-1">
                                <CheckCircle2 className="h-3 w-3 text-green-600" />
                                <span>Validated</span>
                              </div>
                            )}
                          </div>
                        </div>
                        <Button size="sm" variant="outline" onClick={() => setSelectedSuggestion(suggestion)}>
                          View Test
                        </Button>
                      </div>
                    ))}
                  {suggestions.filter((s) => s.status === "testing" || s.status === "completed").length === 0 && (
                    <div className="text-center py-8">
                      <FlaskConical className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">No active A/B tests</p>
                      <p className="text-xs text-muted-foreground">Create tests to validate optimizations</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="suggestions" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Optimization Suggestions</h3>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{suggestions.filter((s) => s.status === "new").length} New</Badge>
                  <Badge variant="outline">{suggestions.filter((s) => s.status === "testing").length} Testing</Badge>
                  <Badge variant="outline">
                    {suggestions.filter((s) => s.status === "in-progress").length} In Progress
                  </Badge>
                </div>
              </div>

              <ScrollArea className="h-[600px]">
                <div className="space-y-3">
                  {suggestions.map((suggestion) => (
                    <Card
                      key={suggestion.id}
                      className={`cursor-pointer transition-colors ${
                        selectedSuggestion?.id === suggestion.id ? "border-blue-500 bg-blue-50" : ""
                      } ${suggestion.status === "dismissed" ? "opacity-50" : ""}`}
                      onClick={() => setSelectedSuggestion(suggestion)}
                    >
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-2">
                              {getCategoryIcon(suggestion.category)}
                              <h4 className="font-medium">{suggestion.title}</h4>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className={getPriorityColor(suggestion.priority)}>
                                {suggestion.priority}
                              </Badge>
                              <Badge variant="outline" className={getStatusColor(suggestion.status)}>
                                {suggestion.status}
                              </Badge>
                            </div>
                          </div>

                          <p className="text-sm text-muted-foreground">{suggestion.description}</p>

                          <div className="grid grid-cols-3 gap-4 text-xs">
                            <div>
                              <p className="text-muted-foreground">Time Savings</p>
                              <p className="font-medium">{suggestion.estimatedSavings.time}h/mo</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Cost Savings</p>
                              <p className="font-medium">${suggestion.estimatedSavings.cost}/mo</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Efficiency</p>
                              <p className="font-medium">+{suggestion.estimatedSavings.efficiency}%</p>
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Brain className="h-3 w-3" />
                              <span>{suggestion.confidence}% confidence</span>
                            </div>
                            {suggestion.abTestId && (
                              <div className="flex items-center gap-1 text-xs text-purple-600">
                                <FlaskConical className="h-3 w-3" />
                                <span>Test: {suggestion.abTestId}</span>
                              </div>
                            )}
                            <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </div>

            <div className="space-y-4">
              {selectedSuggestion ? (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {getCategoryIcon(selectedSuggestion.category)}
                        <CardTitle className="text-lg">{selectedSuggestion.title}</CardTitle>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={getPriorityColor(selectedSuggestion.priority)}>
                          {selectedSuggestion.priority} priority
                        </Badge>
                        <Badge variant="outline" className={getStatusColor(selectedSuggestion.status)}>
                          {selectedSuggestion.status}
                        </Badge>
                      </div>
                    </div>
                    <CardDescription>{selectedSuggestion.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Impact Assessment</h4>
                        <div className="space-y-1 text-xs">
                          <div className="flex justify-between">
                            <span>Impact Level:</span>
                            <span className={`font-medium ${getImpactColor(selectedSuggestion.impact)}`}>
                              {selectedSuggestion.impact}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Implementation Effort:</span>
                            <span className="font-medium">{selectedSuggestion.effort}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Timeline:</span>
                            <span className="font-medium">{selectedSuggestion.implementation.timeline}</span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Projected Savings</h4>
                        <div className="space-y-1 text-xs">
                          <div className="flex justify-between">
                            <span>Time Saved:</span>
                            <span className="font-medium text-green-600">
                              {selectedSuggestion.estimatedSavings.time}h/month
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Cost Saved:</span>
                            <span className="font-medium text-green-600">
                              ${selectedSuggestion.estimatedSavings.cost}/month
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Efficiency Gain:</span>
                            <span className="font-medium text-green-600">
                              +{selectedSuggestion.estimatedSavings.efficiency}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div className="space-y-3">
                      <h4 className="font-medium text-sm">AI Analysis</h4>
                      <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <div className="flex items-start gap-2">
                          <Brain className="h-4 w-4 text-blue-600 mt-0.5" />
                          <p className="text-sm text-blue-800">{selectedSuggestion.aiReasoning}</p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-medium text-sm">Performance Comparison</h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <h5 className="text-xs font-medium text-muted-foreground">Current</h5>
                          <div className="space-y-1 text-xs">
                            <div className="flex justify-between">
                              <span>Processing Time:</span>
                              <span>{selectedSuggestion.currentMetrics.processingTime}s</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Error Rate:</span>
                              <span>{selectedSuggestion.currentMetrics.errorRate}%</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Throughput:</span>
                              <span>{selectedSuggestion.currentMetrics.throughput}/hr</span>
                            </div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <h5 className="text-xs font-medium text-muted-foreground">Projected</h5>
                          <div className="space-y-1 text-xs">
                            <div className="flex justify-between">
                              <span>Processing Time:</span>
                              <span className="text-green-600 font-medium">
                                {selectedSuggestion.projectedMetrics.processingTime}s
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Error Rate:</span>
                              <span className="text-green-600 font-medium">
                                {selectedSuggestion.projectedMetrics.errorRate}%
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Throughput:</span>
                              <span className="text-green-600 font-medium">
                                {selectedSuggestion.projectedMetrics.throughput}/hr
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-medium text-sm">Implementation Steps</h4>
                      <div className="space-y-2">
                        {selectedSuggestion.implementation.steps.map((step, index) => (
                          <div key={index} className="flex items-start gap-2 text-sm">
                            <div className="w-5 h-5 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-medium mt-0.5">
                              {index + 1}
                            </div>
                            <span>{step}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-medium text-sm">Requirements & Risks</h4>
                      <div className="grid grid-cols-1 gap-4">
                        <div>
                          <h5 className="text-xs font-medium text-muted-foreground mb-2">Requirements</h5>
                          <ul className="space-y-1">
                            {selectedSuggestion.implementation.requirements.map((req, index) => (
                              <li key={index} className="text-xs flex items-center gap-2">
                                <CheckCircle2 className="h-3 w-3 text-green-600" />
                                {req}
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h5 className="text-xs font-medium text-muted-foreground mb-2">Risks</h5>
                          <ul className="space-y-1">
                            {selectedSuggestion.implementation.risks.map((risk, index) => (
                              <li key={index} className="text-xs flex items-center gap-2">
                                <AlertTriangle className="h-3 w-3 text-yellow-600" />
                                {risk}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>

                    {selectedSuggestion.abTestId && (
                      <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
                        <div className="flex items-center gap-2 mb-2">
                          <FlaskConical className="h-4 w-4 text-purple-600" />
                          <h5 className="font-medium text-sm text-purple-900">A/B Test Status</h5>
                        </div>
                        <p className="text-sm text-purple-800">
                          This suggestion is currently being validated through A/B test: {selectedSuggestion.abTestId}
                        </p>
                        {selectedSuggestion.status === "completed" && (
                          <p className="text-sm text-green-800 mt-1">
                            ✓ Test completed successfully - ready for implementation
                          </p>
                        )}
                      </div>
                    )}

                    {selectedSuggestion.status === "new" && (
                      <div className="flex gap-2 pt-4">
                        <Dialog open={isCreateTestDialogOpen} onOpenChange={setIsCreateTestDialogOpen}>
                          <DialogTrigger asChild>
                            <Button className="flex-1">
                              <FlaskConical className="h-4 w-4 mr-2" />
                              Create A/B Test
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Create A/B Test</DialogTitle>
                              <DialogDescription>
                                Validate this optimization suggestion with a controlled A/B test
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                                <h4 className="font-medium text-sm mb-2">Test Configuration</h4>
                                <div className="space-y-2 text-sm">
                                  <div className="flex justify-between">
                                    <span>Suggestion:</span>
                                    <span className="font-medium">{selectedSuggestion.title}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span>Expected Improvement:</span>
                                    <span className="font-medium text-green-600">
                                      +{selectedSuggestion.estimatedSavings.efficiency}%
                                    </span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span>Confidence Level:</span>
                                    <span className="font-medium">{selectedSuggestion.confidence}%</span>
                                  </div>
                                </div>
                              </div>
                              <div className="flex justify-end gap-2">
                                <Button variant="outline" onClick={() => setIsCreateTestDialogOpen(false)}>
                                  Cancel
                                </Button>
                                <Button onClick={() => createABTest(selectedSuggestion)}>
                                  <Play className="h-4 w-4 mr-2" />
                                  Start Test
                                </Button>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                        <Button variant="outline" onClick={() => implementSuggestion(selectedSuggestion.id)}>
                          <Zap className="h-4 w-4 mr-2" />
                          Implement Now
                        </Button>
                        <Button variant="outline" onClick={() => dismissSuggestion(selectedSuggestion.id)}>
                          Dismiss
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <Lightbulb className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">Select a Suggestion</h3>
                    <p className="text-muted-foreground text-center">
                      Choose a suggestion from the list to view detailed analysis and implementation guidance.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="bottlenecks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Bottlenecks Analysis</CardTitle>
              <CardDescription>
                Detailed analysis of workflow bottlenecks and optimization opportunities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {bottlenecks.map((bottleneck, index) => (
                  <Card key={index}>
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div
                            className={`w-4 h-4 rounded-full ${getBottleneckSeverityColor(bottleneck.bottleneckSeverity)}`}
                          />
                          <CardTitle className="text-base">{bottleneck.nodeName}</CardTitle>
                        </div>
                        <Badge
                          variant="outline"
                          className={
                            bottleneck.bottleneckSeverity === "critical"
                              ? "bg-red-100 text-red-800"
                              : bottleneck.bottleneckSeverity === "high"
                                ? "bg-orange-100 text-orange-800"
                                : bottleneck.bottleneckSeverity === "medium"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-green-100 text-green-800"
                          }
                        >
                          {bottleneck.bottleneckSeverity} severity
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="space-y-3">
                          <h4 className="font-medium text-sm">Performance Metrics</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Avg Processing Time:</span>
                              <span className="font-medium">{bottleneck.avgProcessingTime}s</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Queue Length:</span>
                              <span className="font-medium">{bottleneck.queueLength} items</span>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <h4 className="font-medium text-sm">Resource Constraints</h4>
                          <div className="space-y-1">
                            {bottleneck.resourceConstraints.map((constraint, idx) => (
                              <div key={idx} className="flex items-center gap-2 text-sm">
                                <AlertTriangle className="h-3 w-3 text-yellow-600" />
                                <span>{constraint}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div className="space-y-3">
                          <h4 className="font-medium text-sm">Suggested Optimizations</h4>
                          <div className="space-y-1">
                            {bottleneck.suggestedOptimizations.map((optimization, idx) => (
                              <div key={idx} className="flex items-center gap-2 text-sm">
                                <Lightbulb className="h-3 w-3 text-blue-600" />
                                <span>{optimization}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-4">
          {flowMetrics && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Processing Time</CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{flowMetrics.totalProcessingTime}s</div>
                    <p className="text-xs text-muted-foreground">End-to-end workflow time</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Average Latency</CardTitle>
                    <Gauge className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{flowMetrics.averageLatency}s</div>
                    <p className="text-xs text-muted-foreground">Per-step processing time</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Resource Utilization</CardTitle>
                    <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{flowMetrics.resourceUtilization}%</div>
                    <p className="text-xs text-muted-foreground">Current resource usage</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Cost Per Process</CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">${flowMetrics.costPerProcess}</div>
                    <p className="text-xs text-muted-foreground">Average processing cost</p>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Optimization Impact Summary</CardTitle>
                  <CardDescription>
                    Projected improvements from implementing all high-priority suggestions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="space-y-3">
                      <h4 className="font-medium text-sm">Time Savings</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Current Total Time:</span>
                          <span>{flowMetrics.totalProcessingTime}s</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Projected Time:</span>
                          <span className="text-green-600 font-medium">
                            {(flowMetrics.totalProcessingTime * 0.65).toFixed(1)}s
                          </span>
                        </div>
                        <div className="flex justify-between text-sm font-medium">
                          <span>Improvement:</span>
                          <span className="text-green-600">-35% faster</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-medium text-sm">Cost Reduction</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Current Monthly Cost:</span>
                          <span>
                            ${(flowMetrics.costPerProcess * flowMetrics.throughputPerHour * 24 * 30).toFixed(0)}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Projected Cost:</span>
                          <span className="text-green-600 font-medium">
                            ${(flowMetrics.costPerProcess * flowMetrics.throughputPerHour * 24 * 30 * 0.7).toFixed(0)}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm font-medium">
                          <span>Savings:</span>
                          <span className="text-green-600">
                            ${(flowMetrics.costPerProcess * flowMetrics.throughputPerHour * 24 * 30 * 0.3).toFixed(0)}
                            /mo
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-medium text-sm">Quality Improvement</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Current Error Rate:</span>
                          <span>{flowMetrics.errorRate}%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Projected Error Rate:</span>
                          <span className="text-green-600 font-medium">
                            {(flowMetrics.errorRate * 0.4).toFixed(1)}%
                          </span>
                        </div>
                        <div className="flex justify-between text-sm font-medium">
                          <span>Improvement:</span>
                          <span className="text-green-600">-60% errors</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

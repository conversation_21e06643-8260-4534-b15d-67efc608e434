"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Trash2, Package } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"

// Sample data for products
const mainProducts = [
  { id: 1, name: "Premium Yoga Mat", category: "Fitness", price: 79.99, image: "/placeholder.svg?height=80&width=80" },
  {
    id: 2,
    name: "Wireless Earbuds",
    category: "Electronics",
    price: 129.99,
    image: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 3,
    name: "Organic Coffee Beans",
    category: "Food & Drink",
    price: 24.99,
    image: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 4,
    name: "Smart Water Bottle",
    category: "Fitness",
    price: 45.99,
    image: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 5,
    name: "Meditation Cushion",
    category: "Wellness",
    price: 59.99,
    image: "/placeholder.svg?height=80&width=80",
  },
]

const allProducts = [
  ...mainProducts,
  { id: 6, name: "Yoga Block Set", category: "Fitness", price: 29.99, image: "/placeholder.svg?height=80&width=80" },
  { id: 7, name: "Resistance Bands", category: "Fitness", price: 19.99, image: "/placeholder.svg?height=80&width=80" },
  {
    id: 8,
    name: "Bluetooth Speaker",
    category: "Electronics",
    price: 89.99,
    image: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 9,
    name: "Fitness Tracker",
    category: "Electronics",
    price: 149.99,
    image: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 10,
    name: "Herbal Tea Set",
    category: "Food & Drink",
    price: 34.99,
    image: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 11,
    name: "Protein Powder",
    category: "Food & Drink",
    price: 39.99,
    image: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 12,
    name: "Insulated Water Bottle",
    category: "Fitness",
    price: 35.99,
    image: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 13,
    name: "Essential Oil Diffuser",
    category: "Wellness",
    price: 49.99,
    image: "/placeholder.svg?height=80&width=80",
  },
  {
    id: 14,
    name: "Mindfulness Journal",
    category: "Wellness",
    price: 19.99,
    image: "/placeholder.svg?height=80&width=80",
  },
]

// Sample complementary product sets
const initialComplementarySets = [
  {
    mainProductId: 1,
    complementaryProducts: [
      {
        id: 6,
        name: "Yoga Block Set",
        category: "Fitness",
        price: 29.99,
        image: "/placeholder.svg?height=80&width=80",
      },
      {
        id: 7,
        name: "Resistance Bands",
        category: "Fitness",
        price: 19.99,
        image: "/placeholder.svg?height=80&width=80",
      },
      {
        id: 14,
        name: "Mindfulness Journal",
        category: "Wellness",
        price: 19.99,
        image: "/placeholder.svg?height=80&width=80",
      },
    ],
    enabled: true,
  },
  {
    mainProductId: 2,
    complementaryProducts: [
      {
        id: 8,
        name: "Bluetooth Speaker",
        category: "Electronics",
        price: 89.99,
        image: "/placeholder.svg?height=80&width=80",
      },
      {
        id: 9,
        name: "Fitness Tracker",
        category: "Electronics",
        price: 149.99,
        image: "/placeholder.svg?height=80&width=80",
      },
      {
        id: 4,
        name: "Smart Water Bottle",
        category: "Fitness",
        price: 45.99,
        image: "/placeholder.svg?height=80&width=80",
      },
    ],
    enabled: true,
  },
  {
    mainProductId: 3,
    complementaryProducts: [
      {
        id: 10,
        name: "Herbal Tea Set",
        category: "Food & Drink",
        price: 34.99,
        image: "/placeholder.svg?height=80&width=80",
      },
      {
        id: 5,
        name: "Meditation Cushion",
        category: "Wellness",
        price: 59.99,
        image: "/placeholder.svg?height=80&width=80",
      },
      {
        id: 13,
        name: "Essential Oil Diffuser",
        category: "Wellness",
        price: 49.99,
        image: "/placeholder.svg?height=80&width=80",
      },
    ],
    enabled: false,
  },
]

export function ComplementaryProducts() {
  const [complementarySets, setComplementarySets] = useState(initialComplementarySets)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [currentMainProduct, setCurrentMainProduct] = useState<number | null>(null)
  const [selectedComplementaryProducts, setSelectedComplementaryProducts] = useState<number[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [currentTab, setCurrentTab] = useState("all")
  
  // Filter products that don't already have complementary sets
  const availableMainProducts = mainProducts.filter(
    product => !complementarySets.some(set => set.mainProductId === product.id)
  )
  
  // Get a main product by ID
  const getMainProductById = (id: number) => {
    return mainProducts.find(product => product.id === id)
  }
  
  // Filter products based on search term
  const filteredProducts = allProducts.filter(product => {
    const productName = product.name || ""
    const productCategory = product.category || ""
    return productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
           productCategory.toLowerCase().includes(searchTerm.toLowerCase())
  })
  
  // Handle adding a new complementary set
  const handleAddComplementarySet = () => {
    if (currentMainProduct && selectedComplementaryProducts.length > 0) {
      const newComplementaryProducts = selectedComplementaryProducts.map(id => 
        allProducts.find(product => product.id === id)!
      )
      
      setComplementarySets([
        ...complementarySets,
        {
          mainProductId: currentMainProduct,
          complementaryProducts: newComplementaryProducts,
          enabled: true
        }
      ])
      
      setIsAddDialogOpen(false)
      setCurrentMainProduct(null)
      setSelectedComplementaryProducts([])
    }
  }
  
  // Handle editing an existing complementary set
  const handleEditComplementarySet = () => {
    if (currentMainProduct && selectedComplementaryProducts.length > 0) {
      const newComplementaryProducts = selectedComplementaryProducts.map(id => 
        allProducts.find(product => product.id === id)!
      )
      
      setComplementarySets(
        complementarySets.map(set => 
          set.mainProductId === currentMainProduct
            ? { ...set, complementaryProducts: newComplementaryProducts }
            : set
        )
      )
      
      setIsEditDialogOpen(false)
    }
  }
  
  // Handle deleting a complementary set
  const handleDeleteComplementarySet = (mainProductId: number) => {
    setComplementarySets(
      complementarySets.filter(set => set.mainProductId !== mainProductId)
    )
  }
  
  // Handle toggling a complementary set
  const handleToggleComplementarySet = (mainProductId: number) => {
    setComplementarySets(
      complementarySets.map(set => 
        set.mainProductId === mainProductId
          ? { ...set, enabled: !set.enabled }
          : set
      )
    )
  }
  
  // Open edit dialog for a specific set
  const openEditDialog = (mainProductId: number) => {
    const set = complementarySets.find(set => set.mainProductId === mainProductId)
    if (set) {
      setCurrentMainProduct(mainProductId)
      setSelectedComplementaryProducts(set.complementaryProducts.map(p => p.id))
      setIsEditDialogOpen(true)
    }
  }
  
  // Toggle selection of a complementary product
  const toggleProductSelection = (productId: number) => {
    if (selectedComplementaryProducts.includes(productId)) {
      setSelectedComplementaryProducts(
        selectedComplementaryProducts.filter(id => id !== productId)
      )
    } else {
      if (selectedComplementaryProducts.length < 3) {
        setSelectedComplementaryProducts([...selectedComplementaryProducts, productId])
      }
    }
  }
  
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Complementary Products</CardTitle>
              <CardDescription>
                Manage sets of complementary products to include in your content links and QR codes
              </CardDescription>
            </div>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button disabled={availableMainProducts.length === 0}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Complementary Set
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Create Complementary Product Set</DialogTitle>
                  <DialogDescription>
                    Select a main product and up to 3 complementary products to suggest alongside it.
                  </DialogDescription>
                </DialogHeader>
                
                <div className="grid gap-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="mainProduct">Main Product</Label>
                    <Select 
                      onValueChange={(value) => setCurrentMainProduct(Number(value))}
                      value={currentMainProduct?.toString() || ""}
                    >
                      <SelectTrigger id="mainProduct">
                        <SelectValue placeholder="Select a product" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableMainProducts.map((product) => (
                          <SelectItem key={product.id} value={product.id.toString()}>
                            {product.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Complementary Products (Select up to 3)</Label>
                    <div className="flex items-center space-x-2 mb-4">
                      <Input
                        placeholder="Search products..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="flex-1"
                      />
                    </div>
                    
                    <Tabs defaultValue="all" onValueChange={setCurrentTab}>
                      <TabsList className="mb-2">
                        <TabsTrigger value="all">All</TabsTrigger>
                        <TabsTrigger value="fitness">Fitness</TabsTrigger>
                        <TabsTrigger value="electronics">Electronics</TabsTrigger>
                        <TabsTrigger value="food">Food & Drink</TabsTrigger>
                        <TabsTrigger value="wellness">Wellness</TabsTrigger>
                      </TabsList>
                      
                      <ScrollArea className="h-[300px] rounded-md border">
                        <div className="p-4">
                          {filteredProducts
                            .filter(product => 
                              currentTab === "all" || 
                              product.category.toLowerCase().includes(currentTab.toLowerCase())
                            )
                            .filter(product => 
                              !currentMainProduct || product.id !== currentMainProduct
                            )
                            .map((product) => (
                              <div 
                                key={product.id} 
                                className={`flex items-center space-x-3 p-2 rounded-md cursor-pointer ${
                                  selectedComplementaryProducts.includes(product.id) 
                                    ? 'bg-primary/10' 
                                    : 'hover:bg-muted'
                                }`}
                                onClick={() => toggleProductSelection(product.id)}
                              >
                                <div className="flex-shrink-0">
                                  <img 
                                    src={product.image || "/placeholder.svg"} 
                                    alt={product.name} 
                                    className="h-10 w-10 rounded-md object-cover"
                                  />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <p className="text-sm font-medium truncate">{product.name}</p>
                                  <p className="text-xs text-muted-foreground">{product.category}</p>
                                </div>
                                <div className="flex-shrink-0">
                                  <p className="text-sm font-medium">${product.price}</p>
                                </div>
                              </div>
                            ))}
                        </div>
                      </ScrollArea>
                    </Tabs>
                  </div>
                  
                  <div>
                    <Label>Selected Products ({selectedComplementaryProducts.length}/3)</Label>
                    <div className="mt-2 space-y-2">
                      {selectedComplementaryProducts.length === 0 ? (
                        <p className="text-sm text-muted-foreground">No products selected</p>
                      ) : (
                        selectedComplementaryProducts.map(id => {
                          const product = allProducts.find(p => p.id === id)!
                          return (
                            <div key={id} className="flex items-center justify-between p-2 bg-muted rounded-md">
                              <div className="flex items-center space-x-2">
                                <img 
                                  src={product.image || "/placeholder.svg"} 
                                  alt={product.name} 
                                  className="h-8 w-8 rounded-md object-cover"
                                />
                                <span className="text-sm">{product.name}</span>
                              </div>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                onClick={(e) => {
                                  e.stopPropagation()
                                  toggleProductSelection(id)
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          )
                        })
                      )}
                    </div>
                  </div>
                </div>
                
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleAddComplementarySet}
                    disabled={!currentMainProduct || selectedComplementaryProducts.length === 0}
                  >
                    Create Set
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {complementarySets.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Package className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">No Complementary Sets</h3>
              <p className="text-sm text-muted-foreground mt-1 max-w-md">
                Create complementary product sets to suggest related products alongside your main offerings in content links and QR codes.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Main Product</TableHead>
                  <TableHead>Complementary Products</TableHead>
                </TableRow>
              </TableHeader>\

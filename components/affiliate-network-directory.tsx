"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Search, Filter, ExternalLink, MoreHorizontal, DollarSign, Globe, CheckCircle2, PlusCircle } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"

// Sample affiliate network data
const networks = [
  {
    id: "network-1",
    name: "ShareASale",
    category: "General",
    commissionRange: "5-30%",
    paymentThreshold: 50,
    paymentFrequency: "Monthly",
    applicationProcess: "Easy",
    requirements: "Active website, content relevant to products",
    status: "active",
    website: "https://www.shareasale.com",
    merchants: 3900,
    cookieDuration: "30 days",
    notes: "Good for beginners, wide variety of merchants",
  },
  {
    id: "network-2",
    name: "Amazon Associates",
    category: "Retail",
    commissionRange: "1-10%",
    paymentThreshold: 10,
    paymentFrequency: "Monthly",
    applicationProcess: "Medium",
    requirements: "3+ sales in first 180 days, content policy compliance",
    status: "active",
    website: "https://affiliate-program.amazon.com",
    merchants: 1000000,
    cookieDuration: "24 hours",
    notes: "Largest selection of products, but lower commission rates",
  },
  {
    id: "network-3",
    name: "CJ Affiliate",
    category: "General",
    commissionRange: "5-45%",
    paymentThreshold: 50,
    paymentFrequency: "Monthly",
    applicationProcess: "Hard",
    requirements: "Established website, traffic stats, marketing plan",
    status: "active",
    website: "https://www.cj.com",
    merchants: 3000,
    cookieDuration: "30-60 days",
    notes: "Higher-end merchants, better for established sites",
  },
  {
    id: "network-4",
    name: "ClickBank",
    category: "Digital Products",
    commissionRange: "30-75%",
    paymentThreshold: 10,
    paymentFrequency: "Bi-weekly",
    applicationProcess: "Easy",
    requirements: "Valid tax info, active promotion plan",
    status: "active",
    website: "https://www.clickbank.com",
    merchants: 6000,
    cookieDuration: "60 days",
    notes: "High commission rates for digital products",
  },
  {
    id: "network-5",
    name: "Awin",
    category: "General",
    commissionRange: "5-50%",
    paymentThreshold: 20,
    paymentFrequency: "Monthly",
    applicationProcess: "Medium",
    requirements: "Active website, $5 activation fee",
    status: "active",
    website: "https://www.awin.com",
    merchants: 15000,
    cookieDuration: "30 days",
    notes: "Strong in Europe, good reporting tools",
  },
  {
    id: "network-6",
    name: "Impact",
    category: "General",
    commissionRange: "5-50%",
    paymentThreshold: 25,
    paymentFrequency: "Monthly",
    applicationProcess: "Hard",
    requirements: "Established website, traffic stats, marketing plan",
    status: "active",
    website: "https://impact.com",
    merchants: 2000,
    cookieDuration: "30-90 days",
    notes: "Premium brands, advanced tracking technology",
  },
  {
    id: "network-7",
    name: "Rakuten Advertising",
    category: "General",
    commissionRange: "5-40%",
    paymentThreshold: 50,
    paymentFrequency: "Monthly",
    applicationProcess: "Hard",
    requirements: "Established website, traffic stats, content quality",
    status: "active",
    website: "https://rakutenadvertising.com",
    merchants: 3500,
    cookieDuration: "30-60 days",
    notes: "Premium network with high-quality merchants",
  },
  {
    id: "network-8",
    name: "FlexOffers",
    category: "General",
    commissionRange: "5-50%",
    paymentThreshold: 25,
    paymentFrequency: "Net-60",
    applicationProcess: "Medium",
    requirements: "Active website, content quality",
    status: "active",
    website: "https://www.flexoffers.com",
    merchants: 12000,
    cookieDuration: "30 days",
    notes: "Good for beginners, responsive support",
  },
]

export function AffiliateNetworkDirectory() {
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [processFilter, setProcessFilter] = useState("all")
  const [isAddNetworkOpen, setIsAddNetworkOpen] = useState(false)
  const [newNetwork, setNewNetwork] = useState({
    name: "",
    category: "General",
    commissionRange: "",
    paymentThreshold: "",
    paymentFrequency: "Monthly",
    applicationProcess: "Medium",
    requirements: "",
    website: "",
    notes: "",
  })

  // Filter networks based on search and filters
  const filteredNetworks = networks.filter((network) => {
    const matchesSearch =
      network.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      network.notes.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === "all" || network.category === categoryFilter
    const matchesProcess = processFilter === "all" || network.applicationProcess === processFilter

    return matchesSearch && matchesCategory && matchesProcess
  })

  // Get application process badge
  const getProcessBadge = (process: string) => {
    switch (process) {
      case "Easy":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Easy</Badge>
      case "Medium":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
            Medium
          </Badge>
        )
      case "Hard":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-200">
            Hard
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Handle adding a new network
  const handleAddNetwork = () => {
    if (!newNetwork.name || !newNetwork.website || !newNetwork.commissionRange || !newNetwork.requirements) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    // In a real app, this would add the network to the database
    toast({
      title: "Network Added",
      description: "The affiliate network has been added to the directory.",
    })

    setIsAddNetworkOpen(false)
    setNewNetwork({
      name: "",
      category: "General",
      commissionRange: "",
      paymentThreshold: "",
      paymentFrequency: "Monthly",
      applicationProcess: "Medium",
      requirements: "",
      website: "",
      notes: "",
    })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Affiliate Network Directory</CardTitle>
            <CardDescription>Browse and manage affiliate networks for your content empire</CardDescription>
          </div>
          <Button onClick={() => setIsAddNetworkOpen(true)}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Network
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search networks..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex flex-1 items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Filter by category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="General">General</SelectItem>
                  <SelectItem value="Retail">Retail</SelectItem>
                  <SelectItem value="Digital Products">Digital Products</SelectItem>
                  <SelectItem value="Travel">Travel</SelectItem>
                  <SelectItem value="Finance">Finance</SelectItem>
                </SelectContent>
              </Select>
              <Select value={processFilter} onValueChange={setProcessFilter}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Filter by application process" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Processes</SelectItem>
                  <SelectItem value="Easy">Easy</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="Hard">Hard</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Network Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Commission</TableHead>
                  <TableHead>Payment</TableHead>
                  <TableHead>Application</TableHead>
                  <TableHead>Requirements</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredNetworks.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      No networks found matching your filters.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredNetworks.map((network) => (
                    <TableRow key={network.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <Globe className="h-4 w-4 text-muted-foreground" />
                          <span>{network.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{network.category}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <DollarSign className="h-3 w-3 text-green-600" />
                          <span>{network.commissionRange}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>${network.paymentThreshold} min</div>
                          <div className="text-xs text-muted-foreground">{network.paymentFrequency}</div>
                        </div>
                      </TableCell>
                      <TableCell>{getProcessBadge(network.applicationProcess)}</TableCell>
                      <TableCell className="max-w-[200px] truncate" title={network.requirements}>
                        {network.requirements}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => window.open(network.website, "_blank")}>
                              <ExternalLink className="mr-2 h-4 w-4" />
                              Visit Website
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => (window.location.href = `/affiliate-networks/apply?id=${network.id}`)}
                            >
                              <CheckCircle2 className="mr-2 h-4 w-4" />
                              Apply Now
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => (window.location.href = `/affiliate-networks/${network.id}`)}
                            >
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => (window.location.href = `/affiliate-networks/${network.id}/edit`)}
                            >
                              Edit Network
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Add Network Dialog */}
        <Dialog open={isAddNetworkOpen} onOpenChange={setIsAddNetworkOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Add Affiliate Network</DialogTitle>
              <DialogDescription>Add a new affiliate network to your directory</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="network-name" className="text-right">
                  Network Name
                </Label>
                <Input
                  id="network-name"
                  placeholder="e.g., ShareASale"
                  className="col-span-3"
                  value={newNetwork.name}
                  onChange={(e) => setNewNetwork({ ...newNetwork, name: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="network-website" className="text-right">
                  Website URL
                </Label>
                <Input
                  id="network-website"
                  placeholder="e.g., https://www.shareasale.com"
                  className="col-span-3"
                  value={newNetwork.website}
                  onChange={(e) => setNewNetwork({ ...newNetwork, website: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="network-category" className="text-right">
                  Category
                </Label>
                <Select
                  value={newNetwork.category}
                  onValueChange={(value) => setNewNetwork({ ...newNetwork, category: value })}
                >
                  <SelectTrigger id="network-category" className="col-span-3">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="General">General</SelectItem>
                    <SelectItem value="Retail">Retail</SelectItem>
                    <SelectItem value="Digital Products">Digital Products</SelectItem>
                    <SelectItem value="Travel">Travel</SelectItem>
                    <SelectItem value="Finance">Finance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="network-commission" className="text-right">
                  Commission Range
                </Label>
                <Input
                  id="network-commission"
                  placeholder="e.g., 5-30%"
                  className="col-span-3"
                  value={newNetwork.commissionRange}
                  onChange={(e) => setNewNetwork({ ...newNetwork, commissionRange: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="network-threshold" className="text-right">
                  Payment Threshold
                </Label>
                <Input
                  id="network-threshold"
                  placeholder="e.g., 50"
                  className="col-span-3"
                  value={newNetwork.paymentThreshold}
                  onChange={(e) => setNewNetwork({ ...newNetwork, paymentThreshold: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="network-frequency" className="text-right">
                  Payment Frequency
                </Label>
                <Select
                  value={newNetwork.paymentFrequency}
                  onValueChange={(value) => setNewNetwork({ ...newNetwork, paymentFrequency: value })}
                >
                  <SelectTrigger id="network-frequency" className="col-span-3">
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Weekly">Weekly</SelectItem>
                    <SelectItem value="Bi-weekly">Bi-weekly</SelectItem>
                    <SelectItem value="Monthly">Monthly</SelectItem>
                    <SelectItem value="Net-30">Net-30</SelectItem>
                    <SelectItem value="Net-60">Net-60</SelectItem>
                    <SelectItem value="Net-90">Net-90</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="network-process" className="text-right">
                  Application Process
                </Label>
                <Select
                  value={newNetwork.applicationProcess}
                  onValueChange={(value) => setNewNetwork({ ...newNetwork, applicationProcess: value })}
                >
                  <SelectTrigger id="network-process" className="col-span-3">
                    <SelectValue placeholder="Select difficulty" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Easy">Easy</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="Hard">Hard</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="network-requirements" className="text-right">
                  Requirements
                </Label>
                <Input
                  id="network-requirements"
                  placeholder="e.g., Active website, content relevant to products"
                  className="col-span-3"
                  value={newNetwork.requirements}
                  onChange={(e) => setNewNetwork({ ...newNetwork, requirements: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="network-notes" className="text-right">
                  Notes
                </Label>
                <Input
                  id="network-notes"
                  placeholder="e.g., Good for beginners, wide variety of merchants"
                  className="col-span-3"
                  value={newNetwork.notes}
                  onChange={(e) => setNewNetwork({ ...newNetwork, notes: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddNetworkOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddNetwork}>Add Network</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}

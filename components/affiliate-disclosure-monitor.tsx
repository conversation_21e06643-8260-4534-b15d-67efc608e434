"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Search,
  Filter,
  Download,
  Plus,
  AlertTriangle,
  FileText,
  ExternalLink,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Textarea } from "@/components/ui/textarea"

// Sample content data with affiliate links
const contentWithAffiliateLinks = [
  {
    id: "content-1",
    title: "10 Best Travel Gadgets for 2023",
    url: "https://travel-avatar.com/best-travel-gadgets-2023",
    platform: "blog",
    avatar: "travel",
    publishDate: "2023-05-10T14:30:00Z",
    affiliateLinks: 8,
    disclosureStatus: "compliant",
    disclosureType: "top",
    lastChecked: "2023-05-16T09:15:00Z",
  },
  {
    id: "content-2",
    title: "Ultimate Home Workout Equipment Guide",
    url: "https://fitness-blog.com/home-workout-equipment",
    platform: "blog",
    avatar: "fitness",
    publishDate: "2023-05-05T10:15:00Z",
    affiliateLinks: 12,
    disclosureStatus: "non-compliant",
    disclosureType: "none",
    lastChecked: "2023-05-16T09:20:00Z",
  },
  {
    id: "content-3",
    title: "Natural Supplements for Better Sleep",
    url: "https://health-tips.com/natural-sleep-supplements",
    platform: "blog",
    avatar: "health",
    publishDate: "2023-05-12T11:45:00Z",
    affiliateLinks: 5,
    disclosureStatus: "warning",
    disclosureType: "bottom",
    lastChecked: "2023-05-16T09:25:00Z",
  },
  {
    id: "content-4",
    title: "Best Productivity Tools for Entrepreneurs",
    url: "https://entrepreneur-podcast.com/productivity-tools",
    platform: "blog",
    avatar: "entrepreneur",
    publishDate: "2023-05-08T16:20:00Z",
    affiliateLinks: 10,
    disclosureStatus: "compliant",
    disclosureType: "top",
    lastChecked: "2023-05-16T09:30:00Z",
  },
  {
    id: "content-5",
    title: "Budget-Friendly Home Decor Ideas",
    url: "https://home-decor-tips.com/budget-friendly-decor",
    platform: "blog",
    avatar: "home",
    publishDate: "2023-05-14T10:30:00Z",
    affiliateLinks: 7,
    disclosureStatus: "compliant",
    disclosureType: "inline",
    lastChecked: "2023-05-16T09:35:00Z",
  },
  {
    id: "content-6",
    title: "Travel Essentials for Summer Vacation",
    url: "https://www.youtube.com/watch?v=summer-travel-essentials",
    platform: "youtube",
    avatar: "travel",
    publishDate: "2023-05-11T15:45:00Z",
    affiliateLinks: 6,
    disclosureStatus: "warning",
    disclosureType: "description",
    lastChecked: "2023-05-16T09:40:00Z",
  },
  {
    id: "content-7",
    title: "Morning Routine for Productivity",
    url: "https://www.instagram.com/p/morning-routine-productivity",
    platform: "instagram",
    avatar: "entrepreneur",
    publishDate: "2023-05-15T08:30:00Z",
    affiliateLinks: 2,
    disclosureStatus: "non-compliant",
    disclosureType: "none",
    lastChecked: "2023-05-16T09:45:00Z",
  },
]

// Sample disclosure templates
const disclosureTemplates = [
  {
    id: "template-1",
    name: "Standard Blog Disclosure",
    text: "This post contains affiliate links. If you purchase through these links, we may earn a commission at no additional cost to you. This helps support our content creation. Learn more about our affiliate policy here.",
    platforms: ["blog"],
    position: "top",
  },
  {
    id: "template-2",
    name: "YouTube Disclosure",
    text: "Some of the links in this description are affiliate links. If you use these links to purchase products, I may earn a commission at no additional cost to you.",
    platforms: ["youtube"],
    position: "description",
  },
  {
    id: "template-3",
    name: "Instagram Disclosure",
    text: "#ad #affiliate - Some products mentioned include affiliate links",
    platforms: ["instagram"],
    position: "caption",
  },
  {
    id: "template-4",
    name: "Minimal Blog Disclosure",
    text: "Affiliate links included. We earn a commission on qualifying purchases.",
    platforms: ["blog"],
    position: "inline",
  },
  {
    id: "template-5",
    name: "Comprehensive Disclosure",
    text: "DISCLOSURE: This post contains affiliate links. When you purchase a product through one of our links, we earn a commission at no additional cost to you. This helps us maintain this site and continue providing valuable content. We only recommend products we genuinely believe in and have personally used or thoroughly researched. Thank you for your support!",
    platforms: ["blog", "youtube", "instagram"],
    position: "top",
  },
]

export function AffiliateDisclosureMonitor() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [avatarFilter, setAvatarFilter] = useState("all")
  const [platformFilter, setPlatformFilter] = useState("all")
  const [activeTab, setActiveTab] = useState("content")
  const [selectedContent, setSelectedContent] = useState<string | null>(null)
  const [isAddTemplateOpen, setIsAddTemplateOpen] = useState(false)
  const [newTemplate, setNewTemplate] = useState({
    name: "",
    text: "",
    platforms: ["blog"],
    position: "top",
  })

  // Filter content based on search and filters
  const filteredContent = contentWithAffiliateLinks.filter((content) => {
    const matchesSearch =
      content.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      content.url.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || content.disclosureStatus === statusFilter
    const matchesAvatar = avatarFilter === "all" || content.avatar === avatarFilter
    const matchesPlatform = platformFilter === "all" || content.platform === platformFilter

    return matchesSearch && matchesStatus && matchesAvatar && matchesPlatform
  })

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "compliant":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Compliant</Badge>
      case "warning":
        return (
          <Badge variant="outline" className="bg-amber-100 text-amber-800 hover:bg-amber-200">
            Warning
          </Badge>
        )
      case "non-compliant":
        return <Badge variant="destructive">Non-Compliant</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  // Get platform badge
  const getPlatformBadge = (platform: string) => {
    switch (platform) {
      case "blog":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-800">
            Blog
          </Badge>
        )
      case "youtube":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-800">
            YouTube
          </Badge>
        )
      case "instagram":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-800">
            Instagram
          </Badge>
        )
      default:
        return <Badge variant="outline">Other</Badge>
    }
  }

  // Handle export
  const handleExport = () => {
    toast({
      title: "Export Started",
      description: "Your affiliate disclosure data is being exported.",
    })
  }

  // View content details
  const handleViewContent = (id: string) => {
    setSelectedContent(id)
  }

  // Handle adding a new template
  const handleAddTemplate = () => {
    if (!newTemplate.name || !newTemplate.text) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    toast({
      title: "Template Added",
      description: "Your new disclosure template has been added.",
    })

    setIsAddTemplateOpen(false)
    setNewTemplate({
      name: "",
      text: "",
      platforms: ["blog"],
      position: "top",
    })
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Affiliate Disclosure Monitor</CardTitle>
              <CardDescription>
                Ensure FTC compliance by monitoring affiliate disclosures across all content
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={handleExport}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button onClick={() => setIsAddTemplateOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Template
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search content..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex flex-1 items-center space-x-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="compliant">Compliant</SelectItem>
                    <SelectItem value="warning">Warning</SelectItem>
                    <SelectItem value="non-compliant">Non-Compliant</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={avatarFilter} onValueChange={setAvatarFilter}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Filter by avatar" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Avatars</SelectItem>
                    <SelectItem value="travel">Travel</SelectItem>
                    <SelectItem value="fitness">Fitness</SelectItem>
                    <SelectItem value="health">Health</SelectItem>
                    <SelectItem value="entrepreneur">Entrepreneur</SelectItem>
                    <SelectItem value="home">Home</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={platformFilter} onValueChange={setPlatformFilter}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Filter by platform" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Platforms</SelectItem>
                    <SelectItem value="blog">Blog</SelectItem>
                    <SelectItem value="youtube">YouTube</SelectItem>
                    <SelectItem value="instagram">Instagram</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList>
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="templates">Disclosure Templates</TabsTrigger>
              </TabsList>

              <TabsContent value="content">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Title</TableHead>
                        <TableHead>Platform</TableHead>
                        <TableHead>Avatar</TableHead>
                        <TableHead>Affiliate Links</TableHead>
                        <TableHead>Disclosure Type</TableHead>
                        <TableHead>Publish Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredContent.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="h-24 text-center">
                            No content found matching your filters.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredContent.map((content) => (
                          <TableRow key={content.id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center space-x-2">
                                <FileText className="h-4 w-4 text-muted-foreground" />
                                <span className="truncate max-w-[200px]">{content.title}</span>
                              </div>
                            </TableCell>
                            <TableCell>{getPlatformBadge(content.platform)}</TableCell>
                            <TableCell>
                              <Badge variant="outline" className="capitalize">
                                {content.avatar}
                              </Badge>
                            </TableCell>
                            <TableCell>{content.affiliateLinks}</TableCell>
                            <TableCell className="capitalize">
                              {content.disclosureType === "none" ? "None" : content.disclosureType}
                            </TableCell>
                            <TableCell>{formatDate(content.publishDate)}</TableCell>
                            <TableCell>{getStatusBadge(content.disclosureStatus)}</TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" className="h-8 w-8 p-0">
                                    <span className="sr-only">Open menu</span>
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => handleViewContent(content.id)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => window.open(content.url, "_blank")}>
                                    <ExternalLink className="mr-2 h-4 w-4" />
                                    Open Content
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit Disclosure
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>

              <TabsContent value="templates">
                <div className="space-y-4">
                  {disclosureTemplates.map((template) => (
                    <Card key={template.id}>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">{template.name}</CardTitle>
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">Edit</span>
                            </Button>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Delete</span>
                            </Button>
                          </div>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {template.platforms.map((platform) => (
                            <Badge key={platform} variant="outline" className="capitalize">
                              {platform}
                            </Badge>
                          ))}
                          <Badge variant="outline" className="capitalize">
                            Position: {template.position}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-sm border rounded-md p-3 bg-muted">{template.text}</div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* Content Details Dialog */}
      <Dialog open={selectedContent !== null} onOpenChange={() => setSelectedContent(null)}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Content Disclosure Details</DialogTitle>
            <DialogDescription>Detailed information about affiliate disclosures for this content</DialogDescription>
          </DialogHeader>
          {selectedContent && (
            <div className="space-y-4 py-4">
              {(() => {
                const content = contentWithAffiliateLinks.find((c) => c.id === selectedContent)
                if (!content) return null

                return (
                  <>
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium truncate max-w-[400px]">{content.title}</h3>
                      {getStatusBadge(content.disclosureStatus)}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-muted-foreground">Platform</Label>
                        <p className="font-medium capitalize">{content.platform}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Avatar</Label>
                        <p className="font-medium capitalize">{content.avatar}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Publish Date</Label>
                        <p className="font-medium">{formatDate(content.publishDate)}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Last Checked</Label>
                        <p className="font-medium">{formatDate(content.lastChecked)}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Affiliate Links</Label>
                        <p className="font-medium">{content.affiliateLinks}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Disclosure Type</Label>
                        <p className="font-medium capitalize">
                          {content.disclosureType === "none" ? "None" : content.disclosureType}
                        </p>
                      </div>
                    </div>

                    <div>
                      <Label className="text-muted-foreground">URL</Label>
                      <div className="flex items-center space-x-2 mt-1">
                        <Input value={content.url} readOnly />
                        <Button variant="outline" size="sm" onClick={() => window.open(content.url, "_blank")}>
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {content.disclosureStatus === "non-compliant" && (
                      <div className="rounded-md bg-red-50 p-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <AlertTriangle className="h-5 w-5 text-red-400" aria-hidden="true" />
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-red-800">Compliance Issues</h3>
                            <div className="mt-2 text-sm text-red-700">
                              <ul className="list-disc space-y-1 pl-5">
                                <li>No affiliate disclosure found</li>
                                <li>Content contains {content.affiliateLinks} affiliate links</li>
                                <li>FTC guidelines require clear and conspicuous disclosure</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {content.disclosureStatus === "warning" && (
                      <div className="rounded-md bg-amber-50 p-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <AlertTriangle className="h-5 w-5 text-amber-400" aria-hidden="true" />
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-amber-800">Compliance Warnings</h3>
                            <div className="mt-2 text-sm text-amber-700">
                              <ul className="list-disc space-y-1 pl-5">
                                <li>
                                  Disclosure placement is not optimal (
                                  {content.disclosureType === "bottom" ? "bottom of content" : "in description"})
                                </li>
                                <li>FTC guidelines recommend disclosure at the top of content</li>
                                <li>Consider updating disclosure placement for better compliance</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )
              })()}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setSelectedContent(null)}>
              Close
            </Button>
            <Button>Fix Disclosure</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Template Dialog */}
      <Dialog open={isAddTemplateOpen} onOpenChange={setIsAddTemplateOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add Disclosure Template</DialogTitle>
            <DialogDescription>Create a new disclosure template for affiliate content</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="template-name" className="text-right">
                Name
              </Label>
              <Input
                id="template-name"
                placeholder="e.g., Standard Blog Disclosure"
                className="col-span-3"
                value={newTemplate.name}
                onChange={(e) => setNewTemplate({ ...newTemplate, name: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="template-platforms" className="text-right">
                Platforms
              </Label>
              <div className="col-span-3 flex flex-wrap gap-2">
                {["blog", "youtube", "instagram"].map((platform) => (
                  <Badge
                    key={platform}
                    variant={newTemplate.platforms.includes(platform) ? "default" : "outline"}
                    className="cursor-pointer capitalize"
                    onClick={() => {
                      if (newTemplate.platforms.includes(platform)) {
                        setNewTemplate({
                          ...newTemplate,
                          platforms: newTemplate.platforms.filter((p) => p !== platform),
                        })
                      } else {
                        setNewTemplate({
                          ...newTemplate,
                          platforms: [...newTemplate.platforms, platform],
                        })
                      }
                    }}
                  >
                    {platform}
                  </Badge>
                ))}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="template-position" className="text-right">
                Position
              </Label>
              <Select
                value={newTemplate.position}
                onValueChange={(value) => setNewTemplate({ ...newTemplate, position: value })}
              >
                <SelectTrigger id="template-position" className="col-span-3">
                  <SelectValue placeholder="Select position" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="top">Top of Content</SelectItem>
                  <SelectItem value="bottom">Bottom of Content</SelectItem>
                  <SelectItem value="inline">Inline with Links</SelectItem>
                  <SelectItem value="description">In Description (Video/Social)</SelectItem>
                  <SelectItem value="caption">In Caption (Social)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 gap-4">
              <Label htmlFor="template-text" className="text-right">
                Disclosure Text
              </Label>
              <Textarea
                id="template-text"
                placeholder="Enter your disclosure text here..."
                className="col-span-3"
                rows={4}
                value={newTemplate.text}
                onChange={(e) => setNewTemplate({ ...newTemplate, text: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddTemplateOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddTemplate}>Add Template</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

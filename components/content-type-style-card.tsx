"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Camera, Image as ImageIcon, Play, Video, Wand2 } from "lucide-react"

export type MediaType = "video" | "image" | "text" | ""

export interface ContentTypeStyleValues {
  content_type?: string
  media_type?: MediaType
  video_type?: string
  duration?: number | null
  camera_angle?: string
  camera_movement?: string
  lighting_style?: string
  image_type?: string
  image_dimensions?: string
  style?: string
  tone?: string
  voice_tone?: string
  ai_generation_type?: string
}

export interface ContentTypeStyleCardProps {
  values: ContentTypeStyleValues
  onChange: (key: keyof ContentTypeStyleValues, value: any) => void
}

export function ContentTypeStyleCard({ values, onChange }: ContentTypeStyleCardProps) {
  const mediaType = values.media_type || ""

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Wand2 className="h-5 w-5" />
          <span>Content Type & Style</span>
        </CardTitle>
        <CardDescription>Define what type of content you want to create</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label>Content Type *</Label>
          <Select value={values.content_type || ""} onValueChange={(v) => onChange("content_type", v)}>
            <SelectTrigger>
              <SelectValue placeholder="Select content type..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="product_advertisement">Product Advertisement</SelectItem>
              <SelectItem value="product_showcase">Product Showcase</SelectItem>
              <SelectItem value="product_review">Product Review</SelectItem>
              <SelectItem value="product_tutorial">Product Tutorial</SelectItem>
              <SelectItem value="brand_story">Brand Story</SelectItem>
              <SelectItem value="promotional_content">Promotional Content</SelectItem>
              <SelectItem value="testimonial">Testimonial</SelectItem>
              <SelectItem value="tutorial">Tutorial</SelectItem>
              <SelectItem value="unboxing">Unboxing</SelectItem>
              <SelectItem value="comparison">Comparison</SelectItem>
              <SelectItem value="lifestyle">Lifestyle</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Media Type *</Label>
          <Select value={mediaType} onValueChange={(v: MediaType) => onChange("media_type", v)}>
            <SelectTrigger>
              <SelectValue placeholder="Select media type..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="video">
                <div className="flex items-center space-x-2">
                  <Video className="h-4 w-4" />
                  <span>Video Content</span>
                </div>
              </SelectItem>
              <SelectItem value="image">
                <div className="flex items-center space-x-2">
                  <ImageIcon className="h-4 w-4" />
                  <span>Image Content</span>
                </div>
              </SelectItem>
              {/**
              <SelectItem value="text">
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>Text Content</span>
                </div>
              </SelectItem>
              */}
            </SelectContent>
          </Select>
        </div>

        {mediaType === "video" && (
          <>
            <div className="space-y-2">
              <Label>Video Type</Label>
              <Select value={values.video_type || ""} onValueChange={(v) => onChange("video_type", v)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select video type..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="reel">
                    <div className="flex items-center space-x-2">
                      <Play className="h-4 w-4" />
                      <span>Reel/Short Form</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="short">
                    <div className="flex items-center space-x-2">
                      <Video className="h-4 w-4" />
                      <span>Short Video</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="long">
                    <div className="flex items-center space-x-2">
                      <Camera className="h-4 w-4" />
                      <span>Long Form Video</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Duration (seconds)</Label>
              <Select
                value={values.duration != null ? String(values.duration) : ""}
                onValueChange={(v) => onChange("duration", parseInt(v))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select duration..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="8">8 seconds</SelectItem>
                  <SelectItem value="15">15 seconds</SelectItem>
                  <SelectItem value="30">30 seconds</SelectItem>
                  <SelectItem value="60">1 minute</SelectItem>
                  <SelectItem value="90">90 seconds</SelectItem>
                  <SelectItem value="180">3 minutes</SelectItem>
                  <SelectItem value="300">5 minutes</SelectItem>
                  <SelectItem value="600">10 minutes</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Camera Angle</Label>
              <Select value={values.camera_angle || ""} onValueChange={(v) => onChange("camera_angle", v)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select camera angle..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="three_quarter_view">Three Quarter View</SelectItem>
                  <SelectItem value="front_view">Front View</SelectItem>
                  <SelectItem value="side_view">Side View</SelectItem>
                  <SelectItem value="top_down">Top Down</SelectItem>
                  <SelectItem value="low_angle">Low Angle</SelectItem>
                  <SelectItem value="high_angle">High Angle</SelectItem>
                  <SelectItem value="close_up">Close Up</SelectItem>
                  <SelectItem value="wide_shot">Wide Shot</SelectItem>
                  <SelectItem value="medium_shot">Medium Shot</SelectItem>
                  <SelectItem value="macro">Macro</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Camera Movement</Label>
              <Select value={values.camera_movement || ""} onValueChange={(v) => onChange("camera_movement", v)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select camera movement..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="static">Static</SelectItem>
                  <SelectItem value="pan_left">Pan Left</SelectItem>
                  <SelectItem value="pan_right">Pan Right</SelectItem>
                  <SelectItem value="tilt_up">Tilt Up</SelectItem>
                  <SelectItem value="tilt_down">Tilt Down</SelectItem>
                  <SelectItem value="zoom_in">Zoom In</SelectItem>
                  <SelectItem value="zoom_out">Zoom Out</SelectItem>
                  <SelectItem value="dolly_in">Dolly In</SelectItem>
                  <SelectItem value="dolly_out">Dolly Out</SelectItem>
                  <SelectItem value="tracking">Tracking</SelectItem>
                  <SelectItem value="handheld">Handheld</SelectItem>
                  <SelectItem value="smooth_glide">Smooth Glide</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Lighting Style</Label>
              <Select value={values.lighting_style || ""} onValueChange={(v) => onChange("lighting_style", v)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select lighting style..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="studio">Studio</SelectItem>
                  <SelectItem value="natural">Natural</SelectItem>
                  <SelectItem value="soft">Soft</SelectItem>
                  <SelectItem value="hard">Hard</SelectItem>
                  <SelectItem value="dramatic">Dramatic</SelectItem>
                  <SelectItem value="cinematic">Cinematic</SelectItem>
                  <SelectItem value="bright">Bright</SelectItem>
                  <SelectItem value="moody">Moody</SelectItem>
                  <SelectItem value="golden_hour">Golden Hour</SelectItem>
                  <SelectItem value="blue_hour">Blue Hour</SelectItem>
                  <SelectItem value="neon">Neon</SelectItem>
                  <SelectItem value="backlit">Backlit</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </>
        )}

        {mediaType === "image" && (
          <>
            <div className="space-y-2">
              <Label>Image Type *</Label>
              <Select value={values.image_type || ""} onValueChange={(v) => onChange("image_type", v)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select image type..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="banner">Banner</SelectItem>
                  <SelectItem value="logo">Logo</SelectItem>
                  <SelectItem value="photo_card">Photo Card</SelectItem>
                  <SelectItem value="social_post">Social Media Post</SelectItem>
                  <SelectItem value="thumbnail">Thumbnail</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Dimensions</Label>
              <Select value={values.image_dimensions || ""} onValueChange={(v) => onChange("image_dimensions", v)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select dimensions..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="square">Square (1:1)</SelectItem>
                  <SelectItem value="portrait">Portrait (4:5)</SelectItem>
                  <SelectItem value="landscape">Landscape (16:9)</SelectItem>
                  <SelectItem value="story">Story (9:16)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </>
        )}

        <div className="space-y-2">
          <Label>Style</Label>
          <Select value={values.style || ""} onValueChange={(v) => onChange("style", v)}>
            <SelectTrigger>
              <SelectValue placeholder="Select style..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="modern">Modern</SelectItem>
              <SelectItem value="classic">Classic</SelectItem>
              <SelectItem value="minimalist">Minimalist</SelectItem>
              <SelectItem value="bold">Bold</SelectItem>
              <SelectItem value="elegant">Elegant</SelectItem>
              <SelectItem value="playful">Playful</SelectItem>
              <SelectItem value="professional">Professional</SelectItem>
              <SelectItem value="casual">Casual</SelectItem>
              <SelectItem value="cinematic">Cinematic</SelectItem>
              <SelectItem value="animated">Animated</SelectItem>
              <SelectItem value="trendy">Trendy</SelectItem>
              <SelectItem value="vintage">Vintage</SelectItem>
              <SelectItem value="luxury">Luxury</SelectItem>
              <SelectItem value="artistic">Artistic</SelectItem>
              <SelectItem value="corporate">Corporate</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Tone</Label>
          <Select value={values.tone || ""} onValueChange={(v) => onChange("tone", v)}>
            <SelectTrigger>
              <SelectValue placeholder="Select tone..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="professional">Professional</SelectItem>
              <SelectItem value="casual">Casual</SelectItem>
              <SelectItem value="energetic">Energetic</SelectItem>
              <SelectItem value="calm">Calm</SelectItem>
              <SelectItem value="authoritative">Authoritative</SelectItem>
              <SelectItem value="playful">Playful</SelectItem>
              <SelectItem value="serious">Serious</SelectItem>
              <SelectItem value="inspirational">Inspirational</SelectItem>
              <SelectItem value="conversational">Conversational</SelectItem>
              <SelectItem value="informative">Informative</SelectItem>
              <SelectItem value="persuasive">Persuasive</SelectItem>
              <SelectItem value="entertaining">Entertaining</SelectItem>
              <SelectItem value="educational">Educational</SelectItem>
              <SelectItem value="friendly">Friendly</SelectItem>
              <SelectItem value="humorous">Humorous</SelectItem>
              <SelectItem value="emotional">Emotional</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {mediaType === "video" && (
          <div className="space-y-2">
            <Label>Voice Tone</Label>
            <Select value={values.voice_tone || ""} onValueChange={(v) => onChange("voice_tone", v)}>
              <SelectTrigger>
                <SelectValue placeholder="Select voice tone..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="natural_human">Natural Human</SelectItem>
                <SelectItem value="ai_generated">AI Generated</SelectItem>
                <SelectItem value="celebrity_style">Celebrity Style</SelectItem>
                <SelectItem value="narrator">Professional Narrator</SelectItem>
                <SelectItem value="enthusiastic">Enthusiastic</SelectItem>
                <SelectItem value="professional_presenter">Professional Presenter</SelectItem>
                <SelectItem value="storyteller">Storyteller</SelectItem>
                <SelectItem value="documentary">Documentary Style</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <div className="space-y-2">
          <Label>AI Generation Type</Label>
          <Select value={values.ai_generation_type || ""} onValueChange={(v) => onChange("ai_generation_type", v)}>
            <SelectTrigger>
              <SelectValue placeholder="Select AI generation type..." />
            </SelectTrigger>
            <SelectContent>
              {/*
              <SelectItem value="text-to-video">Text to Video</SelectItem>
              <SelectItem value="text-to-image">Text to Image</SelectItem>
              <SelectItem value="image-to-video">Image to Video</SelectItem>
              <SelectItem value="script-to-video">Script to Video</SelectItem>
              */}
              <SelectItem value="animation">Animation</SelectItem>
              <SelectItem value="realistic">Realistic</SelectItem>
              <SelectItem value="stylized">Stylized</SelectItem>
              <SelectItem value="voice-over">Voice Over</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  )
}

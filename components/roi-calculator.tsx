"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"

export function RoiCalculator() {
  const [investment, setInvestment] = useState(10000)
  const [returnAmount, setReturnAmount] = useState(12000)

  const calculateROI = () => {
    const roi = ((returnAmount - investment) / investment) * 100
    return roi.toFixed(2)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>ROI Calculator</CardTitle>
        <CardDescription>Calculate the return on investment for a white label product</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="investment">Initial Investment</Label>
          <Input
            id="investment"
            type="number"
            placeholder="Enter investment amount"
            value={investment}
            onChange={(e) => setInvestment(Number(e.target.value))}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="return">Expected Return</Label>
          <Input
            id="return"
            type="number"
            placeholder="Enter expected return amount"
            value={returnAmount}
            onChange={(e) => setReturnAmount(Number(e.target.value))}
          />
        </div>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Return on Investment</h3>
          <Badge className="text-2xl font-bold">{calculateROI()}%</Badge>
        </div>
      </CardContent>
    </Card>
  )
}

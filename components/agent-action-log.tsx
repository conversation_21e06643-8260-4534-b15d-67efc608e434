"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Search,
  Filter,
  Download,
  Bot,
  Clock,
  AlertTriangle,
  CheckCircle2,
  Eye,
  MoreHorizontal,
  Trash2,
  Flag,
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Sample agent actions data
const agentActions = [
  {
    id: "action-1",
    agentName: "Content Scout",
    actionType: "content_research",
    description: "Researched trending topics for Travel avatar",
    timestamp: "2023-05-16T14:30:00Z",
    status: "completed",
    reviewStatus: "approved",
    avatar: "travel",
    decisionFactors: [
      "Current travel trends from Google Trends",
      "Social media engagement on similar topics",
      "Seasonal relevance for summer travel",
    ],
    outcome: "Generated 5 content ideas for summer travel gadgets",
  },
  {
    id: "action-2",
    agentName: "Engagement Bot",
    actionType: "comment_response",
    description: "Responded to user comments on fitness video",
    timestamp: "2023-05-16T10:15:00Z",
    status: "completed",
    reviewStatus: "flagged",
    avatar: "fitness",
    decisionFactors: [
      "Sentiment analysis of user comment",
      "Previous interaction history with user",
      "Response templates for product questions",
    ],
    outcome: "Responded to 12 comments, 1 flagged for human review",
  },
  {
    id: "action-3",
    agentName: "Analytics Miner",
    actionType: "data_analysis",
    description: "Generated weekly performance report",
    timestamp: "2023-05-15T09:45:00Z",
    status: "completed",
    reviewStatus: "approved",
    avatar: "all",
    decisionFactors: [
      "Engagement metrics across all platforms",
      "Conversion rates for affiliate links",
      "Week-over-week growth patterns",
    ],
    outcome: "Created comprehensive report with 3 actionable insights",
  },
  {
    id: "action-4",
    agentName: "Content Repurposer",
    actionType: "content_creation",
    description: "Repurposed blog post into social media content",
    timestamp: "2023-05-14T16:20:00Z",
    status: "completed",
    reviewStatus: "needs_review",
    avatar: "entrepreneur",
    decisionFactors: [
      "Key points from original blog post",
      "Platform-specific formatting requirements",
      "Optimal content length for each platform",
    ],
    outcome: "Created 3 Instagram posts and 5 Twitter posts from blog content",
  },
  {
    id: "action-5",
    agentName: "Hashtag Optimizer",
    actionType: "seo",
    description: "Optimized hashtags for home decor post",
    timestamp: "2023-05-13T11:30:00Z",
    status: "completed",
    reviewStatus: "approved",
    avatar: "home",
    decisionFactors: [
      "Current trending hashtags in home decor",
      "Performance of previous hashtag sets",
      "Relevance to post content and target audience",
    ],
    outcome: "Selected 15 hashtags with combined reach potential of 2.5M",
  },
  {
    id: "action-6",
    agentName: "Affiliate Hunter",
    actionType: "affiliate_research",
    description: "Identified new affiliate opportunities for health products",
    timestamp: "2023-05-12T15:45:00Z",
    status: "completed",
    reviewStatus: "needs_review",
    avatar: "health",
    decisionFactors: [
      "Product relevance to health avatar audience",
      "Commission rates compared to existing affiliates",
      "Product quality based on review analysis",
    ],
    outcome: "Recommended 3 new affiliate programs for health supplements",
  },
  {
    id: "action-7",
    agentName: "Scheduler Bot",
    actionType: "content_scheduling",
    description: "Optimized posting schedule for next week",
    timestamp: "2023-05-11T08:30:00Z",
    status: "completed",
    reviewStatus: "approved",
    avatar: "all",
    decisionFactors: [
      "Historical engagement patterns by day and time",
      "Upcoming holidays and events",
      "Content distribution across platforms",
    ],
    outcome: "Created balanced schedule with 22 posts across all platforms",
  },
]

// Sample audit logs
const auditLogs = [
  {
    id: "audit-1",
    auditor: "System",
    actionId: "action-2",
    agentName: "Engagement Bot",
    auditType: "automated_flag",
    timestamp: "2023-05-16T10:20:00Z",
    notes: "Response flagged for potential misunderstanding of user question about product ingredients",
    resolution: "pending",
  },
  {
    id: "audit-2",
    auditor: "John Smith",
    actionId: "action-1",
    agentName: "Content Scout",
    auditType: "manual_review",
    timestamp: "2023-05-16T15:30:00Z",
    notes: "Reviewed and approved content ideas. All topics are on-brand and timely.",
    resolution: "approved",
  },
  {
    id: "audit-3",
    auditor: "System",
    actionId: "action-4",
    agentName: "Content Repurposer",
    auditType: "automated_review",
    timestamp: "2023-05-14T16:45:00Z",
    notes: "Content flagged for human review due to complex formatting requirements",
    resolution: "pending",
  },
  {
    id: "audit-4",
    auditor: "Jane Doe",
    actionId: "action-3",
    agentName: "Analytics Miner",
    auditType: "manual_review",
    timestamp: "2023-05-15T11:15:00Z",
    notes: "Verified data accuracy and approved report. Insights are valuable for strategy.",
    resolution: "approved",
  },
  {
    id: "audit-5",
    auditor: "System",
    actionId: "action-6",
    agentName: "Affiliate Hunter",
    auditType: "automated_review",
    timestamp: "2023-05-12T16:00:00Z",
    notes: "New affiliate recommendations require human verification of terms and conditions",
    resolution: "pending",
  },
]

export function AgentActionLog() {
  const [searchTerm, setSearchTerm] = useState("")
  const [agentFilter, setAgentFilter] = useState("all")
  const [actionTypeFilter, setActionTypeFilter] = useState("all")
  const [reviewStatusFilter, setReviewStatusFilter] = useState("all")
  const [avatarFilter, setAvatarFilter] = useState("all")
  const [activeTab, setActiveTab] = useState("actions")
  const [selectedAction, setSelectedAction] = useState<string | null>(null)
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0], // 7 days ago
    end: new Date().toISOString().split("T")[0], // today
  })

  // Filter actions based on search and filters
  const filteredActions = agentActions.filter((action) => {
    const matchesSearch =
      action.agentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      action.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesAgent = agentFilter === "all" || action.agentName === agentFilter
    const matchesActionType = actionTypeFilter === "all" || action.actionType === actionTypeFilter
    const matchesReviewStatus = reviewStatusFilter === "all" || action.reviewStatus === reviewStatusFilter
    const matchesAvatar = avatarFilter === "all" || action.avatar === avatarFilter

    // Check if action is within date range
    const actionDate = new Date(action.timestamp)
    const startDate = new Date(dateRange.start)
    const endDate = new Date(dateRange.end)
    endDate.setHours(23, 59, 59, 999) // Set to end of day
    const isInDateRange = actionDate >= startDate && actionDate <= endDate

    return matchesSearch && matchesAgent && matchesActionType && matchesReviewStatus && matchesAvatar && isInDateRange
  })

  // Filter audit logs based on search
  const filteredAuditLogs = auditLogs.filter((log) => {
    const matchesSearch =
      log.agentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.notes.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesAgent = agentFilter === "all" || log.agentName === agentFilter
    const matchesActionType = actionTypeFilter === "all" || log.auditType.includes(actionTypeFilter)
    const matchesReviewStatus =
      reviewStatusFilter === "all" ||
      (log.resolution === "approved" && reviewStatusFilter === "approved") ||
      (log.resolution === "pending" && reviewStatusFilter === "needs_review")

    // Check if log is within date range
    const logDate = new Date(log.timestamp)
    const startDate = new Date(dateRange.start)
    const endDate = new Date(dateRange.end)
    endDate.setHours(23, 59, 59, 999) // Set to end of day
    const isInDateRange = logDate >= startDate && logDate <= endDate

    return matchesSearch && matchesAgent && matchesActionType && matchesReviewStatus && isInDateRange
  })

  // Get review status badge
  const getReviewStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Approved</Badge>
      case "needs_review":
        return (
          <Badge variant="outline" className="bg-amber-100 text-amber-800 hover:bg-amber-200">
            Needs Review
          </Badge>
        )
      case "flagged":
        return <Badge variant="destructive">Flagged</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  // Handle export
  const handleExport = () => {
    toast({
      title: "Export Started",
      description: "Your agent action log data is being exported.",
    })
  }

  // View action details
  const handleViewAction = (id: string) => {
    setSelectedAction(id)
  }

  // Handle action review
  const handleReviewAction = (id: string, approved: boolean) => {
    toast({
      title: approved ? "Action Approved" : "Action Flagged",
      description: approved
        ? "The agent action has been approved and logged."
        : "The agent action has been flagged for further review.",
    })
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Agent Action Log</CardTitle>
              <CardDescription>Audit trail of all actions taken by autonomous agents in your system</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={handleExport}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search actions..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Label htmlFor="date-start" className="text-sm">
                  From
                </Label>
                <Input
                  id="date-start"
                  type="date"
                  className="w-[150px]"
                  value={dateRange.start}
                  onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
                />
                <Label htmlFor="date-end" className="text-sm">
                  To
                </Label>
                <Input
                  id="date-end"
                  type="date"
                  className="w-[150px]"
                  value={dateRange.end}
                  onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
                />
              </div>
            </div>

            <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2">
              <div className="flex flex-1 items-center space-x-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <Select value={agentFilter} onValueChange={setAgentFilter}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Filter by agent" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Agents</SelectItem>
                    <SelectItem value="Content Scout">Content Scout</SelectItem>
                    <SelectItem value="Engagement Bot">Engagement Bot</SelectItem>
                    <SelectItem value="Analytics Miner">Analytics Miner</SelectItem>
                    <SelectItem value="Content Repurposer">Content Repurposer</SelectItem>
                    <SelectItem value="Hashtag Optimizer">Hashtag Optimizer</SelectItem>
                    <SelectItem value="Affiliate Hunter">Affiliate Hunter</SelectItem>
                    <SelectItem value="Scheduler Bot">Scheduler Bot</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={actionTypeFilter} onValueChange={setActionTypeFilter}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Filter by action type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Action Types</SelectItem>
                    <SelectItem value="content_research">Content Research</SelectItem>
                    <SelectItem value="comment_response">Comment Response</SelectItem>
                    <SelectItem value="data_analysis">Data Analysis</SelectItem>
                    <SelectItem value="content_creation">Content Creation</SelectItem>
                    <SelectItem value="seo">SEO</SelectItem>
                    <SelectItem value="affiliate_research">Affiliate Research</SelectItem>
                    <SelectItem value="content_scheduling">Content Scheduling</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={reviewStatusFilter} onValueChange={setReviewStatusFilter}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Filter by review status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="needs_review">Needs Review</SelectItem>
                    <SelectItem value="flagged">Flagged</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={avatarFilter} onValueChange={setAvatarFilter}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Filter by avatar" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Avatars</SelectItem>
                    <SelectItem value="travel">Travel</SelectItem>
                    <SelectItem value="fitness">Fitness</SelectItem>
                    <SelectItem value="health">Health</SelectItem>
                    <SelectItem value="entrepreneur">Entrepreneur</SelectItem>
                    <SelectItem value="home">Home</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList>
                <TabsTrigger value="actions">Agent Actions</TabsTrigger>
                <TabsTrigger value="audits">Audit Logs</TabsTrigger>
              </TabsList>

              <TabsContent value="actions">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Agent</TableHead>
                        <TableHead>Action</TableHead>
                        <TableHead>Avatar</TableHead>
                        <TableHead>Timestamp</TableHead>
                        <TableHead>Review Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredActions.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="h-24 text-center">
                            No actions found matching your filters.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredActions.map((action) => (
                          <TableRow key={action.id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center space-x-2">
                                <Bot className="h-4 w-4 text-muted-foreground" />
                                <span>{action.agentName}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div>
                                <p className="truncate max-w-[250px]">{action.description}</p>
                                <p className="text-xs text-muted-foreground capitalize">
                                  {action.actionType.replace("_", " ")}
                                </p>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="capitalize">
                                {action.avatar}
                              </Badge>
                            </TableCell>
                            <TableCell>{formatDate(action.timestamp)}</TableCell>
                            <TableCell>{getReviewStatusBadge(action.reviewStatus)}</TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" className="h-8 w-8 p-0">
                                    <span className="sr-only">Open menu</span>
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => handleViewAction(action.id)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  {action.reviewStatus === "needs_review" && (
                                    <>
                                      <DropdownMenuItem onClick={() => handleReviewAction(action.id, true)}>
                                        <CheckCircle2 className="mr-2 h-4 w-4" />
                                        Approve
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleReviewAction(action.id, false)}>
                                        <Flag className="mr-2 h-4 w-4" />
                                        Flag for Review
                                      </DropdownMenuItem>
                                    </>
                                  )}
                                  <DropdownMenuItem>
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete Record
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>

              <TabsContent value="audits">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Auditor</TableHead>
                        <TableHead>Agent</TableHead>
                        <TableHead>Audit Type</TableHead>
                        <TableHead>Timestamp</TableHead>
                        <TableHead>Resolution</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAuditLogs.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="h-24 text-center">
                            No audit logs found matching your filters.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredAuditLogs.map((logEntry) => (
                          <TableRow key={logEntry.id}>
                            <TableCell className="font-medium">{logEntry.auditor}</TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <Bot className="h-4 w-4 text-muted-foreground" />
                                <span>{logEntry.agentName}</span>
                              </div>
                            </TableCell>
                            <TableCell className="capitalize">{logEntry.auditType.replace("_", " ")}</TableCell>
                            <TableCell>{formatDate(logEntry.timestamp)}</TableCell>
                            <TableCell>
                              {logEntry.resolution === "approved" ? (
                                <Badge className="bg-green-100 text-green-800">Approved</Badge>
                              ) : logEntry.resolution === "pending" ? (
                                <Badge variant="outline" className="bg-amber-100 text-amber-800">
                                  Pending
                                </Badge>
                              ) : (
                                <Badge variant="destructive">Rejected</Badge>
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewAction(logEntry.actionId)}
                                className="h-8 w-8 p-0"
                              >
                                <span className="sr-only">View action</span>
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* Action Details Dialog */}
      <Dialog open={selectedAction !== null} onOpenChange={() => setSelectedAction(null)}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Agent Action Details</DialogTitle>
            <DialogDescription>Detailed information about this agent action</DialogDescription>
          </DialogHeader>
          {selectedAction && (
            <div className="space-y-4 py-4">
              {(() => {
                const action = agentActions.find((a) => a.id === selectedAction)
                if (!action) return null

                // Find related audit logs
                const relatedAudits = auditLogs.filter((log) => log.actionId === selectedAction)

                return (
                  <>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Bot className="h-5 w-5 text-muted-foreground" />
                        <h3 className="text-lg font-medium">{action.agentName}</h3>
                      </div>
                      {getReviewStatusBadge(action.reviewStatus)}
                    </div>

                    <div>
                      <Label className="text-muted-foreground">Action Description</Label>
                      <p className="font-medium">{action.description}</p>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-muted-foreground">Action Type</Label>
                        <p className="font-medium capitalize">{action.actionType.replace("_", " ")}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Avatar</Label>
                        <p className="font-medium capitalize">{action.avatar}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Timestamp</Label>
                        <p className="font-medium">{formatDate(action.timestamp)}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Status</Label>
                        <p className="font-medium capitalize">{action.status}</p>
                      </div>
                    </div>

                    <div>
                      <Label className="text-muted-foreground">Decision Factors</Label>
                      <ul className="mt-1 space-y-1 list-disc pl-5">
                        {action.decisionFactors.map((factor, index) => (
                          <li key={index} className="text-sm">
                            {factor}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <Label className="text-muted-foreground">Outcome</Label>
                      <p className="font-medium">{action.outcome}</p>
                    </div>

                    {relatedAudits.length > 0 && (
                      <div>
                        <Label className="text-muted-foreground">Audit History</Label>
                        <div className="mt-2 space-y-3">
                          {relatedAudits.map((audit) => (
                            <div key={audit.id} className="border rounded-md p-3 text-sm">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <Clock className="h-4 w-4 text-muted-foreground" />
                                  <span>{formatDate(audit.timestamp)}</span>
                                </div>
                                <Badge
                                  variant="outline"
                                  className={
                                    audit.auditType === "manual_review"
                                      ? "bg-blue-50 text-blue-800"
                                      : "bg-amber-50 text-amber-800"
                                  }
                                >
                                  {audit.auditType === "manual_review" ? "Manual Review" : "Automated Flag"}
                                </Badge>
                              </div>
                              <div className="mt-2">
                                <p className="font-medium">Auditor: {audit.auditor}</p>
                                <p className="mt-1">{audit.notes}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {action.reviewStatus === "flagged" && (
                      <div className="rounded-md bg-red-50 p-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <AlertTriangle className="h-5 w-5 text-red-400" aria-hidden="true" />
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-red-800">Action Flagged</h3>
                            <div className="mt-2 text-sm text-red-700">
                              This action has been flagged for human review due to potential issues. Please review the
                              action details and take appropriate action.
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )
              })()}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setSelectedAction(null)}>
              Close
            </Button>
            {selectedAction &&
              (() => {
                const action = agentActions.find((a) => a.id === selectedAction)
                if (!action) return null

                return action.reviewStatus === "needs_review" ? (
                  <div className="flex space-x-2">
                    <Button variant="outline" onClick={() => handleReviewAction(action.id, false)}>
                      Flag for Review
                    </Button>
                    <Button onClick={() => handleReviewAction(action.id, true)}>Approve</Button>
                  </div>
                ) : null
              })()}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

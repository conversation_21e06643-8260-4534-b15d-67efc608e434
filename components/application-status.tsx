"use client"

import { Label } from "@/components/ui/label"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Search,
  Filter,
  MoreHorizontal,
  Globe,
  Calendar,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  Clock,
  RefreshCw,
  MessageSquare,
  Download,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  <PERSON>alog,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"
import { ScrollArea } from "@/components/ui/scroll-area"

// Sample application status data
const applicationStatus = [
  {
    id: "app-status-1",
    networkId: "network-1",
    networkName: "ShareASale",
    avatar: "health",
    status: "approved",
    submittedAt: "2023-05-10T08:45:00Z",
    updatedAt: "2023-05-12T14:30:00Z",
    notes: "Application approved. Affiliate ID: SA-12345678",
    feedback: "Website meets all requirements. Strong health content focus appreciated.",
  },
  {
    id: "app-status-2",
    networkId: "network-2",
    networkName: "Amazon Associates",
    avatar: "home",
    status: "rejected",
    submittedAt: "2023-05-08T10:15:00Z",
    updatedAt: "2023-05-11T09:20:00Z",
    notes: "Application rejected due to insufficient content",
    feedback: "Need more original content. Reapply after adding at least 10 more articles.",
    retryEligible: true,
    retryDate: "2023-06-11T09:20:00Z",
  },
  {
    id: "app-status-3",
    networkId: "network-3",
    networkName: "CJ Affiliate",
    avatar: "travel",
    status: "pending",
    submittedAt: "2023-05-14T11:30:00Z",
    updatedAt: "2023-05-14T11:30:00Z",
    notes: "Application submitted and awaiting review",
    estimatedResponse: "2023-05-21T11:30:00Z",
  },
  {
    id: "app-status-4",
    networkId: "network-4",
    networkName: "ClickBank",
    avatar: "entrepreneur",
    status: "approved",
    submittedAt: "2023-05-05T14:45:00Z",
    updatedAt: "2023-05-06T10:15:00Z",
    notes: "Application approved. Affiliate ID: CB-********",
    feedback: "Great entrepreneur content. Fast approval due to quality site.",
  },
  {
    id: "app-status-5",
    networkId: "network-5",
    networkName: "Awin",
    avatar: "fitness",
    status: "in_review",
    submittedAt: "2023-05-13T09:00:00Z",
    updatedAt: "2023-05-15T16:30:00Z",
    notes: "Application under review by network team",
    estimatedResponse: "2023-05-20T09:00:00Z",
  },
  {
    id: "app-status-6",
    networkId: "network-6",
    networkName: "Impact",
    avatar: "travel",
    status: "additional_info",
    submittedAt: "2023-05-11T13:20:00Z",
    updatedAt: "2023-05-14T15:45:00Z",
    notes: "Network requested additional information",
    feedback: "Please provide traffic statistics and marketing plan.",
    responseDeadline: "2023-05-21T15:45:00Z",
  },
  {
    id: "app-status-7",
    networkId: "network-7",
    networkName: "Rakuten Advertising",
    avatar: "home",
    status: "in_review",
    submittedAt: "2023-05-12T10:30:00Z",
    updatedAt: "2023-05-15T11:15:00Z",
    notes: "Application under review by network team",
    estimatedResponse: "2023-05-22T10:30:00Z",
  },
]

export function ApplicationStatus() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [avatarFilter, setAvatarFilter] = useState("all")
  const [isResponseOpen, setIsResponseOpen] = useState(false)
  const [currentApp, setCurrentApp] = useState<string | null>(null)
  const [responseText, setResponseText] = useState("")
  const [isSubmittingResponse, setIsSubmittingResponse] = useState(false)

  // Filter applications based on search and filters
  const filteredApplications = applicationStatus.filter((app) => {
    const matchesSearch =
      app.networkName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.notes.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || app.status === statusFilter
    const matchesAvatar = avatarFilter === "all" || app.avatar === avatarFilter

    return matchesSearch && matchesStatus && matchesAvatar
  })

  // Get status badge and icon
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Approved</Badge>
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
            Pending
          </Badge>
        )
      case "in_review":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-200">
            In Review
          </Badge>
        )
      case "additional_info":
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-800 hover:bg-purple-200">
            Info Needed
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle2 className="h-5 w-5 text-green-500" />
      case "rejected":
        return <XCircle className="h-5 w-5 text-red-500" />
      case "pending":
        return <Clock className="h-5 w-5 text-yellow-500" />
      case "in_review":
        return <RefreshCw className="h-5 w-5 text-blue-500" />
      case "additional_info":
        return <AlertTriangle className="h-5 w-5 text-purple-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  // Handle submitting additional information
  const handleSubmitResponse = () => {
    if (!responseText) {
      toast({
        title: "Response Required",
        description: "Please provide a response before submitting.",
        variant: "destructive",
      })
      return
    }

    setIsSubmittingResponse(true)

    // Simulate API call
    setTimeout(() => {
      setIsSubmittingResponse(false)
      setIsResponseOpen(false)
      setCurrentApp(null)
      setResponseText("")

      toast({
        title: "Response Submitted",
        description: "Your response has been submitted to the affiliate network.",
      })
    }, 1500)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Application Status Tracker</CardTitle>
            <CardDescription>Track the status of your affiliate network applications</CardDescription>
          </div>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search applications..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex flex-1 items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="in_review">In Review</SelectItem>
                  <SelectItem value="additional_info">Info Needed</SelectItem>
                </SelectContent>
              </Select>
              <Select value={avatarFilter} onValueChange={setAvatarFilter}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Filter by avatar" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Avatars</SelectItem>
                  <SelectItem value="travel">Travel</SelectItem>
                  <SelectItem value="fitness">Fitness</SelectItem>
                  <SelectItem value="health">Health</SelectItem>
                  <SelectItem value="home">Home</SelectItem>
                  <SelectItem value="entrepreneur">Entrepreneur</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <ScrollArea className="h-[500px]">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">Status</TableHead>
                    <TableHead>Network</TableHead>
                    <TableHead>Avatar</TableHead>
                    <TableHead>Submitted</TableHead>
                    <TableHead>Last Updated</TableHead>
                    <TableHead>Notes</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredApplications.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        No applications found matching your filters.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredApplications.map((app) => (
                      <TableRow key={app.id}>
                        <TableCell>{getStatusIcon(app.status)}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Globe className="h-4 w-4 text-muted-foreground" />
                            <span>{app.networkName}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="capitalize">
                            {app.avatar}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3 text-muted-foreground" />
                            <span>{formatDate(app.submittedAt)}</span>
                          </div>
                        </TableCell>
                        <TableCell>{formatDate(app.updatedAt)}</TableCell>
                        <TableCell className="max-w-[200px] truncate" title={app.notes}>
                          {app.notes}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => (window.location.href = `/affiliate-networks/status/${app.id}`)}
                              >
                                View Details
                              </DropdownMenuItem>
                              {app.status === "additional_info" && (
                                <DropdownMenuItem
                                  onClick={() => {
                                    setCurrentApp(app.id)
                                    setIsResponseOpen(true)
                                  }}
                                >
                                  <MessageSquare className="mr-2 h-4 w-4" />
                                  Provide Information
                                </DropdownMenuItem>
                              )}
                              {app.status === "rejected" && app.retryEligible && (
                                <DropdownMenuItem
                                  onClick={() => (window.location.href = `/affiliate-networks/retry/${app.id}`)}
                                >
                                  <RefreshCw className="mr-2 h-4 w-4" />
                                  Retry Application
                                </DropdownMenuItem>
                              )}
                              {app.status === "approved" && (
                                <DropdownMenuItem
                                  onClick={() => (window.location.href = `/affiliate-networks/setup/${app.id}`)}
                                >
                                  <CheckCircle2 className="mr-2 h-4 w-4" />
                                  Setup Account
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </ScrollArea>
          </div>
        </div>

        {/* Response Dialog */}
        <Dialog open={isResponseOpen} onOpenChange={setIsResponseOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Provide Additional Information</DialogTitle>
              <DialogDescription>
                The affiliate network has requested additional information for your application
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="network-feedback">Network Request</Label>
                <div className="rounded-md bg-muted p-3 text-sm">
                  {(currentApp && applicationStatus.find((app) => app.id === currentApp)?.feedback) ||
                    "Please provide the requested information."}
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="response-text">Your Response</Label>
                <Textarea
                  id="response-text"
                  placeholder="Enter your response..."
                  className="min-h-[150px]"
                  value={responseText}
                  onChange={(e) => setResponseText(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsResponseOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSubmitResponse} disabled={isSubmittingResponse}>
                {isSubmittingResponse ? "Submitting..." : "Submit Response"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}

"use client"

import type React from "react"

import Link from "next/link"
import { usePathname } from "next/navigation"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useAuth } from "@/contexts/auth-context"
import {
  BarChart3,
  Calendar,
  CreditCard,
  FileText,
  Home,
  Package,
  User,
  Settings,
  TrendingUp,
  Zap,
  Info,
  AlertCircle,
  Bot,
  Network,
  Tag,
  Lightbulb,
  Mail,
  Shield,
  BarChart,
  Users,
  ShoppingBag,
  Activity,
  FolderTree,
  BookOpen,
  FileEdit,
  Image,
  Crown,
} from "lucide-react"

interface SidebarNavProps extends React.HTMLAttributes<HTMLElement> {
  items?: {
    href: string
    title: string
    icon: React.ReactNode
    adminOnly?: boolean
  }[]
}

// Update the sidebarNavItems array to include the Operations page
export const sidebarNavItems = [
  {
    href: "/dashboard",
    title: "Dashboard",
    icon: <Home className="mr-2 h-4 w-4" />,
  },
  {
    href: "/categories",
    title: "Category Management",
    icon: <FolderTree className="mr-2 h-4 w-4" />,
    adminOnly: true,
  },
  {
    href: "/blog-categories",
    title: "Blog Categories",
    icon: <BookOpen className="mr-2 h-4 w-4" />,
    adminOnly: true,
  },
  {
    href: "/blog-management",
    title: "Blog Management",
    icon: <FileEdit className="mr-2 h-4 w-4" />,
    adminOnly: true,
  },
  {
    href: "/products",
    title: "Product Management",
    icon: <ShoppingBag className="mr-2 h-4 w-4" />,
  },
  {
    href: "/prototypes",
    title: "Prototype Management",
    icon: <Package className="mr-2 h-4 w-4" />,
  },
  {
    href: "/analysis-queue",
    title: "Analysis Queue Report",
    icon: <Activity className="mr-2 h-4 w-4" />,
  },
  {
    href: "/content-creator",
    title: "Create Content & AI Studio",
    icon: <Lightbulb className="mr-2 h-4 w-4" />,
  },
  {
    href: "/subscription",
    title: "My Subscription",
    icon: <Crown className="mr-2 h-4 w-4" />,
  },
  {
    href: "/media-management",
    title: "Media Management",
    icon: <Image className="mr-2 h-4 w-4" />,
    adminOnly: true,
  },
  
  {
    href: "/avatars",
    title: "Avatar Management",
    icon: <Users className="mr-2 h-4 w-4" />,
    adminOnly: true,
  },
  {
    href: "/user-management",
    title: "User Management",
    icon: <Shield className="mr-2 h-4 w-4" />,
    adminOnly: true,
  },
  {
    href: "/plan-management",
    title: "Plan Management",
    icon: <Crown className="mr-2 h-4 w-4" />,
    adminOnly: true,
  },
  {
    href: "/order-history",
    title: "Order History",
    icon: <CreditCard className="mr-2 h-4 w-4" />,
    adminOnly: true,
  },
  {
    href: "/content",
    title: "Content Queue",
    icon: <FileText className="mr-2 h-4 w-4" />,
  },
  // {
  //   href: "/trends",
  //   title: "Trends",
  //   icon: <TrendingUp className="mr-2 h-4 w-4" />,
  // },
  // TEMPORARILY DISABLED - Calendar & Analytics
  // {
  //   href: "/calendar",
  //   title: "Calendar",
  //   icon: <Calendar className="mr-2 h-4 w-4" />,
  // },
  // {
  //   href: "/analytics",
  //   title: "Analytics",
  //   icon: <BarChart3 className="mr-2 h-4 w-4" />,
  // },
  
  // TEMPORARILY DISABLED - CRM, Operations, Compliance, Info Center
  // {
  //   href: "/crm",
  //   title: "CRM & Email",
  //   icon: <Mail className="mr-2 h-4 w-4" />,
  // },
  // {
  //   href: "/operations",
  //   title: "Ops & Resources",
  //   icon: <BarChart className="mr-2 h-4 w-4" />,
  // },
  // {
  //   href: "/compliance",
  //   title: "Compliance & Audit",
  //   icon: <Shield className="mr-2 h-4 w-4" />,
  // },
  // {
  //   href: "/info",
  //   title: "Info Center",
  //   icon: <Info className="mr-2 h-4 w-4" />,
  // },
  
  // TEMPORARILY DISABLED - System Check
  // {
  //   href: "/system-check",
  //   title: "System Check",
  //   icon: <AlertCircle className="mr-2 h-4 w-4" />,
  // },
  // {
  //   href: "/agents",
  //   title: "Agent Management",
  //   icon: <Bot className="mr-2 h-4 w-4" />,
  // },
  {
    href: "/profile",
    title: "Profile",
    icon: <User className="mr-2 h-4 w-4" />,
  },
  // TEMPORARILY DISABLED - Affiliate Networks, White Label, Feedback & Learning
  // {
  //   href: "/affiliate-networks",
  //   title: "Affiliate Networks",
  //   icon: <Network className="mr-2 h-4 w-4" />,
  // },
  // {
  //   href: "/white-label",
  //   title: "White Label",
  //   icon: <Tag className="mr-2 h-4 w-4" />,
  // },
  // {
  //   href: "/feedback-loop",
  //   title: "Feedback & Learning",
  //   icon: <Lightbulb className="mr-2 h-4 w-4" />,
  // },
  {
    href: "/settings",
    title: "Settings",
    icon: <Settings className="mr-2 h-4 w-4" />,
  },
]

export function SidebarNav({ className, items, ...props }: SidebarNavProps) {
  const pathname = usePathname()
  const { user } = useAuth()

  const navItems = items || sidebarNavItems

  // Filter out admin-only items for non-admin users
  const filteredNavItems = navItems.filter(item => {
    if (item.adminOnly && user?.role !== 'admin') {
      return false
    }
    return true
  })

  return (
    <nav className={cn("flex flex-col space-y-1", className)} {...props}>
      {filteredNavItems.map((item) => (
        <Button
          key={item.href}
          variant={pathname === item.href ? "secondary" : "ghost"}
          className={cn(
            "w-full justify-start",
            pathname === item.href ? "bg-muted hover:bg-muted" : "hover:bg-transparent hover:underline",
          )}
          asChild
        >
          <Link href={item.href}>
            {item.icon}
            {item.title}
          </Link>
        </Button>
      ))}
    </nav>
  )
}

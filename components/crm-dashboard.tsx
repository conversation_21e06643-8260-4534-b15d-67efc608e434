"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { AvatarContactLists } from "@/components/avatar-contact-lists"
import { FunnelView } from "@/components/funnel-view"
import { EmailSequenceBuilder } from "@/components/email-sequence-builder"
import { EngagementHeatmap } from "@/components/engagement-heatmap"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Download, Upload, RefreshCw, Users, Filter } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export function CrmDashboard() {
  const [activeTab, setActiveTab] = useState("contacts")
  const [selectedAvatar, setSelectedAvatar] = useState("all")

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-sage-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Subscribers</CardTitle>
            <Users className="h-4 w-4 text-sage-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-sage-900">24,892</div>
            <div className="text-xs text-sage-600">+2,345 this month</div>
          </CardContent>
        </Card>
        <Card className="bg-sage-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Open Rate</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-sage-600"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-sage-900">28.4%</div>
            <div className="text-xs text-sage-600">+2.1% from last month</div>
          </CardContent>
        </Card>
        <Card className="bg-sage-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Click Rate</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-sage-600"
            >
              <rect width="20" height="14" x="2" y="5" rx="2" />
              <path d="M2 10h20" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-sage-900">12.8%</div>
            <div className="text-xs text-sage-600">+1.2% from last month</div>
          </CardContent>
        </Card>
        <Card className="bg-sage-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-sage-600"
            >
              <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-sage-900">18</div>
            <div className="text-xs text-sage-600">Across 5 avatars</div>
          </CardContent>
        </Card>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" className="h-8 gap-1">
            <Filter className="h-3.5 w-3.5" />
            <span>Filters</span>
          </Button>
          <Select value={selectedAvatar} onValueChange={setSelectedAvatar}>
            <SelectTrigger className="h-8 w-[150px]">
              <SelectValue placeholder="All Avatars" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Avatars</SelectItem>
              <SelectItem value="travel">Travel</SelectItem>
              <SelectItem value="home">Home</SelectItem>
              <SelectItem value="health">Health</SelectItem>
              <SelectItem value="fitness">Fitness</SelectItem>
              <SelectItem value="entrepreneur">Entrepreneur</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" className="h-8 gap-1">
            <Upload className="h-3.5 w-3.5" />
            <span>Import</span>
          </Button>
          <Button variant="outline" size="sm" className="h-8 gap-1">
            <Download className="h-3.5 w-3.5" />
            <span>Export</span>
          </Button>
          <Button variant="outline" size="icon" className="h-8 w-8">
            <RefreshCw className="h-3.5 w-3.5" />
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="bg-sage-100">
          <TabsTrigger value="contacts" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
            Contact Lists
          </TabsTrigger>
          <TabsTrigger value="funnel" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
            Funnel View
          </TabsTrigger>
          <TabsTrigger value="sequences" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
            Email Sequences
          </TabsTrigger>
          <TabsTrigger value="heatmap" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
            Engagement Heatmap
          </TabsTrigger>
        </TabsList>

        <TabsContent value="contacts">
          <AvatarContactLists selectedAvatar={selectedAvatar} />
        </TabsContent>

        <TabsContent value="funnel">
          <FunnelView selectedAvatar={selectedAvatar} />
        </TabsContent>

        <TabsContent value="sequences">
          <EmailSequenceBuilder selectedAvatar={selectedAvatar} />
        </TabsContent>

        <TabsContent value="heatmap">
          <EngagementHeatmap selectedAvatar={selectedAvatar} />
        </TabsContent>
      </Tabs>
    </div>
  )
}

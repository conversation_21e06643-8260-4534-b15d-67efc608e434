"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import {
  Search,
  LayoutDashboard,
  Calendar,
  TrendingUp,
  BarChart3,
  Sparkles,
  Package,
  FileText,
  Settings,
  ExternalLink,
  PlayCircle,
  Lightbulb,
  HelpCircle,
  BookOpen,
} from "lucide-react"

export function InfoCenter() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("dashboard")

  const filterContent = (content: string) => {
    if (!searchQuery) return true
    return content.toLowerCase().includes(searchQuery.toLowerCase())
  }

  return (
    <Card className="bg-white">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Command Center Guide</CardTitle>
            <CardDescription>Learn how to use all features of your Command Center</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search guides..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button variant="outline" size="sm" className="gap-1">
              <BookOpen className="h-4 w-4" />
              <span>Full Documentation</span>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="bg-sage-100 flex flex-wrap h-auto py-1">
            <TabsTrigger
              value="dashboard"
              className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900 gap-1"
            >
              <LayoutDashboard className="h-4 w-4" />
              <span>Dashboard</span>
            </TabsTrigger>
            <TabsTrigger
              value="content"
              className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900 gap-1"
            >
              <FileText className="h-4 w-4" />
              <span>Content</span>
            </TabsTrigger>
            <TabsTrigger
              value="calendar"
              className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900 gap-1"
            >
              <Calendar className="h-4 w-4" />
              <span>Calendar</span>
            </TabsTrigger>
            <TabsTrigger
              value="products"
              className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900 gap-1"
            >
              <Package className="h-4 w-4" />
              <span>Products</span>
            </TabsTrigger>
            <TabsTrigger
              value="trends"
              className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900 gap-1"
            >
              <TrendingUp className="h-4 w-4" />
              <span>Trends</span>
            </TabsTrigger>
            <TabsTrigger
              value="ai-studio"
              className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900 gap-1"
            >
              <Sparkles className="h-4 w-4" />
              <span>AI Studio</span>
            </TabsTrigger>
            <TabsTrigger
              value="analytics"
              className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900 gap-1"
            >
              <BarChart3 className="h-4 w-4" />
              <span>Analytics</span>
            </TabsTrigger>
            <TabsTrigger
              value="settings"
              className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900 gap-1"
            >
              <Settings className="h-4 w-4" />
              <span>Settings</span>
            </TabsTrigger>
          </TabsList>

          <ScrollArea className="h-[500px] pr-4">
            <TabsContent value="dashboard" className="space-y-4 mt-0">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge className="bg-sage-100 text-sage-800">Overview</Badge>
                  <h3 className="text-lg font-medium">Command Center Dashboard</h3>
                </div>

                <p>
                  The Command Center Dashboard is your central hub for managing all aspects of your avatar-based content
                  and product strategy. It provides a comprehensive overview of your performance metrics, content
                  schedule, and product analytics.
                </p>

                <div className="rounded-md overflow-hidden border">
                  <img
                    src="/placeholder.svg?height=300&width=600"
                    alt="Dashboard Overview"
                    className="w-full object-cover"
                  />
                  <div className="bg-sage-50 p-2 text-xs text-sage-700">
                    Command Center Dashboard with key performance metrics
                  </div>
                </div>

                <Accordion type="single" collapsible className="w-full">
                  {filterContent("key performance metrics revenue engagement campaigns conversion") && (
                    <AccordionItem value="item-1">
                      <AccordionTrigger className="text-base">Key Performance Metrics</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>
                          The top section of your dashboard displays key performance metrics that give you an
                          at-a-glance view of your business health:
                        </p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Total Revenue:</strong> Your overall revenue across all avatars and products
                          </li>
                          <li>
                            <strong>Engagement:</strong> Total engagement metrics across all platforms
                          </li>
                          <li>
                            <strong>Active Campaigns:</strong> Number of currently active content campaigns
                          </li>
                          <li>
                            <strong>Conversion Rate:</strong> Average conversion rate across all products
                          </li>
                        </ul>
                        <div className="flex items-center gap-2 mt-2">
                          <Button variant="outline" size="sm" className="gap-1">
                            <PlayCircle className="h-4 w-4" />
                            <span>Watch Tutorial</span>
                          </Button>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("avatar performance tracking engagement followers growth") && (
                    <AccordionItem value="item-2">
                      <AccordionTrigger className="text-base">Avatar Performance</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>
                          The Avatar Performance section shows you how each of your avatar personas is performing across
                          different platforms:
                        </p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Engagement Metrics:</strong> Followers, likes, comments, and shares by platform
                          </li>
                          <li>
                            <strong>Growth Rate:</strong> Month-over-month growth percentage
                          </li>
                          <li>
                            <strong>Revenue Attribution:</strong> Revenue generated by each avatar
                          </li>
                        </ul>
                        <div className="mt-2 p-3 bg-sage-50 rounded-md border border-sage-200">
                          <div className="flex items-start gap-2">
                            <Lightbulb className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium">Pro Tip</p>
                              <p className="text-sm">
                                Focus your content creation efforts on the avatars showing the highest conversion rates,
                                not just the highest follower counts.
                              </p>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("content queue upcoming scheduled content platforms") && (
                    <AccordionItem value="item-3">
                      <AccordionTrigger className="text-base">Content Queue</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>The Content Queue displays your upcoming scheduled content across all platforms:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Scheduled Posts:</strong> View all upcoming content with dates and times
                          </li>
                          <li>
                            <strong>Platform Distribution:</strong> See which platforms each piece of content is
                            scheduled for
                          </li>
                          <li>
                            <strong>Avatar Assignment:</strong> Which avatar is associated with each content piece
                          </li>
                        </ul>
                        <p className="mt-2">
                          You can click on any content item to edit, reschedule, or delete it. Use the "Create New
                          Content" button to add new content to your queue.
                        </p>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent(
                    "tabs navigation overview avatars content products calendar trends analytics sales",
                  ) && (
                    <AccordionItem value="item-4">
                      <AccordionTrigger className="text-base">Dashboard Tabs</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>The dashboard tabs allow you to quickly navigate between different views:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Overview:</strong> The main dashboard view with key metrics
                          </li>
                          <li>
                            <strong>Avatars:</strong> Detailed view of all your avatar personas
                          </li>
                          <li>
                            <strong>Content:</strong> Content management and scheduling
                          </li>
                          <li>
                            <strong>Products:</strong> Product catalog and performance
                          </li>
                          <li>
                            <strong>Calendar:</strong> Calendar view of all scheduled content
                          </li>
                          <li>
                            <strong>Trends:</strong> Trending topics and content opportunities
                          </li>
                          <li>
                            <strong>Analytics:</strong> Detailed performance analytics
                          </li>
                          <li>
                            <strong>Sales:</strong> Sales data and revenue tracking
                          </li>
                        </ul>
                      </AccordionContent>
                    </AccordionItem>
                  )}
                </Accordion>
              </div>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4 mt-0">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge className="bg-sage-100 text-sage-800">Overview</Badge>
                  <h3 className="text-lg font-medium">Analytics Dashboard</h3>
                </div>

                <p>
                  The Analytics Dashboard provides comprehensive data analysis tools to track performance, measure
                  conversions, and identify growth opportunities across all your avatars and products.
                </p>

                <div className="rounded-md overflow-hidden border">
                  <img
                    src="/placeholder.svg?height=300&width=600"
                    alt="Analytics Dashboard"
                    className="w-full object-cover"
                  />
                  <div className="bg-sage-50 p-2 text-xs text-sage-700">
                    Analytics Dashboard with performance metrics and data visualization
                  </div>
                </div>

                <Accordion type="single" collapsible className="w-full">
                  {filterContent("data export options excel csv pdf") && (
                    <AccordionItem value="item-1">
                      <AccordionTrigger className="text-base">Data Export Options</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>
                          The Command Center allows you to export your analytics data in multiple formats for further
                          analysis or reporting:
                        </p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Quick Export:</strong> Use the Export button to quickly download data in Excel, CSV,
                            or PDF format
                          </li>
                          <li>
                            <strong>Advanced Export:</strong> Configure custom export options including:
                            <ul className="list-circle list-inside ml-6 space-y-1">
                              <li>Date range selection</li>
                              <li>Include/exclude charts and visualizations</li>
                              <li>Include raw data</li>
                              <li>Format-specific options</li>
                            </ul>
                          </li>
                        </ul>
                        <div className="mt-2 p-3 bg-sage-50 rounded-md border border-sage-200">
                          <div className="flex items-start gap-2">
                            <Lightbulb className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium">Pro Tip</p>
                              <p className="text-sm">
                                Use Excel exports for data you plan to manipulate further, PDF for reports you'll share
                                with stakeholders, and CSV for importing into other analytics tools.
                              </p>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("product analytics revenue units sold qr link conversions growth") && (
                    <AccordionItem value="item-2">
                      <AccordionTrigger className="text-base">Product Analytics</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>The Product Analytics tab provides detailed performance data for each product:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Revenue Tracking:</strong> Monitor revenue by product, avatar, and category
                          </li>
                          <li>
                            <strong>Units Sold:</strong> Track quantity of products sold over time
                          </li>
                          <li>
                            <strong>Conversion Comparison:</strong> Compare QR code vs. link conversion rates
                          </li>
                          <li>
                            <strong>Growth Trends:</strong> View growth percentages and identify top performers
                          </li>
                        </ul>
                        <p className="mt-2">
                          Use the filters at the top to narrow down by avatar, category, or date range. Toggle between
                          table and chart views using the view mode buttons.
                        </p>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("avatar performance revenue products conversion qr link growth platform") && (
                    <AccordionItem value="item-3">
                      <AccordionTrigger className="text-base">Avatar Performance</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>The Avatar Performance tab shows metrics for each of your avatar personas:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Revenue by Avatar:</strong> Total revenue generated by each avatar
                          </li>
                          <li>
                            <strong>Product Count:</strong> Number of products associated with each avatar
                          </li>
                          <li>
                            <strong>Conversion Rates:</strong> Average, QR, and link conversion rates
                          </li>
                          <li>
                            <strong>Growth Rate:</strong> Period-over-period growth percentage
                          </li>
                          <li>
                            <strong>Top Platform:</strong> Best performing platform for each avatar
                          </li>
                        </ul>
                        <div className="mt-2 p-3 bg-sage-50 rounded-md border border-sage-200">
                          <div className="flex items-start gap-2">
                            <HelpCircle className="h-5 w-5 text-sage-600 flex-shrink-0 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium">How to Use This Data</p>
                              <p className="text-sm">
                                Identify your highest-performing avatars and analyze what makes them successful. Apply
                                those insights to optimize your lower-performing avatars.
                              </p>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("projections revenue forecast trend strength confidence") && (
                    <AccordionItem value="item-4">
                      <AccordionTrigger className="text-base">Revenue Projections</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>
                          The Revenue Projections tab provides forecasted revenue based on trend strength and historical
                          data:
                        </p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Current Revenue:</strong> Baseline revenue for comparison
                          </li>
                          <li>
                            <strong>30-Day Projection:</strong> Forecasted revenue for the next 30 days
                          </li>
                          <li>
                            <strong>90-Day Projection:</strong> Forecasted revenue for the next 90 days
                          </li>
                          <li>
                            <strong>Trend Strength:</strong> Confidence indicator based on market trends
                          </li>
                          <li>
                            <strong>Confidence Rating:</strong> High, Medium, or Low confidence in the projection
                          </li>
                        </ul>
                        <p className="mt-2">
                          Use these projections for inventory planning, content strategy, and budgeting. The "Optimize"
                          button provides AI-powered recommendations to improve your projected revenue.
                        </p>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("conversion tracking qr code link scans clicks revenue best method") && (
                    <AccordionItem value="item-5">
                      <AccordionTrigger className="text-base">Conversion Tracking</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>The Conversion Tracking tab compares QR code vs. link conversions across platforms:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>QR Code Metrics:</strong> Scans, conversion rate, and revenue
                          </li>
                          <li>
                            <strong>Link Metrics:</strong> Clicks, conversion rate, and revenue
                          </li>
                          <li>
                            <strong>Best Method:</strong> Automatically identifies the better-performing method
                          </li>
                          <li>
                            <strong>Platform Breakdown:</strong> Conversion data by platform (Instagram, YouTube, etc.)
                          </li>
                        </ul>
                        <div className="mt-2 p-3 bg-sage-50 rounded-md border border-sage-200">
                          <div className="flex items-start gap-2">
                            <Lightbulb className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium">Pro Tip</p>
                              <p className="text-sm">
                                For products with significant differences between QR and link performance, consider
                                creating platform-specific strategies. For example, use QR codes predominantly on
                                YouTube if they perform better there.
                              </p>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("product suggestions emerging markets opportunities") && (
                    <AccordionItem value="item-6">
                      <AccordionTrigger className="text-base">Product Suggestions</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>
                          The Product Suggestions tab provides AI-powered recommendations for new products and markets:
                        </p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Product Recommendations:</strong> Suggested products based on trends
                          </li>
                          <li>
                            <strong>Market Opportunity:</strong> High, Medium, or Emerging opportunity rating
                          </li>
                          <li>
                            <strong>Estimated Revenue:</strong> Projected revenue for suggested products
                          </li>
                          <li>
                            <strong>Competition Level:</strong> Assessment of market competition
                          </li>
                          <li>
                            <strong>Emerging Markets:</strong> New market opportunities with growth rates
                          </li>
                        </ul>
                        <p className="mt-2">
                          Review these suggestions regularly as they update based on the latest trend data. Use the
                          "Research" button to get more detailed market analysis for any suggestion.
                        </p>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("automation events workflow tracking success rate status") && (
                    <AccordionItem value="item-7">
                      <AccordionTrigger className="text-base">Automation Tracking</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>The Automation tab tracks all automated events between your connected programs:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Event Monitoring:</strong> Track all automated workflows
                          </li>
                          <li>
                            <strong>Success Rate:</strong> Percentage of successful automation runs
                          </li>
                          <li>
                            <strong>Status Indicators:</strong> Active, Warning, or Error status for each automation
                          </li>
                          <li>
                            <strong>Improvement Suggestions:</strong> AI-powered recommendations to enhance automation
                          </li>
                        </ul>
                        <p className="mt-2">
                          Pay special attention to automations with Warning or Error status. Use the "Fix Issues" button
                          to troubleshoot problems and the "Optimize" button to improve performance.
                        </p>
                      </AccordionContent>
                    </AccordionItem>
                  )}
                </Accordion>
              </div>
            </TabsContent>

            <TabsContent value="trends" className="space-y-4 mt-0">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge className="bg-sage-100 text-sage-800">Overview</Badge>
                  <h3 className="text-lg font-medium">Trends Tracker</h3>
                </div>

                <p>
                  The Trends Tracker helps you identify trending topics, monitor market opportunities, and align your
                  content strategy with current consumer interests.
                </p>

                <Accordion type="single" collapsible className="w-full">
                  {filterContent("trending topics keyword volume growth rate trend strength") && (
                    <AccordionItem value="item-1">
                      <AccordionTrigger className="text-base">Trending Topics</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>The Trending Topics section shows current popular topics relevant to your avatars:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Keyword:</strong> The trending topic or phrase
                          </li>
                          <li>
                            <strong>Category:</strong> Which of your categories the trend relates to
                          </li>
                          <li>
                            <strong>Platforms:</strong> Where the trend is most popular
                          </li>
                          <li>
                            <strong>Volume:</strong> Search or mention volume per day
                          </li>
                          <li>
                            <strong>Growth Rate:</strong> How quickly the trend is growing
                          </li>
                          <li>
                            <strong>Trend Strength:</strong> Overall strength indicator
                          </li>
                        </ul>
                        <p className="mt-2">
                          Use the platform filter at the top to view trends specific to YouTube, Instagram, Twitter, or
                          Facebook. The "Create Content" button lets you quickly generate content based on a trending
                          topic.
                        </p>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("affiliate opportunities product matches commission") && (
                    <AccordionItem value="item-2">
                      <AccordionTrigger className="text-base">Affiliate Opportunities</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>Each trend card includes affiliate product matches that align with the trending topic:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Product Matches:</strong> Products that relate to the trending topic
                          </li>
                          <li>
                            <strong>Commission Rate:</strong> Potential earnings from each product
                          </li>
                          <li>
                            <strong>Actions:</strong> Quick actions to create content or view product details
                          </li>
                        </ul>
                        <div className="mt-2 p-3 bg-sage-50 rounded-md border border-sage-200">
                          <div className="flex items-start gap-2">
                            <Lightbulb className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium">Pro Tip</p>
                              <p className="text-sm">
                                Prioritize trends with both high growth rate and strong affiliate matches for the best
                                revenue potential. Create content that naturally incorporates these affiliate products.
                              </p>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("trend metrics active trends highest volume fastest growing affiliate matches") && (
                    <AccordionItem value="item-3">
                      <AccordionTrigger className="text-base">Trend Metrics</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>The metrics cards at the top of the Trends Tracker provide key insights:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Active Trends:</strong> Total number of relevant trends being tracked
                          </li>
                          <li>
                            <strong>Highest Volume:</strong> The trend with the most searches or mentions
                          </li>
                          <li>
                            <strong>Fastest Growing:</strong> The trend with the highest growth rate
                          </li>
                          <li>
                            <strong>Affiliate Matches:</strong> Total number of product matches across all trends
                          </li>
                        </ul>
                        <p className="mt-2">
                          These metrics update in real-time as you filter by platform or category. Use the "Refresh
                          Trends" button to get the latest data.
                        </p>
                      </AccordionContent>
                    </AccordionItem>
                  )}
                </Accordion>
              </div>
            </TabsContent>

            <TabsContent value="ai-studio" className="space-y-4 mt-0">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge className="bg-sage-100 text-sage-800">Overview</Badge>
                  <h3 className="text-lg font-medium">AI Content Studio</h3>
                </div>

                <p>
                  The AI Content Studio helps you create, produce, and repurpose content using integrated AI tools
                  including Poppy AI for scriptwriting, LTX Studio for video production, and OpusClips for content
                  repurposing.
                </p>

                <Accordion type="single" collapsible className="w-full">
                  {filterContent("script generation poppy ai trending topic custom prompt avatar") && (
                    <AccordionItem value="item-1">
                      <AccordionTrigger className="text-base">Script Generation</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>The Script Generation tab allows you to create content scripts using Poppy AI:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Trending Topics:</strong> Select from current trending topics
                          </li>
                          <li>
                            <strong>Custom Prompts:</strong> Enter your own content ideas
                          </li>
                          <li>
                            <strong>Avatar Selection:</strong> Choose which avatar persona to write for
                          </li>
                          <li>
                            <strong>Script Options:</strong> Configure hooks, CTAs, and SEO optimization
                          </li>
                        </ul>
                        <p className="mt-2">
                          After generating a script, you can edit it directly in the editor before sending it to LTX
                          Studio for video production. Click "Generate Script with Poppy AI" to start the process.
                        </p>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("video production ltx studio voice generation") && (
                    <AccordionItem value="item-2">
                      <AccordionTrigger className="text-base">Video Production</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>The Video Production tab interfaces with LTX Studio to create videos from your scripts:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Production Status:</strong> Track the progress of your video creation
                          </li>
                          <li>
                            <strong>Video Settings:</strong> Configure aspect ratio and resolution
                          </li>
                          <li>
                            <strong>Preview:</strong> View your completed video before repurposing
                          </li>
                        </ul>
                        <div className="mt-2 p-3 bg-sage-50 rounded-md border border-sage-200">
                          <div className="flex items-start gap-2">
                            <HelpCircle className="h-5 w-5 text-sage-600 flex-shrink-0 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium">How It Works</p>
                              <p className="text-sm">
                                LTX Studio processes your script in three steps: script analysis, voice generation, and
                                video production. The process typically takes 5-10 minutes depending on video length.
                              </p>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("content repurposing opusclips short form clips highlights audiogram transcript") && (
                    <AccordionItem value="item-3">
                      <AccordionTrigger className="text-base">Content Repurposing</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>
                          The Content Repurposing tab uses OpusClips to create multiple content formats from your video:
                        </p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Repurposing Options:</strong> Select which formats to create
                          </li>
                          <li>
                            <strong>Number of Clips:</strong> Choose how many short-form clips to generate
                          </li>
                          <li>
                            <strong>Editing Style:</strong> Select from different editing styles
                          </li>
                          <li>
                            <strong>Output Formats:</strong> Short clips, highlights, audiograms, and transcripts
                          </li>
                        </ul>
                        <p className="mt-2">
                          After processing, you can download all generated content or schedule it directly to your
                          social media platforms. Click "Start Repurposing" to begin the process.
                        </p>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("api connections poppy ai ltx studio opusclips") && (
                    <AccordionItem value="item-4">
                      <AccordionTrigger className="text-base">API Connections</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>To use the AI Content Studio, you need to connect your API services:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Poppy AI:</strong> For scriptwriting and content suggestions
                          </li>
                          <li>
                            <strong>LTX Studio:</strong> For video production
                          </li>
                          <li>
                            <strong>OpusClips:</strong> For content repurposing
                          </li>
                        </ul>
                        <p className="mt-2">
                          Click the "Connect APIs" button in the top right corner of the AI Studio page to set up your
                          connections. You'll need to enter your API keys for each service.
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <Button variant="outline" size="sm" className="gap-1">
                            <ExternalLink className="h-4 w-4" />
                            <span>Get API Keys</span>
                          </Button>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  )}
                </Accordion>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4 mt-0">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge className="bg-sage-100 text-sage-800">Overview</Badge>
                  <h3 className="text-lg font-medium">Website Integration</h3>
                </div>

                <p>
                  The Website Integration Guide helps you connect your Command Center to your website to track
                  conversions, display products, and synchronize data.
                </p>

                <Accordion type="single" collapsible className="w-full">
                  {filterContent("api integration generate api keys install sdk initialize test connection") && (
                    <AccordionItem value="item-1">
                      <AccordionTrigger className="text-base">API Integration</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>Follow these steps to integrate your website with the Command Center API:</p>
                        <ol className="list-decimal list-inside space-y-1 ml-4">
                          <li>
                            <strong>Generate API Keys:</strong> Create authentication keys for your website
                          </li>
                          <li>
                            <strong>Install the SDK:</strong> Add the Command Center SDK to your website
                          </li>
                          <li>
                            <strong>Initialize the SDK:</strong> Configure the SDK with your API keys
                          </li>
                          <li>
                            <strong>Test the Connection:</strong> Verify that your website is properly connected
                          </li>
                        </ol>
                        <p className="mt-2">
                          Copy the code snippets provided in the integration guide and add them to your website. The SDK
                          is available for JavaScript, React, and other popular frameworks.
                        </p>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("tracking setup qr code link tracking event tracking") && (
                    <AccordionItem value="item-2">
                      <AccordionTrigger className="text-base">Tracking Setup</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>Set up conversion tracking to monitor performance from different sources:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>QR Code Tracking:</strong> Generate and track QR codes for physical or digital use
                          </li>
                          <li>
                            <strong>Link Tracking:</strong> Create trackable links for comments, bio, or on-screen
                            placement
                          </li>
                          <li>
                            <strong>Event Tracking:</strong> Monitor user interactions like product views and
                            add-to-cart
                          </li>
                        </ul>
                        <div className="mt-2 p-3 bg-sage-50 rounded-md border border-sage-200">
                          <div className="flex items-start gap-2">
                            <Lightbulb className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
                            <div>
                              <p className="text-sm font-medium">Pro Tip</p>
                              <p className="text-sm">
                                Use different tracking parameters for each platform and placement to get granular
                                insights into which content and placement strategies perform best.
                              </p>
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("embed components product widgets trending content react components") && (
                    <AccordionItem value="item-3">
                      <AccordionTrigger className="text-base">Embed Components</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>Display Command Center data directly on your website with these embeddable components:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>Product Widgets:</strong> Show products from specific avatars
                          </li>
                          <li>
                            <strong>Trending Content:</strong> Display trending content from your Command Center
                          </li>
                          <li>
                            <strong>React Components:</strong> Use React components for deeper integration
                          </li>
                        </ul>
                        <p className="mt-2">
                          Copy the HTML or React code provided in the integration guide and add it to your website.
                          Customize the appearance using the theme options to match your website design.
                        </p>
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {filterContent("cms integration wordpress shopify other platforms") && (
                    <AccordionItem value="item-4">
                      <AccordionTrigger className="text-base">CMS Integration</AccordionTrigger>
                      <AccordionContent className="space-y-2">
                        <p>Integrate with popular content management systems:</p>
                        <ul className="list-disc list-inside space-y-1 ml-4">
                          <li>
                            <strong>WordPress:</strong> Install the Command Center plugin
                          </li>
                          <li>
                            <strong>Shopify:</strong> Add the Command Center app from the Shopify App Store
                          </li>
                          <li>
                            <strong>Other CMS:</strong> Use the universal embed code for any platform
                          </li>
                        </ul>
                        <p className="mt-2">
                          Follow the platform-specific instructions in the integration guide. Each integration method
                          provides the same tracking and embedding capabilities with platform-optimized implementation.
                        </p>
                      </AccordionContent>
                    </AccordionItem>
                  )}
                </Accordion>
              </div>
            </TabsContent>
          </ScrollArea>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" className="gap-1">
          <BookOpen className="h-4 w-4" />
          <span>View Full Documentation</span>
        </Button>
        <Button className="gap-1 bg-sage-700 hover:bg-sage-800 text-white">
          <PlayCircle className="h-4 w-4" />
          <span>Watch Video Tutorials</span>
        </Button>
      </CardFooter>
    </Card>
  )
}

'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { AuthLoading } from './auth-loading'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAuth?: boolean
  redirectTo?: string
}

export function ProtectedRoute({
  children,
  requireAuth = true,
  redirectTo = '/auth/login'
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading) {
      if (requireAuth && !isAuthenticated) {
        // Only redirect if not already on the target page
        if (window.location.pathname !== redirectTo) {
          router.replace(redirectTo)
        }
      } else if (!requireAuth && isAuthenticated) {
        // Only redirect if not already on dashboard
        if (!window.location.pathname.startsWith('/dashboard')) {
          router.replace('/dashboard')
        }
      }
    }
  }, [isAuthenticated, isLoading, requireAuth, redirectTo, router])

  if (isLoading) {
    return <AuthLoading message="Checking authentication..." />
  }

  if (requireAuth && !isAuthenticated) {
    return null
  }

  if (!requireAuth && isAuthenticated) {
    return null
  }

  return <>{children}</>
}

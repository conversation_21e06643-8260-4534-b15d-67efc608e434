'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import Link from 'next/link'
import { Eye, EyeOff, Loader2 } from 'lucide-react'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'

// Login form schema
const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
})

// Register form schema
const registerSchema = z.object({
  first_name: z.string().min(2, 'First name must be at least 2 characters'),
  last_name: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  password_confirmation: z.string().min(6, 'Password confirmation is required'),
}).refine((data) => data.password === data.password_confirmation, {
  message: "Passwords don't match",
  path: ["password_confirmation"],
})

// Forgot password form schema
const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
})

type LoginFormData = z.infer<typeof loginSchema>
type RegisterFormData = z.infer<typeof registerSchema>
type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>

interface AuthFormProps {
  type: 'login' | 'register' | 'forgot-password'
  onSubmit: (data: any) => Promise<boolean>
  isLoading?: boolean
}

export function AuthForm({ type, onSubmit, isLoading = false }: AuthFormProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const getSchema = () => {
    switch (type) {
      case 'login':
        return loginSchema
      case 'register':
        return registerSchema
      case 'forgot-password':
        return forgotPasswordSchema
      default:
        return loginSchema
    }
  }

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(getSchema()),
  })

  const handleFormSubmit = async (data: any) => {
    const success = await onSubmit(data)
    if (success && type !== 'login') {
      reset()
    }
  }

  const getTitle = () => {
    switch (type) {
      case 'login':
        return 'Welcome Back'
      case 'register':
        return 'Create Account'
      case 'forgot-password':
        return 'Reset Password'
      default:
        return 'Authentication'
    }
  }

  const getDescription = () => {
    switch (type) {
      case 'login':
        return 'Sign in to your Command Center account'
      case 'register':
        return 'Create a new Command Center account'
      case 'forgot-password':
        return 'Enter your email to receive password reset instructions'
      default:
        return ''
    }
  }

  const getSubmitText = () => {
    if (isLoading) {
      return (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          {type === 'login' ? 'Signing in...' : type === 'register' ? 'Creating account...' : 'Sending email...'}
        </>
      )
    }
    
    switch (type) {
      case 'login':
        return 'Sign In'
      case 'register':
        return 'Create Account'
      case 'forgot-password':
        return 'Send Reset Email'
      default:
        return 'Submit'
    }
  }

  return (
    <Card className="w-full max-w-lg bg-white/90 backdrop-blur-xl border-slate-200/50 shadow-2xl shadow-slate-500/20">
      <CardHeader className="space-y-1 pb-8">
        <CardTitle className="text-2xl font-bold text-center text-slate-800">{getTitle()}</CardTitle>
        <CardDescription className="text-center text-slate-600">{getDescription()}</CardDescription>
      </CardHeader>
      <CardContent className="px-8">
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {type === 'register' && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="first_name" className="text-slate-700 font-medium">First Name</Label>
                <Input
                  id="first_name"
                  type="text"
                  placeholder="Enter your first name"
                  {...register('first_name')}
                  disabled={isLoading}
                  className="bg-white/80 border-slate-300 text-slate-800 placeholder:text-slate-500 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200"
                />
                {errors.first_name && (
                  <p className="text-sm text-red-600">{errors.first_name.message as string}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="last_name" className="text-slate-700 font-medium">Last Name</Label>
                <Input
                  id="last_name"
                  type="text"
                  placeholder="Enter your last name"
                  {...register('last_name')}
                  disabled={isLoading}
                  className="bg-white/80 border-slate-300 text-slate-800 placeholder:text-slate-500 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200"
                />
                {errors.last_name && (
                  <p className="text-sm text-red-600">{errors.last_name.message as string}</p>
                )}
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="email" className="text-slate-700 font-medium">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              {...register('email')}
              disabled={isLoading}
              className="bg-white/80 border-slate-300 text-slate-800 placeholder:text-slate-500 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200"
            />
            {errors.email && (
              <p className="text-sm text-red-600">{errors.email.message as string}</p>
            )}
          </div>

          {type !== 'forgot-password' && (
            <div className="space-y-2">
              <Label htmlFor="password" className="text-slate-700 font-medium">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Enter your password"
                  {...register('password')}
                  disabled={isLoading}
                  className="bg-white/80 border-slate-300 text-slate-800 placeholder:text-slate-500 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 pr-12"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-slate-100 text-slate-600 hover:text-slate-800 transition-colors"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-600">{errors.password.message as string}</p>
              )}
            </div>
          )}

          {type === 'register' && (
            <div className="space-y-2">
              <Label htmlFor="password_confirmation" className="text-slate-700 font-medium">Confirm Password</Label>
              <div className="relative">
                <Input
                  id="password_confirmation"
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder="Confirm your password"
                  {...register('password_confirmation')}
                  disabled={isLoading}
                  className="bg-white/80 border-slate-300 text-slate-800 placeholder:text-slate-500 focus:border-blue-500 focus:ring-blue-500/20 transition-all duration-200 pr-12"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-slate-100 text-slate-600 hover:text-slate-800 transition-colors"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  disabled={isLoading}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {errors.password_confirmation && (
                <p className="text-sm text-red-600">{errors.password_confirmation.message as string}</p>
              )}
            </div>
          )}

          <Button
            type="submit"
            className="w-full "
            disabled={isLoading}
          >
            {getSubmitText()}
          </Button>
        </form>
      </CardContent>
      <CardFooter className="flex flex-col space-y-4 px-8 pb-8">
        {type === 'login' && (
          <>
            <Link
              href="/auth/forgot-password"
              className="text-sm text-slate-600 hover:text-slate-800 transition-colors duration-200 hover:underline text-center"
            >
              Forgot your password?
            </Link>
            <div className="text-sm text-slate-600 text-center">
              Don't have an account?{' '}
              <Link href="/auth/register" className="text-blue-600 hover:text-blue-700 transition-colors duration-200 hover:underline font-medium">
                Sign up
              </Link>
            </div>
          </>
        )}
        {type === 'register' && (
          <>
            {/* <div className="w-full p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg">
              <p className="text-sm font-medium text-green-800 mb-1">Registration:</p>
              <p className="text-xs text-green-700">Create a new account to access the AI-powered dashboard</p>
            </div> */}
            <div className="text-sm text-slate-600 text-center">
              Already have an account?{' '}
              <Link href="/auth/login" className="text-blue-600 hover:text-blue-700 transition-colors duration-200 hover:underline font-medium">
                Sign in
              </Link>
            </div>
          </>
        )}
        {type === 'forgot-password' && (
          <div className="text-sm text-slate-600 text-center">
            Remember your password?{' '}
            <Link href="/auth/login" className="text-blue-600 hover:text-blue-700 transition-colors duration-200 hover:underline font-medium">
              Sign in
            </Link>
          </div>
        )}
      </CardFooter>
    </Card>
  )
}

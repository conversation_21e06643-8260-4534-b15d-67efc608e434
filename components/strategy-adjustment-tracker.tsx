"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Plus, Search, Calendar, ArrowUpDown, CheckCircle2, XCircle, AlertTriangle } from "lucide-react"

type StrategyAdjustment = {
  id: string
  title: string
  category: string
  date: string
  reason: string
  changes: string
  status: "Planned" | "Implemented" | "Monitoring" | "Successful" | "Failed"
  impact: string
  metrics: Record<string, string>
}

export function StrategyAdjustmentTracker() {
  const [adjustments, setAdjustments] = useState<StrategyAdjustment[]>([
    {
      id: "1",
      title: "Shift marketing budget from Facebook to TikTok",
      category: "Marketing",
      date: "2023-04-15",
      reason: "TikTok showing 2.5x better ROI in recent campaigns",
      changes: "Reduced Facebook budget by 30%, increased TikTok budget by equivalent amount",
      status: "Monitoring",
      impact: "Initial 15% improvement in overall ad ROI",
      metrics: \{
        "Ad Spend": "$12,500 → $12,500",
        "Cost Per Acquisition": "$22.40 → $18.75",
        "Conversion Rate": "2.1% → 2.4%",
      \},
    \},
    \{
      id: "2",
      title: "Implement dynamic pricing strategy",
      category: "Pricing",
      date: "2023-03-10",
      reason: "Analysis showed significant demand fluctuations throughout the week",
      changes: "Created algorithm to adjust prices based on day of week and time of day",
      status: "Successful",
      impact: "Revenue increased by 18% with no negative impact on customer satisfaction",
      metrics: \{
        "Average Order Value": "$65.20 → $72.45",
        "Conversion Rate": "3.2% → 3.5%",
        "Revenue": "+18%",
      \},
    \},
    \{
      id: "3",
      title: "Redesign product page layout",
      category: "UX/UI",
      date: "2023-02-20",
      reason: "Heatmap analysis showed users weren't scrolling to key information",
      changes: "Moved product benefits above the fold, simplified layout",
      status: "Failed",
      impact: "No significant improvement in conversion rates after 4 weeks",
      metrics: \{
        "Time on Page": "1:45 → 2:10",
        "Conversion Rate": "2.8% → 2.9% (not significant)",
        "Bounce Rate": "45% → 42%",
      \},
    \},
    \{
      id: "4",
      title: "Launch email re-engagement campaign",
      category: "Email Marketing",
      date: "2023-01-15",
      reason: "22% of customers become dormant after 60 days",
      changes: "Created 3-stage automated email sequence with personalized offers",
      status: "Successful",
      impact: "Re-engaged 8.7% of dormant customers, generating $12,450 in additional revenue",
      metrics: \{
        "Open Rate": "18.5%",
        "Click-through Rate": "4.2%",
        "Conversion Rate": "2.1%",
      \},
    \},
    \{
      id: "5",
      title: "Implement cross-selling recommendations",
      category: "Sales",
      date: "2023-04-01",
      reason: "Analysis showed strong correlations between certain product categories",
      changes: "Added personalized product recommendations to product pages and post-purchase emails",
      status: "Planned",
      impact: "",
      metrics: \{\},
    \},
  ],
  )

  const [searchTerm, setSearchTerm] = useState("")
  const [sortColumn, setSortColumn] = useState<keyof StrategyAdjustment>("date")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
  const [newAdjustment, setNewAdjustment] = useState<Omit<StrategyAdjustment, "id" | "metrics">>(\{
    title: "",
    category: "",
    date: new Date().toISOString().split("T")[0],
    reason: "",
    changes: "",
    status: "Planned",
    impact: "",
  \})
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const handleSort = (column: keyof StrategyAdjustment) => \
  if (sortColumn === column)
  \
  setSortDirection(sortDirection === "asc" ? "desc" : "asc")
  \
  else \
  setSortColumn(column)
  setSortDirection("desc")
  \
  \

  const filteredAdjustments = adjustments.filter(
    (adjustment) =>
      adjustment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      adjustment.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      adjustment.reason.toLowerCase().includes(searchTerm.toLowerCase()) ||
      adjustment.changes.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const sortedAdjustments = [...filteredAdjustments].sort((a, b) => \{
    const aValue = a[sortColumn]
    const bValue = b[sortColumn]

    if (sortColumn === "date") \{
      return sortDirection === "asc"
        ? new Date(a.date).getTime() - new Date(b.date).getTime()
        : new Date(b.date).getTime() - new Date(a.date).getTime()
    \}

  if (typeof aValue === "string" && typeof bValue === "string")
  \
  return sortDirection === "asc" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue)
  \

  return 0
  \
}
)

const handleAddAdjustment = () => \
{
  const id = (adjustments.length + 1).toString()
  setAdjustments([...adjustments, \{ ...newAdjustment, id, metrics: \{\} \}])
  setNewAdjustment(\{
      title: "",
      category: "",
      date: new Date().toISOString().split("T")[0],
      reason: "",
      changes: "",
      status: "Planned",
      impact: "",
    \})
  setIsDialogOpen(false)
  \
}

const formatDate = (dateString: string) => \
{
  const date = new Date(dateString)
  return new Intl.DateTimeFormat("en-US", \{
      year: "numeric",
      month: "short",
      day: "numeric",
    \}).format(date)
  \
}

const getStatusBadge = (status: StrategyAdjustment["status"]) => \
{
    switch (status) \{
      case "Planned":
        return <Badge className="bg-blue-100 text-blue-800">Planned</Badge>
      case "Implemented":
        return <Badge className="bg-amber-100 text-amber-800">Implemented</Badge>
      case "Monitoring":
        return <Badge className="bg-purple-100 text-purple-800">Monitoring</Badge>
      case "Successful":
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle2 className="mr-1 h-3 w-3" />
            Successful
          </Badge>
        )
      case "Failed":
        return (
          <Badge className="bg-red-100 text-red-800">
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        )
    \}
  \}

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Strategy Adjustment Tracker</CardTitle>
              <CardDescription>Track changes to your strategy and their outcomes</CardDescription>
            </div>
            <Dialog open=\{isDialogOpen\} onOpenChange=\{setIsDialogOpen\}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Adjustment
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Add Strategy Adjustment</DialogTitle>
                  <DialogDescription>Record a change to your business strategy</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      value=\{newAdjustment.title\}
                      onChange=\{(e) => setNewAdjustment(\{ ...newAdjustment, title: e.target.value \})\}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="category">Category</Label>
                      <Input
                        id="category"
                        value=\{newAdjustment.category\}
                        onChange=\{(e) => setNewAdjustment(\{ ...newAdjustment, category: e.target.value \})\}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="date">Date</Label>
                      <Input
                        id="date"
                        type="date"
                        value=\{newAdjustment.date\}
                        onChange=\{(e) => setNewAdjustment(\{ ...newAdjustment, date: e.target.value \})\}
                      />
                    </div>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="reason">Reason for Change</Label>
                    <Textarea
                      id="reason"
                      rows=\{2\}
                      value=\{newAdjustment.reason\}
                      onChange=\{(e) => setNewAdjustment(\{ ...newAdjustment, reason: e.target.value \})\}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="changes">What Changed</Label>
                    <Textarea
                      id="changes"
                      rows=\{2\}
                      value=\{newAdjustment.changes\}
                      onChange=\{(e) => setNewAdjustment(\{ ...newAdjustment, changes: e.target.value \})\}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="status">Status</Label>
                    <select
                      id="status"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value=\{newAdjustment.status\}
                      onChange=\{(e) =>
                        setNewAdjustment(\{
                          ...newAdjustment,
                          status: e.target.value as "Planned" | "Implemented" | "Monitoring" | "Successful" | "Failed",
                        \})
                      \}
                    >
                      <option value="Planned">Planned</option>
                      <option value="Implemented">Implemented</option>
                      <option value="Monitoring">Monitoring</option>
                      <option value="Successful">Successful</option>
                      <option value="Failed">Failed</option>
                    </select>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="impact">Impact (if known)</Label>
                    <Textarea
                      id="impact"
                      rows=\{2\}
                      value=\{newAdjustment.impact\}
                      onChange=\{(e) => setNewAdjustment(\{ ...newAdjustment, impact: e.target.value \})\}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick=\{() => setIsDialogOpen(false)\}>
                    Cancel
                  </Button>
                  <Button onClick=\{handleAddAdjustment\}>Add Adjustment</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-4 flex items-center gap-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search adjustments..."
              className="h-9"
              value=\{searchTerm\}
              onChange=\{(e) => setSearchTerm(e.target.value)\}
            />
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[300px]">
                    <div className="flex items-center cursor-pointer" onClick=\{() => handleSort("title")\}>
                      Adjustment
                      \{sortColumn === "title" && <ArrowUpDown className="ml-2 h-4 w-4" />\}
                    </div>
                  </TableHead>
                  <TableHead>
                    <div className="flex items-center cursor-pointer" onClick=\{() => handleSort("category")\}>
                      Category
                      \{sortColumn === "category" && <ArrowUpDown className="ml-2 h-4 w-4" />\}
                    </div>
                  </TableHead>
                  <TableHead>
                    <div className="flex items-center cursor-pointer" onClick=\{() => handleSort("date")\}>
                      Date
                      \{sortColumn === "date" && <ArrowUpDown className="ml-2 h-4 w-4" />\}
                    </div>
                  </TableHead>
                  <TableHead>
                    <div className="flex items-center cursor-pointer" onClick=\{() => handleSort("status")\}>
                      Status
                      \{sortColumn === "status" && <ArrowUpDown className="ml-2 h-4 w-4" />\}
                    </div>
                  </TableHead>
                  <TableHead>Impact</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                \{sortedAdjustments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan=\{5\} className="h-24 text-center">
                      No adjustments found.
                    </TableCell>
                  </TableRow>
                ) : (
                  sortedAdjustments.map((adjustment) => (
                    <TableRow key=\{adjustment.id\}>
                      <TableCell className="font-medium">
                        <div>
                          <div>\{adjustment.title\}</div>
                          <div className="text-sm text-muted-foreground mt-1 line-clamp-1">\{adjustment.reason\}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">\{adjustment.category\}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                          \{formatDate(adjustment.date)\}
                        </div>
                      </TableCell>
                      <TableCell>\{getStatusBadge(adjustment.status)\}</TableCell>
                      <TableCell className="max-w-[200px]">
                        \{adjustment.impact ? (
                          <div className="text-sm">\{adjustment.impact\}</div>
                        ) : (
                          <div className="flex items-center text-sm text-muted-foreground">
                            <AlertTriangle className="mr-2 h-4 w-4" />
                            Not measured yet
                          </div>
                        )\}
                      </TableCell>
                    </TableRow>
                  ))
                )\}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
\}

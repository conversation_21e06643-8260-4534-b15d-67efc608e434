"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Search,
  Filter,
  Plus,
  Calendar,
  CheckCircle2,
  Clock,
  ArrowUpDown,
  BarChart3,
  FileText,
  Beaker,
  ArrowRight,
  Percent,
  Users,
  TrendingUp,
  Lightbulb,
} from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"

// Define the ABTest type
interface ABTest {
  id: string
  name: string
  category: string
  startDate: string
  endDate?: string
  status: string
  sampleSize?: number
  conversionLift?: number
  confidenceLevel?: number
  significant?: boolean
  hypothesis: string
  controlDescription: string
  variantDescription: string
  tags: string[]
  primaryMetric: string
  resultsSummary?: string
}

// Mock ABTest data
const abTests: ABTest[] = [
  {
    id: "1",
    name: "Homepage Button Color",
    category: "UI/UX",
    startDate: "2023-01-01",
    endDate: "2023-01-31",
    status: "Completed",
    sampleSize: 1000,
    conversionLift: 5.2,
    confidenceLevel: 95,
    significant: true,
    hypothesis: "Changing the homepage button color will increase click-through rate.",
    controlDescription: "Original blue button.",
    variantDescription: "New green button.",
    tags: ["color", "button", "homepage"],
    primaryMetric: "Click-Through Rate",
    resultsSummary:
      "The green button showed a statistically significant increase in click-through rate compared to the blue button.",
  },
  {
    id: "2",
    name: "Product Page Headline",
    category: "Content",
    startDate: "2023-02-15",
    endDate: "2023-03-15",
    status: "Active",
    sampleSize: 1500,
    conversionLift: 2.8,
    confidenceLevel: 80,
    significant: false,
    hypothesis: "A more concise product page headline will improve engagement.",
    controlDescription: "Original long headline.",
    variantDescription: "Short and punchy headline.",
    tags: ["headline", "product page", "engagement"],
    primaryMetric: "Time on Page",
  },
  {
    id: "3",
    name: "Email Subject Line",
    category: "Marketing",
    startDate: "2023-03-01",
    status: "Completed",
    sampleSize: 2000,
    conversionLift: -1.5,
    confidenceLevel: 90,
    significant: false,
    hypothesis: "Personalizing the email subject line will increase open rates.",
    controlDescription: "Generic subject line.",
    variantDescription: "Personalized subject line.",
    tags: ["email", "subject line", "personalization"],
    primaryMetric: "Open Rate",
    resultsSummary: "The personalized subject line did not perform better than the generic subject line.",
  },
  {
    id: "4",
    name: "Checkout Process",
    category: "UI/UX",
    startDate: "2023-04-01",
    status: "Planned",
    sampleSize: 0,
    conversionLift: 0,
    confidenceLevel: 0,
    significant: false,
    hypothesis: "Simplifying the checkout process will reduce cart abandonment.",
    controlDescription: "Original multi-step checkout.",
    variantDescription: "Simplified one-page checkout.",
    tags: ["checkout", "UI/UX", "cart abandonment"],
    primaryMetric: "Cart Abandonment Rate",
  },
  {
    id: "5",
    name: "Mobile App Onboarding",
    category: "App",
    startDate: "2023-05-01",
    status: "Active",
    sampleSize: 500,
    conversionLift: 8.0,
    confidenceLevel: 98,
    significant: true,
    hypothesis: "A more interactive onboarding process will increase user retention.",
    controlDescription: "Static onboarding screens.",
    variantDescription: "Interactive tutorial.",
    tags: ["mobile app", "onboarding", "retention"],
    primaryMetric: "User Retention",
  },
  {
    id: "6",
    name: "Landing Page Layout",
    category: "Content",
    startDate: "2023-06-15",
    status: "Completed",
    sampleSize: 1200,
    conversionLift: 3.5,
    confidenceLevel: 92,
    significant: true,
    hypothesis: "A different landing page layout will improve lead generation.",
    controlDescription: "Original layout with text on the left.",
    variantDescription: "New layout with image on the left.",
    tags: ["landing page", "layout", "lead generation"],
    primaryMetric: "Lead Conversion Rate",
    resultsSummary:
      "The new layout with the image on the left showed a statistically significant increase in lead conversion rate.",
  },
  {
    id: "7",
    name: "Pricing Strategy",
    category: "Pricing",
    startDate: "2023-07-01",
    status: "Planned",
    sampleSize: 0,
    conversionLift: 0,
    confidenceLevel: 0,
    significant: false,
    hypothesis: "A tiered pricing strategy will increase overall revenue.",
    controlDescription: "Single price point.",
    variantDescription: "Tiered pricing options.",
    tags: ["pricing", "strategy", "revenue"],
    primaryMetric: "Total Revenue",
  },
  {
    id: "8",
    name: "Blog Post Length",
    category: "Content",
    startDate: "2023-08-01",
    status: "Active",
    sampleSize: 800,
    conversionLift: -2.0,
    confidenceLevel: 85,
    significant: false,
    hypothesis: "Shorter blog posts will increase readership.",
    controlDescription: "Long-form blog post.",
    variantDescription: "Short-form blog post.",
    tags: ["blog", "content", "readership"],
    primaryMetric: "Average Time on Page",
  },
  {
    id: "9",
    name: "Call-to-Action Placement",
    category: "UI/UX",
    startDate: "2023-09-15",
    endDate: "2023-10-15",
    status: "Completed",
    sampleSize: 900,
    conversionLift: 6.0,
    confidenceLevel: 96,
    significant: true,
    hypothesis: "Moving the call-to-action button above the fold will increase conversions.",
    controlDescription: "Call-to-action below the fold.",
    variantDescription: "Call-to-action above the fold.",
    tags: ["call-to-action", "UI/UX", "conversions"],
    primaryMetric: "Conversion Rate",
    resultsSummary:
      "Moving the call-to-action button above the fold showed a statistically significant increase in conversions.",
  },
  {
    id: "10",
    name: "Customer Support Chatbot",
    category: "Support",
    startDate: "2023-10-01",
    status: "Planned",
    sampleSize: 0,
    conversionLift: 0,
    confidenceLevel: 0,
    significant: false,
    hypothesis: "Implementing a customer support chatbot will reduce response times.",
    controlDescription: "Manual customer support.",
    variantDescription: "Automated chatbot support.",
    tags: ["customer support", "chatbot", "response times"],
    primaryMetric: "Average Response Time",
  },
]

export function ABTestingBoard() {
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [sortColumn, setSortColumn] = useState<string>("startDate")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
  const [selectedTest, setSelectedTest] = useState<ABTest | null>(null)
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false)
  const [resultsDialogOpen, setResultsDialogOpen] = useState(false)

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortColumn(column)
      setSortDirection("desc")
    }
  }

  // Filter tests based on search query and active tab
  const filteredTests = abTests.filter((test) => {
    const matchesSearch =
      test.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      test.hypothesis.toLowerCase().includes(searchQuery.toLowerCase()) ||
      test.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
      test.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    if (activeTab === "all") return matchesSearch
    if (activeTab === "active") return matchesSearch && test.status === "Active"
    if (activeTab === "completed") return matchesSearch && test.status === "Completed"
    if (activeTab === "planned") return matchesSearch && test.status === "Planned"
    if (activeTab === "significant") return matchesSearch && test.status === "Completed" && test.significant === true

    return matchesSearch
  })

  // Sort the tests based on the selected column and direction
  const sortedTests = [...filteredTests].sort((a, b) => {
    const aValue = a[sortColumn as keyof ABTest]
    const bValue = b[sortColumn as keyof ABTest]

    if (sortColumn === "startDate" || sortColumn === "endDate") {
      return sortDirection === "asc"
        ? new Date(a[sortColumn]).getTime() - new Date(b[sortColumn]).getTime()
        : new Date(b[sortColumn]).getTime() - new Date(a[sortColumn]).getTime()
    }

    if (typeof aValue === "string" && typeof bValue === "string") {
      return sortDirection === "asc" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue)
    }

    if (typeof aValue === "number" && typeof bValue === "number") {
      return sortDirection === "asc" ? aValue - bValue : bValue - aValue
    }

    return 0
  })

  // Count tests by status
  const statusCounts = abTests.reduce(
    (acc, test) => {
      acc[test.status] = (acc[test.status] || 0) + 1
      return acc
    },
    {} as Record<string, number>,
  )

  // Calculate success rate
  const completedTests = abTests.filter((test) => test.status === "Completed").length
  const significantTests = abTests.filter((test) => test.status === "Completed" && test.significant === true).length
  const successRate = completedTests > 0 ? (significantTests / completedTests) * 100 : 0

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tests</CardTitle>
            <Beaker className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{abTests.length}</div>
            <p className="text-xs text-muted-foreground">Across all categories</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tests</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statusCounts["Active"] || 0}</div>
            <p className="text-xs text-muted-foreground">Currently running</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{successRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">Tests with significant results</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Planned Tests</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statusCounts["Planned"] || 0}</div>
            <p className="text-xs text-muted-foreground">Ready to launch</p>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div className="relative w-full md:w-96">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search tests..."
            className="w-full pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="h-9">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button size="sm" className="h-9">
            <Plus className="mr-2 h-4 w-4" />
            Create New Test
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Tests</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
          <TabsTrigger value="planned">Planned</TabsTrigger>
          <TabsTrigger value="significant">Significant Results</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>A/B Testing Board</CardTitle>
              <CardDescription>Track and analyze split tests to optimize your content and products</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[250px]">
                      <div className="flex items-center cursor-pointer" onClick={() => handleSort("name")}>
                        Test Name
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className="flex items-center cursor-pointer" onClick={() => handleSort("category")}>
                        Category
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className="flex items-center cursor-pointer" onClick={() => handleSort("startDate")}>
                        Start Date
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className="flex items-center cursor-pointer" onClick={() => handleSort("endDate")}>
                        End Date
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className="flex items-center cursor-pointer" onClick={() => handleSort("status")}>
                        Status
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>Sample Size</TableHead>
                    <TableHead>Conversion Lift</TableHead>
                    <TableHead>Significance</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedTests.map((test) => (
                    <TableRow key={test.id}>
                      <TableCell className="font-medium">{test.name}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{test.category}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                          {test.startDate}
                        </div>
                      </TableCell>
                      <TableCell>
                        {test.endDate ? (
                          <div className="flex items-center">
                            <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                            {test.endDate}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={
                            test.status === "Active"
                              ? "bg-green-100 text-green-800"
                              : test.status === "Planned"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-purple-100 text-purple-800"
                          }
                        >
                          {test.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                          {test.sampleSize ? test.sampleSize.toLocaleString() : "-"}
                        </div>
                      </TableCell>
                      <TableCell>
                        {test.conversionLift !== undefined ? (
                          <div className="flex items-center">
                            <Badge
                              className={
                                test.conversionLift > 0
                                  ? "bg-green-100 text-green-800"
                                  : test.conversionLift < 0
                                    ? "bg-red-100 text-red-800"
                                    : "bg-gray-100 text-gray-800"
                              }
                            >
                              {test.conversionLift > 0 ? "+" : ""}
                              {test.conversionLift}%
                            </Badge>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {test.confidenceLevel !== undefined ? (
                          <div className="flex items-center">
                            <Percent className="mr-2 h-4 w-4 text-muted-foreground" />
                            {test.confidenceLevel}%
                            {test.significant && <CheckCircle2 className="ml-1 h-4 w-4 text-green-500" />}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedTest(test)
                              setDetailsDialogOpen(true)
                            }}
                          >
                            <FileText className="h-4 w-4" />
                            <span className="sr-only">Details</span>
                          </Button>
                          {test.status === "Completed" && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedTest(test)
                                setResultsDialogOpen(true)
                              }}
                            >
                              <BarChart3 className="h-4 w-4" />
                              <span className="sr-only">Results</span>
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Test Details Dialog */}
      <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Test Details</DialogTitle>
            <DialogDescription>{selectedTest?.name}</DialogDescription>
          </DialogHeader>

          {selectedTest && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-xs text-muted-foreground">Category</Label>
                  <div className="mt-1">
                    <Badge variant="outline">{selectedTest.category}</Badge>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-muted-foreground">Status</Label>
                  <div className="mt-1">
                    <Badge
                      className={
                        selectedTest.status === "Active"
                          ? "bg-green-100 text-green-800"
                          : selectedTest.status === "Planned"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-purple-100 text-purple-800"
                      }
                    >
                      {selectedTest.status}
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-muted-foreground">Start Date</Label>
                  <div className="flex items-center mt-1">
                    <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span>{selectedTest.startDate}</span>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-muted-foreground">End Date</Label>
                  <div className="flex items-center mt-1">
                    <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span>{selectedTest.endDate || "Not set"}</span>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-muted-foreground">Sample Size</Label>
                  <div className="flex items-center mt-1">
                    <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span>{selectedTest.sampleSize ? selectedTest.sampleSize.toLocaleString() : "Not set"}</span>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-muted-foreground">Primary Metric</Label>
                  <div className="flex items-center mt-1">
                    <BarChart3 className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span>{selectedTest.primaryMetric}</span>
                  </div>
                </div>
              </div>

              <div>
                <Label className="text-xs text-muted-foreground">Hypothesis</Label>
                <div className="mt-1 p-3 border rounded-md bg-muted/30">
                  <p className="text-sm">{selectedTest.hypothesis}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-xs text-muted-foreground">Control (A)</Label>
                  <div className="mt-1 p-3 border rounded-md bg-muted/30 h-24 overflow-auto">
                    <p className="text-sm">{selectedTest.controlDescription}</p>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-muted-foreground">Variant (B)</Label>
                  <div className="mt-1 p-3 border rounded-md bg-muted/30 h-24 overflow-auto">
                    <p className="text-sm">{selectedTest.variantDescription}</p>
                  </div>
                </div>
              </div>

              <div>
                <Label className="text-xs text-muted-foreground">Tags</Label>
                <div className="mt-1 flex flex-wrap gap-1">
                  {selectedTest.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="bg-muted/50">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>

              {selectedTest.status === "Completed" && (
                <div>
                  <Label className="text-xs text-muted-foreground">Results Summary</Label>
                  <div className="mt-1 p-3 border rounded-md bg-muted/30">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium">Conversion Lift:</span>
                      <Badge
                        className={
                          selectedTest.conversionLift! > 0
                            ? "bg-green-100 text-green-800"
                            : selectedTest.conversionLift! < 0
                              ? "bg-red-100 text-red-800"
                              : "bg-gray-100 text-gray-800"
                        }
                      >
                        {selectedTest.conversionLift! > 0 ? "+" : ""}
                        {selectedTest.conversionLift}%
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium">Statistical Significance:</span>
                      <div className="flex items-center">
                        <span>{selectedTest.confidenceLevel}%</span>
                        {selectedTest.significant && <CheckCircle2 className="ml-1 h-4 w-4 text-green-500" />}
                      </div>
                    </div>
                    <p className="text-sm mt-2">{selectedTest.resultsSummary}</p>
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setDetailsDialogOpen(false)}>
              Close
            </Button>
            {selectedTest?.status === "Planned" && (
              <Button>
                <ArrowRight className="mr-2 h-4 w-4" />
                Start Test
              </Button>
            )}
            {selectedTest?.status === "Active" && (
              <Button>
                <CheckCircle2 className="mr-2 h-4 w-4" />
                End Test
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Test Results Dialog */}
      <Dialog open={resultsDialogOpen} onOpenChange={setResultsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Test Results</DialogTitle>
            <DialogDescription>Detailed results for {selectedTest?.name}</DialogDescription>
          </DialogHeader>

          {selectedTest && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-xs text-muted-foreground">Conversion Lift</Label>
                  <div className="mt-1">
                    <Badge
                      className={
                        selectedTest.conversionLift! > 0
                          ? "bg-green-100 text-green-800"
                          : selectedTest.conversionLift! < 0
                            ? "bg-red-100 text-red-800"
                            : "bg-gray-100 text-gray-800"
                      }
                    >
                      {selectedTest.conversionLift! > 0 ? "+" : ""}
                      {selectedTest.conversionLift}%
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-muted-foreground">Statistical Significance</Label>
                  <div className="mt-1 flex items-center">
                    <span>{selectedTest.confidenceLevel}%</span>
                    {selectedTest.significant && <CheckCircle2 className="ml-1 h-4 w-4 text-green-500" />}
                  </div>
                </div>
              </div>

              <div>
                <Label className="text-xs text-muted-foreground">Results Summary</Label>
                <div className="mt-1 p-3 border rounded-md bg-muted/30">
                  <p className="text-sm">{selectedTest.resultsSummary}</p>
                </div>
              </div>

              <div>
                <Label className="text-xs text-muted-foreground">Key Takeaways</Label>
                <Textarea
                  placeholder="Add key takeaways from the test results..."
                  className="mt-1"
                  defaultValue="The variant performed significantly better than the control."
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setResultsDialogOpen(false)}>
              Close
            </Button>
            <Button>
              <Lightbulb className="mr-2 h-4 w-4" />
              Implement Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

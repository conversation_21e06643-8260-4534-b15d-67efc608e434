"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useToast } from "@/components/ui/use-toast"
import {
  CheckCircle2,
  AlertTriangle,
  XCircle,
  Clock,
  Zap,
  Globe,
  BarChart3,
  Settings,
  RefreshCw,
  FlaskConical,
  Activity,
  TrendingUp,
  Gauge,
} from "lucide-react"
import { AutomationFlowChart } from "@/components/automation-flow-chart"
import { ABTestingFramework } from "@/components/ab-testing-framework"

interface SystemComponent {
  id: string
  name: string
  description: string
  status: "healthy" | "warning" | "error" | "offline"
  lastChecked: string
  responseTime: number
  uptime: number
  dependencies: string[]
  metrics: {
    requests: number
    errors: number
    avgResponseTime: number
    throughput: number
  }
  category: "core" | "automation" | "integration" | "monitoring" | "testing"
}

interface SystemAlert {
  id: string
  type: "error" | "warning" | "info"
  component: string
  message: string
  timestamp: string
  resolved: boolean
}

export function SystemCheck() {
  const [components, setComponents] = useState<SystemComponent[]>([])
  const [alerts, setAlerts] = useState<SystemAlert[]>([])
  const [isRunningCheck, setIsRunningCheck] = useState(false)
  const [checkProgress, setCheckProgress] = useState(0)
  const [lastCheckTime, setLastCheckTime] = useState<string>("")
  const [activeTab, setActiveTab] = useState("overview")
  const { toast } = useToast()

  // Initialize system components
  useEffect(() => {
    const mockComponents: SystemComponent[] = [
      {
        id: "content-engine",
        name: "Content Generation Engine",
        description: "AI-powered content creation and optimization",
        status: "healthy",
        lastChecked: new Date().toISOString(),
        responseTime: 245,
        uptime: 99.8,
        dependencies: ["ai-models", "database", "cache"],
        metrics: {
          requests: 15420,
          errors: 12,
          avgResponseTime: 245,
          throughput: 450,
        },
        category: "core",
      },
      {
        id: "automation-orchestrator",
        name: "Automation Orchestrator",
        description: "Workflow automation and task scheduling",
        status: "healthy",
        lastChecked: new Date().toISOString(),
        responseTime: 89,
        uptime: 99.9,
        dependencies: ["queue-system", "database"],
        metrics: {
          requests: 8920,
          errors: 3,
          avgResponseTime: 89,
          throughput: 320,
        },
        category: "automation",
      },
      {
        id: "market-research-api",
        name: "Market Research API",
        description: "External market data and trend analysis",
        status: "warning",
        lastChecked: new Date().toISOString(),
        responseTime: 1240,
        uptime: 97.2,
        dependencies: ["external-apis", "cache"],
        metrics: {
          requests: 5680,
          errors: 45,
          avgResponseTime: 1240,
          throughput: 180,
        },
        category: "integration",
      },
      {
        id: "analytics-processor",
        name: "Analytics Processor",
        description: "Performance metrics and data analysis",
        status: "healthy",
        lastChecked: new Date().toISOString(),
        responseTime: 156,
        uptime: 99.5,
        dependencies: ["database", "metrics-store"],
        metrics: {
          requests: 12340,
          errors: 8,
          avgResponseTime: 156,
          throughput: 280,
        },
        category: "monitoring",
      },
      {
        id: "ab-testing-engine",
        name: "A/B Testing Engine",
        description: "Experiment management and statistical analysis",
        status: "healthy",
        lastChecked: new Date().toISOString(),
        responseTime: 98,
        uptime: 99.7,
        dependencies: ["database", "analytics-processor"],
        metrics: {
          requests: 3420,
          errors: 2,
          avgResponseTime: 98,
          throughput: 150,
        },
        category: "testing",
      },
      {
        id: "notification-service",
        name: "Notification Service",
        description: "Email, SMS, and push notification delivery",
        status: "healthy",
        lastChecked: new Date().toISOString(),
        responseTime: 234,
        uptime: 99.1,
        dependencies: ["email-provider", "sms-provider"],
        metrics: {
          requests: 7890,
          errors: 15,
          avgResponseTime: 234,
          throughput: 200,
        },
        category: "integration",
      },
      {
        id: "compliance-monitor",
        name: "Compliance Monitor",
        description: "Data privacy and regulatory compliance tracking",
        status: "healthy",
        lastChecked: new Date().toISOString(),
        responseTime: 67,
        uptime: 99.9,
        dependencies: ["audit-log", "database"],
        metrics: {
          requests: 2340,
          errors: 1,
          avgResponseTime: 67,
          throughput: 100,
        },
        category: "monitoring",
      },
      {
        id: "optimization-ai",
        name: "Optimization AI",
        description: "AI-powered flow optimization and suggestions",
        status: "healthy",
        lastChecked: new Date().toISOString(),
        responseTime: 312,
        uptime: 99.6,
        dependencies: ["ai-models", "analytics-processor"],
        metrics: {
          requests: 1890,
          errors: 3,
          avgResponseTime: 312,
          throughput: 80,
        },
        category: "core",
      },
    ]

    const mockAlerts: SystemAlert[] = [
      {
        id: "alert-001",
        type: "warning",
        component: "market-research-api",
        message: "Response time above threshold (1240ms > 1000ms)",
        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
        resolved: false,
      },
      {
        id: "alert-002",
        type: "info",
        component: "ab-testing-engine",
        message: "New A/B test started: Parallel Content Generation",
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        resolved: false,
      },
      {
        id: "alert-003",
        type: "error",
        component: "notification-service",
        message: "Email delivery failure rate increased to 2.1%",
        timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
        resolved: true,
      },
    ]

    setComponents(mockComponents)
    setAlerts(mockAlerts)
    setLastCheckTime(new Date().toISOString())
  }, [])

  const runSystemCheck = async () => {
    setIsRunningCheck(true)
    setCheckProgress(0)

    // Simulate system check progress
    for (let i = 0; i <= 100; i += 10) {
      await new Promise((resolve) => setTimeout(resolve, 300))
      setCheckProgress(i)
    }

    // Update component statuses (simulate some changes)
    setComponents((prev) =>
      prev.map((comp) => ({
        ...comp,
        lastChecked: new Date().toISOString(),
        responseTime: comp.responseTime + Math.random() * 20 - 10,
        metrics: {
          ...comp.metrics,
          requests: comp.metrics.requests + Math.floor(Math.random() * 100),
          avgResponseTime: comp.responseTime + Math.random() * 20 - 10,
        },
      })),
    )

    setLastCheckTime(new Date().toISOString())
    setIsRunningCheck(false)

    toast({
      title: "System Check Complete",
      description: "All components have been verified and updated.",
    })
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "healthy":
        return <CheckCircle2 className="h-4 w-4 text-green-600" />
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case "error":
        return <XCircle className="h-4 w-4 text-red-600" />
      case "offline":
        return <XCircle className="h-4 w-4 text-gray-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy":
        return "bg-green-100 text-green-800 border-green-200"
      case "warning":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "error":
        return "bg-red-100 text-red-800 border-red-200"
      case "offline":
        return "bg-gray-100 text-gray-800 border-gray-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "core":
        return <Zap className="h-4 w-4" />
      case "automation":
        return <Settings className="h-4 w-4" />
      case "integration":
        return <Globe className="h-4 w-4" />
      case "monitoring":
        return <BarChart3 className="h-4 w-4" />
      case "testing":
        return <FlaskConical className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case "error":
        return <XCircle className="h-4 w-4 text-red-600" />
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case "info":
        return <CheckCircle2 className="h-4 w-4 text-blue-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const healthyComponents = components.filter((c) => c.status === "healthy").length
  const warningComponents = components.filter((c) => c.status === "warning").length
  const errorComponents = components.filter((c) => c.status === "error").length
  const overallHealth = (healthyComponents / components.length) * 100

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                System Health Dashboard
              </CardTitle>
              <CardDescription>Monitor and verify all Command Center components and integrations</CardDescription>
            </div>
            <Button onClick={runSystemCheck} disabled={isRunningCheck}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isRunningCheck ? "animate-spin" : ""}`} />
              {isRunningCheck ? "Checking..." : "Run Check"}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isRunningCheck && (
            <div className="space-y-2 mb-4">
              <div className="flex items-center justify-between text-sm">
                <span>System Check Progress</span>
                <span>{checkProgress}%</span>
              </div>
              <Progress value={checkProgress} className="w-full" />
            </div>
          )}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Overall Health</CardTitle>
                <Gauge className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{overallHealth.toFixed(1)}%</div>
                <p className="text-xs text-muted-foreground">System operational</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Healthy</CardTitle>
                <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{healthyComponents}</div>
                <p className="text-xs text-muted-foreground">Components running well</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Warnings</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{warningComponents}</div>
                <p className="text-xs text-muted-foreground">Need attention</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Errors</CardTitle>
                <XCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{errorComponents}</div>
                <p className="text-xs text-muted-foreground">Require immediate action</p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="components">Components</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="flowchart">Flow Chart</TabsTrigger>
          <TabsTrigger value="ab-testing">A/B Testing</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Component Status</CardTitle>
                <CardDescription>Current status of all system components</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  <div className="space-y-3">
                    {components.map((component) => (
                      <div key={component.id} className="flex items-center gap-3 p-3 border rounded-lg">
                        <div className="flex items-center gap-2">
                          {getCategoryIcon(component.category)}
                          {getStatusIcon(component.status)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-sm">{component.name}</h4>
                            <Badge variant="outline" className={getStatusColor(component.status)}>
                              {component.status}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground mb-2">{component.description}</p>
                          <div className="flex items-center gap-4 text-xs">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>{component.responseTime}ms</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-3 w-3" />
                              <span>{component.uptime}% uptime</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Activity className="h-3 w-3" />
                              <span>{component.metrics.throughput}/hr</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recent Alerts</CardTitle>
                <CardDescription>Latest system alerts and notifications</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  <div className="space-y-3">
                    {alerts.map((alert) => (
                      <div
                        key={alert.id}
                        className={`flex items-start gap-3 p-3 border rounded-lg ${alert.resolved ? "opacity-50" : ""}`}
                      >
                        <div className="mt-1">{getAlertIcon(alert.type)}</div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-sm">{alert.component}</h4>
                            {alert.resolved && (
                              <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                                Resolved
                              </Badge>
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground mb-2">{alert.message}</p>
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            <span>{new Date(alert.timestamp).toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                    {alerts.length === 0 && (
                      <div className="text-center py-8">
                        <CheckCircle2 className="h-8 w-8 text-green-600 mx-auto mb-2" />
                        <p className="text-sm text-muted-foreground">No active alerts</p>
                        <p className="text-xs text-muted-foreground">All systems operating normally</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="components" className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            {["core", "automation", "integration", "monitoring", "testing"].map((category) => {
              const categoryComponents = components.filter((c) => c.category === category)
              if (categoryComponents.length === 0) return null

              return (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 capitalize">
                      {getCategoryIcon(category)}
                      {category} Components
                    </CardTitle>
                    <CardDescription>
                      {category === "core" && "Essential system components for core functionality"}
                      {category === "automation" && "Workflow automation and orchestration services"}
                      {category === "integration" && "External API integrations and data sources"}
                      {category === "monitoring" && "System monitoring and analytics services"}
                      {category === "testing" && "A/B testing and experimentation platform"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {categoryComponents.map((component) => (
                        <Card key={component.id}>
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                {getStatusIcon(component.status)}
                                <CardTitle className="text-base">{component.name}</CardTitle>
                              </div>
                              <Badge variant="outline" className={getStatusColor(component.status)}>
                                {component.status}
                              </Badge>
                            </div>
                            <CardDescription>{component.description}</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                  <p className="text-muted-foreground">Response Time</p>
                                  <p className="font-medium">{component.responseTime}ms</p>
                                </div>
                                <div>
                                  <p className="text-muted-foreground">Uptime</p>
                                  <p className="font-medium">{component.uptime}%</p>
                                </div>
                                <div>
                                  <p className="text-muted-foreground">Requests</p>
                                  <p className="font-medium">{component.metrics.requests.toLocaleString()}</p>
                                </div>
                                <div>
                                  <p className="text-muted-foreground">Errors</p>
                                  <p className="font-medium">{component.metrics.errors}</p>
                                </div>
                              </div>

                              {component.dependencies.length > 0 && (
                                <div>
                                  <p className="text-sm font-medium mb-2">Dependencies</p>
                                  <div className="flex flex-wrap gap-1">
                                    {component.dependencies.map((dep) => (
                                      <Badge key={dep} variant="outline" className="text-xs">
                                        {dep}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              )}

                              <div className="text-xs text-muted-foreground">
                                Last checked: {new Date(component.lastChecked).toLocaleString()}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Alerts & Notifications</CardTitle>
              <CardDescription>Detailed view of all system alerts and their resolution status</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.map((alert) => (
                  <Card key={alert.id} className={alert.resolved ? "opacity-60" : ""}>
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="mt-1">{getAlertIcon(alert.type)}</div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium">{alert.component}</h4>
                            <Badge
                              variant="outline"
                              className={
                                alert.type === "error"
                                  ? "bg-red-100 text-red-800"
                                  : alert.type === "warning"
                                    ? "bg-yellow-100 text-yellow-800"
                                    : "bg-blue-100 text-blue-800"
                              }
                            >
                              {alert.type}
                            </Badge>
                            {alert.resolved && (
                              <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                                Resolved
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mb-3">{alert.message}</p>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Clock className="h-3 w-3" />
                              <span>{new Date(alert.timestamp).toLocaleString()}</span>
                            </div>
                            {!alert.resolved && (
                              <Button size="sm" variant="outline">
                                Mark Resolved
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
                {alerts.length === 0 && (
                  <Card>
                    <CardContent className="flex flex-col items-center justify-center py-12">
                      <CheckCircle2 className="h-12 w-12 text-green-600 mb-4" />
                      <h3 className="text-lg font-medium mb-2">No Active Alerts</h3>
                      <p className="text-muted-foreground text-center">
                        All systems are operating normally with no alerts or warnings.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="flowchart" className="space-y-4">
          <AutomationFlowChart />
        </TabsContent>

        <TabsContent value="ab-testing" className="space-y-4">
          <ABTestingFramework />
        </TabsContent>
      </Tabs>
    </div>
  )
}

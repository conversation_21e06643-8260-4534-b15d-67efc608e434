'use client'

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Video, Sparkles, Image } from "lucide-react"
import { geminiService } from "@/lib/api/gemini"
import { toast } from "@/components/ui/use-toast"

interface PrototypeVideoDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  productName?: string
  onSubmit: (data: any) => void
}

export function PrototypeVideoDialog({
  open,
  onOpenChange,
  productName = "Product",
  onSubmit
}: PrototypeVideoDialogProps) {
  const [formData, setFormData] = useState({
    media_type: "video", // image or video
    content_description: "",
    custom_ai_prompt: ""
  })
  const [isAskingAI, setIsAskingAI] = useState(false)
  const [lastProductName, setLastProductName] = useState<string>("")

  // Reset form when dialog opens for a new product
  useEffect(() => {
    if (open && productName !== lastProductName) {
      setFormData({
        media_type: "video",
        content_description: "",
        custom_ai_prompt: ""
      })
      setLastProductName(productName)
    }
  }, [open, productName, lastProductName])

  // Clear content_description when dialog closes
  useEffect(() => {
    if (!open) {
      setFormData(prev => ({
        ...prev,
        content_description: "",
        custom_ai_prompt: ""
      }))
    }
  }, [open])

  const handleAskAI = async () => {
    setIsAskingAI(true)

    try {
      // Call Gemini API to generate content prompt
      const response = await geminiService.generateContentPrompt({
        product_title: productName,
        media_type: formData.media_type as 'image' | 'video'
      })

      if (response.success) {
        // Update the content description with the generated prompt
        setFormData(prev => ({
          ...prev,
          content_description: response.generated_prompt
        }))

        toast({
          title: "AI Prompt Generated",
          description: "Content description has been generated successfully!",
        })
      } else {
        // Use fallback prompt but still show it
        setFormData(prev => ({
          ...prev,
          content_description: response.generated_prompt
        }))

        toast({
          title: "Fallback Prompt Used",
          description: response.error || "Using fallback prompt due to API limitations.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error generating AI prompt:', error)

      // Fallback prompt
      const fallbackPrompt = formData.media_type === 'video'
        ? `Create an engaging video showcasing ${productName} with dynamic visuals, compelling narration, and clear product benefits that resonate with your target audience.`
        : `Design a stunning image featuring ${productName} with eye-catching visuals, clear product highlights, and compelling messaging that drives engagement.`

      setFormData(prev => ({
        ...prev,
        content_description: fallbackPrompt
      }))

      toast({
        title: "Error Generating Prompt",
        description: "Using fallback prompt. Please check your API configuration.",
        variant: "destructive",
      })
    } finally {
      setIsAskingAI(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
    // Don't reset form data here - let the parent component handle dialog closing
    // The form will be reset when the dialog is reopened for a new product
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <div className="flex items-center space-x-2">
            <Sparkles className="h-5 w-5 text-blue-600" />
            <DialogTitle>Create Prototype Media</DialogTitle>
          </div>
          <DialogDescription>
            Generate prototype content for <strong>{productName}</strong> using AI
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="media_type">Media Type</Label>
            <Select
              value={formData.media_type}
              onValueChange={(value) => setFormData(prev => ({ ...prev, media_type: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select media type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="image">
                  <div className="flex items-center space-x-2">
                    <Image className="h-4 w-4" />
                    <span>Image</span>
                  </div>
                </SelectItem>
                <SelectItem value="video">
                  <div className="flex items-center space-x-2">
                    <Video className="h-4 w-4" />
                    <span>Video</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="content_description">Content Description</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAskAI}
                disabled={isAskingAI}
              >
                {isAskingAI ? (
                  <>
                    <Sparkles className="mr-1 h-3 w-3 animate-spin" />
                    Asking AI...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-1 h-3 w-3" />
                    Ask AI
                  </>
                )}
              </Button>
            </div>
            <Textarea
              id="content_description"
              value={formData.content_description}
              onChange={(e) => setFormData(prev => ({ ...prev, content_description: e.target.value }))}
              placeholder="Describe what you want to highlight in the content..."
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="custom_ai_prompt">Custom AI Prompt</Label>
            <Textarea
              id="custom_ai_prompt"
              value={formData.custom_ai_prompt}
              onChange={(e) => setFormData(prev => ({ ...prev, custom_ai_prompt: e.target.value }))}
              placeholder="Add specific instructions for AI content generation..."
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Skip for Now
            </Button>
            <Button type="submit" className="">
              <Sparkles className="mr-2 h-4 w-4" />
              Generate Prototype Content
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DollarSign,
  Users,
  ShoppingCart,
  TrendingUp,
  CheckCircle,
  Clock,
  XCircle,
  CreditCard,
  Calendar,
} from "lucide-react";
import {
  getAdminOrderStats,
  type AdminOrderStats,
} from "@/lib/api/admin-orders";
import { toast } from "@/hooks/use-toast";

interface StatCardProps {
  title: string;
  value: string | number;
  description: string;
  icon: React.ComponentType<any>;
  trend?: string;
  trendUp?: boolean;
}

function StatCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  trendUp,
}: StatCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
        {trend && (
          <p
            className={`text-xs flex items-center mt-1 ${
              trendUp ? "text-green-600" : "text-red-600"
            }`}
          >
            <TrendingUp className="h-3 w-3 mr-1" />
            {trend}
          </p>
        )}
      </CardContent>
    </Card>
  );
}

export function AdminOrderSummary() {
  const [stats, setStats] = useState<AdminOrderStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadStats = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await getAdminOrderStats();

      if (response.success && response.data) {
        setStats(response.data);
      } else {
        setError(response.message || "Failed to load order statistics");
        toast({
          title: "Error",
          description: response.message || "Failed to load order statistics",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error loading order stats:", error);
      setError("Failed to load order statistics");
      toast({
        title: "Error",
        description: "Failed to load order statistics",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadStats();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardHeader className="space-y-0 pb-2">
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded animate-pulse mb-2" />
              <div className="h-3 bg-gray-200 rounded animate-pulse" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !stats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Order Statistics</CardTitle>
          <CardDescription>
            {error || "Unable to load statistics"}
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const paidRate =
    stats.totalOrders > 0
      ? ((stats.paidOrders / stats.totalOrders) * 100).toFixed(1)
      : "0";
  const subscriptionRate =
    stats.totalOrders > 0
      ? (
          ((stats.monthlySubscriptions + stats.annualSubscriptions) /
            stats.totalOrders) *
          100
        ).toFixed(1)
      : "0";

  return (
    <div className="space-y-6">
      {/* Main Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Revenue"
          value={formatCurrency(stats.totalRevenue)}
          description="Total revenue from all orders"
          icon={DollarSign}
          trend="+12.5% from last month"
          trendUp={true}
        />

        <StatCard
          title="Total Orders"
          value={stats.totalOrders.toLocaleString()}
          description="All orders across the platform"
          icon={ShoppingCart}
          trend="+8.2% from last month"
          trendUp={true}
        />

        <StatCard
          title="Average Order Value"
          value={formatCurrency(stats.averageOrderValue)}
          description="Average value per order"
          icon={TrendingUp}
          trend="+3.1% from last month"
          trendUp={true}
        />

        <StatCard
          title="Success Rate"
          value={`${paidRate}%`}
          description="Orders successfully paid"
          icon={CheckCircle}
          trend={`${stats.paidOrders} of ${stats.totalOrders} orders`}
          trendUp={true}
        />
      </div>

      {/* Order Status Breakdown */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Paid Orders"
          value={stats.paidOrders.toLocaleString()}
          description="Successfully completed orders"
          icon={CheckCircle}
        />

        <StatCard
          title="Pending Orders"
          value={stats.pendingOrders.toLocaleString()}
          description="Orders awaiting payment"
          icon={Clock}
        />

        <StatCard
          title="Failed Orders"
          value={stats.failedOrders.toLocaleString()}
          description="Orders with payment failures"
          icon={XCircle}
        />

        <StatCard
          title="Cancelled Orders"
          value={stats.cancelledOrders.toLocaleString()}
          description="Orders cancelled by users"
          icon={XCircle}
        />
      </div>

      {/* Subscription Breakdown */}
      <div className="grid gap-4 md:grid-cols-3">
        <StatCard
          title="Monthly Subscriptions"
          value={stats.monthlySubscriptions.toLocaleString()}
          description="Active monthly subscriptions"
          icon={Calendar}
        />

        <StatCard
          title="Annual Subscriptions"
          value={stats.annualSubscriptions.toLocaleString()}
          description="Active annual subscriptions"
          icon={CreditCard}
        />

        <StatCard
          title="Subscription Rate"
          value={`${subscriptionRate}%`}
          description="Percentage of subscription orders"
          icon={TrendingUp}
        />
      </div>

      {/* Recent Orders */}
      {stats.recentOrders && stats.recentOrders.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Orders</CardTitle>
            <CardDescription>
              Latest orders from the last 24 hours
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.recentOrders.slice(0, 5).map((order) => (
                <div
                  key={order.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      {order.status === "paid" && (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      )}
                      {order.status === "pending" && (
                        <Clock className="h-4 w-4 text-yellow-600" />
                      )}
                      {order.status === "failed" && (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      {order.status === "cancelled" && (
                        <XCircle className="h-4 w-4 text-gray-600" />
                      )}
                      <span className="capitalize text-sm font-medium">
                        {order.status}
                      </span>
                    </div>
                    <div>
                      <div className="font-medium">
                        {order.user.first_name} {order.user.last_name}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {order.plan.name} • {order.meta.interval}
                        {order.meta.change_type && (
                          <span className="ml-1 text-blue-600">
                            ({order.meta.change_type})
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">
                      {order.amount_total_formatted}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(order.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

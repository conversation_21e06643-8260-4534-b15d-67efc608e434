"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  CreditCard,
  Download,
  Search,
  Filter,
  Calendar,
  DollarSign,
  FileText,
  ChevronLeft,
  ChevronRight,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Users,
  Eye,
  Edit,
  MoreHorizontal,
  User,
  Mail,
  Phone,
} from "lucide-react";
import {
  getAdminOrders,
  exportAdminOrders,
  getAdminOrderDetails,
  updateOrderStatus,
  type AdminOrder,
} from "@/lib/api/admin-orders";
import { type OrdersPagination } from "@/lib/api/user-subscriptions";
import { toast } from "@/hooks/use-toast";
import { AdminOrderDetailsModal } from "./admin-order-details-modal";
import { cn } from "@/lib/utils";
import { AdminOrderSummary } from "./admin-order-summary";

interface AdminOrderHistoryProps {
  isLoading?: boolean;
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "paid":
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case "pending":
      return <Clock className="h-4 w-4 text-yellow-600" />;
    case "failed":
      return <XCircle className="h-4 w-4 text-red-600" />;
    case "cancelled":
      return <XCircle className="h-4 w-4 text-gray-600" />;
    default:
      return <AlertCircle className="h-4 w-4 text-gray-600" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case "paid":
      return "bg-green-100 text-green-800 border-green-200";
    case "pending":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "failed":
      return "bg-red-100 text-red-800 border-red-200";
    case "cancelled":
      return "bg-gray-100 text-gray-800 border-gray-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const getUserRoleBadge = (role: string | undefined) => {
  switch (role) {
    case "admin":
      return (
        <Badge variant="destructive" className="text-xs">
          Admin
        </Badge>
      );
    case "user":
      return (
        <Badge variant="secondary" className="text-xs">
          User
        </Badge>
      );
    default:
      return (
        <Badge variant="outline" className="text-xs">
          User
        </Badge>
      );
  }
};

export function AdminOrderHistory({
  isLoading: externalLoading = false,
}: AdminOrderHistoryProps) {
  const [orders, setOrders] = useState<AdminOrder[]>([]);
  const [pagination, setPagination] = useState<OrdersPagination | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [isOrderModalOpen, setIsOrderModalOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  const loadOrders = async (
    page = 1,
    status?: string,
    type?: string,
    search?: string
  ) => {
    try {
      setIsLoading(true);

      const params: any = {
        page,
        limit: 15, // Show more orders for admin view
      };

      if (status && status !== "all") {
        params.status = status as any;
      }

      if (type && type !== "all") {
        params.type = type as any;
      }

      if (search && search.trim()) {
        params.search = search.trim();
      }

      const response = await getAdminOrders(params);

      if (response.success && response.data) {
        setOrders(response.data.orders);
        setPagination(response.data.pagination);
      } else {
        setOrders([]);
        setPagination(null);
        toast({
          title: "Error",
          description: response.message || "Failed to load order history",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error loading admin orders:", error);
      setOrders([]);
      setPagination(null);
      toast({
        title: "Error",
        description: "Failed to load order history",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadOrders(currentPage, statusFilter, typeFilter, searchTerm);
  }, [currentPage, statusFilter, typeFilter]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
    setCurrentPage(1);
  };

  const handleTypeFilter = (type: string) => {
    setTypeFilter(type);
    setCurrentPage(1);
  };

  const handleSearch = () => {
    setCurrentPage(1);
    loadOrders(1, statusFilter, typeFilter, searchTerm);
  };

  const handleRefresh = () => {
    loadOrders(currentPage, statusFilter, typeFilter, searchTerm);
  };

  const handleViewDetails = (orderId: string) => {
    setSelectedOrderId(orderId);
    setIsOrderModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsOrderModalOpen(false);
    setSelectedOrderId(null);
  };

  const handleExport = async () => {
    try {
      setIsExporting(true);

      const params: any = {};

      if (statusFilter && statusFilter !== "all") {
        params.status = statusFilter;
      }

      if (typeFilter && typeFilter !== "all") {
        params.type = typeFilter;
      }

      const response = await exportAdminOrders(params);

      if (response.success && response.data) {
        // Create CSV content
        const csvHeaders = [
          "Order ID",
          "User Email",
          "User Name",
          "User Role",
          "Plan",
          "Type",
          "Status",
          "Amount",
          "Currency",
          "Created At",
          "Verified",
        ];

        const csvRows = response.data.map((order) => [
          order.id,
          order.user.email,
          `${order.user.first_name} ${order.user.last_name}`.trim(),
          (order.user as any).role || "user",
          order.plan.name,
          order.type,
          order.status,
          order.amount_total_formatted,
          order.currency,
          new Date(order.created_at).toLocaleDateString(),
          (order.user as any).is_verified ? "Yes" : "No",
        ]);

        const csvContent = [
          csvHeaders.join(","),
          ...csvRows.map((row) =>
            row
              .map((cell) =>
                typeof cell === "string" && cell.includes(",")
                  ? `"${cell.replace(/"/g, '""')}"`
                  : cell
              )
              .join(",")
          ),
        ].join("\n");

        // Create and download file
        const blob = new Blob([csvContent], {
          type: "text/csv;charset=utf-8;",
        });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute(
          "download",
          `admin-orders-${new Date().toISOString().split("T")[0]}.csv`
        );
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast({
          title: "Export Successful",
          description: `Exported ${response.data.length} orders to CSV`,
        });
      } else {
        toast({
          title: "Export Failed",
          description: response.message || "Failed to export orders",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error exporting orders:", error);
      toast({
        title: "Export Failed",
        description: "An error occurred while exporting orders",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleStatusUpdate = async (
    orderId: string,
    newStatus: "pending" | "paid" | "failed" | "cancelled"
  ) => {
    try {
      const response = await updateOrderStatus(orderId, newStatus);

      if (response.success) {
        toast({
          title: "Status Updated",
          description: "Order status has been updated successfully",
        });
        // Refresh the orders list
        handleRefresh();
      } else {
        toast({
          title: "Update Failed",
          description:
            response.message ||
            "Failed to update order status. This feature may not be available.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating order status:", error);
      toast({
        title: "Update Failed",
        description: "Order status update is not available in the current API.",
        variant: "destructive",
      });
    }
  };

  if (externalLoading || isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            <CardTitle>Loading Admin Order History...</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex md:flex-row flex-col gap-3 items-start md:items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            Admin Order History
          </h2>
          <p className="text-muted-foreground">
            Manage and monitor all customer orders across the platform
          </p>
        </div>
        <div className="flex items-start md:items-center space-x-2">
          <Button
            onClick={handleExport}
            variant="outline"
            size="sm"
            disabled={isExporting}
          >
            {isExporting ? (
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Download className="mr-2 h-4 w-4" />
            )}
            Export CSV
          </Button>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Order Summary Statistics */}
      <AdminOrderSummary />

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="flex space-x-2">
                <Input
                  placeholder="Order ID, email, plan..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                />
                <Button onClick={handleSearch} size="sm">
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={statusFilter} onValueChange={handleStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Type</label>
              <Select value={typeFilter} onValueChange={handleTypeFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="subscription">Subscription</SelectItem>
                  <SelectItem value="one-time">One-time</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Quick Actions</label>
              <Button variant="outline" className="w-full" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Advanced Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5" />
            <span>Order History</span>
            {pagination && (
              <Badge variant="secondary" className="ml-2">
                {pagination.total_items} orders
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {orders.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No orders found matching your criteria.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Order ID</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Plan</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {orders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell className="font-mono text-sm">
                          {order.id.slice(0, 8)}...
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center space-x-2">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">
                                {order.user.first_name} {order.user.last_name}
                              </span>
                              {getUserRoleBadge((order.user as any).role)}
                            </div>
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                              <Mail className="h-3 w-3" />
                              <span>{order.user.email}</span>
                            </div>
                            {(order.user as any).is_verified && (
                              <div className="flex items-center space-x-1">
                                <CheckCircle className="h-3 w-3 text-green-600" />
                                <span className="text-xs text-green-600">
                                  Verified
                                </span>
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">{order.plan.name}</div>
                            <div className="flex items-center space-x-1">
                              <Badge variant="outline" className="text-xs">
                                {order.meta.interval}
                              </Badge>
                              {order.meta.change_type && (
                                <Badge
                                  variant="secondary"
                                  className="text-xs capitalize"
                                >
                                  {order.meta.change_type}
                                </Badge>
                              )}
                            </div>
                            {order.meta.upgrade_from &&
                              order.meta.upgrade_to && (
                                <div className="text-xs text-muted-foreground">
                                  {order.meta.upgrade_from} →{" "}
                                  {order.meta.upgrade_to}
                                </div>
                              )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              order.type === "subscription"
                                ? "default"
                                : "secondary"
                            }
                            className="capitalize"
                          >
                            {order.type}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(order.status)}
                            <Badge
                              variant="outline"
                              className={cn(
                                "capitalize",
                                getStatusColor(order.status)
                              )}
                            >
                              {order.status}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">
                          {order.amount_total_formatted}
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {new Date(order.created_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => handleViewDetails(order.id)}
                              >
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {/* <DropdownMenuLabel>
                                Change Status
                              </DropdownMenuLabel> */}
                              {/* {order.status !== "paid" && (
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleStatusUpdate(order.id, "paid")
                                  }
                                >
                                  <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                                  Mark as Paid
                                </DropdownMenuItem>
                              )}
                              {order.status !== "pending" && (
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleStatusUpdate(order.id, "pending")
                                  }
                                >
                                  <Clock className="mr-2 h-4 w-4 text-yellow-600" />
                                  Mark as Pending
                                </DropdownMenuItem>
                              )}
                              {order.status !== "failed" && (
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleStatusUpdate(order.id, "failed")
                                  }
                                >
                                  <XCircle className="mr-2 h-4 w-4 text-red-600" />
                                  Mark as Failed
                                </DropdownMenuItem>
                              )}
                              {order.status !== "cancelled" && (
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleStatusUpdate(order.id, "cancelled")
                                  }
                                >
                                  <XCircle className="mr-2 h-4 w-4 text-gray-600" />
                                  Mark as Cancelled
                                </DropdownMenuItem>
                              )} */}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {pagination && pagination.total_pages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing{" "}
                    {(pagination.current_page - 1) * pagination.items_per_page +
                      1}{" "}
                    to{" "}
                    {Math.min(
                      pagination.current_page * pagination.items_per_page,
                      pagination.total_items
                    )}{" "}
                    of {pagination.total_items} orders
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        handlePageChange(pagination.current_page - 1)
                      }
                      disabled={!pagination.has_prev}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>

                    <div className="flex items-center space-x-1">
                      {Array.from(
                        { length: Math.min(5, pagination.total_pages) },
                        (_, i) => {
                          const page = i + 1;
                          return (
                            <Button
                              key={page}
                              variant={
                                page === pagination.current_page
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              onClick={() => handlePageChange(page)}
                              className="w-8 h-8 p-0"
                            >
                              {page}
                            </Button>
                          );
                        }
                      )}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        handlePageChange(pagination.current_page + 1)
                      }
                      disabled={!pagination.has_next}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Admin Order Details Modal */}
      <AdminOrderDetailsModal
        orderId={selectedOrderId}
        isOpen={isOrderModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
}

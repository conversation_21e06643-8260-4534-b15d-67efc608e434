"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  User,
  Mail,
  Phone,
  CreditCard,
  Calendar,
  DollarSign,
  Package,
  RefreshCw,
  Copy,
  ExternalLink,
} from "lucide-react";
import { getAdminOrderDetails } from "@/lib/api/admin-orders";
import { type OrderDetails } from "@/lib/api/user-subscriptions";
import { toast } from "@/hooks/use-toast";

interface AdminOrderDetailsModalProps {
  orderId: string | null;
  isOpen: boolean;
  onClose: () => void;
}

// Component for displaying Stripe IDs with truncation and copy functionality
interface StripeIdDisplayProps {
  id: string;
  label: string;
  onCopy: (text: string, label: string) => void;
}

function StripeIdDisplay({ id, label, onCopy }: StripeIdDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const truncatedId = id.length > 20 ? `${id.slice(0, 20)}...` : id;

  return (
    <div className="space-y-1">
      <p className="text-sm font-medium text-gray-600">{label}</p>
      <div className="flex items-center justify-between p-2 bg-gray-50 rounded border">
        <div className="flex-1 min-w-0 mr-2">
          <p className="font-mono text-xs break-all">
            {isExpanded ? id : truncatedId}
          </p>
        </div>
        <div className="flex items-center space-x-1 flex-shrink-0">
          {id.length > 20 && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? "Less" : "More"}
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => onCopy(id, label)}
            title={`Copy ${label}`}
          >
            <Copy className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => window.open(`https://dashboard.stripe.com`, '_blank')}
            title="Open in Stripe Dashboard"
          >
            <ExternalLink className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "paid":
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case "pending":
      return <Clock className="h-4 w-4 text-yellow-600" />;
    case "failed":
      return <XCircle className="h-4 w-4 text-red-600" />;
    case "cancelled":
      return <XCircle className="h-4 w-4 text-gray-600" />;
    default:
      return <AlertCircle className="h-4 w-4 text-gray-600" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case "paid":
      return "bg-green-100 text-green-800 border-green-200";
    case "pending":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "failed":
      return "bg-red-100 text-red-800 border-red-200";
    case "cancelled":
      return "bg-gray-100 text-gray-800 border-gray-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

export function AdminOrderDetailsModal({
  orderId,
  isOpen,
  onClose,
}: AdminOrderDetailsModalProps) {
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOrderDetails = async (id: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await getAdminOrderDetails(id);

      if (response.success && response.data) {
        setOrderDetails(response.data);
      } else {
        setError(response.message || "Failed to load order details");
      }
    } catch (error) {
      console.error("Error fetching order details:", error);
      setError("An error occurred while loading order details");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (orderId && isOpen) {
      fetchOrderDetails(orderId);
    }
  }, [orderId, isOpen]);

  const handleCopy = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: `${label} copied to clipboard`,
    });
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5" />
            <span>Order Details (Admin View)</span>
          </DialogTitle>
          <DialogDescription>
            Comprehensive order information and management options
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>Loading order details...</span>
          </div>
        ) : error ? (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : orderDetails ? (
          <div className="space-y-6">
            {/* Order Header */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-600">Order ID</p>
                <div className="flex items-center space-x-2">
                  <p className="font-mono text-sm">{orderDetails.id}</p>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => handleCopy(orderDetails.id, "Order ID")}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-600">Status</p>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(orderDetails.status)}
                  <Badge className={getStatusColor(orderDetails.status)}>
                    {orderDetails.status.charAt(0).toUpperCase() +
                      orderDetails.status.slice(1)}
                  </Badge>
                </div>
              </div>

              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-600">
                  Total Amount
                </p>
                <p className="text-2xl font-bold">
                  {orderDetails.amount_total_formatted}
                </p>
              </div>
            </div>

            {/* Customer Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Customer Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="font-medium">
                        {orderDetails.user.first_name}{" "}
                        {orderDetails.user.last_name}
                      </p>
                      <p className="text-sm text-gray-500">Customer Name</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{orderDetails.user.email}</p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() =>
                            handleCopy(orderDetails.user.email, "Email")
                          }
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                      <p className="text-sm text-gray-500">Email Address</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium">User ID:</p>
                    <p className="font-mono text-sm">{orderDetails.user_id}</p>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() =>
                        handleCopy(orderDetails.user_id, "User ID")
                      }
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="font-medium">
                        {new Date(orderDetails.created_at).toLocaleDateString()}
                      </p>
                      <p className="text-sm text-gray-500">Order Date</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Plan Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Plan Information</h3>
              <div className="p-4 border rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-600">
                      Plan Name
                    </p>
                    <div className="flex items-center space-x-2">
                      <Package className="h-4 w-4 text-gray-500" />
                      <p className="font-medium">{orderDetails.plan.name}</p>
                    </div>
                  </div>

                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-600">
                      Billing Cycle
                    </p>
                    <Badge variant="outline" className="capitalize">
                      {orderDetails.meta.interval}
                    </Badge>
                  </div>

                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-600">
                      Order Type
                    </p>
                    <Badge
                      variant={
                        orderDetails.type === "subscription"
                          ? "default"
                          : "secondary"
                      }
                      className="capitalize"
                    >
                      {orderDetails.type}
                    </Badge>
                  </div>
                </div>

                {/* Plan Features */}
                {orderDetails.plan.features && (
                  <div className="mt-4">
                    <p className="text-sm font-medium text-gray-600 mb-2">
                      Plan Features
                    </p>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                      {orderDetails.plan.features.limits && (
                        <div className="space-y-1">
                          <p className="font-medium">Limits:</p>
                          <ul className="text-xs space-y-1 text-gray-600">
                            <li>
                              Products:{" "}
                              {
                                orderDetails.plan.features.limits
                                  .products_created
                              }
                            </li>
                            <li>
                              Analysis:{" "}
                              {
                                orderDetails.plan.features.limits
                                  .analysis_requests
                              }
                            </li>
                            <li>
                              Images:{" "}
                              {
                                orderDetails.plan.features.limits
                                  .image_generations
                              }
                            </li>
                            <li>
                              Videos:{" "}
                              {
                                orderDetails.plan.features.limits
                                  .video_generations
                              }
                            </li>
                          </ul>
                        </div>
                      )}
                      <div className="space-y-1">
                        <p className="font-medium">Features:</p>
                        <ul className="text-xs space-y-1 text-gray-600">
                          <li>
                            API Access:{" "}
                            {orderDetails.plan.features.api_access
                              ? "✅"
                              : "❌"}
                          </li>
                          <li>
                            Priority Support:{" "}
                            {orderDetails.plan.features.priority_support
                              ? "✅"
                              : "❌"}
                          </li>
                          <li>
                            Analytics:{" "}
                            {orderDetails.plan.features.analytics_access
                              ? "✅"
                              : "❌"}
                          </li>
                          <li>
                            AI Agents:{" "}
                            {orderDetails.plan.features.ai_agents_access
                              ? "✅"
                              : "❌"}
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Payment Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold"></h3>
              <div className="p-4 border rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span>Subtotal:</span>
                      <span className="font-medium">
                        {orderDetails.amount_subtotal_formatted}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Total:</span>
                      <span className="font-bold text-lg">
                        {orderDetails.amount_total_formatted}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Currency:</span>
                      <span className="font-medium uppercase">
                        {orderDetails.currency}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    {orderDetails.stripe_checkout_session_id && (
                      <StripeIdDisplay
                        id={orderDetails.stripe_checkout_session_id}
                        label="Stripe Session ID"
                        onCopy={handleCopy}
                      />
                    )}

                    {orderDetails.stripe_payment_intent_id && (
                      <StripeIdDisplay
                        id={orderDetails.stripe_payment_intent_id}
                        label="Payment Intent ID"
                        onCopy={handleCopy}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Transactions */}
            {orderDetails.transactions &&
              orderDetails.transactions.length > 0 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Transactions</h3>
                  <div className="border rounded-lg">
                    {orderDetails.transactions.map((transaction, index) => (
                      <div
                        key={transaction.id}
                        className="p-4 border-b last:border-b-0"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <Badge
                              variant={
                                transaction.status === "succeeded"
                                  ? "default"
                                  : "secondary"
                              }
                              className="capitalize"
                            >
                              {transaction.type}
                            </Badge>
                            <span className="text-sm text-gray-600">
                              {new Date(
                                transaction.processed_at
                              ).toLocaleDateString()}
                            </span>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">
                              {transaction.amount_formatted}
                            </p>
                            <p className="text-sm text-gray-600 capitalize">
                              {transaction.status}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

            {/* Order Items */}
            {orderDetails.items && orderDetails.items.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Order Items</h3>
                <div className="border rounded-lg">
                  {orderDetails.items.map((item, index) => (
                    <div key={item.id} className="p-4 border-b last:border-b-0">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{item.name}</p>
                          <p className="text-sm text-gray-600">
                            {item.description}
                          </p>
                          <p className="text-sm text-gray-600">
                            Quantity: {item.quantity}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">
                            ${(item.total_price / 100).toFixed(2)}
                          </p>
                          <p className="text-sm text-gray-600">
                            ${(item.unit_price / 100).toFixed(2)} each
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <Separator />

            {/* Footer with timestamps */}
            <div className="flex items-center justify-between text-sm text-gray-600">
              <p>
                Created: {new Date(orderDetails.created_at).toLocaleString()}
              </p>
              <p>
                Updated: {new Date(orderDetails.updated_at).toLocaleString()}
              </p>
            </div>
          </div>
        ) : null}
      </DialogContent>
    </Dialog>
  );
}

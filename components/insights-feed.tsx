"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Calendar, ThumbsUp, ThumbsDown, Bookmark, Share2, Lightbulb, TrendingUp, AlertCircle } from "lucide-react"

type Insight =
\
{
  id: string
  title: string
  content: string
  source: string
  sourceIcon?: string
  date: string
  category: string
  tags: string[]
  priority: "High" | "Medium" | "Low"
  saved: boolean
  liked: boolean
  disliked: boolean
  \
}

export function InsightsFeed()
\
{
  const [insights, setInsights] = useState<Insight[]>(
    [
    \{
      id: "1",
      title: "Fitness content engagement peaks on Monday mornings",
      content:
        "Analysis of the last 6 months of content performance shows that fitness-related content receives 2.3x higher engagement when posted on Monday mornings between 6-9am. Consider scheduling your most important fitness content during this window.",
      source: "Content Analytics Engine",
      sourceIcon: "/placeholder.svg",
      date: "2023-04-15",
      category: "Content Strategy",
      tags: ["Fitness", "Timing", "Engagement"],
      priority: "High",
      saved: false,
      liked: false,
      disliked: false,
    \},
    \{
      id: "2",
      title: "Product bundles outperforming individual items by 35%",
      content:
        "Customers who purchase product bundles have a 35% higher average order value compared to those who purchase individual items. Consider creating more strategic product bundles, especially in the kitchen and fitness categories.",
      source: "Sales Analysis Engine",
      sourceIcon: "/placeholder.svg",
      date: "2023-04-12",
      category: "Product Strategy",
      tags: ["Bundles", "AOV", "Sales"],
      priority: "High",
      saved: true,
      liked: true,
      disliked: false,
    \},
    \{
      id: "3",
      title: "Email open rates declining for promotional content",
      content:
        "Over the past 3 months, open rates for promotional emails have declined by 12%, while educational content open rates have remained stable. Consider shifting email strategy to focus more on educational content with subtle product mentions.",
      source: "Email Analytics Engine",
      sourceIcon: "/placeholder.svg",
      date: "2023-04-10",
      category: "Email Marketing",
      tags: ["Open Rates", "Content Type", "Engagement"],
      priority: "Medium",
      saved: false,
      liked: false,
      disliked: false,
    \},
    \{
      id: "4",
      title: "New competitor entering the fitness equipment space",
      content:
        "Market analysis has detected a new competitor (FitTech Solutions) entering the fitness equipment space with competitive pricing. They are currently focusing on yoga and home workout equipment. Monitor their marketing strategies and pricing closely.",
      source: "Market Intelligence Engine",
      sourceIcon: "/placeholder.svg",
      date: "2023-04-08",
      category: "Competitive Analysis",
      tags: ["Competition", "Fitness", "Market Threat"],
      priority: "High",
      saved: true,
      liked: false,
      disliked: false,
    \},
    \{
      id: "5",
      title: "Customer retention improving with loyalty program",
      content:
        "Customers enrolled in the loyalty program have a 45% higher retention rate compared to non-enrolled customers. The program is currently only at 22% enrollment. Consider more aggressive promotion of the loyalty program to increase enrollment.",
      source: "Customer Analytics Engine",
      sourceIcon: "/placeholder.svg",
      date: "2023-04-05",
      category: "Customer Retention",
      tags: ["Loyalty", "Retention", "Program Enrollment"],
      priority: "Medium",
      saved: false,
      liked: true,
      disliked: false,
    \},
    \{
      id: "6",
      title: "Rising shipping costs impacting margins",
      content:
        "Shipping costs have increased by 15% in the last quarter, reducing overall product margins by approximately 3%. Consider adjusting pricing strategy or exploring alternative shipping partners to maintain profitability.",
      source: "Financial Analysis Engine",
      sourceIcon: "/placeholder.svg",
      date: "2023-04-02",
      category: "Financial Analysis",
      tags: ["Shipping", "Costs", "Margins"],
      priority: "High",
      saved: false,
      liked: false,
      disliked: false,
    \},
    \{
      id: "7",
      title: "Video content driving 2x higher conversion rates",
      content:
        "Product pages featuring video demonstrations are converting at 2.1x the rate of pages with only images. Only 30% of product pages currently include video content. Prioritize adding video content to high-traffic product pages.",
      source: "Conversion Analytics Engine",
      sourceIcon: "/placeholder.svg",
      date: "2023-03-28",
      category: "Conversion Optimization",
      tags: ["Video", "Product Pages", "Conversion"],
      priority: "Medium",
      saved: false,
      liked: true,
      disliked: false,
    \},
  ],
  )

  const [activeTab, setActiveTab] = useState("all")

  const filteredInsights = insights.filter((insight) => \{
    if (activeTab === "all") return true
    if (activeTab === "high-priority") return insight.priority === "High"
    if (activeTab === "saved") return insight.saved
    return true
  \})

  const formatDate = (dateString: string) => \
  {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", \{
      year: "numeric",
      month: "short",
      day: "numeric",
    \}).format(date)
    \
  }

  const toggleSaved = (id: string) => \
  setInsights(
      insights.map((insight) => \{
        if (insight.id === id) \{
          return \{ ...insight, saved: !insight.saved \}
        \}
  return insight
  \
  ),
    )
  \
}

const toggleLiked = (id: string) => \
{
  setInsights(
      insights.map((insight) => \{
        if (insight.id === id) \{
          return \{ ...insight, liked: !insight.liked, disliked: false \}
        \}
  return insight
  \
}
),
    )
  \}

const toggleDisliked = (id: string) => \
{
  setInsights(
      insights.map((insight) => \{
        if (insight.id === id) \{
          return \{ ...insight, disliked: !insight.disliked, liked: false \}
        \}
  return insight
  \
}
),
    )
  \}

const getPriorityIcon = (priority: "High" | "Medium" | "Low") => \
{
    switch (priority) \{
      case "High":
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case "Medium":
        return <TrendingUp className="h-4 w-4 text-amber-500" />
      case "Low":
        return <Lightbulb className="h-4 w-4 text-blue-500" />
    \}
  \}

  return (
    <div className="space-y-4">
      <Tabs defaultValue="all" value=\{activeTab\} onValueChange=\{setActiveTab\} className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Insights</TabsTrigger>
          <TabsTrigger value="high-priority">High Priority</TabsTrigger>
          <TabsTrigger value="saved">Saved</TabsTrigger>
        </TabsList>

        <TabsContent value=\{activeTab\} className="space-y-4">
          \{filteredInsights.map((insight) => (
            <Card key=\{insight.id\} className="overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      \{getPriorityIcon(insight.priority)\}
                      <Badge
                        variant="outline"
                        className=\{
                          insight.priority === "High"
                            ? "bg-red-100 text-red-800"
                            : insight.priority === "Medium"
                              ? "bg-amber-100 text-amber-800"
                              : "bg-blue-100 text-blue-800"
                        \}
                      >
                        \{insight.priority\} Priority
                      </Badge>
                      <Badge variant="outline">\{insight.category\}</Badge>
                    </div>
                    <CardTitle className="text-lg">\{insight.title\}</CardTitle>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Calendar className="mr-1 h-3 w-3" />
                        \{formatDate(insight.date)\}
                      </div>
                      <span>•</span>
                      <div className="flex items-center">
                        <Avatar className="h-4 w-4 mr-1">
                          <AvatarImage src={insight.sourceIcon || "/placeholder.svg"} alt=\{insight.source\} />
                          <AvatarFallback>\{insight.source.substring(0, 2).toUpperCase()\}</AvatarFallback>
                        </Avatar>
                        \{insight.source\}
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pb-3">
                <p className="text-sm">\{insight.content\}</p>
                <div className="mt-3 flex flex-wrap gap-1">
                  \{insight.tags.map((tag) => (
                    <Badge key=\{tag\} variant="secondary" className="text-xs">
                      \{tag\}
                    </Badge>
                  ))\}
                </div>
              </CardContent>
              <div className="border-t px-4 py-2 flex justify-between">
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className=\{insight.liked ? "text-green-600" : ""\}
                    onClick=\{() => toggleLiked(insight.id)\}
                  >
                    <ThumbsUp className="h-4 w-4 mr-1" />
                    Helpful
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className=\{insight.disliked ? "text-red-600" : ""\}
                    onClick=\{() => toggleDisliked(insight.id)\}
                  >
                    <ThumbsDown className="h-4 w-4 mr-1" />
                    Not Helpful
                  </Button>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className=\{insight.saved ? "text-blue-600" : ""\}
                    onClick=\{() => toggleSaved(insight.id)\}
                  >
                    <Bookmark className="h-4 w-4 mr-1" />
                    \{insight.saved ? "Saved" : "Save"\}
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Share2 className="h-4 w-4 mr-1" />
                    Share
                  </Button>
                </div>
              </div>
            </Card>
          ))\}
        </TabsContent>
      </Tabs>
    </div>
  )
\}

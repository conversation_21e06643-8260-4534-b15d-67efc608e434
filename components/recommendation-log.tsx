"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Search,
  Filter,
  Plus,
  Calendar,
  CheckCircle2,
  XCircle,
  Clock,
  ArrowUpDown,
  ThumbsUp,
  BarChart3,
  FileText,
  Lightbulb,
} from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"

export function RecommendationLog() {
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [sortColumn, setSortColumn] = useState<string>("date")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
  const [selectedRecommendation, setSelectedRecommendation] = useState<Recommendation | null>(null)
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false)
  const [outcomeDialogOpen, setOutcomeDialogOpen] = useState(false)

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortColumn(column)
      setSortDirection("desc")
    }
  }

  // Filter recommendations based on search query and active tab
  const filteredRecommendations = recommendations.filter((recommendation) => {
    const matchesSearch =
      recommendation.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      recommendation.source.toLowerCase().includes(searchQuery.toLowerCase()) ||
      recommendation.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
      recommendation.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    if (activeTab === "all") return matchesSearch
    if (activeTab === "implemented") return matchesSearch && recommendation.status === "Implemented"
    if (activeTab === "pending") return matchesSearch && recommendation.status === "Pending"
    if (activeTab === "rejected") return matchesSearch && recommendation.status === "Rejected"
    if (activeTab === "high-impact") return matchesSearch && recommendation.impact === "High"

    return matchesSearch
  })

  // Sort the recommendations based on the selected column and direction
  const sortedRecommendations = [...filteredRecommendations].sort((a, b) => {
    const aValue = a[sortColumn as keyof typeof a]
    const bValue = b[sortColumn as keyof typeof b]

    if (sortColumn === "date") {
      return sortDirection === "asc"
        ? new Date(a.date).getTime() - new Date(b.date).getTime()
        : new Date(b.date).getTime() - new Date(a.date).getTime()
    }

    if (typeof aValue === "string" && typeof bValue === "string") {
      return sortDirection === "asc" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue)
    }

    return 0
  })

  // Count recommendations by status
  const statusCounts = recommendations.reduce(
    (acc, rec) => {
      acc[rec.status] = (acc[rec.status] || 0) + 1
      return acc
    },
    {} as Record<string, number>,
  )

  // Calculate success rate
  const implementedCount = statusCounts["Implemented"] || 0
  const totalWithOutcome = recommendations.filter((rec) => rec.outcome).length
  const successCount = recommendations.filter((rec) => rec.outcome === "Success").length
  const successRate = totalWithOutcome > 0 ? (successCount / totalWithOutcome) * 100 : 0

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Recommendations</CardTitle>
            <Lightbulb className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{recommendations.length}</div>
            <p className="text-xs text-muted-foreground">From all AI sources</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Implementation Rate</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {recommendations.length > 0 ? Math.round((implementedCount / recommendations.length) * 100) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">{implementedCount} implemented</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <ThumbsUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{successRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">Of implemented recommendations</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statusCounts["Pending"] || 0}</div>
            <p className="text-xs text-muted-foreground">Awaiting decision</p>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div className="relative w-full md:w-96">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search recommendations..."
            className="w-full pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="h-9">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button size="sm" className="h-9">
            <Plus className="mr-2 h-4 w-4" />
            Add Recommendation
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Recommendations</TabsTrigger>
          <TabsTrigger value="implemented">Implemented</TabsTrigger>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
          <TabsTrigger value="high-impact">High Impact</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>AI Recommendation Log</CardTitle>
              <CardDescription>Track and evaluate AI-generated recommendations and their outcomes</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[300px]">
                      <div className="flex items-center cursor-pointer" onClick={() => handleSort("title")}>
                        Recommendation
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className="flex items-center cursor-pointer" onClick={() => handleSort("source")}>
                        Source
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className="flex items-center cursor-pointer" onClick={() => handleSort("date")}>
                        Date
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className="flex items-center cursor-pointer" onClick={() => handleSort("category")}>
                        Category
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className="flex items-center cursor-pointer" onClick={() => handleSort("impact")}>
                        Impact
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className="flex items-center cursor-pointer" onClick={() => handleSort("status")}>
                        Status
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>Outcome</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedRecommendations.map((recommendation) => (
                    <TableRow key={recommendation.id}>
                      <TableCell className="font-medium">{recommendation.title}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage
                              src={recommendation.sourceIcon || "/placeholder.svg"}
                              alt={recommendation.source}
                            />
                            <AvatarFallback>{recommendation.source.substring(0, 2).toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <span>{recommendation.source}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                          {recommendation.date}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{recommendation.category}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={
                            recommendation.impact === "High"
                              ? "bg-green-100 text-green-800"
                              : recommendation.impact === "Medium"
                                ? "bg-amber-100 text-amber-800"
                                : "bg-blue-100 text-blue-800"
                          }
                        >
                          {recommendation.impact}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={
                            recommendation.status === "Implemented"
                              ? "bg-green-100 text-green-800"
                              : recommendation.status === "Pending"
                                ? "bg-amber-100 text-amber-800"
                                : "bg-red-100 text-red-800"
                          }
                        >
                          {recommendation.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {recommendation.outcome ? (
                          <Badge
                            className={
                              recommendation.outcome === "Success"
                                ? "bg-green-100 text-green-800"
                                : recommendation.outcome === "Partial"
                                  ? "bg-amber-100 text-amber-800"
                                  : "bg-red-100 text-red-800"
                            }
                          >
                            {recommendation.outcome}
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground text-sm">Not measured</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedRecommendation(recommendation)
                              setDetailsDialogOpen(true)
                            }}
                          >
                            <FileText className="h-4 w-4" />
                            <span className="sr-only">Details</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedRecommendation(recommendation)
                              setOutcomeDialogOpen(true)
                            }}
                          >
                            <BarChart3 className="h-4 w-4" />
                            <span className="sr-only">Outcome</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Recommendation Details Dialog */}
      <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Recommendation Details</DialogTitle>
            <DialogDescription>{selectedRecommendation?.title}</DialogDescription>
          </DialogHeader>

          {selectedRecommendation && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-xs text-muted-foreground">Source</Label>
                  <div className="flex items-center mt-1">
                    <Avatar className="h-6 w-6 mr-2">
                      <AvatarImage
                        src={selectedRecommendation.sourceIcon || "/placeholder.svg"}
                        alt={selectedRecommendation.source}
                      />
                      <AvatarFallback>{selectedRecommendation.source.substring(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <span>{selectedRecommendation.source}</span>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-muted-foreground">Date</Label>
                  <div className="flex items-center mt-1">
                    <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span>{selectedRecommendation.date}</span>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-muted-foreground">Category</Label>
                  <div className="mt-1">
                    <Badge variant="outline">{selectedRecommendation.category}</Badge>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-muted-foreground">Impact</Label>
                  <div className="mt-1">
                    <Badge
                      className={
                        selectedRecommendation.impact === "High"
                          ? "bg-green-100 text-green-800"
                          : selectedRecommendation.impact === "Medium"
                            ? "bg-amber-100 text-amber-800"
                            : "bg-blue-100 text-blue-800"
                      }
                    >
                      {selectedRecommendation.impact}
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-muted-foreground">Status</Label>
                  <div className="mt-1">
                    <Badge
                      className={
                        selectedRecommendation.status === "Implemented"
                          ? "bg-green-100 text-green-800"
                          : selectedRecommendation.status === "Pending"
                            ? "bg-amber-100 text-amber-800"
                            : "bg-red-100 text-red-800"
                      }
                    >
                      {selectedRecommendation.status}
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-muted-foreground">Outcome</Label>
                  <div className="mt-1">
                    {selectedRecommendation.outcome ? (
                      <Badge
                        className={
                          selectedRecommendation.outcome === "Success"
                            ? "bg-green-100 text-green-800"
                            : selectedRecommendation.outcome === "Partial"
                              ? "bg-amber-100 text-amber-800"
                              : "bg-red-100 text-red-800"
                        }
                      >
                        {selectedRecommendation.outcome}
                      </Badge>
                    ) : (
                      <span className="text-muted-foreground text-sm">Not measured</span>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <Label className="text-xs text-muted-foreground">Description</Label>
                <div className="mt-1 p-3 border rounded-md bg-muted/30">
                  <p className="text-sm">{selectedRecommendation.description}</p>
                </div>
              </div>

              <div>
                <Label className="text-xs text-muted-foreground">Implementation Notes</Label>
                <div className="mt-1 p-3 border rounded-md bg-muted/30">
                  <p className="text-sm">
                    {selectedRecommendation.implementationNotes || "No implementation notes available."}
                  </p>
                </div>
              </div>

              <div>
                <Label className="text-xs text-muted-foreground">Tags</Label>
                <div className="mt-1 flex flex-wrap gap-1">
                  {selectedRecommendation.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="bg-muted/50">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>

              {selectedRecommendation.metrics && (
                <div>
                  <Label className="text-xs text-muted-foreground">Metrics</Label>
                  <div className="mt-1 space-y-2">
                    {Object.entries(selectedRecommendation.metrics).map(([key, value]) => (
                      <div key={key} className="flex justify-between items-center text-sm">
                        <span>{key}:</span>
                        <span className="font-medium">{value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setDetailsDialogOpen(false)}>
              Close
            </Button>
            {selectedRecommendation?.status === "Pending" && (
              <>
                <Button variant="destructive">
                  <XCircle className="mr-2 h-4 w-4" />
                  Reject
                </Button>
                <Button>
                  <CheckCircle2 className="mr-2 h-4 w-4" />
                  Implement
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Outcome Dialog */}
      <Dialog open={outcomeDialogOpen} onOpenChange={setOutcomeDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Record Outcome</DialogTitle>
            <DialogDescription>{selectedRecommendation?.title}</DialogDescription>
          </DialogHeader>

          {selectedRecommendation && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="outcome">Outcome</Label>
                <Select defaultValue={selectedRecommendation.outcome || "not_measured"}>
                  <SelectTrigger id="outcome">
                    <SelectValue placeholder="Select outcome" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="not_measured">Not measured</SelectItem>
                    <SelectItem value="Success">Success</SelectItem>
                    <SelectItem value="Partial">Partial Success</SelectItem>
                    <SelectItem value="Failure">Failure</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="outcome-notes">Outcome Notes</Label>
                <Textarea
                  id="outcome-notes"
                  placeholder="Describe the results of implementing this recommendation"
                  defaultValue={selectedRecommendation.outcomeNotes || ""}
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <Label>Metrics</Label>
                <div className="space-y-2 border rounded-md p-3">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <Label htmlFor="metric-name-1" className="text-xs">
                        Metric Name
                      </Label>
                      <Input id="metric-name-1" placeholder="e.g., Conversion Rate" />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="metric-value-1" className="text-xs">
                        Value
                      </Label>
                      <Input id="metric-value-1" placeholder="e.g., +15%" />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <Label htmlFor="metric-name-2" className="text-xs">
                        Metric Name
                      </Label>
                      <Input id="metric-name-2" placeholder="e.g., Revenue" />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="metric-value-2" className="text-xs">
                        Value
                      </Label>
                      <Input id="metric-value-2" placeholder="e.g., $5,200" />
                    </div>
                  </div>

                  <Button variant="outline" size="sm" className="w-full mt-2">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Metric
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="learnings">Key Learnings</Label>
                <Textarea
                  id="learnings"
                  placeholder="What did you learn from implementing this recommendation?"
                  defaultValue={selectedRecommendation.learnings || ""}
                  rows={3}
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setOutcomeDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setOutcomeDialogOpen(false)}>Save Outcome</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Types
interface Recommendation {
  id: number
  title: string
  description: string
  source: string
  sourceIcon?: string
  date: string
  category: string
  impact: "High" | "Medium" | "Low"
  status: "Implemented" | "Pending" | "Rejected"
  outcome?: "Success" | "Partial" | "Failure"
  implementationNotes?: string
  outcomeNotes?: string
  tags: string[]
  metrics?: Record<string, string>
  learnings?: string
}

// Sample data
const recommendations: Recommendation[] = [
  {
    id: 1,
    title: "Implement dynamic pricing based on demand patterns",
    description:
      "Analysis of sales data shows significant fluctuations in demand throughout the week. Implementing dynamic pricing that adjusts based on these patterns could increase overall revenue by an estimated 12-18%.",
    source: "Poppy AI",
    sourceIcon: "/placeholder.svg",
    date: "2023-04-15",
    category: "Pricing Strategy",
    impact: "High",
    status: "Implemented",
    outcome: "Success",
    implementationNotes:
      "Implemented a dynamic pricing algorithm that adjusts product prices based on day of week and time of day. Initial rollout on fitness products, with plans to expand to all categories.",
    outcomeNotes:
      "Revenue increased by 15.3% in the first month after implementation, with no negative impact on customer satisfaction scores.",
    tags: ["Pricing", "Revenue Optimization", "AI Recommendation"],
    metrics: {
      "Revenue Increase": "+15.3%",
      "Conversion Rate": "+2.1%",
      "Average Order Value": "+$12.45",
    },
    learnings:
      "Dynamic pricing works best when changes are subtle and predictable. Customers respond negatively to dramatic price fluctuations.",
  },
  {
    id: 2,
    title: "Create content bundles for high-performing product categories",
    description:
      "Data shows that fitness and health content has 3.2x higher engagement than other categories. Creating specialized content bundles that combine videos, social posts, and product recommendations could increase conversion rates.",
    source: "Content Analysis Engine",
    sourceIcon: "/placeholder.svg",
    date: "2023-04-10",
    category: "Content Strategy",
    impact: "Medium",
    status: "Implemented",
    outcome: "Partial",
    implementationNotes:
      "Created three content bundles for the fitness category, focusing on home workouts, nutrition, and recovery. Each bundle includes 5 videos, 15 social posts, and product recommendations.",
    outcomeNotes:
      "Engagement increased by 45%, but conversion only improved by 5.2%, below the projected 10-15% increase.",
    tags: ["Content", "Fitness", "Bundling"],
    metrics: {
      Engagement: "+45%",
      "Conversion Rate": "+5.2%",
      "Time on Site": "+2:15 min",
    },
  },
  {
    id: 3,
    title: "Optimize product descriptions with emotion-focused language",
    description:
      "Sentiment analysis of customer reviews indicates that emotional language drives higher conversion rates. Updating product descriptions to include more emotion-focused language could improve conversion rates by 8-12%.",
    source: "Poppy AI",
    sourceIcon: "/placeholder.svg",
    date: "2023-04-05",
    category: "Copywriting",
    impact: "Medium",
    status: "Pending",
    tags: ["Copywriting", "Conversion Optimization", "Emotional Marketing"],
  },
  {
    id: 4,
    title: "Implement cross-selling recommendations based on purchase history",
    description:
      "Analysis of purchase patterns reveals strong correlations between certain product categories. Implementing personalized cross-selling recommendations could increase average order value by 15-20%.",
    source: "Sales Analysis Engine",
    sourceIcon: "/placeholder.svg",
    date: "2023-04-02",
    category: "Sales Strategy",
    impact: "High",
    status: "Implemented",
    outcome: "Success",
    implementationNotes:
      "Implemented a recommendation engine that suggests complementary products based on purchase history and browsing behavior. Deployed on product pages and in post-purchase emails.",
    outcomeNotes: "Average order value increased by 18.7% within the first two weeks of implementation.",
    tags: ["Cross-selling", "Personalization", "Revenue Optimization"],
    metrics: {
      "Average Order Value": "+18.7%",
      "Items Per Order": "+1.3",
      "Email Click-through Rate": "+22.5%",
    },
  },
  {
    id: 5,
    title: "Shift marketing budget allocation based on platform performance",
    description:
      "ROI analysis shows that Instagram and TikTok are outperforming other platforms by 2.5x. Reallocating 30% of Facebook budget to these platforms could improve overall marketing ROI.",
    source: "Marketing Analytics Engine",
    sourceIcon: "/placeholder.svg",
    date: "2023-03-28",
    category: "Marketing Strategy",
    impact: "High",
    status: "Rejected",
    outcomeNotes: "Decision made to maintain current budget allocation due to strategic partnerships with Facebook.",
    tags: ["Budget Allocation", "Social Media", "ROI Optimization"],
  },
  {
    id: 6,
    title: "Implement seasonal content calendar based on trend analysis",
    description:
      "Trend analysis shows predictable seasonal patterns in content engagement. Creating a 12-month content calendar that aligns with these patterns could improve content performance by 25-30%.",
    source: "Trends Tracker",
    sourceIcon: "/placeholder.svg",
    date: "2023-03-25",
    category: "Content Strategy",
    impact: "Medium",
    status: "Implemented",
    outcome: "Success",
    implementationNotes:
      "Developed a comprehensive 12-month content calendar with themes aligned to seasonal trends and shopping patterns. Integrated with automation tools for scheduling.",
    outcomeNotes: "Content engagement increased by 32% and conversion from content to sales improved by 18%.",
    tags: ["Content Calendar", "Seasonal Marketing", "Trend Analysis"],
    metrics: {
      "Content Engagement": "+32%",
      "Content-to-Sale Conversion": "+18%",
      "Social Shares": "+45%",
    },
  },
  {
    id: 7,
    title: "Optimize email send times based on open rate analysis",
    description:
      "Analysis of email open rates shows significant variations based on send time. Optimizing send times by segment could increase open rates by 15-25%.",
    source: "Email Analytics Engine",
    sourceIcon: "/placeholder.svg",
    date: "2023-03-20",
    category: "Email Marketing",
    impact: "Low",
    status: "Implemented",
    outcome: "Success",
    implementationNotes:
      "Implemented segment-specific send times based on historical open rate data. Created five time slots optimized for different customer segments.",
    outcomeNotes: "Email open rates increased by 22.3% and click-through rates improved by 17.8%.",
    tags: ["Email Marketing", "Timing Optimization", "Segmentation"],
    metrics: {
      "Open Rate": "+22.3%",
      "Click-through Rate": "+17.8%",
      "Conversion Rate": "+8.5%",
    },
  },
  {
    id: 8,
    title: "Develop avatar-specific landing pages for top traffic sources",
    description:
      "Traffic analysis shows that different avatars attract visitors from different sources. Creating avatar-specific landing pages could improve conversion rates by 20-30%.",
    source: "Conversion Optimization Engine",
    sourceIcon: "/placeholder.svg",
    date: "2023-03-15",
    category: "Conversion Optimization",
    impact: "High",
    status: "Pending",
    tags: ["Landing Pages", "Avatars", "Conversion Optimization"],
  },
  {
    id: 9,
    title: "Implement A/B testing for product page layouts",
    description:
      "Heatmap analysis shows potential issues with current product page layout. Implementing A/B testing with alternative layouts could identify opportunities for conversion improvement.",
    source: "UX Analysis Engine",
    sourceIcon: "/placeholder.svg",
    date: "2023-03-10",
    category: "UX Optimization",
    impact: "Medium",
    status: "Implemented",
    outcome: "Failure",
    implementationNotes:
      "Created three variant layouts for product pages and implemented A/B testing across 30% of traffic.",
    outcomeNotes:
      "None of the variant layouts outperformed the control. Testing was concluded after 4 weeks with no significant improvements.",
    tags: ["A/B Testing", "Product Pages", "UX Design"],
    metrics: {
      "Variant A Performance": "-2.1%",
      "Variant B Performance": "+0.8% (not significant)",
      "Variant C Performance": "-1.5%",
    },
    learnings:
      "Current layout appears to be well-optimized. Future tests should focus on smaller elements rather than complete layout changes.",
  },
  {
    id: 10,
    title: "Create automated re-engagement campaigns for dormant customers",
    description:
      "Customer analysis shows that 22% of customers become dormant after 60 days of inactivity. Implementing automated re-engagement campaigns could recover 5-10% of these customers.",
    source: "Customer Analytics Engine",
    sourceIcon: "/placeholder.svg",
    date: "2023-03-05",
    category: "Customer Retention",
    impact: "Medium",
    status: "Implemented",
    outcome: "Success",
    implementationNotes:
      "Created a three-stage re-engagement campaign with personalized offers based on previous purchase history. Automated to trigger after 60 days of inactivity.",
    outcomeNotes:
      "Successfully re-engaged 8.7% of dormant customers, generating additional revenue of $12,450 in the first month.",
    tags: ["Customer Retention", "Automation", "Personalization"],
    metrics: {
      "Re-engagement Rate": "8.7%",
      "Revenue Generated": "$12,450",
      ROI: "420%",
    },
  },
]

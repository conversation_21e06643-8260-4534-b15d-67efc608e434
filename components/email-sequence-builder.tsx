"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  PlusCircle,
  Mail,
  Clock,
  MoreHorizontal,
  Edit,
  Copy,
  Trash2,
  Play,
  Pause,
  Users,
  FileText,
  ChevronDown,
  ChevronUp,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"

interface EmailSequenceBuilderProps {
  selectedAvatar: string
}

export function EmailSequenceBuilder({ selectedAvatar }: EmailSequenceBuilderProps) {
  const [activeTab, setActiveTab] = useState("sequences")
  const [isCreateSequenceOpen, setIsCreateSequenceOpen] = useState(false)
  const [newSequence, setNewSequence] = useState({
    name: "",
    description: "",
    avatar: "travel",
    type: "welcome",
  })

  // Filter sequences based on selected avatar
  const filteredSequences = emailSequences.filter((sequence) => {
    return selectedAvatar === "all" || sequence.avatar === selectedAvatar
  })

  // Get avatar badge color
  const getAvatarBadgeColor = (avatar: string) => {
    switch (avatar) {
      case "travel":
        return "bg-blue-100 text-blue-800"
      case "home":
        return "bg-green-100 text-green-800"
      case "health":
        return "bg-purple-100 text-purple-800"
      case "fitness":
        return "bg-red-100 text-red-800"
      case "entrepreneur":
        return "bg-amber-100 text-amber-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    }).format(date)
  }

  // Handle creating a new sequence
  const handleCreateSequence = () => {
    if (!newSequence.name) {
      toast({
        title: "Missing Information",
        description: "Please provide a name for the sequence.",
        variant: "destructive",
      })
      return
    }

    // In a real app, this would add the sequence to the database
    toast({
      title: "Sequence Created",
      description: "Your new email sequence has been created successfully.",
    })

    setIsCreateSequenceOpen(false)
    setNewSequence({
      name: "",
      description: "",
      avatar: "travel",
      type: "welcome",
    })
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Email Sequence Builder</CardTitle>
            <CardDescription>Create and manage email sequences for each avatar</CardDescription>
          </div>
          <Button onClick={() => setIsCreateSequenceOpen(true)}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Sequence
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="sequences">Sequences</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="sequences" className="space-y-4">
            <div className="rounded-md border">
              <Table>
                <TableHeader className="bg-sage-50">
                  <TableRow>
                    <TableHead className="w-[250px]">Sequence Name</TableHead>
                    <TableHead>Avatar</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead className="text-center">Emails</TableHead>
                    <TableHead className="text-center">Subscribers</TableHead>
                    <TableHead className="text-center">Open Rate</TableHead>
                    <TableHead className="text-center">Click Rate</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSequences.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="h-24 text-center">
                        No sequences found for the selected avatar.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredSequences.map((sequence) => (
                      <TableRow key={sequence.id}>
                        <TableCell className="font-medium">
                          <div>
                            <div>{sequence.name}</div>
                            <div className="text-xs text-muted-foreground">
                              Created: {formatDate(sequence.createdAt)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className={`capitalize ${getAvatarBadgeColor(sequence.avatar)}`}>
                            {sequence.avatar}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="capitalize">
                            {sequence.type}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-center">{sequence.emailCount}</TableCell>
                        <TableCell className="text-center">{sequence.subscriberCount}</TableCell>
                        <TableCell className="text-center">
                          <span className={sequence.openRate > 25 ? "text-green-600" : "text-amber-600"}>
                            {sequence.openRate}%
                          </span>
                        </TableCell>
                        <TableCell className="text-center">
                          <span className={sequence.clickRate > 5 ? "text-green-600" : "text-amber-600"}>
                            {sequence.clickRate}%
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={
                              sequence.status === "active"
                                ? "bg-green-100 text-green-800"
                                : sequence.status === "draft"
                                  ? "bg-gray-100 text-gray-800"
                                  : "bg-amber-100 text-amber-800"
                            }
                          >
                            {sequence.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Sequence
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Copy className="mr-2 h-4 w-4" />
                                Duplicate
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {sequence.status === "active" ? (
                                <DropdownMenuItem>
                                  <Pause className="mr-2 h-4 w-4" />
                                  Pause Sequence
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem>
                                  <Play className="mr-2 h-4 w-4" />
                                  Activate Sequence
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem>
                                <Users className="mr-2 h-4 w-4" />
                                Manage Subscribers
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Sequence
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {filteredSequences.length > 0 && (
              <Card className="p-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium">Welcome Sequence</h3>
                      <p className="text-sm text-muted-foreground">
                        Introduces new subscribers to the Travel Avatar and builds trust
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <FileText className="mr-2 h-4 w-4" />
                        Preview
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {emailSequenceSteps.map((step, index) => (
                      <div key={step.id} className="flex items-start rounded-md border p-3 hover:bg-sage-50">
                        <div className="mr-4 flex h-8 w-8 items-center justify-center rounded-full bg-sage-100 text-sage-700">
                          {index + 1}
                        </div>
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{step.subject}</h4>
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline" className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {step.delay} {step.delayUnit}
                              </Badge>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground">{step.preview}</p>
                          <div className="flex items-center justify-between text-xs">
                            <div className="flex items-center space-x-4">
                              <span className="text-muted-foreground">Open Rate: {step.openRate}%</span>
                              <span className="text-muted-foreground">Click Rate: {step.clickRate}%</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              {index !== 0 && (
                                <Button variant="ghost" size="icon" className="h-6 w-6">
                                  <ChevronUp className="h-3 w-3" />
                                </Button>
                              )}
                              {index !== emailSequenceSteps.length - 1 && (
                                <Button variant="ghost" size="icon" className="h-6 w-6">
                                  <ChevronDown className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    <Button variant="outline" className="w-full justify-center">
                      <PlusCircle className="mr-2 h-4 w-4" />
                      Add Email
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {emailTemplates.map((template) => (
                <Card key={template.id} className="overflow-hidden">
                  <div className="h-40 bg-sage-100 flex items-center justify-center">
                    <Mail className="h-12 w-12 text-sage-400" />
                  </div>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">{template.name}</CardTitle>
                    <CardDescription>{template.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="flex items-center justify-between text-sm">
                      <Badge variant="outline" className="capitalize">
                        {template.category}
                      </Badge>
                      <span className="text-muted-foreground">Used {template.usageCount} times</span>
                    </div>
                  </CardContent>
                  <div className="p-4 pt-0 flex justify-end space-x-2">
                    <Button variant="outline" size="sm">
                      Preview
                    </Button>
                    <Button size="sm">Use Template</Button>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Email Performance</CardTitle>
                  <CardDescription>Open and click rates across all sequences</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex items-center justify-center bg-sage-50 rounded-md">
                    <p className="text-muted-foreground">Email Performance Chart</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Subscriber Growth</CardTitle>
                  <CardDescription>New subscribers and unsubscribes over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex items-center justify-center bg-sage-50 rounded-md">
                    <p className="text-muted-foreground">Subscriber Growth Chart</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Create Sequence Dialog */}
        <Dialog open={isCreateSequenceOpen} onOpenChange={setIsCreateSequenceOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Create New Email Sequence</DialogTitle>
              <DialogDescription>
                Set up an automated email sequence to nurture your subscribers and customers.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="sequence-name">Sequence Name *</Label>
                <Input
                  id="sequence-name"
                  placeholder="e.g., Welcome Sequence, Product Launch, etc."
                  value={newSequence.name}
                  onChange={(e) => setNewSequence({ ...newSequence, name: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="sequence-description">Description</Label>
                <Input
                  id="sequence-description"
                  placeholder="Brief description of this sequence's purpose"
                  value={newSequence.description}
                  onChange={(e) => setNewSequence({ ...newSequence, description: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sequence-avatar">Avatar</Label>
                  <Select
                    value={newSequence.avatar}
                    onValueChange={(value) => setNewSequence({ ...newSequence, avatar: value })}
                  >
                    <SelectTrigger id="sequence-avatar">
                      <SelectValue placeholder="Select avatar" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="travel">Travel</SelectItem>
                      <SelectItem value="home">Home</SelectItem>
                      <SelectItem value="health">Health</SelectItem>
                      <SelectItem value="fitness">Fitness</SelectItem>
                      <SelectItem value="entrepreneur">Entrepreneur</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sequence-type">Sequence Type</Label>
                  <Select
                    value={newSequence.type}
                    onValueChange={(value) => setNewSequence({ ...newSequence, type: value })}
                  >
                    <SelectTrigger id="sequence-type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="welcome">Welcome</SelectItem>
                      <SelectItem value="product-launch">Product Launch</SelectItem>
                      <SelectItem value="abandoned-cart">Abandoned Cart</SelectItem>
                      <SelectItem value="re-engagement">Re-engagement</SelectItem>
                      <SelectItem value="webinar">Webinar</SelectItem>
                      <SelectItem value="course">Course</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateSequenceOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateSequence}>Create Sequence</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}

// Sample email sequences
const emailSequences = [
  {
    id: "sequence-1",
    name: "Welcome Sequence",
    avatar: "travel",
    type: "welcome",
    emailCount: 5,
    subscriberCount: 1245,
    openRate: 32.7,
    clickRate: 8.4,
    status: "active",
    createdAt: "2023-03-15T10:30:00Z",
  },
  {
    id: "sequence-2",
    name: "Fitness Challenge",
    avatar: "fitness",
    type: "course",
    emailCount: 8,
    subscriberCount: 876,
    openRate: 41.2,
    clickRate: 12.8,
    status: "active",
    createdAt: "2023-04-02T14:45:00Z",
  },
  {
    id: "sequence-3",
    name: "Home Decor Tips",
    avatar: "home",
    type: "welcome",
    emailCount: 4,
    subscriberCount: 654,
    openRate: 28.9,
    clickRate: 6.2,
    status: "draft",
    createdAt: "2023-05-10T09:15:00Z",
  },
  {
    id: "sequence-4",
    name: "Wellness Program",
    avatar: "health",
    type: "course",
    emailCount: 10,
    subscriberCount: 932,
    openRate: 35.6,
    clickRate: 9.7,
    status: "active",
    createdAt: "2023-02-20T11:20:00Z",
  },
  {
    id: "sequence-5",
    name: "Business Launch",
    avatar: "entrepreneur",
    type: "product-launch",
    emailCount: 6,
    subscriberCount: 1087,
    openRate: 38.4,
    clickRate: 11.2,
    status: "paused",
    createdAt: "2023-04-15T16:30:00Z",
  },
  {
    id: "sequence-6",
    name: "Asia Travel Guide",
    avatar: "travel",
    type: "course",
    emailCount: 7,
    subscriberCount: 765,
    openRate: 29.8,
    clickRate: 7.5,
    status: "active",
    createdAt: "2023-03-05T13:45:00Z",
  },
]

// Sample email sequence steps
const emailSequenceSteps = [
  {
    id: "step-1",
    subject: "Welcome to Your Travel Journey!",
    preview: "Thank you for joining our travel community. Here's what to expect...",
    delay: 0,
    delayUnit: "days",
    openRate: 45.2,
    clickRate: 12.8,
  },
  {
    id: "step-2",
    subject: "Your First Travel Planning Guide",
    preview: "Here's a step-by-step guide to planning your next adventure...",
    delay: 2,
    delayUnit: "days",
    openRate: 38.7,
    clickRate: 10.4,
  },
  {
    id: "step-3",
    subject: "Travel Essentials: What to Pack",
    preview: "Don't forget these essential items for your next trip...",
    delay: 4,
    delayUnit: "days",
    openRate: 36.1,
    clickRate: 9.2,
  },
  {
    id: "step-4",
    subject: "Hidden Gems: Off the Beaten Path",
    preview: "Discover these lesser-known destinations that tourists often miss...",
    delay: 7,
    delayUnit: "days",
    openRate: 33.5,
    clickRate: 8.7,
  },
  {
    id: "step-5",
    subject: "Special Offer for Our Travel Community",
    preview: "As a valued member, here's an exclusive discount on our premium travel guides...",
    delay: 10,
    delayUnit: "days",
    openRate: 29.8,
    clickRate: 15.3,
  },
]

// Sample email templates
const emailTemplates = [
  {
    id: "template-1",
    name: "Welcome Email",
    description: "Introduce new subscribers to your brand",
    category: "onboarding",
    usageCount: 5,
  },
  {
    id: "template-2",
    name: "Product Announcement",
    description: "Announce a new product or service",
    category: "marketing",
    usageCount: 3,
  },
  {
    id: "template-3",
    name: "Abandoned Cart",
    description: "Remind customers about items left in cart",
    category: "e-commerce",
    usageCount: 2,
  },
  {
    id: "template-4",
    name: "Weekly Newsletter",
    description: "Share weekly updates and content",
    category: "newsletter",
    usageCount: 8,
  },
  {
    id: "template-5",
    name: "Testimonial Showcase",
    description: "Highlight customer success stories",
    category: "social proof",
    usageCount: 1,
  },
  {
    id: "template-6",
    name: "Event Invitation",
    description: "Invite subscribers to an upcoming event",
    category: "events",
    usageCount: 4,
  },
]

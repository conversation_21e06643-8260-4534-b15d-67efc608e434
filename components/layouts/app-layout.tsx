'use client'

import { 
  <PERSON>bar<PERSON>rov<PERSON>, 
  Sidebar, 
  Sidebar<PERSON>ontent, 
  SidebarHeader, 
  SidebarTrigger,
  SidebarInset
} from "@/components/ui/sidebar"
import { SidebarNav, sidebarNavItems } from "@/components/sidebar-nav"
import { DashboardHeader } from "@/components/dashboard-header"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { Toaster } from "@/components/ui/toaster"

interface AppLayoutProps {
  children: React.ReactNode
  title?: string
  requireAuth?: boolean
}

export function AppLayout({ children, title, requireAuth = true }: AppLayoutProps) {
  return (
    <ProtectedRoute requireAuth={requireAuth}>
      <SidebarProvider>
        <div className="flex min-h-screen w-full">
          <Sidebar>
            <SidebarHeader className="p-4">
              <h2 className="text-lg font-semibold">Command Center</h2>
            </SidebarHeader>
            <SidebarContent className="p-4">
              <SidebarNav items={sidebarNavItems} />
            </SidebarContent>
          </Sidebar>
          
          <SidebarInset className="flex-1">
            <DashboardHeader />

            <div className="flex-1">
              {children}
            </div>
          </SidebarInset>
        </div>
        <Toaster />
      </SidebarProvider>
    </ProtectedRoute>
  )
}

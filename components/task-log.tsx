"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Search, Filter, CheckCircle2, XCircle, Clock, AlertTriangle, Bot, Calendar, Download } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"

// Sample task data
const tasks = [
  {
    id: "task-1",
    agentId: "agent-1",
    agentName: "Content Scout",
    action: "Research trending topics",
    status: "completed",
    timestamp: "2023-05-16T08:30:00Z",
    duration: 120, // seconds
    details: "Researched 15 trending topics for travel niche",
    avatar: "travel",
  },
  {
    id: "task-2",
    agentId: "agent-2",
    agentName: "Engagement Bot",
    action: "Reply to comments",
    status: "completed",
    timestamp: "2023-05-16T09:15:00Z",
    duration: 45, // seconds
    details: "Replied to 12 comments on latest fitness video",
    avatar: "fitness",
  },
  {
    id: "task-3",
    agentId: "agent-3",
    agentName: "Analytics Miner",
    action: "Generate weekly report",
    status: "failed",
    timestamp: "2023-05-16T07:00:00Z",
    duration: 180, // seconds
    details: "Failed to access analytics API: Rate limit exceeded",
    avatar: "all",
  },
  {
    id: "task-4",
    agentId: "agent-4",
    agentName: "Content Repurposer",
    action: "Create social media posts",
    status: "in_progress",
    timestamp: "2023-05-16T10:00:00Z",
    duration: 300, // seconds
    details: "Creating 5 Instagram posts from latest entrepreneur video",
    avatar: "entrepreneur",
  },
  {
    id: "task-5",
    agentId: "agent-5",
    agentName: "Hashtag Optimizer",
    action: "Update hashtag database",
    status: "completed",
    timestamp: "2023-05-16T06:45:00Z",
    duration: 90, // seconds
    details: "Added 25 new trending hashtags for home decor",
    avatar: "home",
  },
  {
    id: "task-6",
    agentId: "agent-6",
    agentName: "Affiliate Hunter",
    action: "Search new affiliate programs",
    status: "warning",
    timestamp: "2023-05-16T05:30:00Z",
    duration: 240, // seconds
    details: "Found 3 new programs but verification needed",
    avatar: "health",
  },
  {
    id: "task-7",
    agentId: "agent-7",
    agentName: "Scheduler Bot",
    action: "Optimize posting schedule",
    status: "completed",
    timestamp: "2023-05-16T04:15:00Z",
    duration: 150, // seconds
    details: "Rescheduled 8 posts for optimal engagement times",
    avatar: "all",
  },
  {
    id: "task-8",
    agentId: "agent-1",
    agentName: "Content Scout",
    action: "Analyze competitor content",
    status: "completed",
    timestamp: "2023-05-16T03:00:00Z",
    duration: 210, // seconds
    details: "Analyzed top 10 competitor travel channels",
    avatar: "travel",
  },
  {
    id: "task-9",
    agentId: "agent-2",
    agentName: "Engagement Bot",
    action: "Monitor mentions",
    status: "completed",
    timestamp: "2023-05-16T02:30:00Z",
    duration: 60, // seconds
    details: "Monitored and flagged 3 important mentions",
    avatar: "fitness",
  },
  {
    id: "task-10",
    agentId: "agent-4",
    agentName: "Content Repurposer",
    action: "Generate blog post",
    status: "failed",
    timestamp: "2023-05-16T01:45:00Z",
    duration: 270, // seconds
    details: "Failed to generate blog post: Content API error",
    avatar: "entrepreneur",
  },
  {
    id: "task-11",
    agentId: "agent-7",
    agentName: "Scheduler Bot",
    action: "Reschedule canceled posts",
    status: "completed",
    timestamp: "2023-05-16T00:30:00Z",
    duration: 45, // seconds
    details: "Rescheduled 2 canceled posts",
    avatar: "all",
  },
  {
    id: "task-12",
    agentId: "agent-3",
    agentName: "Analytics Miner",
    action: "Analyze conversion rates",
    status: "completed",
    timestamp: "2023-05-15T23:15:00Z",
    duration: 180, // seconds
    details: "Generated conversion rate report for all products",
    avatar: "all",
  },
]

export function TaskLog() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [agentFilter, setAgentFilter] = useState("all")
  const [avatarFilter, setAvatarFilter] = useState("all")
  const [dateFilter, setDateFilter] = useState("today")

  // Filter tasks based on search and filters
  const filteredTasks = tasks.filter((task) => {
    const matchesSearch =
      task.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.details.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.agentName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || task.status === statusFilter
    const matchesAgent = agentFilter === "all" || task.agentId === agentFilter
    const matchesAvatar = avatarFilter === "all" || task.avatar === avatarFilter

    // Filter by date
    let dateFilterMatches = true
    if (dateFilter !== "all") {
      const taskDate = new Date(task.timestamp)
      const today = new Date()

      if (dateFilter === "today") {
        dateFilterMatches = taskDate.toDateString() === today.toDateString()
      } else if (dateFilter === "yesterday") {
        const yesterday = new Date(today)
        yesterday.setDate(yesterday.getDate() - 1)
        dateFilterMatches = taskDate.toDateString() === yesterday.toDateString()
      } else if (dateFilter === "week") {
        const weekAgo = new Date(today)
        weekAgo.setDate(weekAgo.getDate() - 7)
        dateFilterMatches = taskDate >= weekAgo
      }
    }

    return matchesSearch && matchesStatus && matchesAgent && matchesAvatar && dateFilterMatches
  })

  // Get status badge and icon
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Completed</Badge>
      case "in_progress":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-200">
            In Progress
          </Badge>
        )
      case "failed":
        return <Badge variant="destructive">Failed</Badge>
      case "warning":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
            Warning
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle2 className="h-5 w-5 text-green-500" />
      case "in_progress":
        return <Clock className="h-5 w-5 text-blue-500" />
      case "failed":
        return <XCircle className="h-5 w-5 text-red-500" />
      case "warning":
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  // Format duration
  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`
    return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Task Log</CardTitle>
            <CardDescription>View all tasks performed by your AI agents</CardDescription>
          </div>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Log
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tasks..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex flex-1 items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="warning">Warning</SelectItem>
                </SelectContent>
              </Select>
              <Select value={agentFilter} onValueChange={setAgentFilter}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Filter by agent" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Agents</SelectItem>
                  <SelectItem value="agent-1">Content Scout</SelectItem>
                  <SelectItem value="agent-2">Engagement Bot</SelectItem>
                  <SelectItem value="agent-3">Analytics Miner</SelectItem>
                  <SelectItem value="agent-4">Content Repurposer</SelectItem>
                  <SelectItem value="agent-5">Hashtag Optimizer</SelectItem>
                  <SelectItem value="agent-6">Affiliate Hunter</SelectItem>
                  <SelectItem value="agent-7">Scheduler Bot</SelectItem>
                </SelectContent>
              </Select>
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Filter by date" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="yesterday">Yesterday</SelectItem>
                  <SelectItem value="week">Last 7 Days</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <ScrollArea className="h-[600px]">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">Status</TableHead>
                    <TableHead>Agent</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>Details</TableHead>
                    <TableHead>Timestamp</TableHead>
                    <TableHead className="text-right">Duration</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTasks.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="h-24 text-center">
                        No tasks found matching your filters.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredTasks.map((task) => (
                      <TableRow key={task.id}>
                        <TableCell>{getStatusIcon(task.status)}</TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Bot className="h-4 w-4 text-muted-foreground" />
                            <span>{task.agentName}</span>
                            <Badge variant="outline" className="capitalize">
                              {task.avatar}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>{task.action}</TableCell>
                        <TableCell className="max-w-[300px] truncate" title={task.details}>
                          {task.details}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3 text-muted-foreground" />
                            <span>{formatDate(task.timestamp)}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">{formatDuration(task.duration)}</TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </ScrollArea>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

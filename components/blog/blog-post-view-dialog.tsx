'use client'

import { BlogPost } from '@/lib/api/blog-posts'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Calendar, User, Tag, Eye, Clock, CheckCircle, Archive } from 'lucide-react'

interface BlogPostViewDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  post: BlogPost | null
  onEdit?: () => void
}

export function BlogPostViewDialog({
  open,
  onOpenChange,
  post,
  onEdit
}: BlogPostViewDialogProps) {
  if (!post) return null

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published':
        return <CheckCircle className="h-4 w-4" />
      case 'draft':
        return <Clock className="h-4 w-4" />
      case 'archived':
        return <Archive className="h-4 w-4" />
      default:
        return <Eye className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'archived':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200'
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-[900px] max-h-[95vh] rounded-2xl overflow-auto border-0 shadow-2xl bg-gradient-to-br from-background via-background to-muted/20 sm:mx-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">{post.title}</DialogTitle>
          <DialogDescription>
            Blog post details and content preview
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Post Meta Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Badge className={`${getStatusColor(post.status)} flex items-center space-x-1`}>
                  {getStatusIcon(post.status)}
                  <span className="capitalize">{post.status}</span>
                </Badge>
              </div>
              
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Tag className="h-4 w-4" />
                <span>Category: {post.category?.title || 'Uncategorized'}</span>
              </div>

              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <User className="h-4 w-4" />
                <span>Author: {post.created_by}</span>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>Created: {formatDate(post.created_at)}</span>
              </div>

              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4" />
                <span>Updated: {formatDate(post.updated_at)}</span>
              </div>

              {post.published_at && (
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4" />
                  <span>Published: {formatDate(post.published_at)}</span>
                </div>
              )}
            </div>
          </div>

          {/* Featured Image */}
          {post.featured_image && (
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Featured Image</h3>
              <div className="w-full h-64 border rounded-lg overflow-hidden">
                <img
                  src={post.featured_image}
                  alt={post.title}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          )}

          {/* Excerpt */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Excerpt</h3>
            <p className="text-muted-foreground bg-muted/30 p-4 rounded-lg italic">
              {post.excerpt}
            </p>
          </div>

          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-sm">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Content */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Content</h3>
            <div className="prose max-w-none bg-muted/30 p-6 rounded-lg">
              <div className="whitespace-pre-wrap text-sm leading-relaxed">
                {post.content}
              </div>
            </div>
          </div>

          {/* Slug */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">URL Slug</h3>
            <code className="bg-muted px-3 py-1 rounded text-sm">
              {post.slug}
            </code>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button className='mb-2 sm:mb-0'
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
          {onEdit && (
            <Button className='mb-2 sm:mb-0' onClick={onEdit}>
              Edit Post
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

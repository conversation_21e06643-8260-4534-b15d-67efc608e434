'use client'

import { useState, useEffect } from 'react'
import { BlogPost, CreateBlogPostRequest, UpdateBlogPostRequest } from '@/lib/api/blog-posts'
import { BlogCategory } from '@/lib/api/blog-categories'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { X, Plus, Upload, Image } from 'lucide-react'

interface BlogPostDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  post?: BlogPost | null
  categories: BlogCategory[]
  onSave: (data: CreateBlogPostRequest | UpdateBlogPostRequest) => Promise<void>
  loading?: boolean
}

export function BlogPostDialog({
  open,
  onOpenChange,
  post,
  categories,
  onSave,
  loading = false
}: BlogPostDialogProps) {
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    excerpt: '',
    category_id: '',
    tags: [] as string[],
    status: 'draft' as 'draft' | 'published',
    featured_image: null as File | null
  })
  const [tagInput, setTagInput] = useState('')
  const [imagePreview, setImagePreview] = useState<string | null>(null)

  const isEditing = !!post

  useEffect(() => {
    if (post) {
      setFormData({
        title: post.title,
        content: post.content,
        excerpt: post.excerpt,
        category_id: post.category_id,
        tags: post.tags || [],
        status: post.status as 'draft' | 'published',
        featured_image: null
      })
      setImagePreview(post.featured_image)
    } else {
      setFormData({
        title: '',
        content: '',
        excerpt: '',
        category_id: '',
        tags: [],
        status: 'draft',
        featured_image: null
      })
      setImagePreview(null)
    }
    setTagInput('')
  }, [post, open])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }))
      setTagInput('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setFormData(prev => ({ ...prev, featured_image: file }))
      
      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim() || !formData.content.trim() || !formData.category_id) {
      return
    }

    try {
      await onSave(formData)
      onOpenChange(false)
    } catch (error) {
      console.error('Failed to save post:', error)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-[900px] max-h-[95vh] rounded-2xl overflow-auto border-0 shadow-2xl bg-gradient-to-br from-background via-background to-muted/20 sm:mx-auto">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 pointer-events-none" />
        <div className="relative">
          <DialogHeader className="pb-6 border-b border-border/50 bg-gradient-to-r from-primary/10 via-transparent to-secondary/10 -mx-6 -mt-6 px-6 pt-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-xl bg-primary/10 border border-primary/20">
                <Image className="h-5 w-5 text-primary" />
              </div>
              <div>
                <DialogTitle className="text-xl font-bold ">
                  {isEditing ? 'Edit Blog Post' : 'Create New Blog Post'}
                </DialogTitle>
                <DialogDescription className="text-muted-foreground/80 mt-1">
                  {isEditing
                    ? 'Update your blog post details below.'
                    : 'Fill in the details to create a new blog post.'
                  }
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-8 pt-2">
            <div className="grid gap-8 lg:grid-cols-2">
              {/* Left Column */}
              <div className="space-y-6">
                <div className="group">
                  <Label htmlFor="title" className="text-sm font-semibold text-foreground/90 mb-2 block">
                    Title <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter an engaging post title..."
                    className="h-11 border bg-background/50 backdrop-blur-sm transition-all duration-200 "
                    required
                  />
                </div>

                <div className="group">
                  <Label htmlFor="excerpt" className="text-sm font-semibold text-foreground/90 mb-2 block">
                    Excerpt <span className="text-destructive">*</span>
                  </Label>
                  <Textarea
                    id="excerpt"
                    value={formData.excerpt}
                    onChange={(e) => handleInputChange('excerpt', e.target.value)}
                    placeholder="Write a compelling brief description..."
                    rows={3}
                    className="border rounded-xl bg-background/50 backdrop-blur-sm transition-all duration-200  resize-none"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="group">
                    <Label htmlFor="category" className="text-sm font-semibold text-foreground/90 mb-2 block">
                      Category <span className="text-destructive">*</span>
                    </Label>
                    <Select
                      value={formData.category_id}
                      onValueChange={(value) => handleInputChange('category_id', value)}
                    >
                      <SelectTrigger className="h-11 border bg-background/50 backdrop-blur-sm transition-all duration-200 ">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent className="rounded-xl border-2">
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id} className="rounded-lg">
                            {category.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="group">
                    <Label htmlFor="status" className="text-sm font-semibold text-foreground/90 mb-2 block">
                      Status
                    </Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value) => handleInputChange('status', value)}
                    >
                      <SelectTrigger className="h-11 bg-background/50 backdrop-blur-sm transition-all duration-200">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="rounded-xl border-2">
                        <SelectItem value="draft" className="rounded-lg">
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                            <span>Draft</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="published" className="rounded-lg">
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 rounded-full bg-green-500"></div>
                            <span>Published</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Tags */}
                <div className="group">
                  <Label className="text-sm font-semibold text-foreground/90 mb-2 block">
                    Tags
                  </Label>
                  <div className="space-y-3">
                    <div className="flex space-x-2">
                      <Input
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        placeholder="Add a tag..."
                        className="h-11 border bg-background/50 backdrop-blur-sm transition-all duration-200 "
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            handleAddTag()
                          }
                        }}
                      />
                      <Button
                        type="button"
                        onClick={handleAddTag}
                        size="sm"
                        className="h-11 px-4 text-white border-primary/20 hover:border-primary/30 transition-all duration-200"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    {formData.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 p-3 rounded-xl bg-muted/30 border border-border/30">
                        {formData.tags.map((tag, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                            className="text-sm px-3 py-1 rounded-full bg-primary/10 text-primary border border-primary/20 hover:bg-primary/20 transition-colors duration-200"
                          >
                            {tag}
                            <button
                              type="button"
                              onClick={() => handleRemoveTag(tag)}
                              className="ml-2 hover:text-destructive transition-colors duration-200"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Featured Image */}
                <div className="group">
                  <Label className="text-sm font-semibold text-foreground/90 mb-2 block">
                    Featured Image
                  </Label>
                  <div className="space-y-3">
                    <div className="flex items-center justify-center">
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                        className="hidden"
                        id="featured-image"
                      />
                      <Label
                        htmlFor="featured-image"
                        className="cursor-pointer flex items-center justify-center space-x-3 w-full h-12 px-6 border-2 border-dashed border-border/50 rounded-lg hover:border-primary/50 hover:bg-primary/5 transition-all duration-200 group/upload"
                      >
                        <Upload className="h-5 w-5 text-muted-foreground group-hover/upload:text-primary transition-colors duration-200" />
                        <span className="text-sm font-medium text-muted-foreground group-hover/upload:text-primary transition-colors duration-200">
                          Choose Featured Image
                        </span>
                      </Label>
                    </div>
                    {imagePreview && (
                      <div className="relative w-full h-40 border-2 border-border/30 rounded-xl overflow-hidden bg-muted/20">
                        <img
                          src={imagePreview}
                          alt="Preview"
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                        <button
                          type="button"
                          onClick={() => {
                            setImagePreview(null)
                            setFormData(prev => ({ ...prev, featured_image: null }))
                          }}
                          className="absolute top-3 right-3 p-2 bg-destructive/90 text-destructive-foreground rounded-full hover:bg-destructive transition-all duration-200 shadow-lg backdrop-blur-sm"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Right Column - Content */}
              <div className="space-y-4">
                <div className="group">
                  <Label htmlFor="content" className="text-sm font-semibold text-foreground/90 mb-2 block">
                    Content <span className="text-destructive">*</span>
                  </Label>
                  <div className="relative">
                    <Textarea
                      id="content"
                      value={formData.content}
                      onChange={(e) => handleInputChange('content', e.target.value)}
                      placeholder="Write your engaging blog post content here..."
                      className="md:min-h-[500px] min-h-[200px] border bg-background/50 backdrop-blur-sm transition-all duration-200 resize-none"
                      required
                    />
                    <div className="absolute bottom-3 right-3 text-xs text-muted-foreground/60 bg-background/80 px-2 py-1 rounded-md backdrop-blur-sm">
                      {formData.content.length} characters
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter className="pt-6 border-t border-border/50 bg-gradient-to-r from-muted/20 via-transparent to-muted/20 -mx-6 -mb-6 px-6 pb-6 mt-8">
              <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto sm:ml-auto">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={loading}
                  className="h-11 px-6  border-2 border-border/50 hover:border-primary/30 hover:bg-primary/5 transition-all duration-200"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                  className="h-11 px-8  bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg hover:shadow-xl transition-all duration-200 font-semibold"
                >
                  {loading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                      <span>Saving...</span>
                    </div>
                  ) : (
                    isEditing ? 'Update Post' : 'Create Post'
                  )}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  )
}

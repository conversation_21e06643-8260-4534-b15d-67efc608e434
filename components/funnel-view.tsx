"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowRight, RefreshCw, Settings, UserPlus, MoreHorizontal } from "lucide-react"

interface FunnelViewProps {
  selectedAvatar: string
}

export function FunnelView({ selectedAvatar }: FunnelViewProps) {
  const [funnelType, setFunnelType] = useState("all")

  // Filter funnel stages based on selected avatar
  const filteredFunnelStages = funnelStages.filter((stage) => {
    return selectedAvatar === "all" || stage.avatar === selectedAvatar
  })

  // Filter contacts in each stage based on selected avatar and funnel type
  const getFilteredContacts = (stageId: string) => {
    return funnelContacts
      .filter((contact) => {
        const matchesStage = contact.stageId === stageId
        const matchesAvatar = selectedAvatar === "all" || contact.avatar === selectedAvatar
        const matchesFunnelType = funnelType === "all" || contact.funnelType === funnelType

        return matchesStage && matchesAvatar && matchesFunnelType
      })
      .sort((a, b) => new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime())
  }

  // Get avatar badge color
  const getAvatarBadgeColor = (avatar: string) => {
    switch (avatar) {
      case "travel":
        return "bg-blue-100 text-blue-800"
      case "home":
        return "bg-green-100 text-green-800"
      case "health":
        return "bg-purple-100 text-purple-800"
      case "fitness":
        return "bg-red-100 text-red-800"
      case "entrepreneur":
        return "bg-amber-100 text-amber-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
    }).format(date)
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Funnel View</CardTitle>
            <CardDescription>Track leads and followers through funnel stages</CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Select value={funnelType} onValueChange={setFunnelType}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="All Funnels" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Funnels</SelectItem>
                <SelectItem value="lead-magnet">Lead Magnet</SelectItem>
                <SelectItem value="product-launch">Product Launch</SelectItem>
                <SelectItem value="webinar">Webinar</SelectItem>
                <SelectItem value="course">Course</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon">
              <Settings className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon">
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {filteredFunnelStages.map((stage) => (
            <div key={stage.id} className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <h3 className="font-medium">{stage.name}</h3>
                  <Badge>{getFilteredContacts(stage.id).length}</Badge>
                </div>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>

              <ScrollArea className="h-[500px] rounded-md border p-2">
                <div className="space-y-2">
                  {getFilteredContacts(stage.id).length === 0 ? (
                    <div className="flex h-20 items-center justify-center text-sm text-muted-foreground">
                      No contacts in this stage
                    </div>
                  ) : (
                    getFilteredContacts(stage.id).map((contact) => (
                      <Card key={contact.id} className="p-3">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={contact.avatarUrl || "/placeholder.svg"} alt={contact.name} />
                                <AvatarFallback>{contact.name.charAt(0)}</AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium">{contact.name}</div>
                                <div className="text-xs text-muted-foreground">{contact.email}</div>
                              </div>
                            </div>
                            <Badge variant="outline" className={`capitalize ${getAvatarBadgeColor(contact.avatar)}`}>
                              {contact.avatar}
                            </Badge>
                          </div>

                          <div className="flex items-center justify-between text-xs">
                            <Badge variant="outline" className="capitalize">
                              {contact.funnelType}
                            </Badge>
                            <span className="text-muted-foreground">{formatDate(contact.lastActivity)}</span>
                          </div>

                          <div className="flex items-center justify-between">
                            <Button variant="ghost" size="sm" className="h-7 px-2 text-xs">
                              View Details
                            </Button>
                            <Button variant="outline" size="sm" className="h-7 px-2 text-xs">
                              <ArrowRight className="mr-1 h-3 w-3" />
                              Move
                            </Button>
                          </div>
                        </div>
                      </Card>
                    ))
                  )}

                  <Button variant="ghost" className="w-full justify-center text-xs">
                    <UserPlus className="mr-1 h-3 w-3" />
                    Add Contact
                  </Button>
                </div>
              </ScrollArea>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

// Sample funnel stages
const funnelStages = [
  {
    id: "stage-1",
    name: "Subscriber",
    avatar: "all",
    order: 1,
  },
  {
    id: "stage-2",
    name: "Lead",
    avatar: "all",
    order: 2,
  },
  {
    id: "stage-3",
    name: "Prospect",
    avatar: "all",
    order: 3,
  },
  {
    id: "stage-4",
    name: "Customer",
    avatar: "all",
    order: 4,
  },
]

// Sample funnel contacts
const funnelContacts = [
  {
    id: "funnel-contact-1",
    name: "Emma Wilson",
    email: "<EMAIL>",
    avatar: "travel",
    avatarUrl: "/placeholder-user.jpg",
    stageId: "stage-1",
    funnelType: "lead-magnet",
    lastActivity: "2023-05-16T10:30:00Z",
  },
  {
    id: "funnel-contact-2",
    name: "Michael Johnson",
    email: "<EMAIL>",
    avatar: "fitness",
    avatarUrl: "/placeholder-user.jpg",
    stageId: "stage-3",
    funnelType: "product-launch",
    lastActivity: "2023-05-16T09:15:00Z",
  },
  {
    id: "funnel-contact-3",
    name: "Sophia Martinez",
    email: "<EMAIL>",
    avatar: "health",
    avatarUrl: "/placeholder-user.jpg",
    stageId: "stage-2",
    funnelType: "webinar",
    lastActivity: "2023-05-15T14:45:00Z",
  },
  {
    id: "funnel-contact-4",
    name: "Daniel Taylor",
    email: "<EMAIL>",
    avatar: "home",
    avatarUrl: "/placeholder-user.jpg",
    stageId: "stage-4",
    funnelType: "course",
    lastActivity: "2023-05-14T11:20:00Z",
  },
  {
    id: "funnel-contact-5",
    name: "Olivia Brown",
    email: "<EMAIL>",
    avatar: "entrepreneur",
    avatarUrl: "/placeholder-user.jpg",
    stageId: "stage-3",
    funnelType: "product-launch",
    lastActivity: "2023-05-16T08:30:00Z",
  },
  {
    id: "funnel-contact-6",
    name: "James Anderson",
    email: "<EMAIL>",
    avatar: "travel",
    avatarUrl: "/placeholder-user.jpg",
    stageId: "stage-1",
    funnelType: "lead-magnet",
    lastActivity: "2023-05-15T16:45:00Z",
  },
  {
    id: "funnel-contact-7",
    name: "Isabella Garcia",
    email: "<EMAIL>",
    avatar: "fitness",
    avatarUrl: "/placeholder-user.jpg",
    stageId: "stage-2",
    funnelType: "webinar",
    lastActivity: "2023-05-16T13:10:00Z",
  },
  {
    id: "funnel-contact-8",
    name: "Ethan Miller",
    email: "<EMAIL>",
    avatar: "health",
    avatarUrl: "/placeholder-user.jpg",
    stageId: "stage-1",
    funnelType: "lead-magnet",
    lastActivity: "2023-05-15T10:20:00Z",
  },
  {
    id: "funnel-contact-9",
    name: "Ava Davis",
    email: "<EMAIL>",
    avatar: "home",
    avatarUrl: "/placeholder-user.jpg",
    stageId: "stage-4",
    funnelType: "course",
    lastActivity: "2023-05-14T15:30:00Z",
  },
  {
    id: "funnel-contact-10",
    name: "Noah Rodriguez",
    email: "<EMAIL>",
    avatar: "entrepreneur",
    avatarUrl: "/placeholder-user.jpg",
    stageId: "stage-2",
    funnelType: "product-launch",
    lastActivity: "2023-05-16T11:45:00Z",
  },
  {
    id: "funnel-contact-11",
    name: "Charlotte Lee",
    email: "<EMAIL>",
    avatar: "travel",
    avatarUrl: "/placeholder-user.jpg",
    stageId: "stage-3",
    funnelType: "webinar",
    lastActivity: "2023-05-15T09:00:00Z",
  },
  {
    id: "funnel-contact-12",
    name: "William Clark",
    email: "<EMAIL>",
    avatar: "fitness",
    avatarUrl: "/placeholder-user.jpg",
    stageId: "stage-4",
    funnelType: "product-launch",
    lastActivity: "2023-05-14T14:15:00Z",
  },
]

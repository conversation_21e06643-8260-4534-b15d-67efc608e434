"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "@/components/ui/use-toast"
import { Download, FileSpreadsheet, FileText, FileIcon as FilePdf, Calendar, Filter, ChevronDown } from "lucide-react"

interface DataExportOptionsProps {
  dataType: "products" | "avatars" | "categories" | "projections" | "conversions" | "suggestions" | "automation"
}

export function DataExportOptions({ dataType }: DataExportOptionsProps) {
  const [open, setOpen] = useState(false)
  const [exportFormat, setExportFormat] = useState<"csv" | "excel" | "pdf">("excel")
  const [dateRange, setDateRange] = useState<"current" | "30d" | "90d" | "12m" | "custom">("current")
  const [includeCharts, setIncludeCharts] = useState(true)
  const [includeRawData, setIncludeRawData] = useState(true)
  const [isExporting, setIsExporting] = useState(false)

  const handleQuickExport = (format: "csv" | "excel" | "pdf") => {
    setIsExporting(true)

    // Simulate export process
    setTimeout(() => {
      setIsExporting(false)

      toast({
        title: "Export Complete",
        description: `Your ${dataType} data has been exported as ${format.toUpperCase()}.`,
      })
    }, 1500)
  }

  const handleAdvancedExport = () => {
    setIsExporting(true)

    // Simulate export process
    setTimeout(() => {
      setIsExporting(false)
      setOpen(false)

      toast({
        title: "Export Complete",
        description: `Your customized ${dataType} data has been exported as ${exportFormat.toUpperCase()}.`,
      })
    }, 2000)
  }

  const getDataTypeLabel = () => {
    switch (dataType) {
      case "products":
        return "Product Analytics"
      case "avatars":
        return "Avatar Performance"
      case "categories":
        return "Category Data"
      case "projections":
        return "Revenue Projections"
      case "conversions":
        return "Conversion Tracking"
      case "suggestions":
        return "Product Suggestions"
      case "automation":
        return "Automation Events"
      default:
        return "Data"
    }
  }

  return (
    <div className="flex items-center gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="gap-1 bg-sage-50 border-sage-200 text-sage-800 hover:bg-sage-100">
            <Download className="h-4 w-4" />
            <span>Export</span>
            <ChevronDown className="h-3 w-3 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>Quick Export</DropdownMenuLabel>
          <DropdownMenuItem onClick={() => handleQuickExport("excel")} className="gap-2">
            <FileSpreadsheet className="h-4 w-4 text-green-600" />
            <span>Excel (.xlsx)</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleQuickExport("csv")} className="gap-2">
            <FileText className="h-4 w-4 text-blue-600" />
            <span>CSV (.csv)</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleQuickExport("pdf")} className="gap-2">
            <FilePdf className="h-4 w-4 text-red-600" />
            <span>PDF Report (.pdf)</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DialogTrigger asChild onClick={() => setOpen(true)}>
            <DropdownMenuItem className="gap-2">
              <Filter className="h-4 w-4" />
              <span>Advanced Export...</span>
            </DropdownMenuItem>
          </DialogTrigger>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Export {getDataTypeLabel()}</DialogTitle>
            <DialogDescription>Customize your export options to get exactly the data you need.</DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Export Format</h3>
              <RadioGroup
                defaultValue={exportFormat}
                onValueChange={(value) => setExportFormat(value as "csv" | "excel" | "pdf")}
                className="flex space-x-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="excel" id="excel" />
                  <Label htmlFor="excel" className="flex items-center gap-1">
                    <FileSpreadsheet className="h-4 w-4 text-green-600" />
                    <span>Excel</span>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="csv" id="csv" />
                  <Label htmlFor="csv" className="flex items-center gap-1">
                    <FileText className="h-4 w-4 text-blue-600" />
                    <span>CSV</span>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="pdf" id="pdf" />
                  <Label htmlFor="pdf" className="flex items-center gap-1">
                    <FilePdf className="h-4 w-4 text-red-600" />
                    <span>PDF</span>
                  </Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Date Range</h3>
              <RadioGroup
                defaultValue={dateRange}
                onValueChange={(value) => setDateRange(value as "current" | "30d" | "90d" | "12m" | "custom")}
                className="grid grid-cols-2 gap-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="current" id="current" />
                  <Label htmlFor="current">Current View</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="30d" id="30d" />
                  <Label htmlFor="30d">Last 30 Days</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="90d" id="90d" />
                  <Label htmlFor="90d">Last 90 Days</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="12m" id="12m" />
                  <Label htmlFor="12m">Last 12 Months</Label>
                </div>
                <div className="flex items-center space-x-2 col-span-2">
                  <RadioGroupItem value="custom" id="custom" />
                  <Label htmlFor="custom" className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>Custom Range</span>
                  </Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Include</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-charts"
                    checked={includeCharts}
                    onCheckedChange={(checked) => setIncludeCharts(checked as boolean)}
                  />
                  <Label htmlFor="include-charts">Charts and Visualizations</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-raw"
                    checked={includeRawData}
                    onCheckedChange={(checked) => setIncludeRawData(checked as boolean)}
                  />
                  <Label htmlFor="include-raw">Raw Data</Label>
                </div>
                {dataType === "projections" && (
                  <div className="flex items-center space-x-2">
                    <Checkbox id="include-forecast" defaultChecked />
                    <Label htmlFor="include-forecast">Forecast Models</Label>
                  </div>
                )}
                {dataType === "conversions" && (
                  <div className="flex items-center space-x-2">
                    <Checkbox id="include-comparison" defaultChecked />
                    <Label htmlFor="include-comparison">QR vs. Link Comparison</Label>
                  </div>
                )}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleAdvancedExport}
              disabled={isExporting}
              className="bg-sage-700 hover:bg-sage-800 text-white"
            >
              {isExporting ? "Exporting..." : "Export Data"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/chart"
import { ResponsiveContainer, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Line, Pie, Cell } from "recharts"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DollarSign, Calendar, PieChartIcon } from "lucide-react"

const platformData = [
  {
    platform: "Instagram",
    monthlyCost: 1200,
    timeSpent: 45,
    contentCount: 60,
    costPerContent: 20,
    roi: 2.5,
  },
  {
    platform: "YouTube",
    monthlyCost: 2500,
    timeSpent: 80,
    contentCount: 12,
    costPerContent: 208.33,
    roi: 3.2,
  },
  {
    platform: "TikTok",
    monthlyCost: 1800,
    timeSpent: 60,
    contentCount: 90,
    costPerContent: 20,
    roi: 2.8,
  },
  {
    platform: "Twitter",
    monthlyCost: 800,
    timeSpent: 30,
    contentCount: 120,
    costPerContent: 6.67,
    roi: 1.5,
  },
  {
    platform: "Facebook",
    monthlyCost: 1500,
    timeSpent: 50,
    contentCount: 45,
    costPerContent: 33.33,
    roi: 1.8,
  },
  {
    platform: "Pinterest",
    monthlyCost: 600,
    timeSpent: 25,
    contentCount: 30,
    costPerContent: 20,
    roi: 1.2,
  },
  {
    platform: "LinkedIn",
    monthlyCost: 900,
    timeSpent: 35,
    contentCount: 20,
    costPerContent: 45,
    roi: 2.0,
  },
]

const projectData = [
  {
    project: "Travel Niche",
    monthlyCost: 3500,
    timeSpent: 120,
    contentCount: 80,
    costPerContent: 43.75,
    roi: 2.8,
  },
  {
    project: "Fitness Niche",
    monthlyCost: 2800,
    timeSpent: 100,
    contentCount: 65,
    costPerContent: 43.08,
    roi: 2.5,
  },
  {
    project: "Tech Niche",
    monthlyCost: 4200,
    timeSpent: 150,
    contentCount: 90,
    costPerContent: 46.67,
    roi: 3.2,
  },
  {
    project: "Home Decor",
    monthlyCost: 2200,
    timeSpent: 80,
    contentCount: 50,
    costPerContent: 44,
    roi: 1.9,
  },
  {
    project: "Finance Education",
    monthlyCost: 3800,
    timeSpent: 130,
    contentCount: 70,
    costPerContent: 54.29,
    roi: 2.7,
  },
]

const monthlyTrendData = [
  {
    name: "Jan",
    "Total Cost": 8500,
    "Content Count": 220,
    "Avg Cost Per Content": 38.64,
  },
  {
    name: "Feb",
    "Total Cost": 9200,
    "Content Count": 235,
    "Avg Cost Per Content": 39.15,
  },
  {
    name: "Mar",
    "Total Cost": 10500,
    "Content Count": 260,
    "Avg Cost Per Content": 40.38,
  },
  {
    name: "Apr",
    "Total Cost": 9800,
    "Content Count": 250,
    "Avg Cost Per Content": 39.2,
  },
  {
    name: "May",
    "Total Cost": 11200,
    "Content Count": 280,
    "Avg Cost Per Content": 40,
  },
  {
    name: "Jun",
    "Total Cost": 10800,
    "Content Count": 275,
    "Avg Cost Per Content": 39.27,
  },
]

const costBreakdownData = [
  { name: "Human Resources", value: 45 },
  { name: "AI Tools", value: 20 },
  { name: "Software Subscriptions", value: 15 },
  { name: "Advertising", value: 12 },
  { name: "Miscellaneous", value: 8 },
]

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"]

export function ResourceBurnRate() {
  const [view, setView] = useState("platform")
  const [sortBy, setSortBy] = useState("monthlyCost")
  const [sortOrder, setSortOrder] = useState("desc")

  const data = view === "platform" ? platformData : projectData

  const sortedData = [...data].sort((a, b) => {
    const factor = sortOrder === "asc" ? 1 : -1
    return factor * (a[sortBy] - b[sortBy])
  })

  const totalMonthlyCost = data.reduce((sum, item) => sum + item.monthlyCost, 0)
  const totalTimeSpent = data.reduce((sum, item) => sum + item.timeSpent, 0)
  const totalContentCount = data.reduce((sum, item) => sum + item.contentCount, 0)
  const avgCostPerContent = totalMonthlyCost / totalContentCount
  const avgRoi = data.reduce((sum, item) => sum + item.roi, 0) / data.length

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortBy(field)
      setSortOrder("desc")
    }
  }

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Monthly Cost</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalMonthlyCost.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Across {data.length} {view}s
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Time Spent</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalTimeSpent} hours</div>
            <p className="text-xs text-muted-foreground">
              Avg {Math.round(totalTimeSpent / data.length)} hrs per {view}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Content</CardTitle>
            <PieChartIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalContentCount}</div>
            <p className="text-xs text-muted-foreground">
              Avg {Math.round(totalContentCount / data.length)} per {view}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Cost/Content</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${avgCostPerContent.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">Per piece of content</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average ROI</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgRoi.toFixed(1)}x</div>
            <p className="text-xs text-muted-foreground">Return on investment</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="table" className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="table">Table View</TabsTrigger>
            <TabsTrigger value="trends">Monthly Trends</TabsTrigger>
            <TabsTrigger value="breakdown">Cost Breakdown</TabsTrigger>
          </TabsList>
          <div className="flex items-center space-x-2">
            <Select value={view} onValueChange={setView}>
              <SelectTrigger className="w-[180px] h-8">
                <SelectValue placeholder="View by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="platform">By Platform</SelectItem>
                <SelectItem value="project">By Project</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              Export Data
            </Button>
          </div>
        </div>

        <TabsContent value="table" className="space-y-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{view === "platform" ? "Platform" : "Project"}</TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("monthlyCost")}>
                      Monthly Cost {sortBy === "monthlyCost" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("timeSpent")}>
                      Time Spent (hrs) {sortBy === "timeSpent" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("contentCount")}>
                      Content Count {sortBy === "contentCount" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("costPerContent")}>
                      Cost/Content {sortBy === "costPerContent" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("roi")}>
                      ROI {sortBy === "roi" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedData.map((item, i) => (
                    <TableRow key={i}>
                      <TableCell className="font-medium">
                        {view === "platform" ? item.platform : item.project}
                      </TableCell>
                      <TableCell>${item.monthlyCost.toLocaleString()}</TableCell>
                      <TableCell>{item.timeSpent}</TableCell>
                      <TableCell>{item.contentCount}</TableCell>
                      <TableCell>${item.costPerContent.toFixed(2)}</TableCell>
                      <TableCell>{item.roi.toFixed(1)}x</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Cost & Content Trends</CardTitle>
              <CardDescription>Cost and content production over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={monthlyTrendData}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Line yAxisId="left" type="monotone" dataKey="Total Cost" stroke="#8884d8" activeDot={{ r: 8 }} />
                    <Line yAxisId="left" type="monotone" dataKey="Content Count" stroke="#82ca9d" />
                    <Line yAxisId="right" type="monotone" dataKey="Avg Cost Per Content" stroke="#ff7300" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="breakdown" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cost Breakdown</CardTitle>
              <CardDescription>Distribution of costs by category</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={costBreakdownData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={150}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {costBreakdownData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
            <CardFooter>
              <div className="grid grid-cols-2 gap-4 w-full">
                {costBreakdownData.map((item, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    ></div>
                    <span className="text-sm">
                      {item.name}: {item.value}%
                    </span>
                  </div>
                ))}
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

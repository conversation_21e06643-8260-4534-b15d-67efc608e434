"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Plus, Search, Calendar, Mail, Phone } from "lucide-react"

type ContactLog = {
  id: string
  manufacturer: string
  contactName: string
  date: string
  method: "Email" | "Phone" | "Meeting"
  notes: string
  followUpDate: string | null
}

export function ManufacturerContactLog() {
  const [logs, setLogs] = useState<ContactLog[]>([
    {
      id: "1",
      manufacturer: "EcoFriendly Products Co.",
      contactName: "<PERSON> Johnson",
      date: "2023-04-15",
      method: "Email",
      notes: "Discussed bamboo cutting board samples. They will send pricing by next week.",
      followUpDate: "2023-04-22",
    },
    {
      id: "2",
      manufacturer: "Premium Fitness Gear",
      contactName: "Michael Chen",
      date: "2023-04-10",
      method: "Phone",
      notes: "Talked about yoga mat specifications and minimum order quantities.",
      followUpDate: "2023-04-17",
    },
    {
      id: "3",
      manufacturer: "Organic Harvest",
      contactName: "David Miller",
      date: "2023-04-05",
      method: "Meeting",
      notes: "Met at trade show. They can provide organic coffee beans with custom packaging.",
      followUpDate: "2023-04-20",
    },
    {
      id: "4",
      manufacturer: "SiliTech Kitchen",
      contactName: "Lisa Wong",
      date: "2023-03-28",
      method: "Email",
      notes: "Requested samples of silicone baking mats in different colors.",
      followUpDate: null,
    },
  ])

  const [searchTerm, setSearchTerm] = useState("")
  const [newLog, setNewLog] = useState<Omit<ContactLog, "id">>({
    manufacturer: "",
    contactName: "",
    date: new Date().toISOString().split("T")[0],
    method: "Email",
    notes: "",
    followUpDate: null,
  })
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const filteredLogs = logs.filter(
    (log) =>
      log.manufacturer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.contactName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.notes.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleAddLog = () => {
    const id = (logs.length + 1).toString()
    setLogs([...logs, { ...newLog, id }])
    setNewLog({
      manufacturer: "",
      contactName: "",
      date: new Date().toISOString().split("T")[0],
      method: "Email",
      notes: "",
      followUpDate: null,
    })
    setIsDialogOpen(false)
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "—"
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date)
  }

  const getMethodIcon = (method: "Email" | "Phone" | "Meeting") => {
    switch (method) {
      case "Email":
        return <Mail className="h-4 w-4" />
      case "Phone":
        return <Phone className="h-4 w-4" />
      case "Meeting":
        return <Calendar className="h-4 w-4" />
    }
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Manufacturer Contact Log</CardTitle>
            <CardDescription>Track communications with potential suppliers</CardDescription>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Contact
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Log Manufacturer Contact</DialogTitle>
                <DialogDescription>Record details of your communication with a manufacturer</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="manufacturer">Manufacturer Name</Label>
                  <Input
                    id="manufacturer"
                    value={newLog.manufacturer}
                    onChange={(e) => setNewLog({ ...newLog, manufacturer: e.target.value })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="contactName">Contact Person</Label>
                  <Input
                    id="contactName"
                    value={newLog.contactName}
                    onChange={(e) => setNewLog({ ...newLog, contactName: e.target.value })}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="date">Contact Date</Label>
                    <Input
                      id="date"
                      type="date"
                      value={newLog.date}
                      onChange={(e) => setNewLog({ ...newLog, date: e.target.value })}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="method">Contact Method</Label>
                    <select
                      id="method"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={newLog.method}
                      onChange={(e) =>
                        setNewLog({
                          ...newLog,
                          method: e.target.value as "Email" | "Phone" | "Meeting",
                        })
                      }
                    >
                      <option value="Email">Email</option>
                      <option value="Phone">Phone</option>
                      <option value="Meeting">Meeting</option>
                    </select>
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    rows={3}
                    value={newLog.notes}
                    onChange={(e) => setNewLog({ ...newLog, notes: e.target.value })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="followUpDate">Follow-up Date (Optional)</Label>
                  <Input
                    id="followUpDate"
                    type="date"
                    value={newLog.followUpDate || ""}
                    onChange={(e) => setNewLog({ ...newLog, followUpDate: e.target.value || null })}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddLog}>Add Log</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex items-center gap-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search contacts..."
            className="h-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Manufacturer</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Method</TableHead>
                <TableHead>Notes</TableHead>
                <TableHead>Follow-up</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLogs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    No contact logs found.
                  </TableCell>
                </TableRow>
              ) : (
                filteredLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell className="font-medium">{log.manufacturer}</TableCell>
                    <TableCell>{log.contactName}</TableCell>
                    <TableCell>{formatDate(log.date)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {getMethodIcon(log.method)}
                        <span>{log.method}</span>
                      </div>
                    </TableCell>
                    <TableCell className="max-w-[200px] truncate" title={log.notes}>
                      {log.notes}
                    </TableCell>
                    <TableCell>
                      {log.followUpDate ? (
                        <Badge variant="outline" className="bg-blue-100 text-blue-800">
                          {formatDate(log.followUpDate)}
                        </Badge>
                      ) : (
                        "—"
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}

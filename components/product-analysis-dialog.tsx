'use client'

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  BarChart3,
  TrendingUp,
  Search,
  Target,
  RefreshCw,
  CheckCircle,
  Clock,
  AlertCircle,
  Eye,
  FileText,
  Database,
  Activity,
  DollarSign,
  ArrowLeft,
  ChevronDown,
  ChevronRight,
  Plus,
  Video,
  Image as ImageIcon,
  Edit,
  ExternalLink,
  Trash2,
  Upload,
  ShoppingCart,
  Wand2,
  RotateCcw,
  Package,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { Product, ProductAnalysis, productService } from "@/lib/api/products"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import PrototypeBuilder from "@/components/prototype-builder"
import { AnalysisDataViewer } from "@/components/analysis-data-viewer"

// All components are defined inline below

// Component for displaying analyzed data in a user-friendly format
function AnalyzedDataViewer({ data }: { data: any }) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({})

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }
  
  const formatFieldName = (key: string): string => {
    return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const renderReadableContent = (key: string, value: any) => {
    if (!value) return <p className="text-muted-foreground">No data available</p>

    // Handle audience insights
    if (key === 'audience_insights' && typeof value === 'object') {
      return (
        <div className="space-y-4">
          {value.customer_pain_points && value.customer_pain_points.length > 0 && (
            <div>
              <h5 className="font-medium text-gray-800 mb-2">Customer Pain Points</h5>
              <ul className="space-y-1">
                {value.customer_pain_points.map((point: string, index: number) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-gray-400 mt-1">•</span>
                    <span className="text-sm text-gray-700">{point}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {value.demographic_indicators && value.demographic_indicators.length > 0 && (
            <div>
              <h5 className="font-medium text-gray-800 mb-2">Target Demographics</h5>
              <div className="flex flex-wrap gap-2">
                {value.demographic_indicators.map((demo: string, index: number) => (
                  <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">{demo}</span>
                ))}
              </div>
            </div>
          )}

          {value.search_behavior_patterns && value.search_behavior_patterns.length > 0 && (
            <div>
              <h5 className="font-medium text-gray-800 mb-2">Search Behavior</h5>
              <ul className="space-y-1">
                {value.search_behavior_patterns.map((pattern: string, index: number) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-gray-400 mt-1">•</span>
                    <span className="text-sm text-gray-700">{pattern}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {value.customer_journey_stage && typeof value.customer_journey_stage === 'object' && (
            <div>
              <h5 className="font-medium text-gray-800 mb-2">Customer Journey Stages</h5>
              <div className="space-y-2">
                {value.customer_journey_stage.awareness && (
                  <div className="p-3 bg-gray-50 rounded border-l-4 border-gray-300">
                    <div className="font-medium text-gray-800 text-sm">Awareness Stage</div>
                    <div className="text-sm text-gray-600 mt-1">{value.customer_journey_stage.awareness}</div>
                  </div>
                )}
                {value.customer_journey_stage.consideration && (
                  <div className="p-3 bg-gray-50 rounded border-l-4 border-gray-300">
                    <div className="font-medium text-gray-800 text-sm">Consideration Stage</div>
                    <div className="text-sm text-gray-600 mt-1">{value.customer_journey_stage.consideration}</div>
                  </div>
                )}
                {value.customer_journey_stage.decision && (
                  <div className="p-3 bg-gray-50 rounded border-l-4 border-gray-300">
                    <div className="font-medium text-gray-800 text-sm">Decision Stage</div>
                    <div className="text-sm text-gray-600 mt-1">{value.customer_journey_stage.decision}</div>
                  </div>
                )}
              </div>
            </div>
          )}

          {value.customer_questions && value.customer_questions.length > 0 && (
            <div>
              <h5 className="font-medium text-gray-800 mb-2">Customer Questions</h5>
              <ul className="space-y-1">
                {value.customer_questions.map((question: string, index: number) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-gray-400 mt-1">•</span>
                    <span className="text-sm text-gray-700">{question}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {value.purchase_intent_signals && value.purchase_intent_signals.length > 0 && (
            <div>
              <h5 className="font-medium text-gray-800 mb-2">Purchase Intent Signals</h5>
              <ul className="space-y-1">
                {value.purchase_intent_signals.map((signal: string, index: number) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-gray-400 mt-1">•</span>
                    <span className="text-sm text-gray-700">{signal}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )
    }

    // Handle organic results
    if (key === 'organic_results' && Array.isArray(value)) {
      return (
        <div className="space-y-3">
          {value.map((result: any, index: number) => (
            <Card key={index} className="p-4">
              <h4 className="font-medium text-blue-600 mb-2">{result.title || 'No title'}</h4>
              <p className="text-sm text-muted-foreground mb-2">{result.snippet || 'No description'}</p>
              <a href={result.link} target="_blank" rel="noopener noreferrer"
                 className="text-xs text-blue-500 hover:underline">
                {result.displayed_link || result.link}
              </a>
            </Card>
          ))}
        </div>
      )
    }

    // Handle shopping results
    if (key === 'shopping_results' && Array.isArray(value)) {
      return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
          {value.map((product: any, index: number) => (
            <Card key={index} className="p-3 sm:p-4">
              <div className="flex items-start space-x-2 sm:space-x-3">
                {product.thumbnail && (
                  <img src={product.thumbnail} alt={product.title} className="w-12 h-12 sm:w-16 sm:h-16 object-cover rounded flex-shrink-0" />
                )}
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium mb-1 text-sm sm:text-base truncate">{product.title || 'No title'}</h4>
                  <p className="text-base sm:text-lg font-bold text-green-600 mb-1">{product.price || 'Price not available'}</p>
                  {product.source && <p className="text-xs sm:text-sm text-muted-foreground truncate">{product.source}</p>}
                </div>
              </div>
            </Card>
          ))}
        </div>
      )
    }

    // Handle knowledge graph
    if (key === 'knowledge_graph' && typeof value === 'object') {
      return (
        <Card className="p-4">
          <div className="space-y-3">
            {value.title && <h3 className="text-lg font-semibold">{value.title}</h3>}
            {value.type && <Badge variant="outline">{value.type}</Badge>}
            {value.description && <p className="text-muted-foreground">{value.description}</p>}
            {value.attributes && (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-4">
                {Object.entries(value.attributes).map(([attr, val]: [string, any]) => (
                  <div key={attr} className="border-l-2 border-blue-200 pl-3">
                    <p className="text-xs font-medium text-muted-foreground">{formatFieldName(attr)}</p>
                    <p className="text-sm break-words">{String(val)}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </Card>
      )
    }

    // Handle related questions
    if (key === 'related_questions' && Array.isArray(value)) {
      return (
        <div className="space-y-2">
          {value.map((question: any, index: number) => (
            <Card key={index} className="p-3">
              <p className="font-medium text-sm">{typeof question === 'string' ? question : question.question || String(question)}</p>
              {question.snippet && <p className="text-xs text-muted-foreground mt-1">{question.snippet}</p>}
            </Card>
          ))}
        </div>
      )
    }

    // Handle related searches
    if (key === 'related_searches' && Array.isArray(value)) {
      return (
        <div className="flex flex-wrap gap-2">
          {value.map((search: any, index: number) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {typeof search === 'string' ? search : search.query || search.title}
            </Badge>
          ))}
        </div>
      )
    }

    // Handle product market validation
    if (key === 'product_market_validation' && typeof value === 'object') {
      return (
        <div className="space-y-4">
          {value.market_maturity && (
            <div className="p-3 bg-gray-50 rounded border-l-4 border-gray-300">
              <div className="font-medium text-gray-800 text-sm mb-1">Market Maturity</div>
              <div className="text-sm text-gray-600">{value.market_maturity}</div>
            </div>
          )}

          {value.demand_validation_score && (
            <div className="p-3 bg-gray-50 rounded border-l-4 border-gray-300">
              <div className="font-medium text-gray-800 text-sm mb-1">Demand Score</div>
              <div className="text-sm text-gray-600">{value.demand_validation_score}</div>
            </div>
          )}

          {value.key_demand_indicators && value.key_demand_indicators.length > 0 && (
            <div>
              <h5 className="font-medium text-gray-800 mb-2">Key Demand Indicators</h5>
              <ul className="space-y-1">
                {value.key_demand_indicators.map((indicator: string, index: number) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-gray-400 mt-1">•</span>
                    <span className="text-sm text-gray-700">{indicator}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )
    }

    // Handle content strategy gold mine
    if (key === 'content_strategy_gold_mine' && typeof value === 'object') {
      return (
        <div className="space-y-4">
          {value.content_opportunities && value.content_opportunities.length > 0 && (
            <div>
              <h5 className="font-medium text-gray-800 mb-2">Content Opportunities</h5>
              <div className="space-y-3">
                {value.content_opportunities.slice(0, 5).map((opportunity: any, index: number) => (
                  <div key={index} className="p-3 bg-gray-50 rounded border-l-4 border-gray-300">
                    <div className="font-medium text-gray-800 text-sm">{opportunity.topic || 'Content Topic'}</div>
                    <div className="text-xs text-gray-600 mt-1">
                      {opportunity.content_type && <span className="mr-2">Type: {opportunity.content_type}</span>}
                      {opportunity.opportunity_score && <span>Score: {opportunity.opportunity_score}</span>}
                    </div>
                  </div>
                ))}
                {value.content_opportunities.length > 5 && (
                  <div className="text-center py-2">
                    <span className="text-xs text-gray-500">
                      +{value.content_opportunities.length - 5} more opportunities
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )
    }

    // Handle inline videos
    if (key === 'inline_videos' && Array.isArray(value)) {
      return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
          {value.map((video: any, index: number) => (
            <Card key={index} className="p-4">
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  {video.thumbnail && (
                    <img src={video.thumbnail} alt={video.title} className="w-20 h-14 object-cover rounded" />
                  )}
                  <div className="flex-1">
                    <h4 className="font-medium text-sm mb-1">{video.title || 'No title'}</h4>
                    {video.source && <p className="text-xs text-muted-foreground">{video.source}</p>}
                    {video.length && <p className="text-xs text-muted-foreground">Duration: {video.length}</p>}
                    {video.link && (
                      <a href={video.link} target="_blank" rel="noopener noreferrer"
                         className="text-xs text-blue-500 hover:underline inline-flex items-center">
                        Watch Video <ExternalLink className="w-3 h-3 ml-1" />
                      </a>
                    )}
                  </div>
                </div>



                {/* Display other video properties */}
                {(video.position || video.block_position) && (
                  <div className="border-t pt-2">
                    <div className="flex justify-between text-xs text-gray-500">
                      {video.position && <span>Position: {video.position}</span>}
                      {video.block_position && <span>Block Position: {video.block_position}</span>}
                    </div>
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>
      )
    }

    // Handle generic objects with better formatting
    if (typeof value === 'object' && !Array.isArray(value)) {
      return (
        <div className="space-y-2">
          {Object.entries(value).map(([objKey, objValue]) => (
            <div key={objKey} className="p-3 bg-gray-50 rounded border-l-4 border-gray-300">
              <div className="font-medium text-gray-800 text-sm mb-1 capitalize">
                {objKey.replace(/_/g, ' ')}
              </div>
              <div className="text-sm text-gray-600">
                {typeof objValue === 'string' ? objValue :
                 typeof objValue === 'number' ? objValue.toLocaleString() :
                 Array.isArray(objValue) ? (
                   objValue.length > 0 ? (
                     <div className="space-y-1">
                       {objValue.map((item, idx) => (
                         <div key={idx} className="text-xs bg-white p-2 rounded border text-gray-700">
                           {typeof item === 'string' ? item : JSON.stringify(item)}
                         </div>
                       ))}
                     </div>
                   ) : 'No items'
                 ) : JSON.stringify(objValue)}
              </div>
            </div>
          ))}
        </div>
      )
    }

    // Default: show as formatted JSON for complex objects
    return (
      <pre className="text-xs bg-gray-50 p-4 rounded overflow-auto max-h-96 whitespace-pre-wrap">
        {JSON.stringify(value, null, 2)}
      </pre>
    )
  }

  if (!data || typeof data !== 'object') {
    return (
      <div className="text-center py-8">
        <AlertCircle className="mx-auto h-8 w-8 text-muted-foreground" />
        <p className="text-muted-foreground mt-2">No analyzed data available</p>
      </div>
    )
  }

  // Filter out metadata fields that shouldn't be shown
  const filteredData = Object.entries(data).filter(([key, value]) => {
    // Skip metadata fields that are not user-friendly
    const metadataFields = ['search_metadata', 'search_parameters', 'search_information', 'pagination', 'serpapi_pagination', 'analysis_metadata', 'computed_fields']
    return !metadataFields.includes(key) && value != null
  })

  if (filteredData.length === 0) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="mx-auto h-8 w-8 text-muted-foreground" />
        <p className="text-muted-foreground mt-2">No relevant analyzed data available</p>
      </div>
    )
  }

  const getSectionIcon = (sectionKey: string, isOpen: boolean = false) => {
    const iconMap: Record<string, { closed: React.ReactNode; open: React.ReactNode }> = {
      'audience_insights': {
        closed: <Target className="h-4 w-4 text-gray-600" />,
        open: <Target className="h-4 w-4 text-blue-600" />
      },
      'product_market_validation': {
        closed: <TrendingUp className="h-4 w-4 text-gray-600" />,
        open: <TrendingUp className="h-4 w-4 text-blue-600" />
      },
      'content_strategy_gold_mine': {
        closed: <FileText className="h-4 w-4 text-gray-600" />,
        open: <FileText className="h-4 w-4 text-blue-600" />
      },
      'organic_results': {
        closed: <Search className="h-4 w-4 text-gray-600" />,
        open: <Search className="h-4 w-4 text-blue-600" />
      },
      'shopping_results': {
        closed: <DollarSign className="h-4 w-4 text-gray-600" />,
        open: <DollarSign className="h-4 w-4 text-blue-600" />
      },
      'knowledge_graph': {
        closed: <Database className="h-4 w-4 text-gray-600" />,
        open: <Database className="h-4 w-4 text-blue-600" />
      },
      'related_searches': {
        closed: <Search className="h-4 w-4 text-gray-600" />,
        open: <Search className="h-4 w-4 text-blue-600" />
      },
      'related_questions': {
        closed: <AlertCircle className="h-4 w-4 text-gray-600" />,
        open: <AlertCircle className="h-4 w-4 text-blue-600" />
      },
      'ai_overview': {
        closed: <BarChart3 className="h-4 w-4 text-gray-600" />,
        open: <BarChart3 className="h-4 w-4 text-blue-600" />
      },
      'inline_videos': {
        closed: <Eye className="h-4 w-4 text-gray-600" />,
        open: <Eye className="h-4 w-4 text-blue-600" />
      },
      'related_products': {
        closed: <Activity className="h-4 w-4 text-gray-600" />,
        open: <Activity className="h-4 w-4 text-blue-600" />
      }
    }

    const icons = iconMap[sectionKey] || {
      closed: <FileText className="h-4 w-4 text-gray-600" />,
      open: <FileText className="h-4 w-4 text-blue-600" />
    }

    return isOpen ? icons.open : icons.closed
  }

  const getSectionDescription = (sectionKey: string): string => {
    const descriptions: Record<string, string> = {
      'audience_insights': 'Customer behavior and demographic insights',
      'product_market_validation': 'Market maturity and demand validation data',
      'content_strategy_gold_mine': 'Content opportunities and strategy recommendations',
      'organic_results': 'Organic search results and rankings',
      'shopping_results': 'Shopping and product comparison data',
      'knowledge_graph': 'Structured knowledge and entity information',
      'related_searches': 'Related search queries and trends',
      'related_questions': 'Frequently asked questions and answers',
      'ai_overview': 'AI-generated overview and insights',
      'inline_videos': 'Video content and multimedia results',
      'related_products': 'Related products and alternatives'
    }
    return descriptions[sectionKey] || 'Analyzed data information'
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-4">
        <div className="min-w-0">
          <h4 className="text-base sm:text-lg font-semibold text-gray-800">Analyzed Data</h4>
          <p className="text-xs sm:text-sm text-muted-foreground">Click on any section below to view detailed information</p>
        </div>
        <Badge variant="outline" className="text-xs w-fit">{filteredData.length} sections</Badge>
      </div>

      {filteredData.map(([key, value]) => (
        <div key={key} className="border rounded-lg overflow-hidden">
          <Collapsible
            open={openSections[key]}
            onOpenChange={() => toggleSection(key)}
          >
            <CollapsibleTrigger asChild>
              <div className="w-full p-3 sm:p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
                    {getSectionIcon(key, openSections[key])}
                    <div className="min-w-0 flex-1">
                      <h5 className="font-semibold text-gray-800 text-sm sm:text-base truncate">{formatFieldName(key)}</h5>
                      <p className="text-xs text-gray-600 truncate hidden sm:block">{getSectionDescription(key)}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                    {Array.isArray(value) && (
                      <Badge variant="secondary" className="text-xs hidden sm:inline-flex">
                        {value.length} items
                      </Badge>
                    )}
                    {openSections[key] ? (
                      <ChevronDown className="h-4 w-4 text-blue-600" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    )}
                  </div>
                </div>
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div className="p-3 sm:p-4 bg-white">
                {renderReadableContent(key, value)}
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      ))}
    </div>
  )
}
 







// Component for displaying data summary in a user-friendly format
function DataSummaryViewer({ data }: { data: any }) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({})

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const formatFieldName = (key: string): string => {
    return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const renderSimpleValue = (value: any): React.ReactNode => {
    if (value === null || value === undefined) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-500">
          <AlertCircle className="w-3 h-3 mr-1" />
          Not available
        </span>
      )
    }

    if (typeof value === 'boolean') {
      return (
        <Badge variant={value ? "default" : "secondary"} className="text-xs">
          {value ? (
            <><CheckCircle className="w-3 h-3 mr-1" /> Yes</>
          ) : (
            <><AlertCircle className="w-3 h-3 mr-1" /> No</>
          )}
        </Badge>
      )
    }

    if (typeof value === 'number') {
      return (
        <span className="inline-flex items-center px-3 py-1 bg-green-50 text-green-700 rounded-lg font-semibold text-sm">
          {value.toLocaleString()}
        </span>
      )
    }

    if (typeof value === 'string') {
      return <span className="text-gray-700 text-sm leading-relaxed">{value}</span>
    }

    // Handle objects by showing key properties
    if (typeof value === 'object' && value !== null) {
      const entries = Object.entries(value)
      if (entries.length === 0) {
        return <span className="text-gray-500 italic text-sm">Empty object</span>
      }

      // Show all key-value pairs
      return (
        <div className="space-y-1">
          {entries.map(([key, val]) => (
            <div key={key} className="text-xs bg-gray-50 rounded px-2 py-1">
              <span className="font-medium text-gray-600">{formatFieldName(key)}: </span>
              <span className="text-gray-700">{String(val)}</span>
            </div>
          ))}
        </div>
      )
    }

    return <span className="text-gray-700 text-sm">{String(value)}</span>
  }

  const getSectionIcon = (sectionKey: string, isOpen: boolean = false) => {
    const iconMap: Record<string, { closed: React.ReactNode; open: React.ReactNode }> = {
      'analysis_metadata': {
        closed: <Database className="h-4 w-4 text-gray-600" />,
        open: <Database className="h-4 w-4 text-blue-600" />
      },
      'product_market_validation': {
        closed: <Target className="h-4 w-4 text-gray-600" />,
        open: <Target className="h-4 w-4 text-blue-600" />
      },
      'content_strategy_gold_mine': {
        closed: <FileText className="h-4 w-4 text-gray-600" />,
        open: <FileText className="h-4 w-4 text-blue-600" />
      },
      'audience_insights': {
        closed: <Target className="h-4 w-4 text-gray-600" />,
        open: <Target className="h-4 w-4 text-blue-600" />
      },
      'competitive_intelligence': {
        closed: <BarChart3 className="h-4 w-4 text-gray-600" />,
        open: <BarChart3 className="h-4 w-4 text-blue-600" />
      },
      'market_trends': {
        closed: <TrendingUp className="h-4 w-4 text-gray-600" />,
        open: <TrendingUp className="h-4 w-4 text-blue-600" />
      },
    }
    const icons = iconMap[sectionKey] || {
      closed: <Eye className="h-4 w-4 text-gray-600" />,
      open: <Eye className="h-4 w-4 text-blue-600" />
    }
    return isOpen ? icons.open : icons.closed
  }

  const getSectionDescription = (sectionKey: string) => {
    const descriptions: Record<string, string> = {
      'analysis_metadata': 'Information about the analysis process and data sources',
      'product_market_validation': 'Market demand validation and key indicators',
      'content_strategy_gold_mine': 'High-value keywords and content opportunities',
      'audience_insights': 'Target audience behavior and preferences',
      'competitive_intelligence': 'Competitor analysis and market positioning',
      'market_trends': 'Current market trends and opportunities',
    }
    return descriptions[sectionKey] || 'Detailed summary insights and data'
  }

  const renderSectionContent = (sectionKey: string, sectionValue: any): React.ReactNode => {
    if (sectionKey === 'analysis_metadata') {
      return (
        <div className="space-y-4">
          <div className="grid gap-4 sm:grid-cols-1 lg:grid-cols-2">
            <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4">
              <h6 className="font-semibold text-gray-800 mb-2 text-sm sm:text-base">Product Information</h6>
              <div className="space-y-2">
                <div className="flex flex-col sm:flex-row sm:justify-between gap-1">
                  <span className="text-xs sm:text-sm text-gray-600">Product Name:</span>
                  <span className="text-xs sm:text-sm font-medium break-words">{sectionValue.product_name || 'N/A'}</span>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between gap-1">
                  <span className="text-xs sm:text-sm text-gray-600">Analysis Date:</span>
                  <span className="text-xs sm:text-sm font-medium">
                    {sectionValue.analysis_timestamp
                      ? new Date(sectionValue.analysis_timestamp).toLocaleDateString()
                      : 'N/A'
                    }
                  </span>
                </div>
              </div>
            </div>

            {sectionValue.data_sources && (
              <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4">
                <h6 className="font-semibold text-gray-800 mb-2 text-sm sm:text-base">Data Sources</h6>
                <div className="space-y-2">
                  {Object.entries(sectionValue.data_sources).map(([source, count]: [string, any]) => (
                    <div key={source} className="flex flex-col sm:flex-row sm:justify-between gap-1">
                      <span className="text-xs sm:text-sm text-gray-600 capitalize">{source.replace('_', ' ')}:</span>
                      <Badge variant="outline" className="text-xs w-fit">{String(count)}</Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )
    }

    if (Array.isArray(sectionValue)) {
      return (
        <div className="space-y-2">
          {sectionValue.map((item, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center">
                <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3 flex-shrink-0"></span>
                <span className="text-gray-700 text-sm">{renderSimpleValue(item)}</span>
              </div>
            </div>
          ))}
        </div>
      )
    }

    if (typeof sectionValue === 'object' && sectionValue !== null) {
      return (
        <div className="grid gap-4">
          {Object.entries(sectionValue).map(([key, value]) => (
            <div key={key} className="bg-white border border-gray-200 rounded-lg p-4">
              <h6 className="font-semibold text-gray-800 text-base mb-3">{formatFieldName(key)}</h6>
              <div className="pl-4 border-l-2 border-blue-100">
                {Array.isArray(value) ? (
                  <div className="space-y-2">
                    <Badge variant="outline" className="text-xs mb-2">{value.length} items</Badge>
                    {value.map((item, idx) => (
                      <div key={idx} className="bg-blue-50 rounded-lg p-3 border-l-4 border-blue-200">
                        <div className="flex items-center">
                          <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3 flex-shrink-0"></span>
                          <span className="text-gray-700 text-sm">{renderSimpleValue(item)}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : typeof value === 'object' && value !== null ? (
                  <div className="space-y-2">
                    {Object.entries(value).map(([subKey, subValue]) => (
                      <div key={subKey} className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-600">
                            {formatFieldName(subKey)}
                          </span>
                          <div className="text-sm">
                            {renderSimpleValue(subValue)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="bg-blue-50 rounded-lg p-3">
                    <div className="text-lg font-medium text-gray-800">
                      {renderSimpleValue(value)}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )
    }

    return (
      <div className="bg-blue-50 rounded-lg p-4">
        <div className="text-lg font-medium text-gray-800">
          {renderSimpleValue(sectionValue)}
        </div>
      </div>
    )
  }

  if (!data || typeof data !== 'object') {
    return (
      <div className="text-center py-8">
        <AlertCircle className="mx-auto h-8 w-8 text-muted-foreground" />
        <p className="text-muted-foreground mt-2">No data summary available</p>
      </div>
    )
  }

  const sections = Object.entries(data)

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-6">
        <div className="min-w-0">
          <h4 className="font-semibold text-base sm:text-lg">Data Summary</h4>
          <p className="text-xs sm:text-sm text-muted-foreground">Click on any section below to view detailed summary</p>
        </div>
        <Badge variant="outline" className="text-xs w-fit">{sections.length} sections</Badge>
      </div>

      {sections.map(([sectionKey, sectionValue]) => (
        <div key={sectionKey} className="border rounded-lg overflow-hidden">
          <Collapsible
            open={openSections[sectionKey]}
            onOpenChange={() => toggleSection(sectionKey)}
          >
            <CollapsibleTrigger asChild>
              <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getSectionIcon(sectionKey, openSections[sectionKey])}
                    <div>
                      <h5 className="font-semibold text-gray-800">{formatFieldName(sectionKey)}</h5>
                      <p className="text-xs text-gray-600">{getSectionDescription(sectionKey)}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {typeof sectionValue === 'object' && sectionValue !== null && (
                      <Badge variant="secondary" className="text-xs">
                        {Array.isArray(sectionValue)
                          ? `${sectionValue.length} items`
                          : `${Object.keys(sectionValue).length} fields`
                        }
                      </Badge>
                    )}
                    {openSections[sectionKey] ? (
                      <ChevronDown className="h-4 w-4 text-blue-600" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    )}
                  </div>
                </div>
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div className="p-4 bg-white">
                {renderSectionContent(sectionKey, sectionValue)}
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      ))}
    </div>
  )
}

// Component for displaying submitted data in a user-friendly format
function SubmittedDataViewer({ data }: { data: any }) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({})

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const formatFieldName = (key: string): string => {
    return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const renderSimpleValue = (value: any): React.ReactNode => {
    if (value === null || value === undefined) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-500">
          <AlertCircle className="w-3 h-3 mr-1" />
          Not provided
        </span>
      )
    }

    if (typeof value === 'boolean') {
      return (
        <Badge variant={value ? "default" : "secondary"} className="text-xs">
          {value ? (
            <><CheckCircle className="w-3 h-3 mr-1" /> Yes</>
          ) : (
            <><AlertCircle className="w-3 h-3 mr-1" /> No</>
          )}
        </Badge>
      )
    }

    if (typeof value === 'number') {
      return (
        <span className="inline-flex items-center px-3 py-1 bg-purple-50 text-purple-700 rounded-lg font-semibold text-sm">
          {value.toLocaleString()}
        </span>
      )
    }

    if (typeof value === 'string') {
      // Check if it's a URL
      if (value.startsWith('http://') || value.startsWith('https://')) {
        return (
          <a
            href={value}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-purple-600 hover:text-purple-800 underline text-sm"
          >
            {value}
            <ArrowLeft className="w-3 h-3 ml-1 rotate-180" />
          </a>
        )
      }

      return <span className="text-gray-700 text-sm leading-relaxed">{value}</span>
    }

    // Handle objects by showing key properties
    if (typeof value === 'object' && value !== null) {
      const entries = Object.entries(value)
      if (entries.length === 0) {
        return <span className="text-gray-500 italic text-sm">Empty object</span>
      }

      // Show all key-value pairs
      return (
        <div className="space-y-1">
          {entries.map(([key, val]) => (
            <div key={key} className="text-xs bg-gray-50 rounded px-2 py-1">
              <span className="font-medium text-gray-600">{formatFieldName(key)}: </span>
              <span className="text-gray-700">{String(val)}</span>
            </div>
          ))}
        </div>
      )
    }

    return <span className="text-gray-700 text-sm">{String(value)}</span>
  }

  const getSectionIcon = (sectionKey: string, isOpen: boolean = false) => {
    const iconMap: Record<string, { closed: React.ReactNode; open: React.ReactNode }> = {
      'geo': {
        closed: <Target className="h-4 w-4 text-gray-600" />,
        open: <Target className="h-4 w-4 text-blue-600" />
      },
      'sku': {
        closed: <Database className="h-4 w-4 text-gray-600" />,
        open: <Database className="h-4 w-4 text-blue-600" />
      },
      'asin': {
        closed: <Database className="h-4 w-4 text-gray-600" />,
        open: <Database className="h-4 w-4 text-blue-600" />
      },
      'product_name': {
        closed: <FileText className="h-4 w-4 text-gray-600" />,
        open: <FileText className="h-4 w-4 text-blue-600" />
      },
      'timeframe': {
        closed: <Clock className="h-4 w-4 text-gray-600" />,
        open: <Clock className="h-4 w-4 text-blue-600" />
      },
      'additional_keywords': {
        closed: <Search className="h-4 w-4 text-gray-600" />,
        open: <Search className="h-4 w-4 text-blue-600" />
      },
    }
    const icons = iconMap[sectionKey] || {
      closed: <Eye className="h-4 w-4 text-gray-600" />,
      open: <Eye className="h-4 w-4 text-blue-600" />
    }
    return isOpen ? icons.open : icons.closed
  }

  const getSectionDescription = (sectionKey: string) => {
    const descriptions: Record<string, string> = {
      'geo': 'Geographic location for market analysis',
      'sku': 'Stock Keeping Unit identifier',
      'asin': 'Amazon Standard Identification Number',
      'product_name': 'Product name used for analysis',
      'timeframe': 'Analysis time period',
      'additional_keywords': 'Extra keywords for enhanced analysis',
    }
    return descriptions[sectionKey] || 'Analysis parameter information'
  }

  const renderSectionContent = (sectionKey: string, sectionValue: any): React.ReactNode => {
    if (Array.isArray(sectionValue)) {
      if (sectionValue.length === 0) {
        return (
          <div className="text-center py-4">
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <AlertCircle className="h-4 w-4 text-gray-400" />
            </div>
            <p className="text-gray-500 text-sm">No items provided</p>
          </div>
        )
      }

      return (
        <div className="space-y-2">
          {sectionValue.map((item, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-3">
              <div className="flex items-center">
                <span className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-3 flex-shrink-0"></span>
                <span className="text-gray-700 text-sm">{renderSimpleValue(item)}</span>
              </div>
            </div>
          ))}
        </div>
      )
    }

    return (
      <div className="bg-purple-50 rounded-lg p-4">
        <div className="text-lg font-medium text-gray-800">
          {renderSimpleValue(sectionValue)}
        </div>
      </div>
    )
  }

  if (!data || typeof data !== 'object') {
    return (
      <div className="text-center py-8">
        <AlertCircle className="mx-auto h-8 w-8 text-muted-foreground" />
        <p className="text-muted-foreground mt-2">No submitted data available</p>
      </div>
    )
  }

  const sections = Object.entries(data)

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-6">
        <div className="min-w-0">
          <h4 className="font-semibold text-base sm:text-lg">Submitted Parameters</h4>
          <p className="text-xs sm:text-sm text-muted-foreground">Analysis parameters that were submitted for processing</p>
        </div>
        <Badge variant="outline" className="text-xs w-fit">{sections.length} parameters</Badge>
      </div>

      {sections.map(([sectionKey, sectionValue]) => (
        <div key={sectionKey} className="border rounded-lg overflow-hidden">
          <Collapsible
            open={openSections[sectionKey]}
            onOpenChange={() => toggleSection(sectionKey)}
          >
            <CollapsibleTrigger asChild>
              <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getSectionIcon(sectionKey, openSections[sectionKey])}
                    <div>
                      <h5 className="font-semibold text-gray-800">{formatFieldName(sectionKey)}</h5>
                      <p className="text-xs text-gray-600">{getSectionDescription(sectionKey)}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {typeof sectionValue === 'object' && sectionValue !== null && (
                      <Badge variant="secondary" className="text-xs">
                        {Array.isArray(sectionValue)
                          ? `${sectionValue.length} items`
                          : `${Object.keys(sectionValue).length} fields`
                        }
                      </Badge>
                    )}
                    {openSections[sectionKey] ? (
                      <ChevronDown className="h-4 w-4 text-blue-600" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    )}
                  </div>
                </div>
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div className="p-4 bg-white">
                {renderSectionContent(sectionKey, sectionValue)}
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      ))}
    </div>
  )
}

// Component for displaying analysis result in a user-friendly format
function AnalysisResultViewer({ data }: { data: any }) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({})

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const formatFieldName = (key: string): string => {
    return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const renderSimpleValue = (value: any): React.ReactNode => {
    if (value === null || value === undefined) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-500">
          <AlertCircle className="w-3 h-3 mr-1" />
          Not available
        </span>
      )
    }

    if (typeof value === 'boolean') {
      return (
        <Badge variant={value ? "default" : "secondary"} className="text-xs">
          {value ? (
            <><CheckCircle className="w-3 h-3 mr-1" /> Yes</>
          ) : (
            <><AlertCircle className="w-3 h-3 mr-1" /> No</>
          )}
        </Badge>
      )
    }

    if (typeof value === 'number') {
      return (
        <span className="inline-flex items-center px-3 py-1 bg-orange-50 text-orange-700 rounded-lg font-semibold text-sm">
          {value.toLocaleString()}
        </span>
      )
    }

    if (typeof value === 'string') {
      // Check if it's a URL
      if (value.startsWith('http://') || value.startsWith('https://')) {
        return (
          <a
            href={value}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-orange-600 hover:text-orange-800 underline text-sm"
          >
            {value}
            <ArrowLeft className="w-3 h-3 ml-1 rotate-180" />
          </a>
        )
      }

      return <span className="text-gray-700 text-sm leading-relaxed">{value}</span>
    }

    // Handle objects by showing key properties
    if (typeof value === 'object' && value !== null) {
      const entries = Object.entries(value)
      if (entries.length === 0) {
        return <span className="text-gray-500 italic text-sm">Empty object</span>
      }

      // Show all key-value pairs
      return (
        <div className="space-y-1">
          {entries.map(([key, val]) => (
            <div key={key} className="text-xs bg-gray-50 rounded px-2 py-1">
              <span className="font-medium text-gray-600">{formatFieldName(key)}: </span>
              <span className="text-gray-700">{String(val)}</span>
            </div>
          ))}
        </div>
      )
    }

    return <span className="text-gray-700 text-sm">{String(value)}</span>
  }

  const getSectionIcon = (sectionKey: string, isOpen: boolean = false) => {
    const iconMap: Record<string, { closed: React.ReactNode; open: React.ReactNode }> = {
      'recommendations': {
        closed: <CheckCircle className="h-4 w-4 text-gray-600" />,
        open: <CheckCircle className="h-4 w-4 text-blue-600" />
      },
      'insights': {
        closed: <Eye className="h-4 w-4 text-gray-600" />,
        open: <Eye className="h-4 w-4 text-blue-600" />
      },
      'analysis_summary': {
        closed: <FileText className="h-4 w-4 text-gray-600" />,
        open: <FileText className="h-4 w-4 text-blue-600" />
      },
      'key_findings': {
        closed: <Search className="h-4 w-4 text-gray-600" />,
        open: <Search className="h-4 w-4 text-blue-600" />
      },
      'market_opportunities': {
        closed: <TrendingUp className="h-4 w-4 text-gray-600" />,
        open: <TrendingUp className="h-4 w-4 text-blue-600" />
      },
      'competitive_analysis': {
        closed: <BarChart3 className="h-4 w-4 text-gray-600" />,
        open: <BarChart3 className="h-4 w-4 text-blue-600" />
      },
      'action_items': {
        closed: <Activity className="h-4 w-4 text-gray-600" />,
        open: <Activity className="h-4 w-4 text-blue-600" />
      },
    }
    const icons = iconMap[sectionKey] || {
      closed: <Eye className="h-4 w-4 text-gray-600" />,
      open: <Eye className="h-4 w-4 text-blue-600" />
    }
    return isOpen ? icons.open : icons.closed
  }

  const getSectionDescription = (sectionKey: string) => {
    const descriptions: Record<string, string> = {
      'recommendations': 'Actionable recommendations based on analysis',
      'insights': 'Key insights and discoveries from the data',
      'analysis_summary': 'Executive summary of the analysis results',
      'key_findings': 'Most important findings and conclusions',
      'market_opportunities': 'Identified market opportunities and potential',
      'competitive_analysis': 'Competitive landscape and positioning insights',
      'action_items': 'Specific next steps and action items',
    }
    return descriptions[sectionKey] || 'Analysis result information and insights'
  }

  const renderSectionContent = (sectionKey: string, sectionValue: any): React.ReactNode => {
    if (Array.isArray(sectionValue)) {
      if (sectionValue.length === 0) {
        return (
          <div className="text-center py-4">
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <AlertCircle className="h-4 w-4 text-gray-400" />
            </div>
            <p className="text-gray-500 text-sm">No items available</p>
          </div>
        )
      }

      return (
        <div className="space-y-2">
          {sectionValue.map((item, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
              {typeof item === 'object' && item !== null ? (
                <div className="space-y-3">
                  {Object.entries(item).map(([key, value]) => (
                    <div key={key} className="border-b border-gray-100 pb-2 last:border-b-0 last:pb-0">
                      <div className="flex items-start justify-between">
                        <span className="text-sm font-medium text-gray-700 min-w-0 flex-1">
                          {formatFieldName(key)}
                        </span>
                        <div className="ml-4 text-right min-w-0 flex-1">
                          {renderSimpleValue(value)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mr-3 flex-shrink-0"></div>
                  <span className="text-gray-700">{renderSimpleValue(item)}</span>
                </div>
              )}
            </div>
          ))}
        </div>
      )
    }

    if (typeof sectionValue === 'object' && sectionValue !== null) {
      return (
        <div className="grid gap-4">
          {Object.entries(sectionValue).map(([key, value]) => (
            <div key={key} className="bg-white border border-gray-200 rounded-lg p-4">
              <h6 className="font-semibold text-gray-800 text-base mb-3">{formatFieldName(key)}</h6>
              <div className="pl-4 border-l-2 border-orange-100">
                {Array.isArray(value) ? (
                  <div className="space-y-2">
                    <Badge variant="outline" className="text-xs mb-2">{value.length} items</Badge>
                    {value.map((item, idx) => (
                      <div key={idx} className="bg-orange-50 rounded-lg p-3 border-l-4 border-orange-200">
                        <div className="flex items-center">
                          <span className="w-1.5 h-1.5 bg-orange-500 rounded-full mr-3 flex-shrink-0"></span>
                          <span className="text-gray-700 text-sm">{renderSimpleValue(item)}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : typeof value === 'object' && value !== null ? (
                  <div className="space-y-2">
                    {Object.entries(value).map(([subKey, subValue]) => (
                      <div key={subKey} className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-600">
                            {formatFieldName(subKey)}
                          </span>
                          <div className="text-sm">
                            {renderSimpleValue(subValue)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="bg-orange-50 rounded-lg p-3">
                    <div className="text-lg font-medium text-gray-800">
                      {renderSimpleValue(value)}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )
    }

    return (
      <div className="bg-orange-50 rounded-lg p-4">
        <div className="text-lg font-medium text-gray-800">
          {renderSimpleValue(sectionValue)}
        </div>
      </div>
    )
  }

  if (!data || typeof data !== 'object') {
    return (
      <div className="text-center py-8">
        <AlertCircle className="mx-auto h-8 w-8 text-muted-foreground" />
        <p className="text-muted-foreground mt-2">No analysis result available</p>
      </div>
    )
  }

  const sections = Object.entries(data)

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-6">
        <div className="min-w-0">
          <h4 className="font-semibold text-base sm:text-lg">Analysis Results</h4>
          <p className="text-xs sm:text-sm text-muted-foreground">Final analysis results and actionable insights</p>
        </div>
        <Badge variant="outline" className="text-xs w-fit">{sections.length} sections</Badge>
      </div>

      {sections.map(([sectionKey, sectionValue]) => (
        <div key={sectionKey} className="border rounded-lg overflow-hidden">
          <Collapsible
            open={openSections[sectionKey]}
            onOpenChange={() => toggleSection(sectionKey)}
          >
            <CollapsibleTrigger asChild>
              <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getSectionIcon(sectionKey, openSections[sectionKey])}
                    <div>
                      <h5 className="font-semibold text-gray-800">{formatFieldName(sectionKey)}</h5>
                      <p className="text-xs text-gray-600">{getSectionDescription(sectionKey)}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {typeof sectionValue === 'object' && sectionValue !== null && (
                      <Badge variant="secondary" className="text-xs">
                        {Array.isArray(sectionValue)
                          ? `${sectionValue.length} items`
                          : `${Object.keys(sectionValue).length} fields`
                        }
                      </Badge>
                    )}
                    {openSections[sectionKey] ? (
                      <ChevronDown className="h-4 w-4 text-blue-600" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    )}
                  </div>
                </div>
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div className="p-4 bg-white">
                {renderSectionContent(sectionKey, sectionValue)}
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      ))}
    </div>
  )
}

// Component for displaying organic results in a user-friendly format
function OrganicResultsViewer({ data }: { data: any }) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({})

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const formatFieldName = (key: string): string => {
    return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const renderSimpleValue = (value: any): React.ReactNode => {
    if (value === null || value === undefined) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-500">
          <AlertCircle className="w-3 h-3 mr-1" />
          Not available
        </span>
      )
    }

    if (typeof value === 'boolean') {
      return (
        <Badge variant={value ? "default" : "secondary"} className="text-xs">
          {value ? (
            <><CheckCircle className="w-3 h-3 mr-1" /> Yes</>
          ) : (
            <><AlertCircle className="w-3 h-3 mr-1" /> No</>
          )}
        </Badge>
      )
    }

    if (typeof value === 'number') {
      return (
        <span className="inline-flex items-center px-3 py-1 bg-green-50 text-green-700 rounded-lg font-semibold text-sm">
          {value.toLocaleString()}
        </span>
      )
    }

    if (typeof value === 'string') {
      // Check if it's a URL
      if (value.startsWith('http://') || value.startsWith('https://')) {
        return (
          <a
            href={value}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-green-600 hover:text-green-800 underline text-sm break-all"
          >
            {value}
            <ArrowLeft className="w-3 h-3 ml-1 rotate-180 flex-shrink-0" />
          </a>
        )
      }

      return <span className="text-gray-700 text-sm leading-relaxed">{value}</span>
    }

    // Handle arrays
    if (Array.isArray(value)) {
      if (value.length === 0) {
        return <span className="text-gray-500 italic text-sm">Empty array</span>
      }

      return (
        <div className="space-y-1">
          {value.map((item, index) => (
            <div key={index} className="text-xs bg-blue-50 rounded px-2 py-1 border-l-2 border-blue-200">
              {typeof item === 'object' && item !== null ? (
                <div className="space-y-1">
                  {Object.entries(item).map(([subKey, subVal]) => (
                    <div key={subKey}>
                      <span className="font-medium text-blue-600">{formatFieldName(subKey)}: </span>
                      <span className="text-gray-700">{typeof subVal === 'object' ? JSON.stringify(subVal) : String(subVal)}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <span className="text-gray-700">{String(item)}</span>
              )}
            </div>
          ))}
        </div>
      )
    }

    // Handle objects by showing key properties
    if (typeof value === 'object' && value !== null) {
      const entries = Object.entries(value)
      if (entries.length === 0) {
        return <span className="text-gray-500 italic text-sm">Empty object</span>
      }

      // Show all key-value pairs with better formatting
      return (
        <div className="space-y-1">
          {entries.map(([key, val]) => (
            <div key={key} className="text-xs bg-gray-50 rounded px-2 py-1">
              <span className="font-medium text-gray-600">{formatFieldName(key)}: </span>
              <span className="text-gray-700">
                {typeof val === 'object' && val !== null ? (
                  Array.isArray(val) ? (
                    val.length > 0 ? (
                      <div className="mt-1 space-y-1">
                        {val.map((item, index) => (
                          <div key={index} className="bg-white rounded px-1 py-0.5 text-xs">
                            {typeof item === 'object' ? JSON.stringify(item) : String(item)}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <span className="italic text-gray-500">Empty array</span>
                    )
                  ) : (
                    <div className="mt-1 space-y-1">
                      {Object.entries(val).map(([subKey, subVal]) => (
                        <div key={subKey} className="bg-white rounded px-1 py-0.5 text-xs">
                          <span className="font-medium text-gray-500">{formatFieldName(subKey)}: </span>
                          <span>{typeof subVal === 'object' ? JSON.stringify(subVal) : String(subVal)}</span>
                        </div>
                      ))}
                    </div>
                  )
                ) : (
                  String(val)
                )}
              </span>
            </div>
          ))}
        </div>
      )
    }

    return <span className="text-gray-700 text-sm">{String(value)}</span>
  }

  const getSectionIcon = (sectionKey: string, isOpen: boolean = false) => {
    const iconMap: Record<string, { closed: React.ReactNode; open: React.ReactNode }> = {
      'title': {
        closed: <FileText className="h-4 w-4 text-gray-600" />,
        open: <FileText className="h-4 w-4 text-blue-600" />
      },
      'link': {
        closed: <ArrowLeft className="h-4 w-4 text-gray-600 rotate-180" />,
        open: <ArrowLeft className="h-4 w-4 text-blue-600 rotate-180" />
      },
      'snippet': {
        closed: <Eye className="h-4 w-4 text-gray-600" />,
        open: <Eye className="h-4 w-4 text-blue-600" />
      },
      'position': {
        closed: <Target className="h-4 w-4 text-gray-600" />,
        open: <Target className="h-4 w-4 text-blue-600" />
      },
      'displayed_link': {
        closed: <ArrowLeft className="h-4 w-4 text-gray-600 rotate-180" />,
        open: <ArrowLeft className="h-4 w-4 text-blue-600 rotate-180" />
      },
      'source': {
        closed: <Database className="h-4 w-4 text-gray-600" />,
        open: <Database className="h-4 w-4 text-blue-600" />
      },
    }
    const icons = iconMap[sectionKey] || {
      closed: <Search className="h-4 w-4 text-gray-600" />,
      open: <Search className="h-4 w-4 text-blue-600" />
    }
    return isOpen ? icons.open : icons.closed
  }

  const getSectionDescription = (sectionKey: string) => {
    const descriptions: Record<string, string> = {
      'title': 'Page title from search results',
      'link': 'Direct URL link to the page',
      'snippet': 'Preview text from the search result',
      'position': 'Ranking position in search results',
      'displayed_link': 'Formatted display URL',
      'source': 'Source website or domain',
    }
    return descriptions[sectionKey] || 'Organic search result information'
  }

  const renderOrganicResult = (result: any, index: number): React.ReactNode => {
    const resultKey = `result_${index}`
    const title = result.title || `Search Result #${index + 1}`
    const position = result.position ? `#${result.position}` : `Result ${index + 1}`

    return (
      <div key={index} className="border rounded-lg overflow-hidden">
        <Collapsible
          open={openSections[resultKey]}
          onOpenChange={() => toggleSection(resultKey)}
        >
          <CollapsibleTrigger asChild>
            <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Search className={`h-4 w-4 ${openSections[resultKey] ? 'text-blue-600' : 'text-gray-600'}`} />
                  <div className="flex-1 min-w-0">
                    <h5 className="font-semibold text-gray-800 ">{title}</h5>
                    <div className="flex items-center flex-wrap space-x-4 text-xs text-gray-600">
                      {result.position && (
                        <span className="flex items-center">
                          <Target className="h-3 w-3 mr-1" />
                          {position}
                        </span>
                      )}
                      {result.link && (
                        <span className="flex items-center ">
                          <ArrowLeft className="h-3 w-3 mr-1 rotate-180" />
                          {result.displayed_link || new URL(result.link).hostname}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {openSections[resultKey] ? (
                    <ChevronDown className="h-4 w-4 text-blue-600" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  )}
                </div>
              </div>
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <div className="p-4 bg-white space-y-4">
              {/* Link */}
              {result.link && (
                <div className="flex items-center space-x-2">
                  <ArrowLeft className="h-4 w-4 text-green-600 rotate-180 flex-shrink-0" />
                  <a
                    href={result.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-green-600 hover:text-green-800 underline text-sm break-all"
                  >
                    {result.link}
                  </a>
                </div>
              )}

              {/* Position */}
              {result.position && (
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4 text-blue-600 flex-shrink-0" />
                  <span className="text-sm text-gray-600">
                    Search Position: <span className="font-medium text-blue-600">#{result.position}</span>
                  </span>
                </div>
              )}

              {/* Snippet */}
              {result.snippet && (
                <div className="bg-gray-50 rounded-lg p-3">
                  <h6 className="text-sm font-medium text-gray-700 mb-2">Preview Snippet:</h6>
                  <p className="text-sm text-gray-700 leading-relaxed">
                    {result.snippet}
                  </p>
                </div>
              )}

              {/* Rich Snippet */}
              {result.rich_snippet && (
                <div className="bg-blue-50 rounded-lg p-3 border-l-4 border-blue-200">
                  <h6 className="text-sm font-medium text-blue-700 mb-3 flex items-center">
                    <Database className="h-4 w-4 mr-2" />
                    Rich Snippet Data
                  </h6>
                  <div className="space-y-3">
                    {/* Top Extensions */}
                    {result.rich_snippet.top && (
                      <div className="bg-white rounded p-3">
                        <h7 className="text-xs font-semibold text-gray-600 uppercase tracking-wide mb-2 block">Top</h7>
                        {result.rich_snippet.top.extensions && Array.isArray(result.rich_snippet.top.extensions) && (
                          <div className="mb-2">
                            <span className="text-xs text-gray-500">Extensions:</span>
                            <div className="mt-1 space-y-1">
                              {result.rich_snippet.top.extensions.map((ext: string, index: number) => (
                                <div key={index} className="text-xs bg-gray-100 rounded px-2 py-1">
                                  {ext}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                        {result.rich_snippet.top.detected_extensions && (
                          <div>
                            <span className="text-xs text-gray-500">Detected Extensions:</span>
                            <div className="mt-1 space-y-1">
                              {Object.entries(result.rich_snippet.top.detected_extensions).map(([key, value]) => (
                                <div key={key} className="text-xs bg-gray-100 rounded px-2 py-1">
                                  <span className="font-medium">{formatFieldName(key)}:</span> {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Bottom Extensions */}
                    {result.rich_snippet.bottom && Object.keys(result.rich_snippet.bottom).length > 0 && (
                      <div className="bg-white rounded p-3">
                        <h7 className="text-xs font-semibold text-gray-600 uppercase tracking-wide mb-2 block">Bottom</h7>
                        <div className="space-y-1">
                          {Object.entries(result.rich_snippet.bottom).map(([key, value]) => (
                            <div key={key} className="text-xs bg-gray-100 rounded px-2 py-1">
                              <span className="font-medium">{formatFieldName(key)}:</span> {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Other Rich Snippet Properties */}
                    {Object.entries(result.rich_snippet).filter(([key]) => !['top', 'bottom'].includes(key)).map(([key, value]) => (
                      <div key={key} className="bg-white rounded p-3">
                        <h7 className="text-xs font-semibold text-gray-600 uppercase tracking-wide mb-2 block">{formatFieldName(key)}</h7>
                        <div className="text-xs text-gray-700">
                          {typeof value === 'object' && value !== null ? (
                            <div className="space-y-1">
                              {Object.entries(value).map(([subKey, subValue]) => (
                                <div key={subKey} className="bg-gray-100 rounded px-2 py-1">
                                  <span className="font-medium">{formatFieldName(subKey)}:</span> {typeof subValue === 'object' ? JSON.stringify(subValue) : String(subValue)}
                                </div>
                              ))}
                            </div>
                          ) : (
                            <span>{String(value)}</span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Additional properties */}
              {Object.entries(result).filter(([key]) =>
                !['title', 'link', 'snippet', 'position', 'displayed_link', 'rich_snippet'].includes(key)
              ).length > 0 && (
                <div className="space-y-2">
                  <h6 className="text-sm font-medium text-gray-700">Additional Information:</h6>
                  {Object.entries(result).filter(([key]) =>
                    !['title', 'link', 'snippet', 'position', 'displayed_link', 'rich_snippet'].includes(key)
                  ).map(([key, value]) => (
                    <div key={key} className="flex items-start justify-between bg-gray-50 rounded p-2">
                      <span className="text-sm font-medium text-gray-600 min-w-0 flex-1">
                        {formatFieldName(key)}:
                      </span>
                      <div className="ml-4 text-right min-w-0 flex-1">
                        {renderSimpleValue(value)}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <Search className="mx-auto h-8 w-8 text-muted-foreground" />
        <p className="text-muted-foreground mt-2">No organic results available</p>
      </div>
    )
  }

  // Handle array of results
  if (Array.isArray(data)) {
    if (data.length === 0) {
      return (
        <div className="text-center py-8">
          <Search className="mx-auto h-8 w-8 text-muted-foreground" />
          <p className="text-muted-foreground mt-2">No organic results found</p>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h4 className="font-semibold text-lg">Organic Search Results</h4>
            <p className="text-sm text-muted-foreground">Click on any result below to view detailed information</p>
          </div>
          <Badge variant="outline">{data.length} results</Badge>
        </div>

        <div className="space-y-3">
          {data.map((result, index) => renderOrganicResult(result, index))}
        </div>
      </div>
    )
  }

  // Handle single object or other data structures
  const sections = Object.entries(data)

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h4 className="font-semibold text-lg">Organic Results Data</h4>
          <p className="text-sm text-muted-foreground">Detailed organic search information</p>
        </div>
        <Badge variant="outline">{sections.length} sections</Badge>
      </div>

      {sections.map(([sectionKey, sectionValue]) => (
        <div key={sectionKey} className="border rounded-lg overflow-hidden">
          <Collapsible
            open={openSections[sectionKey]}
            onOpenChange={() => toggleSection(sectionKey)}
          >
            <CollapsibleTrigger asChild>
              <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getSectionIcon(sectionKey, openSections[sectionKey])}
                    <div>
                      <h5 className="font-semibold text-gray-800">{formatFieldName(sectionKey)}</h5>
                      <p className="text-xs text-gray-600">{getSectionDescription(sectionKey)}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {typeof sectionValue === 'object' && sectionValue !== null && (
                      <Badge variant="secondary" className="text-xs">
                        {Array.isArray(sectionValue)
                          ? `${sectionValue.length} items`
                          : `${Object.keys(sectionValue).length} fields`
                        }
                      </Badge>
                    )}
                    {openSections[sectionKey] ? (
                      <ChevronDown className="h-4 w-4 text-blue-600" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    )}
                  </div>
                </div>
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div className="p-4 bg-white">
                {Array.isArray(sectionValue) ? (
                  <div className="space-y-4">
                    {sectionValue.map((item, index) => renderOrganicResult(item, index))}
                  </div>
                ) : (
                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="text-lg font-medium text-gray-800">
                      {renderSimpleValue(sectionValue)}
                    </div>
                  </div>
                )}
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      ))}
    </div>
  )
}

// Component for displaying knowledge graph in a user-friendly format
function KnowledgeGraphViewer({ data }: { data: any }) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({})

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const formatFieldName = (key: string): string => {
    return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const renderSimpleValue = (value: any): React.ReactNode => {
    if (value === null || value === undefined) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-500">
          <AlertCircle className="w-3 h-3 mr-1" />
          Not available
        </span>
      )
    }

    if (typeof value === 'boolean') {
      return (
        <Badge variant={value ? "default" : "secondary"} className="text-xs">
          {value ? (
            <><CheckCircle className="w-3 h-3 mr-1" /> Yes</>
          ) : (
            <><AlertCircle className="w-3 h-3 mr-1" /> No</>
          )}
        </Badge>
      )
    }

    if (typeof value === 'number') {
      return (
        <span className="inline-flex items-center px-3 py-1 bg-indigo-50 text-indigo-700 rounded-lg font-semibold text-sm">
          {value.toLocaleString()}
        </span>
      )
    }

    if (typeof value === 'string') {
      // Check if it's a URL
      if (value.startsWith('http://') || value.startsWith('https://')) {
        return (
          <a
            href={value}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-indigo-600 hover:text-indigo-800 underline text-sm break-all"
          >
            {value}
            <ArrowLeft className="w-3 h-3 ml-1 rotate-180 flex-shrink-0" />
          </a>
        )
      }

      return <span className="text-gray-700 text-sm leading-relaxed">{value}</span>
    }

    // Handle objects by showing all key properties (no truncation)
    if (typeof value === 'object' && value !== null) {
      const entries = Object.entries(value)
      if (entries.length === 0) {
        return <span className="text-gray-500 italic text-sm">Empty object</span>
      }

      // Show all key-value pairs without truncation
      return (
        <div className="space-y-1">
          {entries.map(([key, val]) => (
            <div key={key} className="text-xs bg-gray-50 rounded px-2 py-1">
              <span className="font-medium text-gray-600">{formatFieldName(key)}: </span>
              <span className="text-gray-700">{String(val)}</span>
            </div>
          ))}
        </div>
      )
    }

    return <span className="text-gray-700 text-sm">{String(value)}</span>
  }

  const getSectionIcon = (sectionKey: string, isOpen: boolean = false) => {
    const iconMap: Record<string, { closed: React.ReactNode; open: React.ReactNode }> = {
      'title': {
        closed: <FileText className="h-4 w-4 text-gray-600" />,
        open: <FileText className="h-4 w-4 text-blue-600" />
      },
      'type': {
        closed: <Target className="h-4 w-4 text-gray-600" />,
        open: <Target className="h-4 w-4 text-blue-600" />
      },
      'description': {
        closed: <Eye className="h-4 w-4 text-gray-600" />,
        open: <Eye className="h-4 w-4 text-blue-600" />
      },
      'website': {
        closed: <ArrowLeft className="h-4 w-4 text-gray-600 rotate-180" />,
        open: <ArrowLeft className="h-4 w-4 text-blue-600 rotate-180" />
      },
      'source': {
        closed: <Database className="h-4 w-4 text-gray-600" />,
        open: <Database className="h-4 w-4 text-blue-600" />
      },
      'thumbnail': {
        closed: <Eye className="h-4 w-4 text-gray-600" />,
        open: <Eye className="h-4 w-4 text-blue-600" />
      },
      'organic_offers': {
        closed: <TrendingUp className="h-4 w-4 text-gray-600" />,
        open: <TrendingUp className="h-4 w-4 text-blue-600" />
      },
      'known_attributes': {
        closed: <BarChart3 className="h-4 w-4 text-gray-600" />,
        open: <BarChart3 className="h-4 w-4 text-blue-600" />
      },
      'computed_fields': {
        closed: <Activity className="h-4 w-4 text-gray-600" />,
        open: <Activity className="h-4 w-4 text-blue-600" />
      },
      'rating': {
        closed: <CheckCircle className="h-4 w-4 text-gray-600" />,
        open: <CheckCircle className="h-4 w-4 text-blue-600" />
      },
    }
    const icons = iconMap[sectionKey] || {
      closed: <Database className="h-4 w-4 text-gray-600" />,
      open: <Database className="h-4 w-4 text-blue-600" />
    }
    return isOpen ? icons.open : icons.closed
  }

  const getSectionDescription = (sectionKey: string) => {
    const descriptions: Record<string, string> = {
      'title': 'Entity or brand name from knowledge graph',
      'type': 'Category or type of the entity',
      'description': 'Detailed description of the entity',
      'website': 'Official website or homepage',
      'source': 'Data source or origin information',
      'thumbnail': 'Visual representation or logo',
      'organic_offers': 'Shopping offers and pricing information',
      'known_attributes': 'Product attributes and specifications',
      'computed_fields': 'Computed metadata and flags',
      'rating': 'User ratings and review scores',
      'profiles': 'Associated profiles and social links',
      'related_searches': 'Related search queries',
      'see_results_about': 'Additional result categories',
    }
    return descriptions[sectionKey] || 'Knowledge graph information'
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <Database className="mx-auto h-8 w-8 text-muted-foreground" />
        <p className="text-muted-foreground mt-2">No knowledge graph data available</p>
      </div>
    )
  }

  // Handle single knowledge graph entity
  if (typeof data === 'object' && !Array.isArray(data)) {
    const sections = Object.entries(data)

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h4 className="font-semibold text-lg">Knowledge Graph Entity</h4>
            <p className="text-sm text-muted-foreground">Structured information about the entity</p>
          </div>
          <Badge variant="outline">{sections.length} properties</Badge>
        </div>

        {/* Main entity info card */}
        <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4 mb-4">
          <div className="space-y-3">
            {/* Title */}
            {data.title && (
              <div>
                <h5 className="font-semibold text-indigo-900 text-lg">{data.title}</h5>
              </div>
            )}

            {/* Type and ID */}
            <div className="flex items-center space-x-4">
              {data.type && (
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4 text-indigo-600" />
                  <span className="text-sm text-indigo-700 font-medium">{data.type}</span>
                </div>
              )}
              {data.id && (
                <div className="flex items-center space-x-2">
                  <Database className="h-4 w-4 text-indigo-600" />
                  <span className="text-xs text-indigo-600 font-mono">{data.id}</span>
                </div>
              )}
            </div>

            {/* Rating */}
            {data.rating && (
              <div className="flex items-center space-x-2">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <span key={i} className={`text-sm ${i < Math.floor(data.rating) ? 'text-yellow-500' : 'text-gray-300'}`}>
                      ★
                    </span>
                  ))}
                  <span className="ml-2 text-sm font-medium text-gray-700">{data.rating}/5</span>
                </div>
              </div>
            )}

            {/* Block Position */}
            {data.block_position && (
              <div className="flex items-center space-x-2">
                <Target className="h-4 w-4 text-indigo-600" />
                <span className="text-sm text-indigo-700">Knowledge Graph Position: #{data.block_position}</span>
              </div>
            )}

            {/* Description */}
            {data.description && (
              <div className="text-sm text-gray-700 leading-relaxed">
                {data.description}
              </div>
            )}

            {/* Website */}
            {data.website && (
              <div className="flex items-center space-x-2">
                <ArrowLeft className="h-4 w-4 text-indigo-600 rotate-180" />
                <a
                  href={data.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-indigo-600 hover:text-indigo-800 underline text-sm"
                >
                  {data.website}
                </a>
              </div>
            )}
          </div>
        </div>

        {/* Collapsible sections for other properties */}
        {sections.filter(([key]) => !['title', 'type', 'description', 'website', 'id', 'rating', 'block_position'].includes(key)).map(([sectionKey, sectionValue]) => (
          <div key={sectionKey} className="border rounded-lg overflow-hidden">
            <Collapsible
              open={openSections[sectionKey]}
              onOpenChange={() => toggleSection(sectionKey)}
            >
              <CollapsibleTrigger asChild>
                <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getSectionIcon(sectionKey, openSections[sectionKey])}
                      <div>
                        <h5 className="font-semibold text-gray-800">{formatFieldName(sectionKey)}</h5>
                        <p className="text-xs text-gray-600">{getSectionDescription(sectionKey)}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {typeof sectionValue === 'object' && sectionValue !== null && (
                        <Badge variant="secondary" className="text-xs">
                          {Array.isArray(sectionValue)
                            ? `${sectionValue.length} items`
                            : `${Object.keys(sectionValue).length} fields`
                          }
                        </Badge>
                      )}
                      {openSections[sectionKey] ? (
                        <ChevronDown className="h-4 w-4 text-blue-600" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-gray-500" />
                      )}
                    </div>
                  </div>
                </div>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className="p-4 bg-white">
                  {sectionKey === 'organic_offers' && Array.isArray(sectionValue) ? (
                    <div className="space-y-3">
                      {sectionValue.map((offer, index) => {
                        // Try to find the actual price from known_attributes
                        const knownAttrs = (data as any).known_attributes || []
                        const priceAttr = knownAttrs.find((attr: any) =>
                          attr.link === offer.link && attr.value && attr.value.startsWith('$')
                        )
                        const actualPrice = priceAttr?.value || offer.price

                        return (
                          <div key={index} className="bg-green-50 border border-green-200 rounded-lg p-3">
                            <div className="space-y-2">
                              {/* Store and Link */}
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <span className="text-lg">🛒</span>
                                  <span className="font-medium text-green-900">
                                    {offer.link.includes('ebay.com') ? 'eBay' :
                                     offer.link.includes('amazon.com') ? 'Amazon' :
                                     offer.link.includes('walmart.com') ? 'Walmart' :
                                     new URL(offer.link).hostname}
                                  </span>
                                </div>
                                <div>
                                  <span className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-bold">
                                    {actualPrice}
                                  </span>
                                </div>
                              </div>

                              {/* Full Link */}
                              <div className="bg-white rounded p-2 border">
                                <a
                                  href={offer.link}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-green-600 hover:text-green-800 underline text-xs break-all"
                                >
                                  🔗 {offer.link}
                                </a>
                              </div>

                              {/* Additional Info */}
                              {actualPrice !== offer.price && (
                                <div className="text-xs text-gray-600">
                                  <span className="bg-gray-100 px-2 py-1 rounded">
                                    Listed as: {offer.price}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  ) : sectionKey === 'known_attributes' && Array.isArray(sectionValue) ? (
                    <div className="space-y-3">
                      {sectionValue.map((attr, index) => {
                        // Determine if this is a price attribute
                        const isPriceAttribute = attr.attribute?.includes('organic-offers') || attr.name?.includes('Price history')
                        // Determine if this is a product specification
                        const isProductSpec = attr.attribute?.includes('computer_covers_and_skins') ||
                                             attr.value?.includes('Size:') ||
                                             attr.value?.includes('Type:') ||
                                             attr.value?.includes('Use:')
                        // Determine if this is a review attribute
                        const isReviewAttribute = attr.attribute?.includes('user-review') || attr.name?.includes('review')

                        // Clean up the name for display
                        let displayName = attr.name
                        if (displayName.startsWith('kc:/shopping/')) {
                          displayName = displayName.replace(/^kc:\/shopping\/[^:]*:/, '').replace(/_/g, ' ')
                        }

                        // Clean up the value for display
                        let displayValue = attr.value || ''
                        if (displayValue.includes('&nbsp;')) {
                          displayValue = displayValue.replace(/&nbsp;/g, ' ')
                        }

                        return displayName ? (
                          <div key={index} className={`border rounded-lg p-3 ${
                            isPriceAttribute ? 'bg-green-50 border-green-200' :
                            isProductSpec ? 'bg-blue-50 border-blue-200' :
                            isReviewAttribute ? 'bg-yellow-50 border-yellow-200' :
                            'bg-gray-50 border-gray-200'
                          }`}>
                            <div className="space-y-2">
                              {/* Attribute Name/Title */}
                              <div className={`font-medium text-sm ${
                                isPriceAttribute ? 'text-green-900' :
                                isProductSpec ? 'text-blue-900' :
                                isReviewAttribute ? 'text-yellow-900' :
                                'text-gray-900'
                              }`}>
                                {isPriceAttribute && '💰 '}
                                {isProductSpec && '📋 '}
                                {isReviewAttribute && '⭐ '}
                                {displayName}
                              </div>

                              {/* Attribute Value */}
                              {displayValue && (
                                <div className="text-gray-700 text-sm">
                                  {isPriceAttribute ? (
                                    <span className="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                                      {displayValue}
                                    </span>
                                  ) : isProductSpec ? (
                                    <div className="bg-white rounded px-2 py-1 border">
                                      <span dangerouslySetInnerHTML={{ __html: displayValue }} />
                                    </div>
                                  ) : isReviewAttribute ? (
                                    <div className="bg-white rounded px-2 py-1 border max-h-20 overflow-y-auto">
                                      <span className="text-xs">{displayValue}</span>
                                    </div>
                                  ) : (
                                    <div className="bg-white rounded px-2 py-1 border">
                                      <span className="text-xs">{displayValue}</span>
                                    </div>
                                  )}
                                </div>
                              )}

                              {/* Link */}
                              {attr.link && (
                                <div className="mt-2">
                                  <a
                                    href={attr.link}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className={`underline text-xs ${
                                      isPriceAttribute ? 'text-green-600 hover:text-green-800' :
                                      isProductSpec ? 'text-blue-600 hover:text-blue-800' :
                                      isReviewAttribute ? 'text-yellow-600 hover:text-yellow-800' :
                                      'text-gray-600 hover:text-gray-800'
                                    }`}
                                  >
                                    🔗 {isPriceAttribute ? 'View on eBay' :
                                         isReviewAttribute ? 'Write Review' :
                                         'View Details'}
                                  </a>
                                </div>
                              )}

                              {/* Technical Attribute Info (collapsed by default) */}
                              {attr.attribute && (
                                <details className="mt-2">
                                  <summary className="cursor-pointer text-xs text-gray-500">
                                    Technical Info
                                  </summary>
                                  <div className="mt-1 text-xs text-gray-400 font-mono bg-gray-100 p-1 rounded">
                                    {attr.attribute}
                                  </div>
                                </details>
                              )}
                            </div>
                          </div>
                        ) : null
                      })}
                    </div>
                  ) : sectionKey === 'computed_fields' && typeof sectionValue === 'object' && sectionValue !== null ? (
                    <div className="space-y-2">
                      {Object.entries(sectionValue).map(([key, value]) => (
                        <div key={key} className="bg-gray-50 rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-700">
                              {formatFieldName(key)}:
                            </span>
                            <div className="text-sm">
                              {typeof value === 'boolean' ? (
                                <Badge variant={value ? "default" : "secondary"} className="text-xs">
                                  {value ? (
                                    <><CheckCircle className="w-3 h-3 mr-1" /> {String(value)}</>
                                  ) : (
                                    <><AlertCircle className="w-3 h-3 mr-1" /> {String(value)}</>
                                  )}
                                </Badge>
                              ) : (
                                renderSimpleValue(value)
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : Array.isArray(sectionValue) ? (
                    <div className="space-y-3">
                      {sectionValue.map((item, index) => (
                        <div key={index} className="bg-indigo-50 border border-indigo-200 rounded-lg p-3">
                          <div className="space-y-2">
                            <div className="font-medium text-indigo-900 text-sm">
                              Item #{index + 1}
                            </div>
                            {typeof item === 'object' && item !== null ? (
                              <div className="space-y-2">
                                {Object.entries(item).map(([key, val]) => (
                                  <div key={key} className="flex items-start justify-between">
                                    <span className="text-sm font-medium text-gray-600 min-w-0 flex-1">
                                      {formatFieldName(key)}:
                                    </span>
                                    <div className="ml-4 text-right min-w-0 flex-1">
                                      {renderSimpleValue(val)}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="text-gray-700 text-sm">
                                {renderSimpleValue(item)}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="bg-indigo-50 rounded-lg p-4">
                      <div className="text-lg font-medium text-gray-800">
                        {renderSimpleValue(sectionValue)}
                      </div>
                    </div>
                  )}
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        ))}
      </div>
    )
  }

  // Handle array or other data structures
  if (Array.isArray(data)) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h4 className="font-semibold text-lg">Knowledge Graph Entities</h4>
            <p className="text-sm text-muted-foreground">Multiple knowledge graph entities</p>
          </div>
          <Badge variant="outline">{data.length} entities</Badge>
        </div>

        <div className="space-y-3">
          {data.map((entity, index) => (
            <div key={index} className="border rounded-lg overflow-hidden">
              <Collapsible
                open={openSections[`entity_${index}`]}
                onOpenChange={() => toggleSection(`entity_${index}`)}
              >
                <CollapsibleTrigger asChild>
                  <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Database className={`h-4 w-4 ${openSections[`entity_${index}`] ? 'text-blue-600' : 'text-gray-600'}`} />
                        <div>
                          <h5 className="font-semibold text-gray-800">
                            {entity.title || entity.name || `Entity #${index + 1}`}
                          </h5>
                          <p className="text-xs text-gray-600">Knowledge graph entity</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {openSections[`entity_${index}`] ? (
                          <ChevronDown className="h-4 w-4 text-blue-600" />
                        ) : (
                          <ChevronRight className="h-4 w-4 text-gray-500" />
                        )}
                      </div>
                    </div>
                  </div>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <div className="p-4 bg-white">
                    <div className="bg-indigo-50 rounded-lg p-4">
                      <div className="text-lg font-medium text-gray-800">
                        {renderSimpleValue(entity)}
                      </div>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </div>
          ))}
        </div>
      </div>
    )
  }

  const sections = Object.entries(data)

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h4 className="font-semibold text-lg">Knowledge Graph Data</h4>
          <p className="text-sm text-muted-foreground">Structured knowledge information</p>
        </div>
        <Badge variant="outline">{sections.length} sections</Badge>
      </div>

      {sections.map(([sectionKey, sectionValue]) => (
        <div key={sectionKey} className="border rounded-lg overflow-hidden">
          <Collapsible
            open={openSections[sectionKey]}
            onOpenChange={() => toggleSection(sectionKey)}
          >
            <CollapsibleTrigger asChild>
              <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getSectionIcon(sectionKey, openSections[sectionKey])}
                    <div>
                      <h5 className="font-semibold text-gray-800">{formatFieldName(sectionKey)}</h5>
                      <p className="text-xs text-gray-600">{getSectionDescription(sectionKey)}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {typeof sectionValue === 'object' && sectionValue !== null && (
                      <Badge variant="secondary" className="text-xs">
                        {Array.isArray(sectionValue)
                          ? `${sectionValue.length} items`
                          : `${Object.keys(sectionValue).length} fields`
                        }
                      </Badge>
                    )}
                    {openSections[sectionKey] ? (
                      <ChevronDown className="h-4 w-4 text-blue-600" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    )}
                  </div>
                </div>
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div className="p-4 bg-white">
                {sectionKey === 'organic_offers' && Array.isArray(sectionValue) ? (
                  <div className="space-y-3">
                    {sectionValue.map((offer, index) => (
                      <div key={index} className="bg-green-50 border border-green-200 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <a
                              href={offer.link}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-green-700 hover:text-green-900 underline text-sm break-all"
                            >
                              {offer.link.includes('ebay.com') ? 'eBay' :
                               offer.link.includes('amazon.com') ? 'Amazon' :
                               new URL(offer.link).hostname}
                            </a>
                          </div>
                          <div className="ml-4">
                            <span className="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                              {offer.price}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : sectionKey === 'known_attributes' && Array.isArray(sectionValue) ? (
                  <div className="space-y-3">
                    {sectionValue.map((attr, index) => (
                      <div key={index} className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div className="space-y-2">
                          {attr.name && (
                            <div className="font-medium text-blue-900 text-sm">
                              {attr.name.replace(/^kc:\/shopping\/[^:]*:/, '').replace(/_/g, ' ')}
                            </div>
                          )}
                          {attr.value && (
                            <div className="text-gray-700 text-sm" dangerouslySetInnerHTML={{ __html: attr.value }} />
                          )}
                          {attr.link && (
                            <div className="mt-2">
                              <a
                                href={attr.link}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 underline text-xs"
                              >
                                View Details
                              </a>
                            </div>
                          )}
                          {attr.attribute && (
                            <div className="text-xs text-gray-500 mt-1">
                              Attribute: {attr.attribute}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : sectionKey === 'computed_fields' && typeof sectionValue === 'object' && sectionValue !== null ? (
                  <div className="space-y-2">
                    {Object.entries(sectionValue).map(([key, value]) => (
                      <div key={key} className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">
                            {formatFieldName(key)}:
                          </span>
                          <div className="text-sm">
                            {typeof value === 'boolean' ? (
                              <Badge variant={value ? "default" : "secondary"} className="text-xs">
                                {value ? (
                                  <><CheckCircle className="w-3 h-3 mr-1" /> {String(value)}</>
                                ) : (
                                  <><AlertCircle className="w-3 h-3 mr-1" /> {String(value)}</>
                                )}
                              </Badge>
                            ) : (
                              renderSimpleValue(value)
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : Array.isArray(sectionValue) ? (
                  <div className="space-y-3">
                    {sectionValue.map((item, index) => (
                      <div key={index} className="bg-indigo-50 border border-indigo-200 rounded-lg p-3">
                        <div className="space-y-2">
                          <div className="font-medium text-indigo-900 text-sm">
                            Item #{index + 1}
                          </div>
                          {typeof item === 'object' && item !== null ? (
                            <div className="space-y-2">
                              {Object.entries(item).map(([key, val]) => (
                                <div key={key} className="flex items-start justify-between">
                                  <span className="text-sm font-medium text-gray-600 min-w-0 flex-1">
                                    {formatFieldName(key)}:
                                  </span>
                                  <div className="ml-4 text-right min-w-0 flex-1">
                                    {renderSimpleValue(val)}
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="text-gray-700 text-sm">
                              {renderSimpleValue(item)}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="bg-indigo-50 rounded-lg p-4">
                    <div className="text-lg font-medium text-gray-800">
                      {renderSimpleValue(sectionValue)}
                    </div>
                  </div>
                )}
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      ))}
    </div>
  )
}

// Component for displaying shopping results in a user-friendly format
function ShoppingResultsViewer({ data }: { data: any }) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({})

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const formatFieldName = (key: string): string => {
    return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const renderShoppingResult = (result: any, index: number): React.ReactNode => {
    const resultKey = `result_${index}`
    const title = result.title || `Product #${index + 1}`
    const price = result.price || result.sale_price || 'Price not available'
    const rating = result.rating || 0
    const reviews = result.reviews || 0

    return (
      <div key={index} className="border rounded-lg overflow-hidden">
        <Collapsible
          open={openSections[resultKey]}
          onOpenChange={() => toggleSection(resultKey)}
        >
          <CollapsibleTrigger asChild>
            <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <TrendingUp className={`h-4 w-4 ${openSections[resultKey] ? 'text-blue-600' : 'text-gray-600'}`} />
                  <div className="flex-1 min-w-0">
                    <h5 className="font-semibold text-gray-800 ">{title}</h5>
                    <div className="flex items-center flex-wrap space-x-4 text-xs text-gray-600">
                      <span className="flex items-center">
                        <span className="text-green-600 font-bold">{price}</span>
                      </span>
                      {rating > 0 && (
                        <span className="flex items-center">
                          <span className="text-yellow-500">★</span>
                          <span className="ml-1">{rating}</span>
                          {reviews > 0 && <span className="ml-1">({reviews})</span>}
                        </span>
                      )}
                      {result.delivery && (
                        <span className="flex items-center ">
                          <span className="text-blue-600">{result.delivery}</span>
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {openSections[resultKey] ? (
                    <ChevronDown className="h-4 w-4 text-blue-600" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  )}
                </div>
              </div>
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <div className="p-4 bg-white space-y-4">
              {/* Product Image */}
              {result.thumbnail && (
                <div className="flex justify-center">
                  <img
                    src={result.thumbnail}
                    alt={title}
                    className="w-32 h-32 object-cover rounded-lg border"
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = 'none'
                    }}
                  />
                </div>
              )}

              {/* Pricing Information */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <h6 className="font-medium text-green-900 mb-2">💰 Pricing</h6>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Current Price:</span>
                    <span className="text-lg font-bold text-green-700">{price}</span>
                  </div>
                  {result.compare_at_price && result.compare_at_price !== price && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Original Price:</span>
                      <span className="text-sm text-gray-500 line-through">{result.compare_at_price}</span>
                    </div>
                  )}
                  {result.extracted_price && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Numeric Value:</span>
                      <span className="text-sm text-gray-700">${result.extracted_price}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Rating and Reviews */}
              {(rating > 0 || reviews > 0) && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <h6 className="font-medium text-yellow-900 mb-2">⭐ Reviews</h6>
                  <div className="space-y-2">
                    {rating > 0 && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Rating:</span>
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <span key={i} className={`text-sm ${i < Math.floor(rating) ? 'text-yellow-500' : 'text-gray-300'}`}>
                              ★
                            </span>
                          ))}
                          <span className="ml-2 text-sm font-medium text-gray-700">{rating}/5</span>
                        </div>
                      </div>
                    )}
                    {reviews > 0 && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Total Reviews:</span>
                        <span className="text-sm font-medium text-gray-700">{reviews.toLocaleString()}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Product Features */}
              {result.extensions && result.extensions.length > 0 && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <h6 className="font-medium text-blue-900 mb-2">📋 Features</h6>
                  <div className="flex flex-wrap gap-2">
                    {result.extensions.map((feature: string, idx: number) => (
                      <span key={idx} className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Delivery Information */}
              {result.delivery && (
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                  <h6 className="font-medium text-purple-900 mb-2">🚚 Delivery</h6>
                  <p className="text-sm text-gray-700">{result.delivery}</p>
                </div>
              )}

              {/* Product Link */}
              {result.link && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                  <h6 className="font-medium text-gray-900 mb-2">🔗 Product Link</h6>
                  <a
                    href={result.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline text-sm break-all"
                  >
                    {result.link}
                  </a>
                </div>
              )}

              {/* Additional Images */}
              {result.images && result.images.length > 0 && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                  <h6 className="font-medium text-gray-900 mb-2">🖼️ Additional Images</h6>
                  <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2">
                    {result.images.slice(0, 4).map((image: string, idx: number) => (
                      <img
                        key={idx}
                        src={image}
                        alt={`${title} - Image ${idx + 1}`}
                        className="w-full h-16 sm:h-20 object-cover rounded border"
                        onError={(e) => {
                          (e.target as HTMLImageElement).style.display = 'none'
                        }}
                      />
                    ))}
                  </div>
                  {result.images.length > 4 && (
                    <p className="text-xs text-gray-500 mt-2">
                      +{result.images.length - 4} more images
                    </p>
                  )}
                </div>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <TrendingUp className="mx-auto h-8 w-8 text-muted-foreground" />
        <p className="text-muted-foreground mt-2">No shopping results available</p>
      </div>
    )
  }

  // Handle array of shopping results
  if (Array.isArray(data)) {
    if (data.length === 0) {
      return (
        <div className="text-center py-8">
          <TrendingUp className="mx-auto h-8 w-8 text-muted-foreground" />
          <p className="text-muted-foreground mt-2">No shopping results found</p>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h4 className="font-semibold text-lg">Shopping Results</h4>
            <p className="text-sm text-muted-foreground">Click on any product to view detailed information</p>
          </div>
          <Badge variant="outline">{data.length} products</Badge>
        </div>

        <div className="space-y-3">
          {data.map((result, index) => renderShoppingResult(result, index))}
        </div>
      </div>
    )
  }

  // Handle single object or other data structures
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h4 className="font-semibold text-lg">Shopping Data</h4>
          <p className="text-sm text-muted-foreground">Shopping information and results</p>
        </div>
      </div>

      <div className="bg-blue-50 rounded-lg p-4">
        <div className="text-lg font-medium text-gray-800">
          {JSON.stringify(data, null, 2)}
        </div>
      </div>
    </div>
  )
}

// Component for displaying related questions in a user-friendly format
function RelatedQuestionsViewer({ data }: { data: any }) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({})

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const renderRelatedQuestion = (question: any, index: number): React.ReactNode => {
    const questionKey = `question_${index}`
    const questionText = question.question || `Question #${index + 1}`
    const answer = question.answer || 'No answer available'
    const answerLength = question.computed_fields?.answer_length || 0
    const hasAnswer = question.computed_fields?.has_answer || false

    return (
      <div key={index} className="border rounded-lg overflow-hidden">
        <Collapsible
          open={openSections[questionKey]}
          onOpenChange={() => toggleSection(questionKey)}
        >
          <CollapsibleTrigger asChild>
            <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`h-4 w-4 ${openSections[questionKey] ? 'text-blue-600' : 'text-gray-600'}`}>
                    ❓
                  </div>
                  <div className="flex-1 min-w-0">
                    <h5 className="font-semibold text-gray-800">{questionText}</h5>
                    <div className="flex items-center flex-wrap space-x-4 text-xs text-gray-600">
                      {hasAnswer && (
                        <span className="flex items-center">
                          <span className="text-green-600">✓ Answered</span>
                        </span>
                      )}
                      {answerLength > 0 && (
                        <span className="flex items-center">
                          <span className="text-blue-600">{answerLength} characters</span>
                        </span>
                      )}
                      {question.block_position && (
                        <span className="flex items-center">
                          <span className="text-purple-600">Position #{question.block_position}</span>
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {openSections[questionKey] ? (
                    <ChevronDown className="h-4 w-4 text-blue-600" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  )}
                </div>
              </div>
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <div className="p-4 bg-white space-y-4">
              {/* Question */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h6 className="font-medium text-blue-900 mb-2">❓ Question</h6>
                <p className="text-gray-700 text-sm font-medium">
                  {questionText}
                </p>
              </div>

              {/* Answer */}
              {answer && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <h6 className="font-medium text-green-900 mb-2">✅ Answer</h6>
                  <div className="text-gray-700 text-sm leading-relaxed">
                    {answer}
                  </div>
                  {answerLength > 0 && (
                    <div className="mt-2">
                      <Badge variant="outline" className="text-xs">
                        {answerLength} characters
                      </Badge>
                    </div>
                  )}
                </div>
              )}

              {/* Answer List (if available) */}
              {question.answer_list && question.answer_list.length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <h6 className="font-medium text-yellow-900 mb-2">📋 Answer Options</h6>
                  <div className="space-y-2">
                    {question.answer_list.map((item: any, idx: number) => (
                      <div key={idx} className="bg-white rounded p-2 border-l-4 border-yellow-300">
                        <div className="flex items-start space-x-2">
                          <span className="text-yellow-600 font-bold text-xs mt-1">{idx + 1}.</span>
                          <span className="text-gray-700 text-sm" dangerouslySetInnerHTML={{ __html: typeof item === 'string' ? item : item.text || String(item) }} />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}


              {/* Block Position */}
              {question.block_position && (
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                  <h6 className="font-medium text-purple-900 mb-2">📍 Position</h6>
                  <p className="text-sm text-gray-700">
                    This question appears at position <span className="font-bold text-purple-700">#{question.block_position}</span> in search results
                  </p>
                </div>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <div className="text-4xl mb-2">❓</div>
        <p className="text-muted-foreground mt-2">No related questions available</p>
      </div>
    )
  }

  // Handle array of questions
  if (Array.isArray(data)) {
    if (data.length === 0) {
      return (
        <div className="text-center py-8">
          <div className="text-4xl mb-2">❓</div>
          <p className="text-muted-foreground mt-2">No related questions found</p>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h4 className="font-semibold text-lg">Related Questions</h4>
            <p className="text-sm text-muted-foreground">Click on any question to view the answer and details</p>
          </div>
          <Badge variant="outline">{data.length} questions</Badge>
        </div>

        <div className="space-y-3">
          {data.map((question, index) => renderRelatedQuestion(question, index))}
        </div>
      </div>
    )
  }

  // Handle single object or other data structures
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h4 className="font-semibold text-lg">Related Questions Data</h4>
          <p className="text-sm text-muted-foreground">Question and answer information</p>
        </div>
      </div>

      <div className="bg-blue-50 rounded-lg p-4">
        <div className="text-lg font-medium text-gray-800">
          {JSON.stringify(data, null, 2)}
        </div>
      </div>
    </div>
  )
}

// Component for displaying inline videos in a user-friendly format
function InlineVideosViewer({ data }: { data: any }) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({})

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const formatDuration = (duration: string): string => {
    // Convert duration like "PT2M30S" to "2:30"
    if (!duration) return ''

    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
    if (!match) return duration

    const hours = parseInt(match[1] || '0')
    const minutes = parseInt(match[2] || '0')
    const seconds = parseInt(match[3] || '0')

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`
    }
  }

  const formatViews = (views: number): string => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M views`
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K views`
    } else {
      return `${views} views`
    }
  }

  const renderInlineVideo = (video: any, index: number): React.ReactNode => {
    const videoKey = `video_${index}`
    const title = video.title || `Video #${index + 1}`
    const channel = video.channel
    const duration = formatDuration(video.duration)
    const views = video.views ? formatViews(video.views) : null
    const publishedDate = video.published_date || video.date

    return (
      <div key={index} className="border rounded-lg overflow-hidden">
        <Collapsible
          open={openSections[videoKey]}
          onOpenChange={() => toggleSection(videoKey)}
        >
          <CollapsibleTrigger asChild>
            <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`h-4 w-4 ${openSections[videoKey] ? 'text-blue-600' : 'text-gray-600'}`}>
                    🎥
                  </div>
                  <div className="flex-1 min-w-0">
                    <h5 className="font-semibold text-gray-800 ">{title}</h5>
                    <div className="flex items-center flex-wrap space-x-4 text-xs text-gray-600">
                      {channel && (
                        <span className="flex items-center">
                          <span className="text-blue-600">{channel}</span>
                        </span>
                      )}
                      {duration && (
                        <span className="flex items-center">
                          <span className="text-green-600">{duration}</span>
                        </span>
                      )}
                      {views && (
                        <span className="flex items-center">
                          <span className="text-purple-600">{views}</span>
                        </span>
                      )}
                      {publishedDate && (
                        <span className="flex items-center">
                          <span className="text-orange-600">{publishedDate}</span>
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {openSections[videoKey] ? (
                    <ChevronDown className="h-4 w-4 text-blue-600" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  )}
                </div>
              </div>
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <div className="p-4 bg-white space-y-4">
              {/* Video Thumbnail */}
              {video.thumbnail && (
                <div className="flex justify-center">
                  <div className="relative">
                    <img
                      src={video.thumbnail}
                      alt={title}
                      className="w-full max-w-md h-48 object-cover rounded-lg border"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none'
                      }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="bg-black bg-opacity-60 rounded-full p-3">
                        <div className="w-6 h-6 text-white">▶️</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Video Title */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h6 className="font-medium text-blue-900 mb-2">🎥 Video Title</h6>
                <p className="text-gray-700 text-sm font-medium">
                  {title}
                </p>
              </div>

              {/* Channel Information */}
              {channel && (
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                  <h6 className="font-medium text-purple-900 mb-2">📺 Channel</h6>
                  <p className="text-gray-700 text-sm">
                    {channel}
                  </p>
                </div>
              )}

              {/* Video Stats */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <h6 className="font-medium text-green-900 mb-2">📊 Video Stats</h6>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div className="bg-white rounded p-2">
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium text-gray-600">Duration:</span>
                      <span className="text-xs text-gray-700 font-bold">{duration}</span>
                    </div>
                  </div>
                  {views && (
                    <div className="bg-white rounded p-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-medium text-gray-600">Views:</span>
                        <span className="text-xs text-gray-700 font-bold">{views}</span>
                      </div>
                    </div>
                  )}
                  {publishedDate && (
                    <div className="bg-white rounded p-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-medium text-gray-600">Published:</span>
                        <span className="text-xs text-gray-700">{publishedDate}</span>
                      </div>
                    </div>
                  )}
                  {video.position && (
                    <div className="bg-white rounded p-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-medium text-gray-600">Position:</span>
                        <span className="text-xs text-gray-700">#{video.position}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Video Description */}
              {video.description && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <h6 className="font-medium text-yellow-900 mb-2">📝 Description</h6>
                  <div className="text-gray-700 text-sm leading-relaxed">
                    {video.description.length > 200 ? (
                      <details>
                        <summary className="cursor-pointer text-yellow-700 font-medium">
                          {video.description.substring(0, 200)}... (Show more)
                        </summary>
                        <div className="mt-2">
                          {video.description}
                        </div>
                      </details>
                    ) : (
                      video.description
                    )}
                  </div>
                </div>
              )}

              {/* Video Link */}
              {video.link && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                  <h6 className="font-medium text-gray-900 mb-2">🔗 Video Link</h6>
                  <a
                    href={video.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline text-sm break-all"
                  >
                    {video.link}
                  </a>
                </div>
              )}

              {/* Additional Properties */}
              {Object.entries(video).filter(([key]) =>
                !['title', 'channel', 'duration', 'views', 'published_date', 'date', 'thumbnail', 'description', 'link', 'position', 'computed_fields'].includes(key)
              ).length > 0 && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                  <h6 className="font-medium text-gray-900 mb-2">📋 Additional Info</h6>
                  <div className="space-y-2">
                    {Object.entries(video).filter(([key]) =>
                      !['title', 'channel', 'duration', 'views', 'published_date', 'date', 'thumbnail', 'description', 'link', 'position', 'computed_fields'].includes(key)
                    ).map(([key, value]) => (
                      <div key={key} className="bg-white rounded p-2">
                        <div className="flex items-start justify-between">
                          <span className="text-xs font-medium text-gray-600 min-w-0 flex-1">
                            {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                          </span>
                          <div className="ml-4 text-right min-w-0 flex-1">
                            <span className="text-xs text-gray-700">{String(value)}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <div className="text-4xl mb-2">🎥</div>
        <p className="text-muted-foreground mt-2">No inline videos available</p>
      </div>
    )
  }

  // Handle array of videos
  if (Array.isArray(data)) {
    if (data.length === 0) {
      return (
        <div className="text-center py-8">
          <div className="text-4xl mb-2">🎥</div>
          <p className="text-muted-foreground mt-2">No inline videos found</p>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h4 className="font-semibold text-lg">Inline Videos</h4>
            <p className="text-sm text-muted-foreground">Click on any video to view detailed information</p>
          </div>
          <Badge variant="outline">{data.length} videos</Badge>
        </div>

        <div className="space-y-3">
          {data.map((video, index) => renderInlineVideo(video, index))}
        </div>
      </div>
    )
  }

  // Handle single object or other data structures
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h4 className="font-semibold text-lg">Inline Videos Data</h4>
          <p className="text-sm text-muted-foreground">Video content information</p>
        </div>
      </div>

      <div className="bg-blue-50 rounded-lg p-4">
        <div className="text-lg font-medium text-gray-800">
          {JSON.stringify(data, null, 2)}
        </div>
      </div>
    </div>
  )
}

// Component for displaying AI Overview in a user-friendly format
function AIOverviewViewer({ data }: { data: any }) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    contents: true, // Open contents by default
    sources: false,
    metadata: false
  })

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const renderContentItem = (content: any, index: number): React.ReactNode => {
    const type = content.type || 'text'
    const text = content.text || content.content || ''

    switch (type) {
      case 'header':
        return (
          <div key={index} className="bg-blue-50 border-l-4 border-blue-400 p-3 rounded-r-lg">
            <h6 className="font-bold text-blue-900 text-base">
              📋 {text}
            </h6>
          </div>
        )

      case 'paragraph':
      case 'text':
        return (
          <div key={index} className="bg-gray-50 border-l-4 border-gray-300 p-3 rounded-r-lg">
            <p className="text-gray-700 text-sm leading-relaxed">
              {text}
            </p>
          </div>
        )

      case 'list':
      case 'bullet':
        return (
          <div key={index} className="bg-green-50 border-l-4 border-green-400 p-3 rounded-r-lg">
            <div className="flex items-start space-x-2">
              <span className="text-green-600 font-bold text-sm mt-0.5">•</span>
              <p className="text-gray-700 text-sm leading-relaxed flex-1">
                {text}
              </p>
            </div>
          </div>
        )

      case 'quote':
        return (
          <div key={index} className="bg-yellow-50 border-l-4 border-yellow-400 p-3 rounded-r-lg">
            <div className="flex items-start space-x-2">
              <span className="text-yellow-600 text-lg">"</span>
              <p className="text-gray-700 text-sm leading-relaxed italic flex-1">
                {text}
              </p>
              <span className="text-yellow-600 text-lg">"</span>
            </div>
          </div>
        )

      default:
        return (
          <div key={index} className="bg-purple-50 border-l-4 border-purple-400 p-3 rounded-r-lg">
            <div className="space-y-1">
              <div className="text-xs font-medium text-purple-700 uppercase">
                {type}
              </div>
              <p className="text-gray-700 text-sm leading-relaxed">
                {text}
              </p>
            </div>
          </div>
        )
    }
  }

  const renderSource = (source: any, index: number): React.ReactNode => {
    const title = source.source_title || source.title || `Source #${index + 1}`
    const url = source.source_url || source.url || ''
    const domain = source.source_domain || source.domain || ''

    // Determine source type icon
    let sourceIcon = '🌐'
    if (domain.includes('youtube.com')) sourceIcon = '🎥'
    else if (domain.includes('wikipedia.org')) sourceIcon = '📚'
    else if (domain.includes('reddit.com')) sourceIcon = '💬'
    else if (domain.includes('amazon.com')) sourceIcon = '🛒'
    else if (domain.includes('news') || domain.includes('cnn') || domain.includes('bbc')) sourceIcon = '📰'

    return (
      <div key={index} className="bg-white border border-gray-200 rounded-lg p-3 hover:shadow-sm transition-shadow">
        <div className="space-y-2">
          <div className="flex items-start space-x-3">
            <span className="text-lg flex-shrink-0">{sourceIcon}</span>
            <div className="flex-1 min-w-0">
              <h6 className="font-medium text-gray-800 text-sm truncate">
                {title}
              </h6>
              {domain && (
                <p className="text-xs text-gray-500 mt-1">
                  {domain}
                </p>
              )}
            </div>
          </div>

          {url && (
            <div className="ml-8">
              <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 underline text-xs break-all"
              >
                🔗 {url.length > 60 ? `${url.substring(0, 60)}...` : url}
              </a>
            </div>
          )}
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <div className="text-4xl mb-2">🤖</div>
        <p className="text-muted-foreground mt-2">No AI Overview available</p>
      </div>
    )
  }

  const banner = data.ai_overview_banner || 'AI Overview'
  const contents = data.ai_overview_contents || []
  const sources = data.ai_overview_sources || []
  const computedFields = data.computed_fields || {}

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h4 className="font-semibold text-lg flex items-center space-x-2">
            <span>🤖</span>
            <span>{banner}</span>
          </h4>
          <p className="text-sm text-muted-foreground">AI-generated overview and insights</p>
        </div>
        <div className="flex space-x-2">
          {computedFields.content_count && (
            <Badge variant="outline">{computedFields.content_count} contents</Badge>
          )}
          {computedFields.source_count && (
            <Badge variant="outline">{computedFields.source_count} sources</Badge>
          )}
        </div>
      </div>

      {/* AI Overview Contents */}
      {contents.length > 0 && (
        <div className="border rounded-lg overflow-hidden">
          <Collapsible
            open={openSections.contents}
            onOpenChange={() => toggleSection('contents')}
          >
            <CollapsibleTrigger asChild>
              <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`h-4 w-4 ${openSections.contents ? 'text-blue-600' : 'text-gray-600'}`}>
                      🤖
                    </div>
                    <div>
                      <h5 className="font-semibold text-gray-800">AI Overview Content</h5>
                      <p className="text-xs text-gray-600">AI-generated insights and information</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="text-xs">
                      {contents.length} items
                    </Badge>
                    {openSections.contents ? (
                      <ChevronDown className="h-4 w-4 text-blue-600" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    )}
                  </div>
                </div>
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div className="p-4 bg-white space-y-3">
                {contents.map((content: any, index: number) => renderContentItem(content, index))}
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      )}

      {/* AI Overview Sources */}
      {sources.length > 0 && (
        <div className="border rounded-lg overflow-hidden">
          <Collapsible
            open={openSections.sources}
            onOpenChange={() => toggleSection('sources')}
          >
            <CollapsibleTrigger asChild>
              <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`h-4 w-4 ${openSections.sources ? 'text-blue-600' : 'text-gray-600'}`}>
                      📚
                    </div>
                    <div>
                      <h5 className="font-semibold text-gray-800">Sources</h5>
                      <p className="text-xs text-gray-600">References and citations used by AI</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="text-xs">
                      {sources.length} sources
                    </Badge>
                    {openSections.sources ? (
                      <ChevronDown className="h-4 w-4 text-blue-600" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    )}
                  </div>
                </div>
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div className="p-4 bg-white space-y-3">
                {sources.map((source: any, index: number) => renderSource(source, index))}
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      )}

    </div>
  )
}

// Component for displaying related products in a user-friendly format
function RelatedProductsViewer({ data }: { data: any }) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({})

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const renderRelatedProduct = (product: any, index: number): React.ReactNode => {
    const productKey = `product_${index}`
    const title = product.title || `Product #${index + 1}`
    const blockPosition = product.block_position || null
    const hasImage = product.computed_fields?.has_image || false
    const isBase64Image = product.computed_fields?.is_base64_image || false
    const titleLength = product.computed_fields?.title_length || 0

    return (
      <div key={index} className="border rounded-lg overflow-hidden">
        <Collapsible
          open={openSections[productKey]}
          onOpenChange={() => toggleSection(productKey)}
        >
          <CollapsibleTrigger asChild>
            <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`h-4 w-4 ${openSections[productKey] ? 'text-blue-600' : 'text-gray-600'}`}>
                    📦
                  </div>
                  <div className="flex-1 min-w-0">
                    <h5 className="font-semibold text-gray-800 line-clamp-2">{title}</h5>
                    <div className="flex items-center flex-wrap space-x-4 text-xs text-gray-600 mt-1">
                      {blockPosition && (
                        <span className="flex items-center">
                          <span className="text-purple-600">Position #{blockPosition}</span>
                        </span>
                      )}
                      {hasImage && (
                        <span className="flex items-center">
                          <span className="text-green-600">
                            {isBase64Image ? '📷 Base64 Image' : '🖼️ Image'}
                          </span>
                        </span>
                      )}
                      {titleLength > 0 && (
                        <span className="flex items-center">
                          <span className="text-blue-600">{titleLength} chars</span>
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {openSections[productKey] ? (
                    <ChevronDown className="h-4 w-4 text-blue-600" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  )}
                </div>
              </div>
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <div className="p-4 bg-white space-y-4">
              {/* Product Title */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h6 className="font-medium text-blue-900 mb-2">📦 Product Title</h6>
                <p className="text-gray-700 text-sm font-medium leading-relaxed">
                  {title}
                </p>
              </div>

              {/* Product Image */}
              {hasImage && product.image && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <h6 className="font-medium text-green-900 mb-2">
                    {isBase64Image ? '📷 Base64 Image' : '🖼️ Product Image'}
                  </h6>
                  {isBase64Image ? (
                    <div className="bg-white rounded p-3 border">
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-600 text-sm">Base64 encoded image data</span>
                        <Badge variant="secondary" className="text-xs">
                          {product.image.length} bytes
                        </Badge>
                      </div>
                      <details className="mt-2">
                        <summary className="cursor-pointer text-xs text-gray-500">
                          Show base64 data
                        </summary>
                        <div className="mt-2 text-xs font-mono bg-gray-100 p-2 rounded max-h-32 overflow-y-auto break-all">
                          {product.image}
                        </div>
                      </details>
                    </div>
                  ) : (
                    <div className="flex justify-center">
                      <img
                        src={product.image}
                        alt={title}
                        className="w-full max-w-sm h-48 object-cover rounded-lg border"
                        onError={(e) => {
                          (e.target as HTMLImageElement).style.display = 'none'
                        }}
                      />
                    </div>
                  )}
                </div>
              )}

              {/* Position Information */}
              {blockPosition && (
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                  <h6 className="font-medium text-purple-900 mb-2">📍 Position</h6>
                  <p className="text-sm text-gray-700">
                    This product appears at position <span className="font-bold text-purple-700">#{blockPosition}</span> in search results
                  </p>
                </div>
              )}

              {/* Computed Fields */}
              {product.computed_fields && Object.keys(product.computed_fields).length > 0 && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                  <h6 className="font-medium text-gray-900 mb-2">📊 Metadata</h6>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {Object.entries(product.computed_fields).map(([key, value]) => (
                      <div key={key} className="bg-white rounded p-2">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1">
                          <span className="text-xs font-medium text-gray-600">
                            {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                          </span>
                          <div className="text-xs">
                            {typeof value === 'boolean' ? (
                              <Badge variant={value ? "default" : "secondary"} className="text-xs">
                                {value ? (
                                  <><CheckCircle className="w-3 h-3 mr-1" /> {String(value)}</>
                                ) : (
                                  <><AlertCircle className="w-3 h-3 mr-1" /> {String(value)}</>
                                )}
                              </Badge>
                            ) : (
                              <span className="text-gray-700 font-medium break-words">{String(value)}</span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Additional Properties */}
              {Object.entries(product).filter(([key]) =>
                !['title', 'block_position', 'image', 'computed_fields'].includes(key)
              ).length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <h6 className="font-medium text-yellow-900 mb-2">📋 Additional Info</h6>
                  <div className="space-y-2">
                    {Object.entries(product).filter(([key]) =>
                      !['title', 'block_position', 'image', 'computed_fields'].includes(key)
                    ).map(([key, value]) => (
                      <div key={key} className="bg-white rounded p-2">
                        <div className="flex items-start justify-between">
                          <span className="text-xs font-medium text-gray-600 min-w-0 flex-1">
                            {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                          </span>
                          <div className="ml-4 text-right min-w-0 flex-1">
                            <span className="text-xs text-gray-700">{String(value)}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <div className="text-4xl mb-2">📦</div>
        <p className="text-muted-foreground mt-2">No related products available</p>
      </div>
    )
  }

  // Handle array of products
  if (Array.isArray(data)) {
    if (data.length === 0) {
      return (
        <div className="text-center py-8">
          <div className="text-4xl mb-2">📦</div>
          <p className="text-muted-foreground mt-2">No related products found</p>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h4 className="font-semibold text-lg">Related Products</h4>
            <p className="text-sm text-muted-foreground">Click on any product to view detailed information</p>
          </div>
          <Badge variant="outline">{data.length} products</Badge>
        </div>

        <div className="space-y-3">
          {data.map((product, index) => renderRelatedProduct(product, index))}
        </div>
      </div>
    )
  }

  // Handle single object or other data structures
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h4 className="font-semibold text-lg">Related Products Data</h4>
          <p className="text-sm text-muted-foreground">Product information and details</p>
        </div>
      </div>

      <div className="bg-blue-50 rounded-lg p-4">
        <div className="text-lg font-medium text-gray-800">
          {JSON.stringify(data, null, 2)}
        </div>
      </div>
    </div>
  )
}

// Component for displaying related searches in a user-friendly format
function RelatedSearchesViewer({ data }: { data: any }) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({})

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const formatFieldName = (key: string): string => {
    return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const renderSimpleValue = (value: any): React.ReactNode => {
    if (value === null || value === undefined) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-500">
          <AlertCircle className="w-3 h-3 mr-1" />
          Not available
        </span>
      )
    }

    if (typeof value === 'boolean') {
      return (
        <Badge variant={value ? "default" : "secondary"} className="text-xs">
          {value ? (
            <><CheckCircle className="w-3 h-3 mr-1" /> Yes</>
          ) : (
            <><AlertCircle className="w-3 h-3 mr-1" /> No</>
          )}
        </Badge>
      )
    }

    if (typeof value === 'number') {
      return (
        <span className="inline-flex items-center px-3 py-1 bg-purple-50 text-purple-700 rounded-lg font-semibold text-sm">
          {value.toLocaleString()}
        </span>
      )
    }

    if (typeof value === 'string') {
      return <span className="text-gray-700 text-sm leading-relaxed">{value}</span>
    }

    // Handle objects by showing key properties
    if (typeof value === 'object' && value !== null) {
      const entries = Object.entries(value)
      if (entries.length === 0) {
        return <span className="text-gray-500 italic text-sm">Empty object</span>
      }

      // Show all key-value pairs
      return (
        <div className="space-y-1">
          {entries.map(([key, val]) => (
            <div key={key} className="text-xs bg-gray-50 rounded px-2 py-1">
              <span className="font-medium text-gray-600">{formatFieldName(key)}: </span>
              <span className="text-gray-700">{String(val)}</span>
            </div>
          ))}
        </div>
      )
    }

    return <span className="text-gray-700 text-sm">{String(value)}</span>
  }

  const renderRelatedSearch = (search: any, index: number): React.ReactNode => {
    const searchKey = `search_${index}`
    const query = search.query || search.title || search.text || `Related Search #${index + 1}`

    return (
      <div key={index} className="border rounded-lg overflow-hidden">
        <Collapsible
          open={openSections[searchKey]}
          onOpenChange={() => toggleSection(searchKey)}
        >
          <CollapsibleTrigger asChild>
            <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Search className={`h-4 w-4 ${openSections[searchKey] ? 'text-blue-600' : 'text-gray-600'}`} />
                  <div className="flex-1 min-w-0">
                    <h5 className="font-semibold text-gray-800">{query}</h5>
                    <div className="flex items-center flex-wrap space-x-4 text-xs text-gray-600">
                      {search.position && (
                        <span className="flex items-center">
                          <Target className="h-3 w-3 mr-1" />
                          Position {search.position}
                        </span>
                      )}
                      {search.serpapi_link && (
                        <span className="flex items-center">
                          <ArrowLeft className="h-3 w-3 mr-1 rotate-180" />
                          View Results
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {openSections[searchKey] ? (
                    <ChevronDown className="h-4 w-4 text-blue-600" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  )}
                </div>
              </div>
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <div className="p-4 bg-white space-y-4">
              {/* Search Query */}
              <div className="bg-purple-50 rounded-lg p-3">
                <h6 className="text-sm font-medium text-purple-700 mb-2">Search Query:</h6>
                <p className="text-sm text-gray-700 font-medium">
                  "{query}"
                </p>
              </div>

              {/* SerpAPI Link */}
              {search.serpapi_link && (
                <div className="flex items-center space-x-2">
                  <ArrowLeft className="h-4 w-4 text-blue-600 rotate-180 flex-shrink-0" />
                  <a
                    href={search.serpapi_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline text-sm break-all"
                  >
                    View Search Results
                  </a>
                </div>
              )}

              {/* Additional properties */}
              {Object.entries(search).filter(([key]) =>
                !['query', 'title', 'text', 'serpapi_link'].includes(key)
              ).length > 0 && (
                <div className="space-y-2">
                  <h6 className="text-sm font-medium text-gray-700">Additional Information:</h6>
                  {Object.entries(search).filter(([key]) =>
                    !['query', 'title', 'text', 'serpapi_link'].includes(key)
                  ).map(([key, value]) => (
                    <div key={key} className="flex items-start justify-between bg-gray-50 rounded p-2">
                      <span className="text-sm font-medium text-gray-600 min-w-0 flex-1">
                        {formatFieldName(key)}:
                      </span>
                      <div className="ml-4 text-right min-w-0 flex-1">
                        {renderSimpleValue(value)}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <Search className="mx-auto h-8 w-8 text-muted-foreground" />
        <p className="text-muted-foreground mt-2">No related searches available</p>
      </div>
    )
  }

  // Handle array of searches
  if (Array.isArray(data)) {
    if (data.length === 0) {
      return (
        <div className="text-center py-8">
          <Search className="mx-auto h-8 w-8 text-muted-foreground" />
          <p className="text-muted-foreground mt-2">No related searches found</p>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h4 className="font-semibold text-lg">Related Search Queries</h4>
            <p className="text-sm text-muted-foreground">Click on any search query to view details</p>
          </div>
          <Badge variant="outline">{data.length} searches</Badge>
        </div>

        <div className="space-y-3">
          {data.map((search, index) => renderRelatedSearch(search, index))}
        </div>
      </div>
    )
  }

  // Handle single object or other data structures
  const sections = Object.entries(data)

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h4 className="font-semibold text-lg">Related Searches Data</h4>
          <p className="text-sm text-muted-foreground">Detailed related search information</p>
        </div>
        <Badge variant="outline">{sections.length} sections</Badge>
      </div>

      {sections.map(([sectionKey, sectionValue]) => (
        <div key={sectionKey} className="border rounded-lg overflow-hidden">
          <Collapsible
            open={openSections[sectionKey]}
            onOpenChange={() => toggleSection(sectionKey)}
          >
            <CollapsibleTrigger asChild>
              <div className="w-full p-4 bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-100 hover:to-blue-100 cursor-pointer transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Search className={`h-4 w-4 ${openSections[sectionKey] ? 'text-blue-600' : 'text-gray-600'}`} />
                    <div>
                      <h5 className="font-semibold text-gray-800">{formatFieldName(sectionKey)}</h5>
                      <p className="text-xs text-gray-600">Related search information</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {typeof sectionValue === 'object' && sectionValue !== null && (
                      <Badge variant="secondary" className="text-xs">
                        {Array.isArray(sectionValue)
                          ? `${sectionValue.length} items`
                          : `${Object.keys(sectionValue).length} fields`
                        }
                      </Badge>
                    )}
                    {openSections[sectionKey] ? (
                      <ChevronDown className="h-4 w-4 text-blue-600" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    )}
                  </div>
                </div>
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div className="p-4 bg-white">
                {Array.isArray(sectionValue) ? (
                  <div className="space-y-3">
                    {sectionValue.map((item, index) => renderRelatedSearch(item, index))}
                  </div>
                ) : (
                  <div className="bg-purple-50 rounded-lg p-4">
                    <div className="text-lg font-medium text-gray-800">
                      {renderSimpleValue(sectionValue)}
                    </div>
                  </div>
                )}
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      ))}
    </div>
  )
}

// Export viewer components for use in the separate AnalysisDataViewer component
export {
  AnalyzedDataViewer,
  DataSummaryViewer,
  SubmittedDataViewer,
  AnalysisResultViewer,
  OrganicResultsViewer,
  ShoppingResultsViewer,
  KnowledgeGraphViewer,
  RelatedSearchesViewer,
  RelatedQuestionsViewer,
  AIOverviewViewer,
  InlineVideosViewer,
  RelatedProductsViewer,
}


// New PrototypeDialog component with tabs
interface PrototypeDialogProps {
  productId: string
  analysisId: string
  product?: any
  analysis?: any
  onProductUpdate?: (page?: number, limit?: number) => Promise<void>
}

function PrototypeDialog({ productId, analysisId, product, analysis, onProductUpdate }: PrototypeDialogProps) {
  const [open, setOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("prototype-list")
  const [prototypes, setPrototypes] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [showNewPrototype, setShowNewPrototype] = useState(false)
  const [editingPrototype, setEditingPrototype] = useState<any>(null)
  const [deletingPrototype, setDeletingPrototype] = useState<string | null>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [prototypeToDelete, setPrototypeToDelete] = useState<any>(null)
  const [expandedPrompts, setExpandedPrompts] = useState<Set<string>>(new Set())
  const { toast } = useToast()

  // Fetch prototypes when dialog opens
  const fetchPrototypes = async () => {
    if (!productId) return

    setLoading(true)
    try {
      // Pass analysisId to filter prototypes by specific analysis
      const response = await productService.getProductPrototypes(productId, 1, 5, analysisId)

      // Access prototypes from the nested data structure
      const prototypesData = response?.data?.prototypes || response?.prototypes || []

      setPrototypes(prototypesData)
    } catch (error) {
      console.error('Failed to fetch prototypes:', error)
      toast({
        title: 'Failed to load prototypes',
        description: 'Could not load prototype list',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // Handle editing a prototype
  const handleEditPrototype = (prototype: any) => {
    setEditingPrototype(prototype)
    setShowNewPrototype(true)
  }

  // Handle going back to list (reset editing state)
  const handleBackToList = () => {
    setShowNewPrototype(false)
    setEditingPrototype(null)
  }

  // Handle delete prototype confirmation
  const handleDeletePrototype = (prototype: any) => {
    setPrototypeToDelete(prototype)
    setShowDeleteConfirm(true)
  }

  // Handle sync prototype status
  const handleSyncPrototype = async (prototype: any) => {
    if (!prototype.external_job_id) {
      toast({
        title: 'Error',
        description: 'No job ID found for this prototype',
        variant: 'destructive'
      })
      return
    }

    try {
      const response = await productService.syncPrototypeStatus(prototype.external_job_id)
      if (response.success) {
        toast({
          title: 'Success',
          description: 'Prototype status synced successfully',
        })
        // Refresh prototypes list
        fetchPrototypes()
      } else {
        throw new Error(response.message || 'Failed to sync prototype status')
      }
    } catch (error: any) {
      console.error('Error syncing prototype:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to sync prototype status',
        variant: 'destructive'
      })
    }
  }

  // Handle mark as production
  const handleMarkAsProduction = async (prototype: any) => {
    try {
      const result = await productService.markPrototypeAsProduction(prototype.id)

      // Update the prototype in local state
      setPrototypes(prev => prev.map(p =>
        p.id === prototype.id
          ? {
              ...p,
              metadata: {
                ...p.metadata,
                marked_as_production: true,
                production_media_id: result.data?.media?.id,
                marked_production_at: new Date().toISOString()
              }
            }
          : p
      ))

      // Refresh the products list to update the Media Files section in real-time
      if (onProductUpdate) {
        await onProductUpdate()
      }

      toast({
        title: 'Success',
        description: result.message || 'Prototype has been marked as production successfully.',
      })
    } catch (error: any) {
      console.error('Error marking prototype as production:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to mark prototype as production',
        variant: 'destructive',
      })
    }
  }

  // Confirm delete prototype
  const confirmDeletePrototype = async () => {
    if (!prototypeToDelete) return

    setDeletingPrototype(prototypeToDelete.id)
    try {
      await productService.deletePrototype(prototypeToDelete.id)

      // Remove from local state
      setPrototypes(prev => prev.filter(p => p.id !== prototypeToDelete.id))

      toast({
        title: 'Prototype Deleted',
        description: 'The prototype has been deleted successfully.',
      })
    } catch (error: any) {
      console.error('Failed to delete prototype:', error)
      toast({
        title: 'Failed to delete prototype',
        description: error?.message || 'Could not delete the prototype',
        variant: 'destructive'
      })
    } finally {
      setDeletingPrototype(null)
      setShowDeleteConfirm(false)
      setPrototypeToDelete(null)
    }
  }

  // Cancel delete
  const cancelDelete = () => {
    setShowDeleteConfirm(false)
    setPrototypeToDelete(null)
  }

  // Toggle prompt expansion
  const togglePromptExpansion = (prototypeId: string) => {
    setExpandedPrompts(prev => {
      const newSet = new Set(prev)
      if (newSet.has(prototypeId)) {
        newSet.delete(prototypeId)
      } else {
        newSet.add(prototypeId)
      }
      return newSet
    })
  }

  // Check if prompt should be truncated
  const shouldTruncatePrompt = (prompt: string) => {
    if (!prompt) return false
    const lines = prompt.split('\n')
    return lines.length > 2 || prompt.length > 150
  }

  // Fetch prototypes when dialog opens
  React.useEffect(() => {
    if (open && activeTab === 'prototype-list') {
      fetchPrototypes()
    }
  }, [open, activeTab, productId, analysisId])

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="text-xs sm:text-sm w-full sm:w-auto" size="sm" variant="outline">
          <Package className="h-3 w-3 mr-1" />
          Make a Prototype Content 
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-6xl w-[1200px] max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Prototype Management </DialogTitle>
          <DialogDescription>
            Manage prototypes, prompts, and media for your content creation.
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="h-[72vh] pr-4">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="prototype-list" className="flex items-center gap-2">
                <Database className="h-4 w-4" />
                Prototype List
              </TabsTrigger>
              <TabsTrigger value="media" className="flex items-center gap-2">
                <ImageIcon className="h-4 w-4" />
                Output Media
              </TabsTrigger>
            </TabsList>

            <TabsContent value="prototype-list" className="mt-6">
              {showNewPrototype ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">
                      {editingPrototype ? 'Edit Prototype' : 'Create New Prototype'}
                    </h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleBackToList}
                    >
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Back to List
                    </Button>
                  </div>
                  <PrototypeBuilder
                    productId={productId}
                    analysesId={analysisId}
                    editingPrototype={editingPrototype}
                    productData={product}
                    analysisData={analysis}
                    onSuccess={() => {
                      handleBackToList()
                      fetchPrototypes() // Refresh the list
                    }}
                    inDialog={true}
                  />
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Prototype List</h3>
                    {!prototypes.some(p => p.status === 'draft') ? (
                      <Button
                        onClick={() => setShowNewPrototype(true)}
                        className="flex items-center gap-2"
                      >
                        <Plus className="h-4 w-4" />
                        New Prototype
                      </Button>
                    ) : (
                      <div className="text-sm text-amber-600 bg-amber-50 px-3 py-2 rounded-lg border border-amber-200">
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4" />
                          <span>Complete or delete draft prototypes to create new ones</span>
                        </div>
                      </div>
                    )}
                  </div>

                  {loading ? (
                    <div className="flex items-center justify-center py-8">
                      <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                      Loading prototypes...
                    </div>
                  ) : prototypes.length > 0 ? (
                    <div className="space-y-4">
                      {prototypes.map((prototype) => (
                        <Card key={prototype.id} className="overflow-hidden border-l-4 border-l-blue-500 hover:shadow-md transition-shadow">
                          <div className="p-6">
                            {/* Header Section */}
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center justify-between mb-2">
                                  <div className="flex items-center gap-3">
                                    <h4 className="text-lg font-semibold text-gray-900">
                                      {prototype.content_type?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                    </h4>
                                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                                      prototype.status === 'completed' ? 'bg-green-100 text-green-800 border border-green-200' :
                                      prototype.status === 'processing' ? 'bg-blue-100 text-blue-800 border border-blue-200' :
                                      prototype.status === 'failed' ? 'bg-red-100 text-red-800 border border-red-200' :
                                      'bg-yellow-100 text-yellow-800 border border-yellow-200'
                                    }`}>
                                      {prototype.status?.toUpperCase()}
                                    </span>
                                    {prototype.file_url && (
                                      <span className="px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800 border border-purple-200">
                                        📁 File Available
                                      </span>
                                    )}
                                  </div>

                                  {/* Action Buttons */}
                                  <div className="flex items-center gap-2">
                                    {/* Sync button - only show for pending status */}
                                    {prototype.status === 'pending' && (
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleSyncPrototype(prototype)}
                                        title="Sync Status"
                                        className="h-8 w-8 p-0"
                                      >
                                        <RotateCcw className="h-4 w-4" />
                                      </Button>
                                    )}

                                    {/* Edit button - only show for draft status */}
                                    {prototype.status === 'draft' && (
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleEditPrototype(prototype)}
                                        title="Edit Prototype"
                                        className="h-8 w-8 p-0"
                                      >
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                    )}

                                    {/* Delete button */}
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleDeletePrototype(prototype)}
                                      disabled={deletingPrototype === prototype.id}
                                      title="Delete Prototype"
                                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                                    >
                                      {deletingPrototype === prototype.id ? (
                                        <RefreshCw className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <Trash2 className="h-4 w-4" />
                                      )}
                                    </Button>
                                  </div>
                                </div>

                                {/* Prompt Section */}
                                <div className="bg-gray-50 rounded-lg p-3">
                                  <div className="flex items-center gap-2 mb-1">
                                    <FileText className="h-4 w-4 text-gray-500" />
                                    <span className="text-sm font-medium text-gray-700">Prompt</span>
                                  </div>
                                  <div className="flex items-end justify-between">
                                    <p
                                      className="text-sm text-gray-600 leading-relaxed flex-1"
                                      style={{
                                        display: !expandedPrompts.has(prototype.id) && shouldTruncatePrompt(prototype.custom_prompt)
                                          ? '-webkit-box'
                                          : 'block',
                                        WebkitLineClamp: !expandedPrompts.has(prototype.id) && shouldTruncatePrompt(prototype.custom_prompt)
                                          ? 2
                                          : 'unset',
                                        WebkitBoxOrient: !expandedPrompts.has(prototype.id) && shouldTruncatePrompt(prototype.custom_prompt)
                                          ? 'vertical' as const
                                          : 'unset',
                                        overflow: !expandedPrompts.has(prototype.id) && shouldTruncatePrompt(prototype.custom_prompt)
                                          ? 'hidden'
                                          : 'visible'
                                      }}
                                    >
                                      {prototype.custom_prompt || 'No prompt provided'}
                                    </p>
                                    {shouldTruncatePrompt(prototype.custom_prompt) && (
                                      <button
                                        onClick={() => togglePromptExpansion(prototype.id)}
                                        className="text-xs text-blue-600 hover:text-blue-800 font-medium ml-2 flex-shrink-0"
                                      >
                                        {expandedPrompts.has(prototype.id) ? 'See less' : 'See more'}
                                      </button>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>

                            {prototype.error_message && (
                              <div className="text-sm text-amber-600 mb-4">
                                <div className="flex items-center gap-2">
                                  <AlertCircle className="h-4 w-4" />
                                  <span>{prototype.error_message}</span>
                                </div>
                              </div>
                            )}

                            {/* Details Grid */}
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 pt-4 border-t border-gray-100">
                              <div className="flex items-center gap-2">
                                <Video className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                <div className="min-w-0">
                                  <p className="text-xs text-gray-500">Media Type</p>
                                  <p className="text-sm font-medium text-gray-900 truncate">
                                    {prototype.media_type?.toUpperCase() || 'N/A'}
                                  </p>
                                </div>
                              </div>

                              <div className="flex items-center gap-2">
                                <Wand2 className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                <div className="min-w-0">
                                  <p className="text-xs text-gray-500">Style</p>
                                  <p className="text-sm font-medium text-gray-900 truncate">
                                    {prototype.style?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'N/A'}
                                  </p>
                                </div>
                              </div>

                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                <div className="min-w-0">
                                  <p className="text-xs text-gray-500">Created</p>
                                  <p className="text-sm font-medium text-gray-900 truncate">
                                    {new Date(prototype.created_at).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>

                              <div className="flex items-center gap-2">
                                <Target className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                <div className="min-w-0">
                                  <p className="text-xs text-gray-500">Audience</p>
                                  <p className="text-sm font-medium text-gray-900 truncate">
                                    {prototype.target_audience?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'N/A'}
                                  </p>
                                </div>
                              </div>
                            </div>

                            {/* Additional Details */}
                            {(prototype.duration || prototype.tone || prototype.camera_angle || prototype.lighting_style) && (
                              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 pt-4 mt-4 border-t border-gray-100">
                                {prototype.duration && (
                                  <div className="flex items-center gap-2">
                                    <Clock className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                    <div className="min-w-0">
                                      <p className="text-xs text-gray-500">Duration</p>
                                      <p className="text-sm font-medium text-gray-900">{prototype.duration}s</p>
                                    </div>
                                  </div>
                                )}

                                {prototype.tone && (
                                  <div className="flex items-center gap-2">
                                    <Activity className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                    <div className="min-w-0">
                                      <p className="text-xs text-gray-500">Tone</p>
                                      <p className="text-sm font-medium text-gray-900 truncate">
                                        {prototype.tone.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                      </p>
                                    </div>
                                  </div>
                                )}

                                {prototype.camera_angle && (
                                  <div className="flex items-center gap-2">
                                    <Video className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                    <div className="min-w-0">
                                      <p className="text-xs text-gray-500">Camera</p>
                                      <p className="text-sm font-medium text-gray-900 truncate">
                                        {prototype.camera_angle.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                      </p>
                                    </div>
                                  </div>
                                )}

                                {prototype.lighting_style && (
                                  <div className="flex items-center gap-2">
                                    <Eye className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                    <div className="min-w-0">
                                      <p className="text-xs text-gray-500">Lighting</p>
                                      <p className="text-sm font-medium text-gray-900 truncate">
                                        {prototype.lighting_style.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                      </p>
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Database className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No Prototypes Found</h3>
                      <p className="text-muted-foreground mb-4">
                        You haven't created any prototypes for this product yet.
                      </p>
                      {!prototypes.some(p => p.status === 'draft') && (
                        <Button onClick={() => setShowNewPrototype(true)}>
                          <Plus className="h-4 w-4 mr-2" />
                          Create Your First Prototype
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="media" className="mt-6">
              <div className="space-y-6">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                    Loading media files...
                  </div>
                ) : (
                  <>
                    {/* Statistics Cards */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                      <Card className="p-3 sm:p-4">
                        <div className="flex items-center gap-2 sm:gap-3">
                          <div className="p-1.5 sm:p-2 bg-blue-100 rounded-lg flex-shrink-0">
                            <Database className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                          </div>
                          <div className="min-w-0">
                            <p className="text-xs sm:text-sm text-muted-foreground">Total Files</p>
                            <p className="text-lg sm:text-2xl font-bold">{prototypes.filter(p => p.file_url).length}</p>
                          </div>
                        </div>
                      </Card>

                      <Card className="p-3 sm:p-4">
                        <div className="flex items-center gap-2 sm:gap-3">
                          <div className="p-1.5 sm:p-2 bg-green-100 rounded-lg flex-shrink-0">
                            <FileText className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                          </div>
                          <div className="min-w-0">
                            <p className="text-xs sm:text-sm text-muted-foreground">Total Size</p>
                            <p className="text-lg sm:text-2xl font-bold truncate">
                              {prototypes.filter(p => p.file_url && p.file_size).reduce((total, p) => total + (p.file_size || 0), 0) > 0
                                ? `${(prototypes.filter(p => p.file_url && p.file_size).reduce((total, p) => total + (p.file_size || 0), 0) / 1024 / 1024).toFixed(1)} MB`
                                : '0 Bytes'
                              }
                            </p>
                          </div>
                        </div>
                      </Card>

                      <Card className="p-3 sm:p-4">
                        <div className="flex items-center gap-2 sm:gap-3">
                          <div className="p-1.5 sm:p-2 bg-yellow-100 rounded-lg flex-shrink-0">
                            <Activity className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-600" />
                          </div>
                          <div className="min-w-0">
                            <p className="text-xs sm:text-sm text-muted-foreground">Avg Quality</p>
                            <p className="text-lg sm:text-2xl font-bold">N/A</p>
                          </div>
                        </div>
                      </Card>

                      <Card className="p-3 sm:p-4">
                        <div className="flex items-center gap-2 sm:gap-3">
                          <div className="p-1.5 sm:p-2 bg-purple-100 rounded-lg flex-shrink-0">
                            <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600" />
                          </div>
                          <div className="min-w-0">
                            <p className="text-xs sm:text-sm text-muted-foreground">Last Upload</p>
                            <p className="text-lg sm:text-2xl font-bold truncate">
                              {prototypes.filter(p => p.file_url).length > 0
                                ? new Date(Math.max(...prototypes.filter(p => p.file_url).map(p => new Date(p.created_at).getTime()))).toLocaleDateString()
                                : 'Never'
                              }
                            </p>
                          </div>
                        </div>
                      </Card>
                    </div>

                    {/* Media Files Grid */}
                    {prototypes.filter(p => p.file_url).length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4">
                        {prototypes
                          .filter(prototype => prototype.file_url)
                          .map((prototype) => {
                            const isVideo = prototype.media_type?.toLowerCase().includes('video') ||
                                          prototype.file_url?.toLowerCase().includes('.mp4') ||
                                          prototype.file_url?.toLowerCase().includes('.mov') ||
                                          prototype.file_url?.toLowerCase().includes('.avi')

                            const isImage = prototype.media_type?.toLowerCase().includes('image') ||
                                          prototype.file_url?.toLowerCase().includes('.jpg') ||
                                          prototype.file_url?.toLowerCase().includes('.jpeg') ||
                                          prototype.file_url?.toLowerCase().includes('.png') ||
                                          prototype.file_url?.toLowerCase().includes('.gif')

                            return (
                              <Card key={prototype.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                                {/* Media Preview */}
                                <div className="aspect-video bg-gray-100 relative group">
                                  {isVideo ? (
                                    <div className="w-full h-full bg-black flex items-center justify-center">
                                      <video
                                        className="w-full h-full object-cover"
                                        controls
                                        preload="metadata"
                                      >
                                        <source src={prototype.file_url} type="video/mp4" />
                                        Your browser does not support the video tag.
                                      </video>
                                    </div>
                                  ) : isImage ? (
                                    <img
                                      src={prototype.file_url}
                                      alt={prototype.content_type || 'Media file'}
                                      className="w-full h-full object-cover"
                                      onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.style.display = 'none';
                                        const parent = target.parentElement;
                                        if (parent) {
                                          parent.innerHTML = `
                                            <div class="w-full h-full flex items-center justify-center bg-gray-200">
                                              <div class="text-center">
                                                <svg class="mx-auto h-12 w-12 text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                                <p class="text-sm text-gray-500">Preview not available</p>
                                              </div>
                                            </div>
                                          `;
                                        }
                                      }}
                                    />
                                  ) : (
                                    <div className="w-full h-full flex items-center justify-center bg-gray-200">
                                      <div className="text-center">
                                        <FileText className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                                        <p className="text-sm text-gray-500">File Preview</p>
                                      </div>
                                    </div>
                                  )}

                                  {/* Status Badge */}
                                  <div className="absolute top-2 right-2">
                                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                                      prototype.status === 'completed' ? 'bg-green-100 text-green-800' :
                                      prototype.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                                      prototype.status === 'failed' ? 'bg-red-100 text-red-800' :
                                      'bg-yellow-100 text-yellow-800'
                                    }`}>
                                      {prototype.status}
                                    </span>
                                  </div>
                                </div>

                                {/* File Info */}
                                <div className="p-3 sm:p-4">
                                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-2">
                                    <div className="flex items-center gap-2 min-w-0">
                                      {isVideo ? (
                                        <Video className="h-4 w-4 text-gray-500 flex-shrink-0" />
                                      ) : isImage ? (
                                        <ImageIcon className="h-4 w-4 text-gray-500 flex-shrink-0" />
                                      ) : (
                                        <FileText className="h-4 w-4 text-gray-500 flex-shrink-0" />
                                      )}
                                      <span className="font-medium text-xs sm:text-sm truncate">
                                        {isVideo ? 'MP4 video' : isImage ? 'JPG image' : prototype.media_type || 'File'}
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => window.open(prototype.file_url, '_blank')}
                                        className="p-1.5 sm:p-2 h-7 w-7 sm:h-8 sm:w-8"
                                        title="View"
                                      >
                                        <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                          navigator.clipboard.writeText(prototype.file_url)
                                          toast({
                                            title: 'URL Copied',
                                            description: 'File URL copied to clipboard',
                                          })
                                        }}
                                        className="p-1.5 sm:p-2 h-7 w-7 sm:h-8 sm:w-8"
                                        title="Copy URL"
                                      >
                                        <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4" />
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                          const link = document.createElement('a')
                                          link.href = prototype.file_url
                                          link.download = `${prototype.content_type || 'file'}.${isVideo ? 'mp4' : isImage ? 'jpg' : 'file'}`
                                          document.body.appendChild(link)
                                          link.click()
                                          document.body.removeChild(link)
                                        }}
                                        className="p-1.5 sm:p-2 h-7 w-7 sm:h-8 sm:w-8"
                                        title="Download"
                                      >
                                        <Upload className="h-3 w-3 sm:h-4 sm:w-4 rotate-180" />
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleDeletePrototype(prototype)}
                                        className="p-1.5 sm:p-2 h-7 w-7 sm:h-8 sm:w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                                        title="Delete"
                                      >
                                        <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
                                      </Button>
                                    </div>
                                  </div>

                                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
                                      {prototype.status === 'completed' && !prototype.metadata?.marked_as_production && (
                                        <Button
                                          variant="default"
                                          size="sm"
                                          onClick={() => handleMarkAsProduction(prototype)}
                                          className="px-2 sm:px-3 py-1 h-7 sm:h-8 bg-green-600 hover:bg-green-700 text-white text-xs sm:text-sm w-full sm:w-auto"
                                        >
                                          <ShoppingCart className="h-3 w-3 mr-1" />
                                          <span className="hidden sm:inline">Make as Production</span>
                                          <span className="sm:hidden">Production</span>
                                        </Button>
                                      )}
                                      {prototype.metadata?.marked_as_production && (
                                        <div className="flex items-center gap-2 text-green-700">
                                          <ShoppingCart className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                          <span className="text-xs sm:text-sm font-medium">
                                            <span className="hidden sm:inline">This file is already in production</span>
                                            <span className="sm:hidden">In production</span>
                                          </span>
                                        </div>
                                      )}
                                    </div>
                                </div>
                              </Card>
                            )
                          })}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <ImageIcon className="mx-auto h-16 w-16 text-muted-foreground mb-4" />
                        <h3 className="text-xl font-semibold mb-2">No Media Files</h3>
                        <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                          No prototypes with generated files found. Create and generate prototypes to see media files here.
                        </p>
                        <Button onClick={() => setActiveTab('prototype-list')} variant="outline">
                          <Plus className="h-4 w-4 mr-2" />
                          Create Prototype
                        </Button>
                      </div>
                    )}
                  </>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </ScrollArea>
      </DialogContent>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <Trash2 className="h-5 w-5" />
              Delete Prototype
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this prototype? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          {prototypeToDelete && (
            <div className="bg-gray-50 rounded-lg p-3 my-4">
              <div className="flex items-center gap-2 mb-1">
                {prototypeToDelete.content_type && <span className="font-medium">{prototypeToDelete.content_type}</span>}
                <span className={`px-2 py-1 rounded text-xs ${
                  prototypeToDelete.status === 'completed' ? 'bg-green-100 text-green-800' :
                  prototypeToDelete.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                  prototypeToDelete.status === 'failed' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {prototypeToDelete.status}
                </span>
              </div>
              <p className="text-sm text-gray-600">
                {prototypeToDelete.custom_prompt || 'No prompt provided'}
              </p>
            </div>
          )}

          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={cancelDelete}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeletePrototype}
              disabled={deletingPrototype !== null}
            >
              {deletingPrototype ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Prototype
                </>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </Dialog>
  )
}

interface ProductAnalysisDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  product: Product | null
  onProductUpdate?: (page?: number, limit?: number) => Promise<void>
}

export function ProductAnalysisDialog({ open, onOpenChange, product, onProductUpdate }: ProductAnalysisDialogProps) {
  const [requestingAnalysis, setRequestingAnalysis] = useState<string | null>(null)
  const [checkingStatus, setCheckingStatus] = useState<string | null>(null)
  const [analyses, setAnalyses] = useState<ProductAnalysis[]>([])
  const [selectedAnalysis, setSelectedAnalysis] = useState<ProductAnalysis | null>(null)
  const [analysisResult, setAnalysisResult] = useState<any>(null)
  const [analyzedData, setAnalyzedData] = useState<any>(null)
  const [loadingResult, setLoadingResult] = useState(false)
  const [loadingData, setLoadingData] = useState(false)
  const [activeTab, setActiveTab] = useState("analyzed_data")
  const [requestingProductAnalysis, setRequestingProductAnalysis] = useState(false)

  const [showDataViewer, setShowDataViewer] = useState(false)
  
  const { toast } = useToast()

  useEffect(() => {
    if (product && open) {
      setAnalyses(product.analyses || [])
      // Log if price analysis data is available
      if (product.analyses && product.analyses.length > 0 && product.analyses[0].price_analysis) {
        console.log('Price analysis data available for', product.analyses.length, 'analyses')
      }
    }
  }, [product, open])

  const requestAnalysis = async (analysisType: string) => {
    if (!product) return

    setRequestingAnalysis(analysisType)

    try {
      // Prepare analysis data with all required fields according to the backend API
      const analysisData = {
        product_name: product.name,
        sku: product.slug || `SKU-${product.id.slice(0, 8)}`, // Use slug as SKU or generate one
        asin: `ASIN-${product.id.slice(0, 10)}`, // Generate ASIN from product ID
        timeframe: "30", // Default timeframe in days
        geo: product.country || "US",
        additional_keywords: product.category ? [product.category.name] : []
      }

      const response = await productService.analyzeProduct(product.id, analysisData)

      toast({
        title: "Analysis Requested",
        description: `${analysisType} analysis has been initiated successfully.`,
      })

      // Refresh the analyses list by fetching the updated product
      // Since the backend will add the new analysis to the product
      try {
        const updatedProduct = await productService.getProductById(product.id)
        setAnalyses(updatedProduct.analyses || [])
      } catch (fetchError) {
        console.log('Could not fetch updated product, using response data')
        // If we can't fetch the updated product, try to use response data
        if (response && response.data) {
          setAnalyses(prev => [...prev, response.data])
        }
      }

      // Refresh the products list to update the analysis data in real-time
      if (onProductUpdate) {
        await onProductUpdate()
      }

    } catch (error) {
      console.error('Failed to request analysis:', error)
      toast({
        title: "Error",
        description: `Failed to request ${analysisType} analysis. ${error instanceof Error ? error.message : 'Please try again.'}`,
        variant: "destructive",
      })
    } finally {
      setRequestingAnalysis(null)
    }
  }

  const checkAnalysisStatus = async (analysis: ProductAnalysis) => {
    if (!analysis.external_job_id || !product) return

    setCheckingStatus(analysis.id)

    try {
      // First check the external job status
      const statusResponse = await productService.checkAnalysisStatus(analysis.external_job_id)

      // Then fetch the latest product data from database to sync any updates
      const latestProduct = await productService.getProductById(product.id)

      // Update local analyses state with the latest data from database
      if (latestProduct && latestProduct.analyses) {
        setAnalyses(latestProduct.analyses)
      }

      // Handle different response formats for status
      let newStatus: 'pending' | 'processing' | 'completed' | 'failed' = analysis.status // Default to current status

      if (statusResponse) {
        // Check if response has status directly
        if (statusResponse.status && ['pending', 'processing', 'completed', 'failed'].includes(statusResponse.status)) {
          newStatus = statusResponse.status as 'pending' | 'processing' | 'completed' | 'failed'
        }
        // Check if response has data.status
        else if (statusResponse.data && statusResponse.data.status && ['pending', 'processing', 'completed', 'failed'].includes(statusResponse.data.status)) {
          newStatus = statusResponse.data.status as 'pending' | 'processing' | 'completed' | 'failed'
        }
        // Check if response itself is the status string
        else if (typeof statusResponse === 'string' && ['pending', 'processing', 'completed', 'failed'].includes(statusResponse)) {
          newStatus = statusResponse as 'pending' | 'processing' | 'completed' | 'failed'
        }
      }

      // Refresh the products list to update the analysis data in real-time
      if (onProductUpdate) {
        await onProductUpdate()
      }

      // Show appropriate toast message based on status
      const statusMessages = {
        'pending': 'Analysis is queued for processing',
        'processing': 'Analysis is currently being processed',
        'completed': 'Analysis has been completed successfully',
        'failed': 'Analysis processing has failed',
        'cancelled': 'Analysis has been cancelled'
      }

      const statusMessage = statusMessages[newStatus.toLowerCase() as keyof typeof statusMessages] || `Status updated to: ${newStatus}`

      toast({
        title: "Status Updated & Data Synced",
        description: `${statusMessage}. Latest data synced from database.`,
        variant: newStatus.toLowerCase() === 'failed' ? 'destructive' : 'default'
      })

    } catch (error) {
      console.error('Failed to check analysis status:', error)

      // More detailed error handling
      let errorMessage = "Failed to check analysis status."
      if (error instanceof Error) {
        errorMessage = error.message
      } else if (typeof error === 'object' && error !== null && 'message' in error) {
        errorMessage = (error as any).message
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setCheckingStatus(null)
    }
  }

  const loadAnalysisResult = async (analysis: ProductAnalysis) => {
    if (!analysis.external_job_id) return

    setLoadingResult(true)
    try {
      const result = await productService.syncResultData(analysis.external_job_id)
      setAnalysisResult(result)
      setSelectedAnalysis(analysis)
      setActiveTab("result")

      // Refresh the products list to update the analysis data in real-time
      if (onProductUpdate) {
        await onProductUpdate()
      }

      toast({
        title: "Result Loaded",
        description: "Analysis result loaded successfully.",
      })
    } catch (error) {
      console.error('Failed to load analysis result:', error)
      toast({
        title: "Error",
        description: "Failed to load analysis result.",
        variant: "destructive",
      })
    } finally {
      setLoadingResult(false)
    }
  }

  const loadAnalyzedData = async (analysis: ProductAnalysis) => {
    if (!analysis.external_job_id) return

    setLoadingData(true)
    try {
      const data = await productService.syncAnalyzedData(analysis.external_job_id)

      // Update the analyses array with the new data
      setAnalyses(prevAnalyses =>
        prevAnalyses.map(a =>
          a.id === analysis.id
            ? {
                ...a,
                analyzed_data: data.analyzed_data || a.analyzed_data,
                price_analysis: data.price_analysis || a.price_analysis,
                data_summary: data.data_summary || a.data_summary,
                // Add all the new data fields
                organic_results: data.organic_results || (a as any).organic_results,
                shopping_results: data.shopping_results || (a as any).shopping_results,
                knowledge_graph: data.knowledge_graph || (a as any).knowledge_graph,
                related_searches: data.related_searches || (a as any).related_searches,
                related_questions: data.related_questions || (a as any).related_questions,
                ai_overview: data.ai_overview || (a as any).ai_overview,
                inline_videos: data.inline_videos || (a as any).inline_videos,
                related_products: data.related_products || (a as any).related_products
              }
            : a
        )
      )

      setAnalyzedData(data)
      setSelectedAnalysis(analysis)

      // Refresh the products list to update the analysis data in real-time
      if (onProductUpdate) {
        await onProductUpdate()
      }

      toast({
        title: "Data Loaded",
        description: "Analyzed data loaded successfully.",
      })
    } catch (error) {
      console.error('Failed to load analyzed data:', error)
      toast({
        title: "Error",
        description: "Failed to load analyzed data.",
        variant: "destructive",
      })
    } finally {
      setLoadingData(false)
    }
  }

  const requestProductAnalysis = async () => {
    if (!product) return

    setRequestingProductAnalysis(true)

    try {
      // Prepare analysis data with all required fields according to the backend API
      const analysisData = {
        product_name: product.name,
        sku: product.slug || `SKU-${product.id.slice(0, 8)}`, // Use slug as SKU or generate one
        asin: `ASIN-${product.id.slice(0, 10)}`, // Generate ASIN from product ID
        timeframe: "30", // Default timeframe in days
        geo: product.country || "US",
        additional_keywords: product.category ? [product.category.name] : []
      }

      const response = await productService.analyzeProduct(product.id, analysisData)

      toast({
        title: "Analysis Requested",
        description: "Product analysis has been initiated successfully.",
      })

      // Refresh the analyses list by fetching the updated product
      try {
        const updatedProduct = await productService.getProductById(product.id)
        setAnalyses(updatedProduct.analyses || [])
      } catch (fetchError) {
        console.log('Could not fetch updated product, using response data')
        // If we can't fetch the updated product, try to use response data
        if (response && response.data) {
          setAnalyses(prev => [...prev, response.data])
        }
      }

      // Refresh the products list to update the analysis data in real-time
      if (onProductUpdate) {
        await onProductUpdate()
      }

    } catch (error) {
      console.error('Failed to request product analysis:', error)
      toast({
        title: "Error",
        description: `Failed to request product analysis. ${error instanceof Error ? error.message : 'Please try again.'}`,
        variant: "destructive",
      })
    } finally {
      setRequestingProductAnalysis(false)
    }
  }

  const viewAnalysisResults = (analysis: ProductAnalysis) => {
    setSelectedAnalysis(analysis)
    setActiveTab("analysis_result")
    setShowDataViewer(true)
  }

  const viewAnalyzedData = (analysis: ProductAnalysis) => {
    setSelectedAnalysis(analysis)
    setActiveTab("analyzed_data")
    setShowDataViewer(true)
  }

  const handleBackToOverview = () => {
    setShowDataViewer(false)
  }

  const handleNavigateToTab = (tab: string) => {
    setActiveTab(tab)
  }

  // Sync functions for footer buttons
  const syncResultData = async (analysis: ProductAnalysis) => {
    if (!analysis.external_job_id) return

    setLoadingResult(true)
    try {
      await productService.syncResultData(analysis.external_job_id)
      toast({
        title: "Success",
        description: "Sync data successfully",
      })
    } catch (error) {
      console.error('Failed to sync result data:', error)
      toast({
        title: "Error",
        description: "Failed to sync result data.",
        variant: "destructive",
      })
    } finally {
      setLoadingResult(false)
    }
  }

  const syncAnalyzedData = async (analysis: ProductAnalysis) => {
    if (!analysis.external_job_id) return

    setLoadingData(true)
    try {
      await productService.syncAnalyzedData(analysis.external_job_id)
      toast({
        title: "Success",
        description: "Sync data successfully",
      })
    } catch (error) {
      console.error('Failed to sync analyzed data:', error)
      toast({
        title: "Error",
        description: "Failed to sync analyzed data.",
        variant: "destructive",
      })
    } finally {
      setLoadingData(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'processing':
        return <Clock className="h-4 w-4 text-blue-500" />
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'processing':
        return 'bg-blue-100 text-blue-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (!product) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[95vw] sm:max-w-4xl md:max-w-5xl lg:max-w-6xl xl:max-w-7xl h-[95vh] max-h-[95vh] flex flex-col rounded-lg overflow-y-auto  sm:mx-auto">
        <DialogHeader className="flex-shrink-0 px-2 sm:px-4">
          <DialogTitle className="flex items-center space-x-2 text-base sm:text-lg">
            <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5" />
            <span className="truncate">Product Analysis Dashboard</span>
          </DialogTitle>
          <DialogDescription className="text-sm truncate">
            Manage and view analysis for {product.name}
          </DialogDescription>
        </DialogHeader>

        {showDataViewer ? (
          <AnalysisDataViewer
            analyses={analyses}
            selectedAnalysis={selectedAnalysis}
            loadingResult={loadingResult}
            loadingData={loadingData}
            activeTab={activeTab}
            onTabChange={setActiveTab}
            onBack={handleBackToOverview}
            onLoadAnalysisResult={loadAnalysisResult}
            onLoadAnalyzedData={loadAnalyzedData}
            getStatusColor={getStatusColor}
          />
        ) : (
          <div className="mt-4 flex-1 flex flex-col min-h-0 px-2 sm:px-4">
            <div className="space-y-4 flex-1 flex flex-col min-h-0">
              <ScrollArea className="flex-1 min-h-0">
                <div className="pr-2 sm:pr-4">
                  {analyses.length > 0 ? (
                    <div className="space-y-4">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                      <h3 className="text-base sm:text-lg font-semibold">Current Analysis</h3>
                      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
                        <Badge variant="outline" className="text-xs sm:text-sm">{analyses.length} Analys{analyses.length !== 1 ? 'es' : 'is'}</Badge>
                        <Button
                          size="sm"
                          onClick={requestProductAnalysis}
                          disabled={requestingProductAnalysis}
                          className="flex items-center space-x-2 w-full sm:w-auto text-xs sm:text-sm"
                        >
                          {requestingProductAnalysis ? (
                            <>
                              <div className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                              <span className="hidden sm:inline">Requesting...</span>
                              <span className="sm:hidden">Loading...</span>
                            </>
                          ) : (
                            <>
                              <Plus className="w-3 h-3 sm:w-4 sm:h-4" />
                              <span className="hidden sm:inline">Request Analysis</span>
                              <span className="sm:hidden">Request</span>
                            </>
                          )}
                        </Button>
                      </div>
                    </div>

                    <div className="grid gap-3 sm:gap-4">
                      {analyses.map((analysis) => (
                        <Card key={analysis.id} className="border-l-4 border-l-blue-500">
                          <CardHeader className="pb-3 px-3 sm:px-6 pt-3 sm:pt-6">
                            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                              <div className="flex items-center space-x-2 min-w-0 flex-1">
                                <Activity className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                                <div className="min-w-0 flex-1">
                                  <CardTitle className="text-sm sm:text-base">Analysis #{analysis.external_job_id}</CardTitle>
                                  <p className="text-xs sm:text-sm text-muted-foreground">
                                    Created: {new Date(analysis.created_at).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2 flex-shrink-0">
                                {getStatusIcon(analysis.status)}
                                <Badge className={`${getStatusColor(analysis.status)} text-xs`}>
                                  {analysis.status}
                                </Badge>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
                            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-4">
                              <div className="text-xs sm:text-sm text-muted-foreground ">
                                External Job ID: {analysis.external_job_id}
                              </div>
                              <div className="flex flex-col sm:flex-row gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => checkAnalysisStatus(analysis)}
                                  disabled={checkingStatus === analysis.id}
                                  className="text-xs sm:text-sm w-full sm:w-auto"
                                >
                                  {checkingStatus === analysis.id ? (
                                    <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                                  ) : (
                                    <RefreshCw className="h-3 w-3 mr-1" />
                                  )}
                                  <span className="hidden sm:inline">Check Status</span>
                                  <span className="sm:hidden">Status</span>
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => viewAnalyzedData(analysis)}
                                  className="text-xs sm:text-sm w-full sm:w-auto"
                                >
                                  <Database className="h-3 w-3 mr-1" />
                                  <span className="hidden sm:inline">View Analysis Data</span>
                                  <span className="sm:hidden">View Data</span>
                                </Button>
                                <div className="w-full sm:w-auto">
                                  <PrototypeDialog
                                    productId={product.id}
                                    analysisId={analysis.id}
                                    product={product}
                                    analysis={analysis}
                                    onProductUpdate={onProductUpdate}
                                  />
                                </div>
                              </div>
                            </div>

                            {analysis.status === 'processing' && (
                              <div className="text-center py-4">
                                <RefreshCw className="h-6 w-6 animate-spin mx-auto text-blue-500" />
                                <p className="text-sm text-muted-foreground mt-2">Analysis in progress...</p>
                              </div>
                            )}

                            {analysis.status === 'failed' && (
                              <div className="text-center py-4">
                                <AlertCircle className="h-6 w-6 mx-auto text-red-500" />
                                <p className="text-sm text-muted-foreground mt-2">
                                  Analysis failed. {analysis.error_message || 'Please try again.'}
                                </p>
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 sm:py-12 px-4">
                    <BarChart3 className="mx-auto h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground" />
                    <h3 className="mt-4 text-base sm:text-lg font-semibold">No analysis found</h3>
                    <p className="text-muted-foreground mb-4 text-sm sm:text-base">
                      Get started by requesting your first analysis
                    </p>
                    <div className="flex flex-col sm:flex-row justify-center gap-3">
                      <Button
                        onClick={requestProductAnalysis}
                        disabled={requestingProductAnalysis}
                        className="w-full sm:w-auto text-sm"
                      >
                        {requestingProductAnalysis ? (
                          <RefreshCw className="h-3 w-3 sm:h-4 sm:w-4 animate-spin mr-2" />
                        ) : (
                          <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                        )}
                        <span className="hidden sm:inline">Request Product Analysis</span>
                        <span className="sm:hidden">Request Analysis</span>
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setActiveTab("request")}
                        className="w-full sm:w-auto text-sm"
                      >
                        <span className="hidden sm:inline">Manual Analysis</span>
                        <span className="sm:hidden">Manual</span>
                      </Button>
                    </div>
                  </div>
                )}
                </div>
              </ScrollArea>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
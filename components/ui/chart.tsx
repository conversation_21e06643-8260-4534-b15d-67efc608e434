"use client"

import * as React from "react"
import {
  <PERSON><PERSON><PERSON> as Recha<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Responsive<PERSON>ontainer,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Line,
  Bar,
  Pie,
  Cell,
  Area,
  AreaChart as Recharts<PERSON>rea<PERSON><PERSON>
} from "recharts"
import { cn } from "@/lib/utils"

interface ChartProps {
  children: React.ReactNode
  config?: any
  className?: string
}

export const ChartContainer = React.forwardRef<
  HTMLDivElement,
  ChartProps
>(({ children, config, className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn("w-full h-full", className)}
      {...props}
    >
      {children}
    </div>
  )
})
ChartContainer.displayName = "ChartContainer"

export const LineChart = React.forwardRef<
  React.ElementRef<typeof RechartsLineChart>,
  React.ComponentPropsWithoutRef<typeof RechartsLineChart>
>(({ className, ...props }, ref) => {
  return <RechartsLineChart ref={ref} className={className} {...props} />
})
LineChart.displayName = "LineChart"

export const PieChart = React.forwardRef<
  React.ElementRef<typeof RechartsPieChart>,
  React.ComponentPropsWithoutRef<typeof RechartsPieChart>
>(({ className, ...props }, ref) => {
  return <RechartsPieChart ref={ref} className={className} {...props} />
})
PieChart.displayName = "PieChart"

export const BarChart = React.forwardRef<
  React.ElementRef<typeof RechartsBarChart>,
  React.ComponentPropsWithoutRef<typeof RechartsBarChart>
>(({ className, ...props }, ref) => {
  return <RechartsBarChart ref={ref} className={className} {...props} />
})
BarChart.displayName = "BarChart"

export const AreaChart = React.forwardRef<
  React.ElementRef<typeof RechartsAreaChart>,
  React.ComponentPropsWithoutRef<typeof RechartsAreaChart>
>(({ className, ...props }, ref) => {
  return <RechartsAreaChart ref={ref} className={className} {...props} />
})
AreaChart.displayName = "AreaChart"

// Export all recharts components
export {
  ResponsiveContainer,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Line,
  Bar,
  Pie,
  Cell,
  Area
}

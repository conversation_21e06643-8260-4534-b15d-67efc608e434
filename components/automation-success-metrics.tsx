"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ChartContainer } from "@/components/ui/chart"
import {
  ResponsiveContainer,
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from "recharts"
import {
  ArrowUpRight,
  ArrowDownRight,
  Clock,
  DollarSign,
  Users,
  CheckCircle2,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>hum<PERSON>Up,
  Activity,
  Filter,
  Download,
  <PERSON>hare2,
  <PERSON>er,
} from "lucide-react"

// Sample data for automation initiatives
const automationInitiatives = [
  {
    id: "1",
    name: "Invoice Processing Automation",
    department: "Finance",
    implementationDate: "2023-09-15",
    status: "Completed",
    timeBeforeAutomation: 15, // hours per week
    timeAfterAutomation: 2.5, // hours per week
    costBeforeAutomation: 750, // $ per week
    costAfterAutomation: 125, // $ per week
    errorRateBeforeAutomation: 8.5, // percentage
    errorRateAfterAutomation: 1.2, // percentage
    userSatisfaction: 92, // percentage
    roi: 420, // percentage
    paybackPeriod: 3.2, // months
    adoptionRate: 95, // percentage
    maintenanceCost: 50, // $ per month
    performanceMetrics: [
      { month: "Sep 2023", timeSpent: 4.5, errorRate: 2.8, cost: 225 },
      { month: "Oct 2023", timeSpent: 3.8, errorRate: 2.1, cost: 190 },
      { month: "Nov 2023", timeSpent: 3.2, errorRate: 1.8, cost: 160 },
      { month: "Dec 2023", timeSpent: 2.9, errorRate: 1.5, cost: 145 },
      { month: "Jan 2024", timeSpent: 2.7, errorRate: 1.3, cost: 135 },
      { month: "Feb 2024", timeSpent: 2.5, errorRate: 1.2, cost: 125 },
    ],
  },
  {
    id: "2",
    name: "Customer Onboarding Workflow",
    department: "Sales",
    implementationDate: "2023-10-20",
    status: "Completed",
    timeBeforeAutomation: 12, // hours per week
    timeAfterAutomation: 3.5, // hours per week
    costBeforeAutomation: 600, // $ per week
    costAfterAutomation: 175, // $ per week
    errorRateBeforeAutomation: 12, // percentage
    errorRateAfterAutomation: 2.5, // percentage
    userSatisfaction: 88, // percentage
    roi: 350, // percentage
    paybackPeriod: 4.5, // months
    adoptionRate: 90, // percentage
    maintenanceCost: 65, // $ per month
    performanceMetrics: [
      { month: "Oct 2023", timeSpent: 7.5, errorRate: 8.2, cost: 375 },
      { month: "Nov 2023", timeSpent: 6.2, errorRate: 6.5, cost: 310 },
      { month: "Dec 2023", timeSpent: 5.0, errorRate: 4.8, cost: 250 },
      { month: "Jan 2024", timeSpent: 4.2, errorRate: 3.5, cost: 210 },
      { month: "Feb 2024", timeSpent: 3.8, errorRate: 2.8, cost: 190 },
      { month: "Mar 2024", timeSpent: 3.5, errorRate: 2.5, cost: 175 },
    ],
  },
  {
    id: "3",
    name: "Social Media Scheduling",
    department: "Marketing",
    implementationDate: "2023-11-05",
    status: "Completed",
    timeBeforeAutomation: 10, // hours per week
    timeAfterAutomation: 2, // hours per week
    costBeforeAutomation: 500, // $ per week
    costAfterAutomation: 100, // $ per week
    errorRateBeforeAutomation: 5, // percentage
    errorRateAfterAutomation: 0.8, // percentage
    userSatisfaction: 95, // percentage
    roi: 480, // percentage
    paybackPeriod: 2.8, // months
    adoptionRate: 98, // percentage
    maintenanceCost: 45, // $ per month
    performanceMetrics: [
      { month: "Nov 2023", timeSpent: 5.5, errorRate: 3.2, cost: 275 },
      { month: "Dec 2023", timeSpent: 4.2, errorRate: 2.5, cost: 210 },
      { month: "Jan 2024", timeSpent: 3.0, errorRate: 1.8, cost: 150 },
      { month: "Feb 2024", timeSpent: 2.5, errorRate: 1.2, cost: 125 },
      { month: "Mar 2024", timeSpent: 2.2, errorRate: 0.9, cost: 110 },
      { month: "Apr 2024", timeSpent: 2.0, errorRate: 0.8, cost: 100 },
    ],
  },
  {
    id: "4",
    name: "Inventory Management System",
    department: "Operations",
    implementationDate: "2023-12-10",
    status: "Completed",
    timeBeforeAutomation: 18, // hours per week
    timeAfterAutomation: 4.5, // hours per week
    costBeforeAutomation: 900, // $ per week
    costAfterAutomation: 225, // $ per week
    errorRateBeforeAutomation: 15, // percentage
    errorRateAfterAutomation: 3.2, // percentage
    userSatisfaction: 85, // percentage
    roi: 320, // percentage
    paybackPeriod: 5.5, // months
    adoptionRate: 88, // percentage
    maintenanceCost: 80, // $ per month
    performanceMetrics: [
      { month: "Dec 2023", timeSpent: 12.5, errorRate: 10.5, cost: 625 },
      { month: "Jan 2024", timeSpent: 9.8, errorRate: 8.2, cost: 490 },
      { month: "Feb 2024", timeSpent: 7.5, errorRate: 6.5, cost: 375 },
      { month: "Mar 2024", timeSpent: 6.0, errorRate: 5.0, cost: 300 },
      { month: "Apr 2024", timeSpent: 5.2, errorRate: 4.0, cost: 260 },
      { month: "May 2024", timeSpent: 4.5, errorRate: 3.2, cost: 225 },
    ],
  },
  {
    id: "5",
    name: "Employee Expense Processing",
    department: "HR",
    implementationDate: "2024-01-15",
    status: "Completed",
    timeBeforeAutomation: 8, // hours per week
    timeAfterAutomation: 1.5, // hours per week
    costBeforeAutomation: 400, // $ per week
    costAfterAutomation: 75, // $ per week
    errorRateBeforeAutomation: 10, // percentage
    errorRateAfterAutomation: 1.5, // percentage
    userSatisfaction: 90, // percentage
    roi: 380, // percentage
    paybackPeriod: 3.8, // months
    adoptionRate: 92, // percentage
    maintenanceCost: 40, // $ per month
    performanceMetrics: [
      { month: "Jan 2024", timeSpent: 5.5, errorRate: 7.2, cost: 275 },
      { month: "Feb 2024", timeSpent: 4.0, errorRate: 5.5, cost: 200 },
      { month: "Mar 2024", timeSpent: 2.8, errorRate: 3.8, cost: 140 },
      { month: "Apr 2024", timeSpent: 2.0, errorRate: 2.5, cost: 100 },
      { month: "May 2024", timeSpent: 1.8, errorRate: 1.8, cost: 90 },
      { month: "Jun 2024", timeSpent: 1.5, errorRate: 1.5, cost: 75 },
    ],
  },
  {
    id: "6",
    name: "Customer Support Ticket Routing",
    department: "Support",
    implementationDate: "2024-02-05",
    status: "In Progress",
    timeBeforeAutomation: 14, // hours per week
    timeAfterAutomation: 5, // hours per week (projected)
    costBeforeAutomation: 700, // $ per week
    costAfterAutomation: 250, // $ per week (projected)
    errorRateBeforeAutomation: 18, // percentage
    errorRateAfterAutomation: 4, // percentage (projected)
    userSatisfaction: 75, // percentage (current)
    roi: 280, // percentage (projected)
    paybackPeriod: 5.2, // months (projected)
    adoptionRate: 65, // percentage (current)
    maintenanceCost: 70, // $ per month (projected)
    performanceMetrics: [
      { month: "Feb 2024", timeSpent: 12.5, errorRate: 16.5, cost: 625 },
      { month: "Mar 2024", timeSpent: 10.0, errorRate: 12.8, cost: 500 },
      { month: "Apr 2024", timeSpent: 8.5, errorRate: 10.5, cost: 425 },
      { month: "May 2024", timeSpent: 7.0, errorRate: 8.2, cost: 350 },
      { month: "Jun 2024", timeSpent: 6.0, errorRate: 6.0, cost: 300 },
      { month: "Jul 2024", timeSpent: 5.0, errorRate: 4.0, cost: 250 },
    ],
  },
  {
    id: "7",
    name: "Data Backup Verification",
    department: "IT",
    implementationDate: "2024-03-10",
    status: "In Progress",
    timeBeforeAutomation: 6, // hours per week
    timeAfterAutomation: 0.8, // hours per week (projected)
    costBeforeAutomation: 300, // $ per week
    costAfterAutomation: 40, // $ per week (projected)
    errorRateBeforeAutomation: 8, // percentage
    errorRateAfterAutomation: 0.5, // percentage (projected)
    userSatisfaction: 82, // percentage (current)
    roi: 520, // percentage (projected)
    paybackPeriod: 2.5, // months (projected)
    adoptionRate: 70, // percentage (current)
    maintenanceCost: 35, // $ per month (projected)
    performanceMetrics: [
      { month: "Mar 2024", timeSpent: 5.2, errorRate: 7.2, cost: 260 },
      { month: "Apr 2024", timeSpent: 3.8, errorRate: 5.5, cost: 190 },
      { month: "May 2024", timeSpent: 2.5, errorRate: 3.8, cost: 125 },
      { month: "Jun 2024", timeSpent: 1.5, errorRate: 2.0, cost: 75 },
      { month: "Jul 2024", timeSpent: 1.0, errorRate: 1.0, cost: 50 },
      { month: "Aug 2024", timeSpent: 0.8, errorRate: 0.5, cost: 40 },
    ],
  },
  {
    id: "8",
    name: "Email Campaign Automation",
    department: "Marketing",
    implementationDate: "2024-04-15",
    status: "Planning",
    timeBeforeAutomation: 12, // hours per week
    timeAfterAutomation: 3, // hours per week (projected)
    costBeforeAutomation: 600, // $ per week
    costAfterAutomation: 150, // $ per week (projected)
    errorRateBeforeAutomation: 12, // percentage
    errorRateAfterAutomation: 2, // percentage (projected)
    userSatisfaction: 0, // percentage (not yet implemented)
    roi: 400, // percentage (projected)
    paybackPeriod: 3.5, // months (projected)
    adoptionRate: 0, // percentage (not yet implemented)
    maintenanceCost: 55, // $ per month (projected)
    performanceMetrics: [],
  },
  {
    id: "9",
    name: "Supplier Invoice Matching",
    department: "Finance",
    implementationDate: "2024-05-20",
    status: "Planning",
    timeBeforeAutomation: 10, // hours per week
    timeAfterAutomation: 2.5, // hours per week (projected)
    costBeforeAutomation: 500, // $ per week
    costAfterAutomation: 125, // $ per week (projected)
    errorRateBeforeAutomation: 15, // percentage
    errorRateAfterAutomation: 3, // percentage (projected)
    userSatisfaction: 0, // percentage (not yet implemented)
    roi: 350, // percentage (projected)
    paybackPeriod: 4.2, // months (projected)
    adoptionRate: 0, // percentage (not yet implemented)
    maintenanceCost: 60, // $ per month (projected)
    performanceMetrics: [],
  },
  {
    id: "10",
    name: "Automated Quality Assurance",
    department: "Operations",
    implementationDate: "2024-06-10",
    status: "Planning",
    timeBeforeAutomation: 15, // hours per week
    timeAfterAutomation: 4, // hours per week (projected)
    costBeforeAutomation: 750, // $ per week
    costAfterAutomation: 200, // $ per week (projected)
    errorRateBeforeAutomation: 20, // percentage
    errorRateAfterAutomation: 3.5, // percentage (projected)
    userSatisfaction: 0, // percentage (not yet implemented)
    roi: 320, // percentage (projected)
    paybackPeriod: 5, // months (projected)
    adoptionRate: 0, // percentage (not yet implemented)
    maintenanceCost: 75, // $ per month (projected)
    performanceMetrics: [],
  },
]

// Calculate overall metrics
const calculateOverallMetrics = () => {
  const completedInitiatives = automationInitiatives.filter((initiative) => initiative.status === "Completed")
  const inProgressInitiatives = automationInitiatives.filter((initiative) => initiative.status === "In Progress")
  const planningInitiatives = automationInitiatives.filter((initiative) => initiative.status === "Planning")

  const totalTimeBeforeAutomation = automationInitiatives.reduce(
    (sum, initiative) => sum + initiative.timeBeforeAutomation,
    0,
  )
  const totalTimeAfterAutomation = automationInitiatives.reduce(
    (sum, initiative) => sum + initiative.timeAfterAutomation,
    0,
  )
  const totalTimeSaved = totalTimeBeforeAutomation - totalTimeAfterAutomation

  const totalCostBeforeAutomation = automationInitiatives.reduce(
    (sum, initiative) => sum + initiative.costBeforeAutomation,
    0,
  )
  const totalCostAfterAutomation = automationInitiatives.reduce(
    (sum, initiative) => sum + initiative.costAfterAutomation,
    0,
  )
  const totalCostSaved = totalCostBeforeAutomation - totalCostAfterAutomation

  const averageErrorRateBeforeAutomation =
    completedInitiatives.reduce((sum, initiative) => sum + initiative.errorRateBeforeAutomation, 0) /
    completedInitiatives.length
  const averageErrorRateAfterAutomation =
    completedInitiatives.reduce((sum, initiative) => sum + initiative.errorRateAfterAutomation, 0) /
    completedInitiatives.length
  const errorRateReduction = averageErrorRateBeforeAutomation - averageErrorRateAfterAutomation

  const averageUserSatisfaction =
    completedInitiatives.reduce((sum, initiative) => sum + initiative.userSatisfaction, 0) / completedInitiatives.length

  const averageROI =
    automationInitiatives.reduce((sum, initiative) => sum + initiative.roi, 0) / automationInitiatives.length

  const averageAdoptionRate =
    completedInitiatives.reduce((sum, initiative) => sum + initiative.adoptionRate, 0) / completedInitiatives.length

  const totalMaintenanceCost = automationInitiatives.reduce((sum, initiative) => sum + initiative.maintenanceCost, 0)

  return {
    totalInitiatives: automationInitiatives.length,
    completedInitiatives: completedInitiatives.length,
    inProgressInitiatives: inProgressInitiatives.length,
    planningInitiatives: planningInitiatives.length,
    totalTimeBeforeAutomation,
    totalTimeAfterAutomation,
    totalTimeSaved,
    totalCostBeforeAutomation,
    totalCostAfterAutomation,
    totalCostSaved,
    averageErrorRateBeforeAutomation,
    averageErrorRateAfterAutomation,
    errorRateReduction,
    averageUserSatisfaction,
    averageROI,
    averageAdoptionRate,
    totalMaintenanceCost,
    annualCostSavings: totalCostSaved * 52, // 52 weeks in a year
    annualTimeSavings: totalTimeSaved * 52, // 52 weeks in a year
  }
}

// Prepare data for charts
const prepareTimelineData = () => {
  const months = [
    "Sep 2023",
    "Oct 2023",
    "Nov 2023",
    "Dec 2023",
    "Jan 2024",
    "Feb 2024",
    "Mar 2024",
    "Apr 2024",
    "May 2024",
    "Jun 2024",
    "Jul 2024",
    "Aug 2024",
  ]

  return months.map((month) => {
    const completedByMonth = automationInitiatives.filter(
      (initiative) =>
        initiative.status === "Completed" &&
        new Date(initiative.implementationDate) <= new Date(month.split(" ")[0] + " 1, " + month.split(" ")[1]),
    )

    const timeBeforeAutomation = completedByMonth.reduce((sum, initiative) => sum + initiative.timeBeforeAutomation, 0)
    const timeAfterAutomation = completedByMonth.reduce((sum, initiative) => sum + initiative.timeAfterAutomation, 0)
    const timeSaved = timeBeforeAutomation - timeAfterAutomation

    const costBeforeAutomation = completedByMonth.reduce((sum, initiative) => sum + initiative.costBeforeAutomation, 0)
    const costAfterAutomation = completedByMonth.reduce((sum, initiative) => sum + initiative.costAfterAutomation, 0)
    const costSaved = costBeforeAutomation - costAfterAutomation

    return {
      month,
      timeBeforeAutomation,
      timeAfterAutomation,
      timeSaved,
      costBeforeAutomation,
      costAfterAutomation,
      costSaved,
      completedInitiatives: completedByMonth.length,
    }
  })
}

const prepareDepartmentData = () => {
  const departments = ["Finance", "Sales", "Marketing", "Operations", "HR", "Support", "IT"]

  return departments.map((department) => {
    const departmentInitiatives = automationInitiatives.filter(
      (initiative) => initiative.department === department && initiative.status === "Completed",
    )

    const timeBeforeAutomation = departmentInitiatives.reduce(
      (sum, initiative) => sum + initiative.timeBeforeAutomation,
      0,
    )
    const timeAfterAutomation = departmentInitiatives.reduce(
      (sum, initiative) => sum + initiative.timeAfterAutomation,
      0,
    )
    const timeSaved = timeBeforeAutomation - timeAfterAutomation

    const costBeforeAutomation = departmentInitiatives.reduce(
      (sum, initiative) => sum + initiative.costBeforeAutomation,
      0,
    )
    const costAfterAutomation = departmentInitiatives.reduce(
      (sum, initiative) => sum + initiative.costAfterAutomation,
      0,
    )
    const costSaved = costBeforeAutomation - costAfterAutomation

    const averageROI =
      departmentInitiatives.length > 0
        ? departmentInitiatives.reduce((sum, initiative) => sum + initiative.roi, 0) / departmentInitiatives.length
        : 0

    return {
      department,
      timeSaved,
      costSaved,
      averageROI,
      initiativeCount: departmentInitiatives.length,
    }
  })
}

const prepareStatusData = () => {
  const completedInitiatives = automationInitiatives.filter((initiative) => initiative.status === "Completed").length
  const inProgressInitiatives = automationInitiatives.filter((initiative) => initiative.status === "In Progress").length
  const planningInitiatives = automationInitiatives.filter((initiative) => initiative.status === "Planning").length

  return [
    { name: "Completed", value: completedInitiatives },
    { name: "In Progress", value: inProgressInitiatives },
    { name: "Planning", value: planningInitiatives },
  ]
}

const COLORS = ["#4CAF50", "#FFC107", "#2196F3"]

export function AutomationSuccessMetrics() {
  const [selectedInitiative, setSelectedInitiative] = useState(automationInitiatives[0])
  const [departmentFilter, setDepartmentFilter] = useState("All")
  const [statusFilter, setStatusFilter] = useState("All")
  const [timeRange, setTimeRange] = useState("all")

  // Calculate metrics
  const overallMetrics = calculateOverallMetrics()
  const timelineData = prepareTimelineData()
  const departmentData = prepareDepartmentData()
  const statusData = prepareStatusData()

  // Filter initiatives based on selected filters
  const filteredInitiatives = automationInitiatives.filter((initiative) => {
    if (departmentFilter !== "All" && initiative.department !== departmentFilter) return false
    if (statusFilter !== "All" && initiative.status !== statusFilter) return false
    return true
  })

  // Get time range data
  const getTimeRangeData = () => {
    if (timeRange === "3m") {
      return timelineData.slice(-3)
    } else if (timeRange === "6m") {
      return timelineData.slice(-6)
    } else {
      return timelineData
    }
  }

  const timeRangeData = getTimeRangeData()

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Automation Success Metrics</h2>
          <p className="text-muted-foreground">
            Track the performance and impact of your automation initiatives across the organization.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Share2 className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Button variant="outline" size="sm">
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Time Saved</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallMetrics.totalTimeSaved.toFixed(1)} hrs/week</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <ArrowUpRight className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500 font-medium">
                {((overallMetrics.totalTimeSaved / overallMetrics.totalTimeBeforeAutomation) * 100).toFixed(1)}%
              </span>
              <span className="ml-1">reduction from manual process</span>
            </div>
            <div className="mt-3">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>Before: {overallMetrics.totalTimeBeforeAutomation.toFixed(1)} hrs</span>
                <span>After: {overallMetrics.totalTimeAfterAutomation.toFixed(1)} hrs</span>
              </div>
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-500 rounded-full"
                  style={{
                    width: `${
                      ((overallMetrics.totalTimeBeforeAutomation - overallMetrics.totalTimeAfterAutomation) /
                        overallMetrics.totalTimeBeforeAutomation) *
                      100
                    }%`,
                  }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cost Savings</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${overallMetrics.totalCostSaved.toFixed(0)}/week</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <ArrowUpRight className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500 font-medium">
                {((overallMetrics.totalCostSaved / overallMetrics.totalCostBeforeAutomation) * 100).toFixed(1)}%
              </span>
              <span className="ml-1">reduction in operational costs</span>
            </div>
            <div className="mt-3">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>Annual: ${overallMetrics.annualCostSavings.toLocaleString()}</span>
                <span>ROI: {overallMetrics.averageROI.toFixed(0)}%</span>
              </div>
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-500 rounded-full"
                  style={{
                    width: `${
                      ((overallMetrics.totalCostBeforeAutomation - overallMetrics.totalCostAfterAutomation) /
                        overallMetrics.totalCostBeforeAutomation) *
                      100
                    }%`,
                  }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Reduction</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallMetrics.errorRateReduction.toFixed(1)}%</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <ArrowDownRight className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500 font-medium">
                {((overallMetrics.errorRateReduction / overallMetrics.averageErrorRateBeforeAutomation) * 100).toFixed(
                  1,
                )}
                %
              </span>
              <span className="ml-1">decrease in error rates</span>
            </div>
            <div className="mt-3">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>Before: {overallMetrics.averageErrorRateBeforeAutomation.toFixed(1)}%</span>
                <span>After: {overallMetrics.averageErrorRateAfterAutomation.toFixed(1)}%</span>
              </div>
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-500 rounded-full"
                  style={{
                    width: `${
                      ((overallMetrics.averageErrorRateBeforeAutomation -
                        overallMetrics.averageErrorRateAfterAutomation) /
                        overallMetrics.averageErrorRateBeforeAutomation) *
                      100
                    }%`,
                  }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">User Satisfaction</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallMetrics.averageUserSatisfaction.toFixed(0)}%</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <ThumbsUp className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500 font-medium">{overallMetrics.averageAdoptionRate.toFixed(0)}%</span>
              <span className="ml-1">adoption rate across initiatives</span>
            </div>
            <div className="mt-3">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>Completed: {overallMetrics.completedInitiatives}</span>
                <span>In Progress: {overallMetrics.inProgressInitiatives}</span>
              </div>
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-500 rounded-full"
                  style={{ width: `${overallMetrics.averageUserSatisfaction}%` }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="initiatives">Initiatives</TabsTrigger>
          <TabsTrigger value="metrics">Detailed Metrics</TabsTrigger>
          <TabsTrigger value="roi">ROI Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <Card className="col-span-2">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Time & Cost Savings Over Time</CardTitle>
                    <CardDescription>Cumulative impact of automation initiatives</CardDescription>
                  </div>
                  <Select value={timeRange} onValueChange={setTimeRange}>
                    <SelectTrigger className="w-[120px]">
                      <SelectValue placeholder="Time Range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="6m">Last 6 Months</SelectItem>
                      <SelectItem value="3m">Last 3 Months</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ChartContainer
                  config={{
                    timeSaved: {
                      label: "Time Saved (hrs/week)",
                      color: "hsl(var(--chart-1))",
                    },
                    costSaved: {
                      label: "Cost Saved ($/week)",
                      color: "hsl(var(--chart-2))",
                    },
                  }}
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={timeRangeData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Line
                        yAxisId="left"
                        type="monotone"
                        dataKey="timeSaved"
                        name="Time Saved (hrs/week)"
                        stroke="var(--color-timeSaved)"
                        activeDot={{ r: 8 }}
                      />
                      <Line
                        yAxisId="right"
                        type="monotone"
                        dataKey="costSaved"
                        name="Cost Saved ($/week)"
                        stroke="var(--color-costSaved)"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Implementation Status</CardTitle>
                <CardDescription>Status of automation initiatives</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ChartContainer>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={statusData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {statusData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
              <CardFooter className="border-t px-6 py-3">
                <div className="flex justify-between w-full text-xs text-muted-foreground">
                  <div className="flex items-center">
                    <div className="h-3 w-3 rounded-full bg-[#4CAF50] mr-1"></div>
                    <span>Completed: {overallMetrics.completedInitiatives}</span>
                  </div>
                  <div className="flex items-center">
                    <div className="h-3 w-3 rounded-full bg-[#FFC107] mr-1"></div>
                    <span>In Progress: {overallMetrics.inProgressInitiatives}</span>
                  </div>
                  <div className="flex items-center">
                    <div className="h-3 w-3 rounded-full bg-[#2196F3] mr-1"></div>
                    <span>Planning: {overallMetrics.planningInitiatives}</span>
                  </div>
                </div>
              </CardFooter>
            </Card>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Department Impact Analysis</CardTitle>
                <CardDescription>Automation impact by department</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ChartContainer
                  config={{
                    timeSaved: {
                      label: "Time Saved (hrs/week)",
                      color: "hsl(var(--chart-1))",
                    },
                    costSaved: {
                      label: "Cost Saved ($/week)",
                      color: "hsl(var(--chart-2))",
                    },
                  }}
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={departmentData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="department" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Bar
                        yAxisId="left"
                        dataKey="timeSaved"
                        name="Time Saved (hrs/week)"
                        fill="var(--color-timeSaved)"
                      />
                      <Bar
                        yAxisId="right"
                        dataKey="costSaved"
                        name="Cost Saved ($/week)"
                        fill="var(--color-costSaved)"
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>ROI by Department</CardTitle>
                <CardDescription>Return on investment across departments</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ChartContainer
                  config={{
                    averageROI: {
                      label: "Average ROI (%)",
                      color: "hsl(var(--chart-3))",
                    },
                    initiativeCount: {
                      label: "Initiative Count",
                      color: "hsl(var(--chart-4))",
                    },
                  }}
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={departmentData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="department" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Bar yAxisId="left" dataKey="averageROI" name="Average ROI (%)" fill="var(--color-averageROI)" />
                      <Bar
                        yAxisId="right"
                        dataKey="initiativeCount"
                        name="Initiative Count"
                        fill="var(--color-initiativeCount)"
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Automation Efficiency Metrics</CardTitle>
              <CardDescription>Key performance indicators for automation initiatives</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Time Efficiency</span>
                    <span className="text-sm font-medium">
                      {((overallMetrics.totalTimeSaved / overallMetrics.totalTimeBeforeAutomation) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <Progress
                    value={(overallMetrics.totalTimeSaved / overallMetrics.totalTimeBeforeAutomation) * 100}
                    className="h-2"
                  />
                  <p className="text-xs text-muted-foreground">
                    Percentage of time saved through automation initiatives
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Cost Efficiency</span>
                    <span className="text-sm font-medium">
                      {((overallMetrics.totalCostSaved / overallMetrics.totalCostBeforeAutomation) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <Progress
                    value={(overallMetrics.totalCostSaved / overallMetrics.totalCostBeforeAutomation) * 100}
                    className="h-2"
                  />
                  <p className="text-xs text-muted-foreground">
                    Percentage of cost saved through automation initiatives
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Quality Improvement</span>
                    <span className="text-sm font-medium">
                      {(
                        (overallMetrics.errorRateReduction / overallMetrics.averageErrorRateBeforeAutomation) *
                        100
                      ).toFixed(1)}
                      %
                    </span>
                  </div>
                  <Progress
                    value={(overallMetrics.errorRateReduction / overallMetrics.averageErrorRateBeforeAutomation) * 100}
                    className="h-2"
                  />
                  <p className="text-xs text-muted-foreground">Percentage reduction in error rates</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">User Adoption</span>
                    <span className="text-sm font-medium">{overallMetrics.averageAdoptionRate.toFixed(1)}%</span>
                  </div>
                  <Progress value={overallMetrics.averageAdoptionRate} className="h-2" />
                  <p className="text-xs text-muted-foreground">Average adoption rate across initiatives</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="initiatives" className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Filter by:</span>
              <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                <SelectTrigger className="w-[180px] h-8">
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All Departments</SelectItem>
                  <SelectItem value="Finance">Finance</SelectItem>
                  <SelectItem value="Sales">Sales</SelectItem>
                  <SelectItem value="Marketing">Marketing</SelectItem>
                  <SelectItem value="Operations">Operations</SelectItem>
                  <SelectItem value="HR">HR</SelectItem>
                  <SelectItem value="Support">Support</SelectItem>
                  <SelectItem value="IT">IT</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px] h-8">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All Statuses</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                  <SelectItem value="Planning">Planning</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Button variant="outline" size="sm">
                <FileText className="mr-2 h-4 w-4" />
                Export List
              </Button>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Initiative Name</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Time Saved</TableHead>
                  <TableHead>Cost Saved</TableHead>
                  <TableHead>Error Reduction</TableHead>
                  <TableHead>ROI</TableHead>
                  <TableHead>User Satisfaction</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInitiatives.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="h-24 text-center">
                      No initiatives found matching your filters.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredInitiatives.map((initiative) => (
                    <TableRow
                      key={initiative.id}
                      className="cursor-pointer"
                      onClick={() => setSelectedInitiative(initiative)}
                    >
                      <TableCell className="font-medium">{initiative.name}</TableCell>
                      <TableCell>{initiative.department}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            initiative.status === "Completed"
                              ? "success"
                              : initiative.status === "In Progress"
                                ? "warning"
                                : "outline"
                          }
                        >
                          {initiative.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {(initiative.timeBeforeAutomation - initiative.timeAfterAutomation).toFixed(1)} hrs/week
                      </TableCell>
                      <TableCell>
                        ${(initiative.costBeforeAutomation - initiative.costAfterAutomation).toFixed(0)}/week
                      </TableCell>
                      <TableCell>
                        {initiative.status === "Completed"
                          ? `${(initiative.errorRateBeforeAutomation - initiative.errorRateAfterAutomation).toFixed(
                              1,
                            )}%`
                          : "N/A"}
                      </TableCell>
                      <TableCell>{initiative.roi}%</TableCell>
                      <TableCell>
                        {initiative.status === "Completed" ? `${initiative.userSatisfaction}%` : "N/A"}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {selectedInitiative && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>{selectedInitiative.name}</CardTitle>
                    <CardDescription>
                      {selectedInitiative.department} • Implemented: {selectedInitiative.implementationDate} • Status:{" "}
                      {selectedInitiative.status}
                    </CardDescription>
                  </div>
                  <Badge
                    variant={
                      selectedInitiative.status === "Completed"
                        ? "success"
                        : selectedInitiative.status === "In Progress"
                          ? "warning"
                          : "outline"
                    }
                  >
                    {selectedInitiative.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <div>
                    <h3 className="text-lg font-medium mb-4">Performance Metrics</h3>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Time Efficiency</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">
                              {(
                                selectedInitiative.timeBeforeAutomation - selectedInitiative.timeAfterAutomation
                              ).toFixed(1)}{" "}
                              hrs/week
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {(
                                ((selectedInitiative.timeBeforeAutomation - selectedInitiative.timeAfterAutomation) /
                                  selectedInitiative.timeBeforeAutomation) *
                                100
                              ).toFixed(1)}
                              % reduction
                            </div>
                            <div className="mt-2 flex items-center justify-between text-xs">
                              <span>Before: {selectedInitiative.timeBeforeAutomation} hrs</span>
                              <span>After: {selectedInitiative.timeAfterAutomation} hrs</span>
                            </div>
                            <Progress
                              value={
                                ((selectedInitiative.timeBeforeAutomation - selectedInitiative.timeAfterAutomation) /
                                  selectedInitiative.timeBeforeAutomation) *
                                100
                              }
                              className="h-1 mt-1"
                            />
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Cost Savings</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">
                              $
                              {(
                                selectedInitiative.costBeforeAutomation - selectedInitiative.costAfterAutomation
                              ).toFixed(0)}
                              /week
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {(
                                ((selectedInitiative.costBeforeAutomation - selectedInitiative.costAfterAutomation) /
                                  selectedInitiative.costBeforeAutomation) *
                                100
                              ).toFixed(1)}
                              % reduction
                            </div>
                            <div className="mt-2 flex items-center justify-between text-xs">
                              <span>Before: ${selectedInitiative.costBeforeAutomation}/week</span>
                              <span>After: ${selectedInitiative.costAfterAutomation}/week</span>
                            </div>
                            <Progress
                              value={
                                ((selectedInitiative.costBeforeAutomation - selectedInitiative.costAfterAutomation) /
                                  selectedInitiative.costBeforeAutomation) *
                                100
                              }
                              className="h-1 mt-1"
                            />
                          </CardContent>
                        </Card>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Error Reduction</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">
                              {selectedInitiative.status === "Completed"
                                ? `${(
                                    selectedInitiative.errorRateBeforeAutomation -
                                      selectedInitiative.errorRateAfterAutomation
                                  ).toFixed(1)}%`
                                : "N/A"}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {selectedInitiative.status === "Completed"
                                ? `${(
                                    ((selectedInitiative.errorRateBeforeAutomation -
                                      selectedInitiative.errorRateAfterAutomation) /
                                      selectedInitiative.errorRateBeforeAutomation) *
                                      100
                                  ).toFixed(1)}% improvement`
                                : "Not yet measured"}
                            </div>
                            {selectedInitiative.status === "Completed" && (
                              <>
                                <div className="mt-2 flex items-center justify-between text-xs">
                                  <span>Before: {selectedInitiative.errorRateBeforeAutomation}%</span>
                                  <span>After: {selectedInitiative.errorRateAfterAutomation}%</span>
                                </div>
                                <Progress
                                  value={
                                    ((selectedInitiative.errorRateBeforeAutomation -
                                      selectedInitiative.errorRateAfterAutomation) /
                                      selectedInitiative.errorRateBeforeAutomation) *
                                    100
                                  }
                                  className="h-1 mt-1"
                                />
                              </>
                            )}
                          </CardContent>
                        </Card>

                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">User Satisfaction</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">
                              {selectedInitiative.status === "Completed"
                                ? `${selectedInitiative.userSatisfaction}%`
                                : "N/A"}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {selectedInitiative.status === "Completed"
                                ? `${selectedInitiative.adoptionRate}% adoption rate`
                                : "Not yet measured"}
                            </div>
                            {selectedInitiative.status === "Completed" && (
                              <>
                                <div className="mt-2 flex items-center justify-between text-xs">
                                  <span>Target: 90%</span>
                                  <span>
                                    {selectedInitiative.userSatisfaction >= 90
                                      ? "Exceeds Target"
                                      : selectedInitiative.userSatisfaction >= 80
                                        ? "Meets Target"
                                        : "Below Target"}
                                  </span>
                                </div>
                                <Progress value={selectedInitiative.userSatisfaction} className="h-1 mt-1" />
                              </>
                            )}
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">Performance Trend</h3>
                    {selectedInitiative.performanceMetrics.length > 0 ? (
                      <div className="h-[300px]">
                        <ChartContainer
                          config={{
                            timeSpent: {
                              label: "Time Spent (hrs/week)",
                              color: "hsl(var(--chart-1))",
                            },
                            errorRate: {
                              label: "Error Rate (%)",
                              color: "hsl(var(--chart-2))",
                            },
                            cost: {
                              label: "Cost ($/week)",
                              color: "hsl(var(--chart-3))",
                            },
                          }}
                        >
                          <ResponsiveContainer width="100%" height="100%">
                            <AreaChart data={selectedInitiative.performanceMetrics}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="month" />
                              <YAxis />
                              <Tooltip />
                              <Legend />
                              <Area
                                type="monotone"
                                dataKey="timeSpent"
                                name="Time Spent (hrs/week)"
                                stroke="var(--color-timeSpent)"
                                fill="var(--color-timeSpent)"
                                fillOpacity={0.2}
                              />
                              <Area
                                type="monotone"
                                dataKey="errorRate"
                                name="Error Rate (%)"
                                stroke="var(--color-errorRate)"
                                fill="var(--color-errorRate)"
                                fillOpacity={0.2}
                              />
                              <Area
                                type="monotone"
                                dataKey="cost"
                                name="Cost ($/week)"
                                stroke="var(--color-cost)"
                                fill="var(--color-cost)"
                                fillOpacity={0.2}
                              />
                            </AreaChart>
                          </ResponsiveContainer>
                        </ChartContainer>
                      </div>
                    ) : (
                      <div className="flex h-[300px] items-center justify-center rounded-md border border-dashed">
                        <div className="text-center">
                          <AlertTriangle className="mx-auto h-8 w-8 text-muted-foreground" />
                          <h3 className="mt-2 text-sm font-semibold">No Performance Data</h3>
                          <p className="text-xs text-muted-foreground">
                            This initiative is still in the planning phase.
                          </p>
                        </div>
                      </div>
                    )}

                    <div className="mt-4 grid grid-cols-3 gap-4">
                      <div className="rounded-md border p-3">
                        <div className="text-xs text-muted-foreground">ROI</div>
                        <div className="text-lg font-semibold">{selectedInitiative.roi}%</div>
                        <div className="text-xs text-muted-foreground">
                          Payback: {selectedInitiative.paybackPeriod} months
                        </div>
                      </div>
                      <div className="rounded-md border p-3">
                        <div className="text-xs text-muted-foreground">Annual Savings</div>
                        <div className="text-lg font-semibold">
                          $
                          {(
                            (selectedInitiative.costBeforeAutomation - selectedInitiative.costAfterAutomation) *
                            52
                          ).toLocaleString()}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {(selectedInitiative.timeBeforeAutomation - selectedInitiative.timeAfterAutomation) * 52}{" "}
                          hrs/year
                        </div>
                      </div>
                      <div className="rounded-md border p-3">
                        <div className="text-xs text-muted-foreground">Maintenance</div>
                        <div className="text-lg font-semibold">${selectedInitiative.maintenanceCost}/month</div>
                        <div className="text-xs text-muted-foreground">
                          {(selectedInitiative.maintenanceCost * 12) /
                            ((selectedInitiative.costBeforeAutomation - selectedInitiative.costAfterAutomation) * 52) <
                          0.1
                            ? "Low"
                            : "Medium"}{" "}
                          overhead
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t px-6 py-3">
                <div className="flex justify-between w-full">
                  <Button variant="outline" size="sm">
                    <FileText className="mr-2 h-4 w-4" />
                    View Full Report
                  </Button>
                  <Button size="sm">
                    <Activity className="mr-2 h-4 w-4" />
                    Monitor Performance
                  </Button>
                </div>
              </CardFooter>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="metrics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Detailed Performance Metrics</CardTitle>
              <CardDescription>Comprehensive metrics for all automation initiatives</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Initiative</TableHead>
                      <TableHead>Before (hrs)</TableHead>
                      <TableHead>After (hrs)</TableHead>
                      <TableHead>Time Saved</TableHead>
                      <TableHead>Before ($)</TableHead>
                      <TableHead>After ($)</TableHead>
                      <TableHead>Cost Saved</TableHead>
                      <TableHead>Error Before</TableHead>
                      <TableHead>Error After</TableHead>
                      <TableHead>Error Reduction</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {automationInitiatives
                      .filter((initiative) => initiative.status === "Completed")
                      .map((initiative) => (
                        <TableRow key={initiative.id}>
                          <TableCell className="font-medium">{initiative.name}</TableCell>
                          <TableCell>{initiative.timeBeforeAutomation}</TableCell>
                          <TableCell>{initiative.timeAfterAutomation}</TableCell>
                          <TableCell>
                            {(initiative.timeBeforeAutomation - initiative.timeAfterAutomation).toFixed(1)} (
                            {(
                              ((initiative.timeBeforeAutomation - initiative.timeAfterAutomation) /
                                initiative.timeBeforeAutomation) *
                              100
                            ).toFixed(0)}
                            %)
                          </TableCell>
                          <TableCell>${initiative.costBeforeAutomation}</TableCell>
                          <TableCell>${initiative.costAfterAutomation}</TableCell>
                          <TableCell>
                            ${(initiative.costBeforeAutomation - initiative.costAfterAutomation).toFixed(0)} (
                            {(
                              ((initiative.costBeforeAutomation - initiative.costAfterAutomation) /
                                initiative.costBeforeAutomation) *
                              100
                            ).toFixed(0)}
                            %)
                          </TableCell>
                          <TableCell>{initiative.errorRateBeforeAutomation}%</TableCell>
                          <TableCell>{initiative.errorRateAfterAutomation}%</TableCell>
                          <TableCell>
                            {(initiative.errorRateBeforeAutomation - initiative.errorRateAfterAutomation).toFixed(1)}% (
                            {(
                              ((initiative.errorRateBeforeAutomation - initiative.errorRateAfterAutomation) /
                                initiative.errorRateBeforeAutomation) *
                              100
                            ).toFixed(0)}
                            %)
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>User Satisfaction Metrics</CardTitle>
                <CardDescription>User feedback and adoption rates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Initiative</TableHead>
                        <TableHead>User Satisfaction</TableHead>
                        <TableHead>Adoption Rate</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {automationInitiatives
                        .filter((initiative) => initiative.status !== "Planning")
                        .map((initiative) => (
                          <TableRow key={initiative.id}>
                            <TableCell className="font-medium">{initiative.name}</TableCell>
                            <TableCell>
                              {initiative.status === "Completed" ? (
                                <div className="flex items-center">
                                  <Progress value={initiative.userSatisfaction} className="h-2 w-16 mr-2" />
                                  <span>{initiative.userSatisfaction}%</span>
                                </div>
                              ) : (
                                "In progress"
                              )}
                            </TableCell>
                            <TableCell>
                              {initiative.status === "Completed" ? (
                                <div className="flex items-center">
                                  <Progress value={initiative.adoptionRate} className="h-2 w-16 mr-2" />
                                  <span>{initiative.adoptionRate}%</span>
                                </div>
                              ) : (
                                "In progress"
                              )}
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  initiative.userSatisfaction >= 90
                                    ? "success"
                                    : initiative.userSatisfaction >= 80
                                      ? "warning"
                                      : "destructive"
                                }
                              >
                                {initiative.userSatisfaction >= 90
                                  ? "Excellent"
                                  : initiative.userSatisfaction >= 80
                                    ? "Good"
                                    : "Needs Improvement"}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Implementation Metrics</CardTitle>
                <CardDescription>Timeline and resource utilization</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Initiative</TableHead>
                        <TableHead>Implementation Date</TableHead>
                        <TableHead>Payback Period</TableHead>
                        <TableHead>Maintenance Cost</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {automationInitiatives
                        .filter((initiative) => initiative.status !== "Planning")
                        .map((initiative) => (
                          <TableRow key={initiative.id}>
                            <TableCell className="font-medium">{initiative.name}</TableCell>
                            <TableCell>{initiative.implementationDate}</TableCell>
                            <TableCell>{initiative.paybackPeriod} months</TableCell>
                            <TableCell>${initiative.maintenanceCost}/month</TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="roi" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>ROI Analysis</CardTitle>
              <CardDescription>Financial impact of automation initiatives</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                <div className="space-y-2">
                  <div className="text-sm font-medium">Annual Cost Savings</div>
                  <div className="text-3xl font-bold">${overallMetrics.annualCostSavings.toLocaleString()}</div>
                  <div className="text-xs text-muted-foreground">
                    Based on ${overallMetrics.totalCostSaved.toFixed(0)} weekly savings
                  </div>
                  <div className="h-1 w-full bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full bg-green-500 rounded-full"
                      style={{
                        width: `${Math.min(
                          (overallMetrics.annualCostSavings / (overallMetrics.totalCostBeforeAutomation * 52)) * 100,
                          100,
                        )}%`,
                      }}
                    ></div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="text-sm font-medium">Annual Time Savings</div>
                  <div className="text-3xl font-bold">{overallMetrics.annualTimeSavings.toLocaleString()} hours</div>
                  <div className="text-xs text-muted-foreground">
                    Based on {overallMetrics.totalTimeSaved.toFixed(1)} weekly hours saved
                  </div>
                  <div className="h-1 w-full bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full bg-green-500 rounded-full"
                      style={{
                        width: `${Math.min(
                          (overallMetrics.annualTimeSavings / (overallMetrics.totalTimeBeforeAutomation * 52)) * 100,
                          100,
                        )}%`,
                      }}
                    ></div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="text-sm font-medium">Average ROI</div>
                  <div className="text-3xl font-bold">{overallMetrics.averageROI.toFixed(0)}%</div>
                  <div className="text-xs text-muted-foreground">
                    Average return on investment across all initiatives
                  </div>
                  <div className="h-1 w-full bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full bg-green-500 rounded-full"
                      style={{ width: `${Math.min(overallMetrics.averageROI / 5, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4">ROI by Initiative</h3>
                <div className="h-[300px]">
                  <ChartContainer
                    config={{
                      roi: {
                        label: "ROI (%)",
                        color: "hsl(var(--chart-1))",
                      },
                      paybackPeriod: {
                        label: "Payback Period (months)",
                        color: "hsl(var(--chart-2))",
                      },
                    }}
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={automationInitiatives.filter((initiative) => initiative.status !== "Planning")}
                        margin={{ top: 5, right: 30, left: 20, bottom: 120 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" angle={-45} textAnchor="end" height={100} />
                        <YAxis yAxisId="left" />
                        <YAxis yAxisId="right" orientation="right" />
                        <Tooltip />
                        <Legend />
                        <Bar yAxisId="left" dataKey="roi" name="ROI (%)" fill="var(--color-roi)" />
                        <Bar
                          yAxisId="right"
                          dataKey="paybackPeriod"
                          name="Payback Period (months)"
                          fill="var(--color-paybackPeriod)"
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </div>
              </div>

              <div className="mt-6 rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Initiative</TableHead>
                      <TableHead>Implementation Cost</TableHead>
                      <TableHead>Annual Savings</TableHead>
                      <TableHead>ROI</TableHead>
                      <TableHead>Payback Period</TableHead>
                      <TableHead>5-Year Net Benefit</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {automationInitiatives
                      .filter((initiative) => initiative.status !== "Planning")
                      .map((initiative) => {
                        const annualSavings = (initiative.costBeforeAutomation - initiative.costAfterAutomation) * 52
                        const implementationCost = (annualSavings * initiative.paybackPeriod) / 12
                        const fiveYearNetBenefit =
                          annualSavings * 5 - implementationCost - initiative.maintenanceCost * 60
                        return (
                          <TableRow key={initiative.id}>
                            <TableCell className="font-medium">{initiative.name}</TableCell>
                            <TableCell>${implementationCost.toLocaleString()}</TableCell>
                            <TableCell>${annualSavings.toLocaleString()}</TableCell>
                            <TableCell>{initiative.roi}%</TableCell>
                            <TableCell>{initiative.paybackPeriod} months</TableCell>
                            <TableCell>${fiveYearNetBenefit.toLocaleString()}</TableCell>
                          </TableRow>
                        )
                      })}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

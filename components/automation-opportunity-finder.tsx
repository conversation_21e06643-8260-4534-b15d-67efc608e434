"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ScatterChart,
  Scatter,
  ZAxis,
  Cell,
} from "recharts"
import { ChartContainer } from "@/components/ui/chart"
import {
  Bot,
  Filter,
  Lightbulb,
  Zap,
  BarChart2,
  ListChecks,
  Calendar,
  FileSpreadsheet,
  Mail,
  ShoppingCart,
  Users,
  Truck,
  AlertCircle,
  CheckCircle2,
  ArrowRight,
} from "lucide-react"

type Process = {
  id: string
  name: string
  department: string
  frequency: string
  timeSpent: number // hours per week
  complexity: "Low" | "Medium" | "High"
  repetitiveness: "Low" | "Medium" | "High"
  automationPotential: number // percentage
  currentSteps: string[]
  automationSolution: string
  toolsNeeded: string[]
  estimatedSavings: number // hours per week
  implementationDifficulty: "Easy" | "Medium" | "Complex"
  roi: number // percentage
}

export function AutomationOpportunityFinder() {
  const [department, setDepartment] = useState<string>("all")
  const [potentialFilter, setPotentialFilter] = useState<string>("all")
  const [selectedProcesses, setSelectedProcesses] = useState<string[]>([])

  const processes: Process[] = [
    {
      id: "1",
      name: "Invoice Processing",
      department: "Finance",
      frequency: "Daily",
      timeSpent: 15,
      complexity: "Medium",
      repetitiveness: "High",
      automationPotential: 90,
      currentSteps: [
        "Receive invoice via email",
        "Extract data manually",
        "Enter into accounting system",
        "Route for approval",
        "Process payment",
      ],
      automationSolution: "Implement OCR and RPA to automatically extract invoice data and route for approval",
      toolsNeeded: ["OCR Software", "RPA Tool", "Accounting System API"],
      estimatedSavings: 13.5,
      implementationDifficulty: "Medium",
      roi: 320,
    },
    {
      id: "2",
      name: "Customer Onboarding",
      department: "Sales",
      frequency: "Daily",
      timeSpent: 12,
      complexity: "Medium",
      repetitiveness: "High",
      automationPotential: 75,
      currentSteps: [
        "Collect customer information",
        "Create account in CRM",
        "Send welcome email",
        "Schedule kickoff call",
        "Prepare onboarding materials",
      ],
      automationSolution: "Create automated workflow with form capture and triggered emails/calendar invites",
      toolsNeeded: ["Form Builder", "CRM API", "Email Automation"],
      estimatedSavings: 9,
      implementationDifficulty: "Easy",
      roi: 450,
    },
    {
      id: "3",
      name: "Inventory Reconciliation",
      department: "Operations",
      frequency: "Weekly",
      timeSpent: 8,
      complexity: "High",
      repetitiveness: "Medium",
      automationPotential: 60,
      currentSteps: [
        "Export inventory data from system",
        "Compare with physical count",
        "Identify discrepancies",
        "Investigate causes",
        "Update inventory system",
      ],
      automationSolution: "Implement barcode/RFID scanning with real-time inventory updates",
      toolsNeeded: ["Barcode Scanner", "Inventory API", "Mobile App"],
      estimatedSavings: 4.8,
      implementationDifficulty: "Complex",
      roi: 180,
    },
    {
      id: "4",
      name: "Social Media Posting",
      department: "Marketing",
      frequency: "Daily",
      timeSpent: 10,
      complexity: "Low",
      repetitiveness: "High",
      automationPotential: 95,
      currentSteps: [
        "Create content calendar",
        "Design graphics",
        "Write captions",
        "Schedule posts manually",
        "Monitor engagement",
      ],
      automationSolution: "Use social media management platform with scheduling and analytics",
      toolsNeeded: ["Social Media Management Platform", "Content Calendar"],
      estimatedSavings: 9.5,
      implementationDifficulty: "Easy",
      roi: 380,
    },
    {
      id: "5",
      name: "Employee Expense Reports",
      department: "HR",
      frequency: "Monthly",
      timeSpent: 20,
      complexity: "Medium",
      repetitiveness: "High",
      automationPotential: 85,
      currentSteps: [
        "Collect receipts",
        "Enter expense data manually",
        "Categorize expenses",
        "Submit for approval",
        "Process reimbursements",
      ],
      automationSolution: "Implement expense management app with receipt scanning and approval workflow",
      toolsNeeded: ["Expense Management App", "Accounting Integration"],
      estimatedSavings: 17,
      implementationDifficulty: "Medium",
      roi: 340,
    },
    {
      id: "6",
      name: "Customer Support Ticket Routing",
      department: "Support",
      frequency: "Daily",
      timeSpent: 8,
      complexity: "Medium",
      repetitiveness: "High",
      automationPotential: 80,
      currentSteps: [
        "Receive support ticket",
        "Read and categorize manually",
        "Assign to appropriate team member",
        "Track resolution time",
        "Follow up if needed",
      ],
      automationSolution: "Implement AI-based ticket classification and automatic routing",
      toolsNeeded: ["AI Classification Tool", "Helpdesk System API"],
      estimatedSavings: 6.4,
      implementationDifficulty: "Medium",
      roi: 280,
    },
    {
      id: "7",
      name: "Weekly Sales Reporting",
      department: "Sales",
      frequency: "Weekly",
      timeSpent: 6,
      complexity: "Medium",
      repetitiveness: "High",
      automationPotential: 90,
      currentSteps: [
        "Export data from CRM",
        "Clean and format data in spreadsheet",
        "Create visualizations",
        "Write analysis",
        "Distribute report",
      ],
      automationSolution: "Create automated dashboard with real-time data and scheduled report distribution",
      toolsNeeded: ["BI Tool", "CRM API", "Automated Reporting"],
      estimatedSavings: 5.4,
      implementationDifficulty: "Medium",
      roi: 270,
    },
    {
      id: "8",
      name: "Data Backup Verification",
      department: "IT",
      frequency: "Daily",
      timeSpent: 5,
      complexity: "Medium",
      repetitiveness: "High",
      automationPotential: 95,
      currentSteps: [
        "Check backup logs",
        "Verify backup integrity",
        "Document status",
        "Troubleshoot failures",
        "Report issues",
      ],
      automationSolution: "Implement automated backup verification with alerts for failures",
      toolsNeeded: ["Backup Monitoring Tool", "Alert System"],
      estimatedSavings: 4.75,
      implementationDifficulty: "Easy",
      roi: 380,
    },
    {
      id: "9",
      name: "Content Approval Workflow",
      department: "Marketing",
      frequency: "Weekly",
      timeSpent: 10,
      complexity: "Medium",
      repetitiveness: "Medium",
      automationPotential: 70,
      currentSteps: [
        "Submit content for review",
        "Notify stakeholders manually",
        "Collect feedback via email",
        "Consolidate feedback",
        "Get final approval",
      ],
      automationSolution: "Implement content workflow system with automated notifications and approvals",
      toolsNeeded: ["Workflow Management Tool", "Content Management System"],
      estimatedSavings: 7,
      implementationDifficulty: "Medium",
      roi: 210,
    },
    {
      id: "10",
      name: "Order Processing",
      department: "Operations",
      frequency: "Daily",
      timeSpent: 18,
      complexity: "Medium",
      repetitiveness: "High",
      automationPotential: 85,
      currentSteps: [
        "Receive order notification",
        "Verify inventory availability",
        "Process payment",
        "Generate shipping label",
        "Update order status",
      ],
      automationSolution: "Implement end-to-end order processing automation with inventory checks",
      toolsNeeded: ["Order Management System", "Inventory API", "Payment Gateway"],
      estimatedSavings: 15.3,
      implementationDifficulty: "Complex",
      roi: 255,
    },
  ]

  const filteredProcesses = processes.filter((process) => {
    if (department !== "all" && process.department !== department) return false
    if (potentialFilter !== "all") {
      const potential = Number.parseInt(potentialFilter)
      if (process.automationPotential < potential) return false
    }
    return true
  })

  const toggleProcessSelection = (id: string) => {
    if (selectedProcesses.includes(id)) {
      setSelectedProcesses(selectedProcesses.filter((p) => p !== id))
    } else {
      setSelectedProcesses([...selectedProcesses, id])
    }
  }

  const selectedProcessesData = processes.filter((p) => selectedProcesses.includes(p.id))

  // Calculate metrics
  const totalManualHours = processes.reduce((sum, p) => sum + p.timeSpent, 0)
  const potentialSavings = processes.reduce((sum, p) => sum + p.estimatedSavings, 0)
  const averageAutomationPotential = processes.reduce((sum, p) => sum + p.automationPotential, 0) / processes.length
  const averageROI = processes.reduce((sum, p) => sum + p.roi, 0) / processes.length

  // Data for charts
  const departmentData = [
    {
      name: "Finance",
      hours: processes.filter((p) => p.department === "Finance").reduce((sum, p) => sum + p.timeSpent, 0),
    },
    {
      name: "Sales",
      hours: processes.filter((p) => p.department === "Sales").reduce((sum, p) => sum + p.timeSpent, 0),
    },
    {
      name: "Marketing",
      hours: processes.filter((p) => p.department === "Marketing").reduce((sum, p) => sum + p.timeSpent, 0),
    },
    {
      name: "Operations",
      hours: processes.filter((p) => p.department === "Operations").reduce((sum, p) => sum + p.timeSpent, 0),
    },
    { name: "HR", hours: processes.filter((p) => p.department === "HR").reduce((sum, p) => sum + p.timeSpent, 0) },
    {
      name: "Support",
      hours: processes.filter((p) => p.department === "Support").reduce((sum, p) => sum + p.timeSpent, 0),
    },
    { name: "IT", hours: processes.filter((p) => p.department === "IT").reduce((sum, p) => sum + p.timeSpent, 0) },
  ]

  const scatterData = processes.map((p) => ({
    id: p.id,
    name: p.name,
    x: p.implementationDifficulty === "Easy" ? 1 : p.implementationDifficulty === "Medium" ? 2 : 3,
    y: p.roi,
    z: p.timeSpent,
    department: p.department,
    automationPotential: p.automationPotential,
  }))

  // Group selected processes by implementation difficulty
  const implementationPlan = {
    easy: selectedProcessesData.filter((p) => p.implementationDifficulty === "Easy"),
    medium: selectedProcessesData.filter((p) => p.implementationDifficulty === "Medium"),
    complex: selectedProcessesData.filter((p) => p.implementationDifficulty === "Complex"),
  }

  const totalSelectedSavings = selectedProcessesData.reduce((sum, p) => sum + p.estimatedSavings, 0)

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Easy":
        return "bg-green-100 text-green-800"
      case "Medium":
        return "bg-amber-100 text-amber-800"
      case "Complex":
        return "bg-red-100 text-red-800"
      default:
        return ""
    }
  }

  const getAutomationPotentialColor = (potential: number) => {
    if (potential >= 80) return "bg-green-100 text-green-800"
    if (potential >= 60) return "bg-amber-100 text-amber-800"
    return "bg-gray-100 text-gray-800"
  }

  const getDepartmentIcon = (department: string) => {
    switch (department) {
      case "Finance":
        return <FileSpreadsheet className="h-4 w-4" />
      case "Sales":
        return <ShoppingCart className="h-4 w-4" />
      case "Marketing":
        return <BarChart2 className="h-4 w-4" />
      case "Operations":
        return <Truck className="h-4 w-4" />
      case "HR":
        return <Users className="h-4 w-4" />
      case "Support":
        return <Mail className="h-4 w-4" />
      case "IT":
        return <Bot className="h-4 w-4" />
      default:
        return <Lightbulb className="h-4 w-4" />
    }
  }

  return (
    <Card className="col-span-3">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-amber-500" />
          Automation Opportunity Finder
        </CardTitle>
        <CardDescription>Identify manual processes that could be automated to save time and resources</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="discovery">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="discovery">Process Discovery</TabsTrigger>
            <TabsTrigger value="analysis">Process Analysis</TabsTrigger>
            <TabsTrigger value="implementation">Implementation Plan</TabsTrigger>
          </TabsList>

          <TabsContent value="discovery" className="space-y-4">
            <div className="grid grid-cols-4 gap-4">
              <Card className="col-span-1">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Manual Process Hours</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{totalManualHours.toFixed(0)}</div>
                  <p className="text-xs text-muted-foreground">Hours per week</p>
                </CardContent>
              </Card>
              <Card className="col-span-1">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Potential Time Savings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{potentialSavings.toFixed(1)}</div>
                  <p className="text-xs text-muted-foreground">Hours per week</p>
                </CardContent>
              </Card>
              <Card className="col-span-1">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Average ROI</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{averageROI.toFixed(0)}%</div>
                  <p className="text-xs text-muted-foreground">Return on investment</p>
                </CardContent>
              </Card>
              <Card className="col-span-1">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Automation Potential</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{averageAutomationPotential.toFixed(0)}%</div>
                  <p className="text-xs text-muted-foreground">Average across processes</p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <Card className="col-span-2">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Automation ROI vs Implementation Difficulty</CardTitle>
                </CardHeader>
                <CardContent className="h-[300px]">
                  <ChartContainer
                    config={{
                      scatter: {
                        label: "Processes",
                        color: "hsl(var(--chart-1))",
                      },
                    }}
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                          type="number"
                          dataKey="x"
                          name="Difficulty"
                          domain={[0, 4]}
                          tickFormatter={(value) => {
                            if (value === 1) return "Easy"
                            if (value === 2) return "Medium"
                            if (value === 3) return "Complex"
                            return ""
                          }}
                        />
                        <YAxis type="number" dataKey="y" name="ROI %" />
                        <ZAxis type="number" dataKey="z" range={[50, 400]} name="Time Spent" />
                        <Tooltip
                          cursor={{ strokeDasharray: "3 3" }}
                          content={({ active, payload }) => {
                            if (active && payload && payload.length) {
                              const data = payload[0].payload
                              return (
                                <div className="rounded-lg border bg-background p-2 shadow-sm">
                                  <div className="font-semibold">{data.name}</div>
                                  <div className="text-sm text-muted-foreground">{data.department}</div>
                                  <div className="text-sm">ROI: {data.y}%</div>
                                  <div className="text-sm">Time: {data.z} hrs/week</div>
                                  <div className="text-sm">Automation: {data.automationPotential}%</div>
                                </div>
                              )
                            }
                            return null
                          }}
                        />
                        <Scatter name="Processes" data={scatterData}>
                          {scatterData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={
                                entry.department === "Finance"
                                  ? "#8884d8"
                                  : entry.department === "Sales"
                                    ? "#82ca9d"
                                    : entry.department === "Marketing"
                                      ? "#ffc658"
                                      : entry.department === "Operations"
                                        ? "#ff8042"
                                        : entry.department === "HR"
                                          ? "#0088fe"
                                          : entry.department === "Support"
                                            ? "#00C49F"
                                            : "#FFBB28"
                              }
                            />
                          ))}
                        </Scatter>
                      </ScatterChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </CardContent>
              </Card>

              <Card className="col-span-1">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Time Savings by Department</CardTitle>
                </CardHeader>
                <CardContent className="h-[300px]">
                  <ChartContainer
                    config={{
                      hours: {
                        label: "Hours per Week",
                        color: "hsl(var(--chart-2))",
                      },
                    }}
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={departmentData}
                        layout="vertical"
                        margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" />
                        <YAxis type="category" dataKey="name" width={80} />
                        <Tooltip />
                        <Bar dataKey="hours" fill="var(--color-hours)" />
                      </BarChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </CardContent>
              </Card>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Filter by:</span>
                <Select value={department} onValueChange={setDepartment}>
                  <SelectTrigger className="w-[180px] h-8">
                    <SelectValue placeholder="Department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    <SelectItem value="Finance">Finance</SelectItem>
                    <SelectItem value="Sales">Sales</SelectItem>
                    <SelectItem value="Marketing">Marketing</SelectItem>
                    <SelectItem value="Operations">Operations</SelectItem>
                    <SelectItem value="HR">HR</SelectItem>
                    <SelectItem value="Support">Support</SelectItem>
                    <SelectItem value="IT">IT</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={potentialFilter} onValueChange={setPotentialFilter}>
                  <SelectTrigger className="w-[180px] h-8">
                    <SelectValue placeholder="Automation Potential" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Potential</SelectItem>
                    <SelectItem value="90">90%+ Potential</SelectItem>
                    <SelectItem value="80">80%+ Potential</SelectItem>
                    <SelectItem value="70">70%+ Potential</SelectItem>
                    <SelectItem value="60">60%+ Potential</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={selectedProcesses.length === 0}
                  onClick={() => setSelectedProcesses([])}
                >
                  Clear Selection ({selectedProcesses.length})
                </Button>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[30px]"></TableHead>
                    <TableHead>Process Name</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Frequency</TableHead>
                    <TableHead>Time Spent</TableHead>
                    <TableHead>Automation Potential</TableHead>
                    <TableHead>Implementation</TableHead>
                    <TableHead>ROI</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredProcesses.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        No processes found matching your filters.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredProcesses.map((process) => (
                      <TableRow
                        key={process.id}
                        className={selectedProcesses.includes(process.id) ? "bg-muted/50" : ""}
                        onClick={() => toggleProcessSelection(process.id)}
                        style={{ cursor: "pointer" }}
                      >
                        <TableCell>
                          <input
                            type="checkbox"
                            checked={selectedProcesses.includes(process.id)}
                            onChange={() => {}}
                            className="h-4 w-4 rounded border-gray-300"
                          />
                        </TableCell>
                        <TableCell className="font-medium">{process.name}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            {getDepartmentIcon(process.department)}
                            <span>{process.department}</span>
                          </div>
                        </TableCell>
                        <TableCell>{process.frequency}</TableCell>
                        <TableCell>{process.timeSpent} hrs/week</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Progress value={process.automationPotential} className="h-2 w-[60px]" />
                            <Badge
                              variant="outline"
                              className={getAutomationPotentialColor(process.automationPotential)}
                            >
                              {process.automationPotential}%
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className={getDifficultyColor(process.implementationDifficulty)}>
                            {process.implementationDifficulty}
                          </Badge>
                        </TableCell>
                        <TableCell>{process.roi}%</TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          <TabsContent value="analysis" className="space-y-4">
            {selectedProcesses.length === 0 ? (
              <div className="flex h-[400px] flex-col items-center justify-center rounded-md border border-dashed p-8 text-center">
                <Lightbulb className="h-10 w-10 text-muted-foreground/80" />
                <h3 className="mt-4 text-lg font-semibold">No Processes Selected</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  Select processes from the Process Discovery tab to analyze automation potential.
                </p>
                <Button variant="outline" className="mt-4" onClick={() => setSelectedProcesses(["1", "4", "8"])}>
                  Select Sample Processes
                </Button>
              </div>
            ) : (
              <>
                {selectedProcessesData.map((process) => (
                  <Card key={process.id} className="overflow-hidden">
                    <CardHeader className="bg-muted/50 pb-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-lg">{process.name}</CardTitle>
                          <CardDescription className="flex items-center gap-1">
                            {getDepartmentIcon(process.department)}
                            {process.department} • {process.frequency} • {process.timeSpent} hrs/week
                          </CardDescription>
                        </div>
                        <Badge variant="outline" className={getAutomationPotentialColor(process.automationPotential)}>
                          {process.automationPotential}% Automation Potential
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="p-0">
                      <div className="grid grid-cols-2 divide-x">
                        <div className="p-4">
                          <h4 className="mb-2 text-sm font-semibold">Current Process</h4>
                          <ol className="ml-5 list-decimal space-y-1 text-sm">
                            {process.currentSteps.map((step, index) => (
                              <li key={index}>{step}</li>
                            ))}
                          </ol>
                        </div>
                        <div className="p-4">
                          <h4 className="mb-2 text-sm font-semibold">Automation Solution</h4>
                          <p className="mb-4 text-sm">{process.automationSolution}</p>

                          <div className="mb-2 flex items-center gap-2">
                            <h5 className="text-xs font-semibold">Tools Needed:</h5>
                            <div className="flex flex-wrap gap-1">
                              {process.toolsNeeded.map((tool, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {tool}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          <div className="grid grid-cols-3 gap-4 rounded-md bg-muted/50 p-3 text-center text-sm">
                            <div>
                              <div className="text-xs text-muted-foreground">Time Savings</div>
                              <div className="font-semibold">{process.estimatedSavings} hrs/week</div>
                            </div>
                            <div>
                              <div className="text-xs text-muted-foreground">Implementation</div>
                              <div className="font-semibold">{process.implementationDifficulty}</div>
                            </div>
                            <div>
                              <div className="text-xs text-muted-foreground">ROI</div>
                              <div className="font-semibold">{process.roi}%</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </>
            )}
          </TabsContent>

          <TabsContent value="implementation" className="space-y-4">
            {selectedProcesses.length === 0 ? (
              <div className="flex h-[400px] flex-col items-center justify-center rounded-md border border-dashed p-8 text-center">
                <ListChecks className="h-10 w-10 text-muted-foreground/80" />
                <h3 className="mt-4 text-lg font-semibold">No Implementation Plan</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  Select processes from the Process Discovery tab to create an implementation plan.
                </p>
                <Button variant="outline" className="mt-4" onClick={() => setSelectedProcesses(["2", "4", "8"])}>
                  Select Sample Processes
                </Button>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-3 gap-4">
                  <Card className="col-span-2">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Implementation Timeline</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="rounded-md border p-4">
                          <h3 className="mb-2 flex items-center gap-2 font-semibold text-green-600">
                            <Zap className="h-4 w-4" />
                            Phase 1: Quick Wins ({implementationPlan.easy.length})
                          </h3>
                          <p className="mb-2 text-sm text-muted-foreground">Estimated completion: 2-4 weeks</p>
                          {implementationPlan.easy.length > 0 ? (
                            <ul className="ml-6 list-disc space-y-1 text-sm">
                              {implementationPlan.easy.map((p) => (
                                <li key={p.id}>
                                  <span className="font-medium">{p.name}</span> - {p.automationSolution}
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <p className="text-sm italic text-muted-foreground">No quick win processes selected.</p>
                          )}
                        </div>

                        <div className="rounded-md border p-4">
                          <h3 className="mb-2 flex items-center gap-2 font-semibold text-amber-600">
                            <ArrowRight className="h-4 w-4" />
                            Phase 2: Medium Complexity ({implementationPlan.medium.length})
                          </h3>
                          <p className="mb-2 text-sm text-muted-foreground">Estimated completion: 1-3 months</p>
                          {implementationPlan.medium.length > 0 ? (
                            <ul className="ml-6 list-disc space-y-1 text-sm">
                              {implementationPlan.medium.map((p) => (
                                <li key={p.id}>
                                  <span className="font-medium">{p.name}</span> - {p.automationSolution}
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <p className="text-sm italic text-muted-foreground">
                              No medium complexity processes selected.
                            </p>
                          )}
                        </div>

                        <div className="rounded-md border p-4">
                          <h3 className="mb-2 flex items-center gap-2 font-semibold text-red-600">
                            <AlertCircle className="h-4 w-4" />
                            Phase 3: Complex Projects ({implementationPlan.complex.length})
                          </h3>
                          <p className="mb-2 text-sm text-muted-foreground">Estimated completion: 3-6 months</p>
                          {implementationPlan.complex.length > 0 ? (
                            <ul className="ml-6 list-disc space-y-1 text-sm">
                              {implementationPlan.complex.map((p) => (
                                <li key={p.id}>
                                  <span className="font-medium">{p.name}</span> - {p.automationSolution}
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <p className="text-sm italic text-muted-foreground">No complex processes selected.</p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Implementation Summary</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Total Processes:</span>
                          <span className="font-medium">{selectedProcesses.length}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Time Savings:</span>
                          <span className="font-medium">{totalSelectedSavings.toFixed(1)} hrs/week</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Annual Savings:</span>
                          <span className="font-medium">${(totalSelectedSavings * 52 * 50).toFixed(0)}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Implementation Time:</span>
                          <span className="font-medium">
                            {implementationPlan.complex.length > 0
                              ? "3-6 months"
                              : implementationPlan.medium.length > 0
                                ? "1-3 months"
                                : "2-4 weeks"}
                          </span>
                        </div>
                      </div>

                      <div className="rounded-md bg-muted p-3">
                        <h4 className="mb-2 text-sm font-medium">Resource Requirements</h4>
                        <ul className="space-y-1 text-sm">
                          {implementationPlan.easy.length > 0 && (
                            <li className="flex items-center justify-between">
                              <span>Quick Wins:</span>
                              <span>1 developer, 2 weeks</span>
                            </li>
                          )}
                          {implementationPlan.medium.length > 0 && (
                            <li className="flex items-center justify-between">
                              <span>Medium Complexity:</span>
                              <span>2 developers, 4 weeks</span>
                            </li>
                          )}
                          {implementationPlan.complex.length > 0 && (
                            <li className="flex items-center justify-between">
                              <span>Complex Projects:</span>
                              <span>3 developers, 8 weeks</span>
                            </li>
                          )}
                        </ul>
                      </div>

                      <div className="space-y-2">
                        <Button className="w-full">
                          <CheckCircle2 className="mr-2 h-4 w-4" />
                          Approve Implementation Plan
                        </Button>
                        <Button variant="outline" className="w-full">
                          <Calendar className="mr-2 h-4 w-4" />
                          Schedule Kickoff Meeting
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Business Impact Assessment</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-4 gap-4">
                      <div className="space-y-1">
                        <h4 className="text-sm font-medium">Cost Savings</h4>
                        <p className="text-sm text-muted-foreground">
                          Estimated annual savings of ${(totalSelectedSavings * 52 * 50).toFixed(0)} based on average
                          hourly rate of $50.
                        </p>
                      </div>
                      <div className="space-y-1">
                        <h4 className="text-sm font-medium">Quality Improvements</h4>
                        <p className="text-sm text-muted-foreground">
                          Reduction in manual errors by approximately 85% across selected processes.
                        </p>
                      </div>
                      <div className="space-y-1">
                        <h4 className="text-sm font-medium">Business Impact</h4>
                        <p className="text-sm text-muted-foreground">
                          Improved customer response times by 40% and increased operational capacity by 15%.
                        </p>
                      </div>
                      <div className="space-y-1">
                        <h4 className="text-sm font-medium">Team Impact</h4>
                        <p className="text-sm text-muted-foreground">
                          {totalSelectedSavings.toFixed(1)} hours/week freed for strategic work. Training required for{" "}
                          {selectedProcesses.length * 2} team members.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

'use client'

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { blogCategoryService, type BlogCategory, type UpdateBlogCategoryData } from "@/lib/api/blog-categories"

interface BlogCategoryEditDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  category: BlogCategory | null
  onSubmit: (data: UpdateBlogCategoryData) => void
}

export function BlogCategoryEditDialog({ open, onOpenChange, category, onSubmit }: BlogCategoryEditDialogProps) {
  const [formData, setFormData] = useState({
    title: "",
    slug: "",
    is_active: true,
    is_frontend_show: true
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (category) {
      setFormData({
        title: category.title || "",
        slug: category.slug || "",
        is_active: category.is_active ?? true,
        is_frontend_show: category.is_frontend_show ?? true
      })
      setErrors({})
    }
  }, [category])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = "Title is required"
    } else if (formData.title.length < 2) {
      newErrors.title = "Title must be at least 2 characters"
    } else if (formData.title.length > 100) {
      newErrors.title = "Title must be less than 100 characters"
    }

    if (!formData.slug.trim()) {
      newErrors.slug = "Slug is required"
    } else if (!/^[a-z0-9-]+$/.test(formData.slug)) {
      newErrors.slug = "Slug can only contain lowercase letters, numbers, and hyphens"
    } else if (formData.slug.length < 2) {
      newErrors.slug = "Slug must be at least 2 characters"
    } else if (formData.slug.length > 100) {
      newErrors.slug = "Slug must be less than 100 characters"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      await onSubmit(formData)
    } catch (error) {
      console.error('Failed to update blog category:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTitleChange = (value: string) => {
    setFormData(prev => ({ ...prev, title: value }))
    
    // Clear title error when user starts typing
    if (errors.title) {
      setErrors(prev => ({ ...prev, title: "" }))
    }
  }

  const handleSlugChange = (value: string) => {
    // Convert to lowercase and replace spaces with hyphens
    const cleanSlug = value.toLowerCase().replace(/\s+/g, '-')
    setFormData(prev => ({ ...prev, slug: cleanSlug }))
    
    // Clear slug error when user starts typing
    if (errors.slug) {
      setErrors(prev => ({ ...prev, slug: "" }))
    }
  }

  const generateSlugFromTitle = () => {
    const newSlug = blogCategoryService.generateSlug(formData.title)
    setFormData(prev => ({ ...prev, slug: newSlug }))
    if (errors.slug) {
      setErrors(prev => ({ ...prev, slug: "" }))
    }
  }

  const handleClose = () => {
    setErrors({})
    onOpenChange(false)
  }

  if (!category) return null

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="w-[95vw] max-w-[900px] max-h-[95vh] rounded-2xl overflow-auto border-0 shadow-2xl bg-gradient-to-br from-background via-background to-muted/20 sm:mx-auto">
        <DialogHeader>
          <DialogTitle>Edit Blog Category</DialogTitle>
          <DialogDescription>
            Update the category information and settings.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleTitleChange(e.target.value)}
              placeholder="e.g., Technology, Lifestyle, Business"
              className={errors.title ? "border-destructive" : ""}
            />
            {errors.title && (
              <p className="text-sm text-destructive">{errors.title}</p>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="slug">Slug *</Label>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={generateSlugFromTitle}
                className="text-xs"
              >
                Generate from title
              </Button>
            </div>
            <Input
              id="slug"
              value={formData.slug}
              onChange={(e) => handleSlugChange(e.target.value)}
              placeholder="e.g., technology, lifestyle, business"
              className={errors.slug ? "border-destructive" : ""}
            />
            {errors.slug && (
              <p className="text-sm text-destructive">{errors.slug}</p>
            )}
            <p className="text-xs text-muted-foreground">
              URL-friendly version of the title. Only lowercase letters, numbers, and hyphens allowed.
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_active"
                checked={formData.is_active}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ ...prev, is_active: checked === true }))
                }
              />
              <Label htmlFor="is_active" className="text-sm font-normal">
                Active Category
              </Label>
            </div>
            <p className="text-xs text-muted-foreground ml-6">
              Active categories can be used for blog posts
            </p>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_frontend_show"
                checked={formData.is_frontend_show}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ ...prev, is_frontend_show: checked === true }))
                }
              />
              <Label htmlFor="is_frontend_show" className="text-sm font-normal">
                Show on Frontend
              </Label>
            </div>
            <p className="text-xs text-muted-foreground ml-6">
              Visible categories will appear in navigation and category lists
            </p>
          </div>

          {/* Category Info */}
          <div className="bg-muted p-4 rounded-lg space-y-2">
            <h4 className="text-sm font-medium">Category Information</h4>
            <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
              <div>
                <span className="font-medium">Created:</span>
                <br />
                {new Date(category.created_at).toLocaleDateString()}
              </div>
              <div>
                <span className="font-medium">Last Updated:</span>
                <br />
                {new Date(category.updated_at).toLocaleDateString()}
              </div>
            </div>
          </div>

          <DialogFooter className="flex flex-col sm:flex-row sm:justify-end space-x-2 ">
            <Button type="button" className="mb-2 sm:mb-0" variant="outline" onClick={handleClose} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" className="mb-2 sm:mb-0" disabled={loading}>
              {loading ? "Updating..." : "Update Category"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { toast } from "@/components/ui/use-toast"
import {
  Loader2,
  Sparkles,
  Send,
  Video,
  Scissors,
  RefreshCw,
  Download,
  Upload,
  Check,
  ImageIcon,
  FileText,
  Play,
  Edit,
  RotateCcw,
  ShoppingBag,
  Eye,
  Facebook,
  Twitter,
  Instagram,
  Youtube,
  <PERSON>edin,
  <PERSON><PERSON><PERSON>,
} from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

export function AIContentAssistant() {
  const [activeTab, setActiveTab] = useState("script")
  const [isGenerating, setIsGenerating] = useState(false)
  const [scriptPrompt, setScriptPrompt] = useState("")
  const [generatedScript, setGeneratedScript] = useState("")
  const [selectedTrend, setSelectedTrend] = useState("")
  const [selectedAvatar, setSelectedAvatar] = useState("")
  const [isExporting, setIsExporting] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [editProgress, setEditProgress] = useState(0)
  const [videoReady, setVideoReady] = useState(false)
  const [automationEnabled, setAutomationEnabled] = useState(true)
  const [pinterestApiKey, setPinterestApiKey] = useState("")
  const [showApiKeyInput, setShowApiKeyInput] = useState(false)
  const [videoTitle, setVideoTitle] = useState("Untitled Content")
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(["youtube", "instagram", "twitter", "facebook"])
  const [productPlacementOpen, setProductPlacementOpen] = useState(false)
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false)
  const [previewType, setPreviewType] = useState<"video" | "image" | "text">("video")
  const [previewPlatform, setPreviewPlatform] = useState("instagram")
  const [previewContent, setPreviewContent] = useState("")

  // Mock function to simulate API call to Poppy AI
  const generateScriptWithAI = async () => {
    if (!scriptPrompt && !selectedTrend) {
      toast({
        title: "Input Required",
        description: "Please enter a prompt or select a trending topic.",
        variant: "destructive",
      })
      return
    }

    setIsGenerating(true)

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 3000))

    const prompt = selectedTrend || scriptPrompt
    const avatarType = selectedAvatar || "travel"

    // Generate a mock script based on the prompt and avatar
    let script = ""

    if (avatarType === "travel") {
      script = `# ${prompt}\n\n[OPENING SHOT - Scenic landscape]\n\nHello travelers! Today we're exploring ${prompt}.\n\n[TRANSITION - Drone shot]\n\nLet me show you the top 5 must-see locations that most tourists miss.\n\n[LOCATION 1]\n\nFirst up is this hidden gem. The locals call it...\n\n[CONTINUE WITH LOCATIONS 2-5]\n\n[CLOSING]\n\nDon't forget to like and subscribe for more travel tips! See you on the next adventure.`
    } else if (avatarType === "fitness") {
      script = `# ${prompt} Workout Guide\n\n[OPENING - Energetic intro]\n\nHey fitness family! Today we're tackling ${prompt}.\n\n[DEMONSTRATION]\n\nI'll show you 5 exercises that will transform your routine.\n\n[EXERCISE 1]\n\nStart with this movement. Focus on form over speed.\n\n[CONTINUE WITH EXERCISES 2-5]\n\n[CLOSING]\n\nRemember, consistency is key! See you tomorrow for another workout.`
    } else {
      script = `# ${prompt}\n\n[OPENING]\n\nWelcome back to the channel! Today we're diving into ${prompt}.\n\n[MAIN CONTENT]\n\nLet's break this down into 3 key points you need to know.\n\n1. First point with detailed explanation\n2. Second point with examples\n3. Third point with actionable tips\n\n[CLOSING]\n\nIf you found this helpful, hit that like button and subscribe for more content like this!`
    }

    setGeneratedScript(script)
    setIsGenerating(false)
    setVideoTitle(prompt)

    toast({
      title: "Script Generated",
      description: "Your AI script is ready to review and edit.",
    })
  }

  // Mock function to simulate sending to LTX Studio
  const sendToLTXStudio = async () => {
    if (!generatedScript) {
      toast({
        title: "No Script Available",
        description: "Please generate a script first.",
        variant: "destructive",
      })
      return
    }

    setIsExporting(true)

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 2000))

    setIsExporting(false)
    setActiveTab("video")

    toast({
      title: "Script Sent to LTX Studio",
      description: "Your script has been successfully sent to LTX Studio for production.",
    })

    // Simulate video being ready after some time
    simulateVideoProduction()
  }

  // Mock function to simulate video production process
  const simulateVideoProduction = async () => {
    // Simulate video production taking some time
    await new Promise((resolve) => setTimeout(resolve, 5000))
    setVideoReady(true)
  }

  // Mock function to simulate video editing process
  const startVideoEditing = async () => {
    if (!videoReady) {
      toast({
        title: "Video Not Ready",
        description: "Please wait for the video to be ready from LTX Studio.",
        variant: "destructive",
      })
      return
    }

    setIsEditing(true)
    setEditProgress(0)

    // Simulate progress updates
    const interval = setInterval(() => {
      setEditProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsEditing(false)
          toast({
            title: "Video Editing Complete",
            description: "Your content has been repurposed and is ready for distribution.",
          })
          return 100
        }
        return prev + 10
      })
    }, 800)
  }

  const handlePlatformToggle = (platform: string) => {
    setSelectedPlatforms((current) =>
      current.includes(platform) ? current.filter((p) => p !== platform) : [...current, platform],
    )
  }

  const handlePinterestApiSubmit = () => {
    if (!pinterestApiKey) {
      toast({
        title: "API Key Required",
        description: "Please enter your Pinterest API key.",
        variant: "destructive",
      })
      return
    }

    toast({
      title: "Pinterest API Connected",
      description: "Your Pinterest API key has been saved and connected.",
    })

    setShowApiKeyInput(false)
    handlePlatformToggle("pinterest")
  }

  const openPreviewDialog = (type: "video" | "image" | "text", platform: string, content: string) => {
    setPreviewType(type)
    setPreviewPlatform(platform)
    setPreviewContent(content)
    setPreviewDialogOpen(true)
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "youtube":
        return <Youtube className="h-4 w-4" />
      case "instagram":
        return <Instagram className="h-4 w-4" />
      case "twitter":
        return <Twitter className="h-4 w-4" />
      case "facebook":
        return <Facebook className="h-4 w-4" />
      case "linkedin":
        return <Linkedin className="h-4 w-4" />
      case "pinterest":
        return <PenTool className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getPlatformName = (platform: string) => {
    switch (platform) {
      case "youtube":
        return "YouTube"
      case "instagram":
        return "Instagram"
      case "twitter":
        return "Twitter"
      case "facebook":
        return "Facebook"
      case "linkedin":
        return "LinkedIn"
      case "pinterest":
        return "Pinterest"
      default:
        return platform
    }
  }

  return (
    <Card className="col-span-full">
      <CardHeader>
        <CardTitle>AI Content Creation Assistant</CardTitle>
        <CardDescription>
          Generate scripts with AI, send to LTX Studio, and repurpose content - all in one place
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="script">Script Generation</TabsTrigger>
            <TabsTrigger value="video">Video Production</TabsTrigger>
            <TabsTrigger value="repurpose">Content Repurposing</TabsTrigger>
          </TabsList>

          <TabsContent value="script" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-1 space-y-4">
                <div className="space-y-2">
                  <Label>Select Trending Topic</Label>
                  <Select value={selectedTrend} onValueChange={setSelectedTrend}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a trending topic" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Sustainable Travel in 2023">Sustainable Travel in 2023</SelectItem>
                      <SelectItem value="Minimalist Home Office Setup">Minimalist Home Office Setup</SelectItem>
                      <SelectItem value="Gut Health Foods and Recipes">Gut Health Foods and Recipes</SelectItem>
                      <SelectItem value="HIIT Workouts for Busy People">HIIT Workouts for Busy People</SelectItem>
                      <SelectItem value="Passive Income Strategies">Passive Income Strategies</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Or Enter Custom Prompt</Label>
                  <Textarea
                    placeholder="Describe the content you want to create..."
                    value={scriptPrompt}
                    onChange={(e) => setScriptPrompt(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Select Avatar</Label>
                  <div className="grid grid-cols-5 gap-2">
                    {["travel", "home", "health", "fitness", "entrepreneur"].map((avatar) => (
                      <div key={avatar} className="flex flex-col items-center">
                        <Avatar
                          className={`h-12 w-12 cursor-pointer transition-all ${selectedAvatar === avatar ? "ring-2 ring-primary ring-offset-2" : ""}`}
                          onClick={() => setSelectedAvatar(avatar)}
                        >
                          <AvatarImage src="/placeholder-user.jpg" alt={`${avatar} Avatar`} />
                          <AvatarFallback>{avatar.substring(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <span className="text-xs mt-1 capitalize">{avatar}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Script Options</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="hook" defaultChecked />
                      <label htmlFor="hook" className="text-sm">
                        Include attention-grabbing hook
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="cta" defaultChecked />
                      <label htmlFor="cta" className="text-sm">
                        Include call-to-action
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="keywords" defaultChecked />
                      <label htmlFor="keywords" className="text-sm">
                        Optimize for SEO keywords
                      </label>
                    </div>
                  </div>
                </div>

                <Button onClick={generateScriptWithAI} disabled={isGenerating} className="w-full">
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating Script...
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" />
                      Generate Script with Poppy AI
                    </>
                  )}
                </Button>
              </div>

              <div className="md:col-span-2">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Generated Script</Label>
                    {generatedScript && (
                      <Button variant="outline" size="sm" onClick={sendToLTXStudio} disabled={isExporting}>
                        {isExporting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Send className="mr-2 h-4 w-4" />
                            Send to LTX Studio
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                  <div className="border rounded-md p-4 min-h-[400px] bg-muted/30">
                    {isGenerating ? (
                      <div className="flex flex-col items-center justify-center h-full space-y-4">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        <p className="text-sm text-muted-foreground">Poppy AI is crafting your script...</p>
                      </div>
                    ) : generatedScript ? (
                      <Textarea
                        value={generatedScript}
                        onChange={(e) => setGeneratedScript(e.target.value)}
                        className="min-h-[380px] bg-transparent border-0 focus-visible:ring-0 resize-none"
                      />
                    ) : (
                      <div className="flex flex-col items-center justify-center h-full space-y-4 text-muted-foreground">
                        <Sparkles className="h-12 w-12 opacity-20" />
                        <p className="text-center max-w-md">
                          Select a trending topic or enter a custom prompt, then click "Generate Script" to create
                          content with Poppy AI.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="video" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-1 space-y-4">
                <div className="space-y-2">
                  <Label>LTX Studio Status</Label>
                  <div className="border rounded-md p-4 space-y-4">
                    <div className="flex items-center space-x-2">
                      <Badge variant={videoReady ? "default" : "outline"}>
                        {videoReady ? "Video Ready" : "Processing"}
                      </Badge>
                      {!videoReady && <RefreshCw className="h-4 w-4 animate-spin" />}
                    </div>

                    <div className="space-y-1">
                      <div className="text-sm flex justify-between">
                        <span>Script Imported</span>
                        <Check className="h-4 w-4 text-green-500" />
                      </div>
                      <div className="text-sm flex justify-between">
                        <span>Voice Generation</span>
                        <Check className="h-4 w-4 text-green-500" />
                      </div>
                      <div className="text-sm flex justify-between">
                        <span>Video Production</span>
                        {videoReady ? (
                          <Check className="h-4 w-4 text-green-500" />
                        ) : (
                          <RefreshCw className="h-4 w-4 animate-spin" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Video Metadata</Label>
                  <div className="space-y-2">
                    <div className="space-y-1">
                      <Label htmlFor="video-title" className="text-xs">
                        Title
                      </Label>
                      <Input
                        id="video-title"
                        value={videoTitle}
                        onChange={(e) => setVideoTitle(e.target.value)}
                        placeholder="Enter video title"
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="video-description" className="text-xs">
                        Description
                      </Label>
                      <Textarea id="video-description" placeholder="Enter video description" className="min-h-[80px]" />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Video Settings</Label>
                  <div className="space-y-2">
                    <Select defaultValue="16:9">
                      <SelectTrigger>
                        <SelectValue placeholder="Aspect Ratio" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="16:9">16:9 - Landscape</SelectItem>
                        <SelectItem value="9:16">9:16 - Portrait</SelectItem>
                        <SelectItem value="1:1">1:1 - Square</SelectItem>
                        <SelectItem value="4:5">4:5 - Instagram</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select defaultValue="1080p">
                      <SelectTrigger>
                        <SelectValue placeholder="Resolution" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="720p">720p HD</SelectItem>
                        <SelectItem value="1080p">1080p Full HD</SelectItem>
                        <SelectItem value="4k">4K Ultra HD</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Button className="w-full" disabled={!videoReady} onClick={() => setActiveTab("repurpose")}>
                  <Video className="mr-2 h-4 w-4" />
                  {videoReady ? "Continue to Repurposing" : "Waiting for Video..."}
                </Button>
              </div>

              <div className="md:col-span-2">
                <div className="space-y-2">
                  <Label>Video Preview</Label>
                  <div className="border rounded-md aspect-video bg-black flex items-center justify-center">
                    {videoReady ? (
                      <div className="text-center">
                        <Video className="h-16 w-16 mx-auto mb-4 text-white/20" />
                        <p className="text-white">{videoTitle}</p>
                        <Button variant="outline" className="mt-4 bg-white/10 text-white hover:bg-white/20">
                          <Download className="mr-2 h-4 w-4" />
                          Download Preview
                        </Button>
                      </div>
                    ) : (
                      <div className="text-center">
                        <Loader2 className="h-16 w-16 mx-auto mb-4 animate-spin text-white/20" />
                        <p className="text-white">LTX Studio is processing your video...</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="repurpose" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-1 space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>Automation</Label>
                    <div className="flex items-center space-x-2">
                      <Switch checked={automationEnabled} onCheckedChange={setAutomationEnabled} id="automation-mode" />
                      <Label htmlFor="automation-mode" className="text-xs">
                        {automationEnabled ? "Auto" : "Manual"}
                      </Label>
                    </div>
                  </div>

                  <div className="border rounded-md p-3 bg-muted/30">
                    <div className="flex items-center space-x-2 text-sm">
                      {automationEnabled ? (
                        <>
                          <Badge
                            variant="outline"
                            className="bg-green-500/10 text-green-500 hover:bg-green-500/20 border-green-500/20"
                          >
                            Automation On
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            Content will be automatically processed and scheduled
                          </span>
                        </>
                      ) : (
                        <>
                          <Badge
                            variant="outline"
                            className="bg-amber-500/10 text-amber-500 hover:bg-amber-500/20 border-amber-500/20"
                          >
                            Manual Review
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            Content requires manual approval before publishing
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Platforms</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="youtube"
                        checked={selectedPlatforms.includes("youtube")}
                        onCheckedChange={() => handlePlatformToggle("youtube")}
                      />
                      <Label htmlFor="youtube" className="flex items-center gap-1 cursor-pointer">
                        <Youtube className="h-4 w-4" />
                        <span>YouTube</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="instagram"
                        checked={selectedPlatforms.includes("instagram")}
                        onCheckedChange={() => handlePlatformToggle("instagram")}
                      />
                      <Label htmlFor="instagram" className="flex items-center gap-1 cursor-pointer">
                        <Instagram className="h-4 w-4" />
                        <span>Instagram</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="twitter"
                        checked={selectedPlatforms.includes("twitter")}
                        onCheckedChange={() => handlePlatformToggle("twitter")}
                      />
                      <Label htmlFor="twitter" className="flex items-center gap-1 cursor-pointer">
                        <Twitter className="h-4 w-4" />
                        <span>Twitter</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="facebook"
                        checked={selectedPlatforms.includes("facebook")}
                        onCheckedChange={() => handlePlatformToggle("facebook")}
                      />
                      <Label htmlFor="facebook" className="flex items-center gap-1 cursor-pointer">
                        <Facebook className="h-4 w-4" />
                        <span>Facebook</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="linkedin"
                        checked={selectedPlatforms.includes("linkedin")}
                        onCheckedChange={() => handlePlatformToggle("linkedin")}
                      />
                      <Label htmlFor="linkedin" className="flex items-center gap-1 cursor-pointer">
                        <Linkedin className="h-4 w-4" />
                        <span>LinkedIn</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="pinterest"
                        checked={selectedPlatforms.includes("pinterest")}
                        onCheckedChange={() => {
                          if (!selectedPlatforms.includes("pinterest")) {
                            setShowApiKeyInput(true)
                          } else {
                            handlePlatformToggle("pinterest")
                          }
                        }}
                      />
                      <Label htmlFor="pinterest" className="flex items-center gap-1 cursor-pointer">
                        <PenTool className="h-4 w-4" />
                        <span>Pinterest + Blog</span>
                      </Label>
                    </div>

                    {showApiKeyInput && (
                      <div className="mt-2 p-3 border rounded-md bg-muted/30 space-y-2">
                        <Label htmlFor="pinterest-api" className="text-xs">
                          Pinterest API Key
                        </Label>
                        <div className="flex space-x-2">
                          <Input
                            id="pinterest-api"
                            type="password"
                            placeholder="Enter your Pinterest API key"
                            value={pinterestApiKey}
                            onChange={(e) => setPinterestApiKey(e.target.value)}
                            className="flex-1"
                          />
                          <Button size="sm" onClick={handlePinterestApiSubmit}>
                            Connect
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">Required for pingenerator.com integration</p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Repurposing Options</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="shorts" defaultChecked />
                      <label htmlFor="shorts" className="text-sm">
                        Create Short-form Clips (9:16)
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="highlights" defaultChecked />
                      <label htmlFor="highlights" className="text-sm">
                        Extract Key Highlights
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="audiogram" />
                      <label htmlFor="audiogram" className="text-sm">
                        Generate Audiogram
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="transcript" defaultChecked />
                      <label htmlFor="transcript" className="text-sm">
                        Create Transcript for Blog
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="pins" defaultChecked={selectedPlatforms.includes("pinterest")} />
                      <label htmlFor="pins" className="text-sm">
                        Generate Pinterest Pins
                      </label>
                    </div>
                  </div>
                </div>

                <Button
                  className="w-full"
                  onClick={startVideoEditing}
                  disabled={isEditing || !videoReady || selectedPlatforms.length === 0}
                >
                  {isEditing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Scissors className="mr-2 h-4 w-4" />
                      Start Repurposing
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => setProductPlacementOpen(true)}
                  disabled={isEditing || !videoReady}
                >
                  <ShoppingBag className="mr-2 h-4 w-4" />
                  Edit Product Placement
                </Button>
              </div>

              <div className="md:col-span-2">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Content Preview</Label>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">{videoTitle}</span>
                    </div>
                  </div>

                  {isEditing && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Processing content for selected platforms...</span>
                        <span>{editProgress}%</span>
                      </div>
                      <Progress value={editProgress} className="h-2" />
                    </div>
                  )}

                  <div className="border rounded-md p-4 space-y-4">
                    {editProgress === 100 ? (
                      <div className="space-y-6">
                        <div className="text-center pb-2">
                          <Check className="h-8 w-8 mx-auto mb-2 text-green-500" />
                          <h3 className="font-medium">Repurposing Complete!</h3>
                          <p className="text-sm text-muted-foreground">Your content has been successfully repurposed</p>
                        </div>

                        <Accordion type="single" collapsible className="w-full">
                          {selectedPlatforms.includes("youtube") && (
                            <AccordionItem value="youtube">
                              <AccordionTrigger className="hover:no-underline">
                                <div className="flex items-center space-x-2">
                                  <Youtube className="h-4 w-4" />
                                  <span>YouTube</span>
                                  <Badge variant="outline" className="ml-2">
                                    3 items
                                  </Badge>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="grid grid-cols-3 gap-3">
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-video bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "youtube", "Main Video")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">{videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Main Video • 10:24</div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-video bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "youtube", "Highlights")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Key Highlights: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Highlights • 3:45</div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-video bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "youtube", "Teaser")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Teaser: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Teaser • 0:45</div>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          )}

                          {selectedPlatforms.includes("instagram") && (
                            <AccordionItem value="instagram">
                              <AccordionTrigger className="hover:no-underline">
                                <div className="flex items-center space-x-2">
                                  <Instagram className="h-4 w-4" />
                                  <span>Instagram</span>
                                  <Badge variant="outline" className="ml-2">
                                    4 items
                                  </Badge>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="grid grid-cols-4 gap-3">
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-[9/16] bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "instagram", "Reel 1")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Reel 1</div>
                                    <div className="text-xs text-muted-foreground">0:30</div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-[9/16] bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "instagram", "Reel 2")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Reel 2</div>
                                    <div className="text-xs text-muted-foreground">0:30</div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-[9/16] bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "instagram", "Reel 3")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Reel 3</div>
                                    <div className="text-xs text-muted-foreground">0:30</div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-square bg-black rounded flex items-center justify-center relative group">
                                      <ImageIcon className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("image", "instagram", "Carousel")}
                                        >
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Carousel Post</div>
                                    <div className="text-xs text-muted-foreground">5 images</div>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          )}

                          {selectedPlatforms.includes("twitter") && (
                            <AccordionItem value="twitter">
                              <AccordionTrigger className="hover:no-underline">
                                <div className="flex items-center space-x-2">
                                  <Twitter className="h-4 w-4" />
                                  <span>Twitter</span>
                                  <Badge variant="outline" className="ml-2">
                                    3 items
                                  </Badge>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="grid grid-cols-3 gap-3">
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-video bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "twitter", "Clip")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Video Clip</div>
                                    <div className="text-xs text-muted-foreground">0:45</div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-[4/3] bg-black rounded flex items-center justify-center relative group">
                                      <ImageIcon className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("image", "twitter", "Image")}
                                        >
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Image Post</div>
                                    <div className="text-xs text-muted-foreground">With caption</div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="h-24 border rounded p-2 flex items-start overflow-hidden relative group">
                                      <FileText className="h-5 w-5 mr-2 flex-shrink-0 text-muted-foreground" />
                                      <div className="text-xs line-clamp-4">
                                        Check out our latest video on {videoTitle}! We cover everything you need to know
                                        about this topic, including key insights and actionable tips. #ContentCreation
                                        #DigitalMarketing
                                      </div>
                                      <div className="absolute inset-0 bg-white/80 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-black/10 hover:bg-black/20"
                                          onClick={() => openPreviewDialog("text", "twitter", "Thread")}
                                        >
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-black/10 hover:bg-black/20"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Thread</div>
                                    <div className="text-xs text-muted-foreground">5 tweets</div>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          )}

                          {selectedPlatforms.includes("facebook") && (
                            <AccordionItem value="facebook">
                              <AccordionTrigger className="hover:no-underline">
                                <div className="flex items-center space-x-2">
                                  <Facebook className="h-4 w-4" />
                                  <span>Facebook</span>
                                  <Badge variant="outline" className="ml-2">
                                    2 items
                                  </Badge>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="grid grid-cols-2 gap-3">
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-video bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "facebook", "Video")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Main Video</div>
                                    <div className="text-xs text-muted-foreground">5:30</div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="h-24 border rounded p-2 flex items-start overflow-hidden relative group">
                                      <FileText className="h-5 w-5 mr-2 flex-shrink-0 text-muted-foreground" />
                                      <div className="text-xs line-clamp-4">
                                        We're excited to share our latest video on {videoTitle}! In this comprehensive
                                        guide, we explore the key aspects of this topic and provide practical tips you
                                        can implement right away.
                                      </div>
                                      <div className="absolute inset-0 bg-white/80 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-black/10 hover:bg-black/20"
                                          onClick={() => openPreviewDialog("text", "facebook", "Post")}
                                        >
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-black/10 hover:bg-black/20"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Text Post</div>
                                    <div className="text-xs text-muted-foreground">With link</div>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          )}

                          {selectedPlatforms.includes("linkedin") && (
                            <AccordionItem value="linkedin">
                              <AccordionTrigger className="hover:no-underline">
                                <div className="flex items-center space-x-2">
                                  <Linkedin className="h-4 w-4" />
                                  <span>LinkedIn</span>
                                  <Badge variant="outline" className="ml-2">
                                    2 items
                                  </Badge>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="grid grid-cols-2 gap-3">
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-video bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "linkedin", "Video")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Professional Cut</div>
                                    <div className="text-xs text-muted-foreground">3:45</div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="h-24 border rounded p-2 flex items-start overflow-hidden relative group">
                                      <FileText className="h-5 w-5 mr-2 flex-shrink-0 text-muted-foreground" />
                                      <div className="text-xs line-clamp-4">
                                        I'm excited to share our latest insights on {videoTitle}. In this comprehensive
                                        analysis, we break down the key components and provide actionable strategies for
                                        professionals in this space.
                                      </div>
                                      <div className="absolute inset-0 bg-white/80 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-black/10 hover:bg-black/20"
                                          onClick={() => openPreviewDialog("text", "linkedin", "Article")}
                                        >
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-black/10 hover:bg-black/20"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Article</div>
                                    <div className="text-xs text-muted-foreground">Professional format</div>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          )}

                          {selectedPlatforms.includes("pinterest") && (
                            <AccordionItem value="pinterest">
                              <AccordionTrigger className="hover:no-underline">
                                <div className="flex items-center space-x-2">
                                  <PenTool className="h-4 w-4" />
                                  <span>Pinterest + Blog</span>
                                  <Badge variant="outline" className="ml-2">
                                    6 items
                                  </Badge>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="space-y-4">
                                  <div>
                                    <h4 className="text-sm font-medium mb-2">Pinterest Pins</h4>
                                    <div className="grid grid-cols-4 gap-3">
                                      {[1, 2, 3, 4].map((i) => (
                                        <div key={i} className="border rounded-md p-2 space-y-2">
                                          <div className="aspect-[2/3] bg-black rounded flex items-center justify-center relative group">
                                            <ImageIcon className="h-8 w-8 text-white/20" />
                                            <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                              <Button
                                                size="icon"
                                                variant="ghost"
                                                className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                                onClick={() => openPreviewDialog("image", "pinterest", `Pin ${i}`)}
                                              >
                                                <Eye className="h-4 w-4" />
                                              </Button>
                                              <Button
                                                size="icon"
                                                variant="ghost"
                                                className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                              >
                                                <Edit className="h-4 w-4" />
                                              </Button>
                                            </div>
                                          </div>
                                          <div className="text-xs font-medium truncate">Pin {i}</div>
                                          <div className="text-xs text-muted-foreground">
                                            Generated by pingenerator.com
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>

                                  <div>
                                    <h4 className="text-sm font-medium mb-2">Blog Post</h4>
                                    <div className="border rounded-md p-3 space-y-2 relative group">
                                      <div className="flex items-center justify-between">
                                        <h5 className="text-sm font-medium">{videoTitle}: Complete Guide</h5>
                                        <Badge variant="outline">Blog</Badge>
                                      </div>
                                      <div className="text-xs line-clamp-3">
                                        This comprehensive guide explores everything you need to know about {videoTitle}
                                        . From the basics to advanced techniques, we cover it all in this detailed blog
                                        post generated from our video content.
                                      </div>
                                      <div className="absolute inset-0 bg-white/80 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="sm"
                                          variant="outline"
                                          onClick={() => openPreviewDialog("text", "blog", "Full Blog Post")}
                                        >
                                          <Eye className="mr-2 h-4 w-4" />
                                          Preview Blog
                                        </Button>
                                        <Button size="sm" variant="outline">
                                          <Edit className="mr-2 h-4 w-4" />
                                          Edit
                                        </Button>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          )}
                        </Accordion>

                        <div className="flex justify-between pt-2">
                          <Button variant="outline" size="sm">
                            <Download className="mr-2 h-4 w-4" />
                            Download All
                          </Button>
                          <div className="space-x-2">
                            {!automationEnabled && (
                              <Button variant="outline" size="sm">
                                <Eye className="mr-2 h-4 w-4" />
                                Review All
                              </Button>
                            )}
                            <Button size="sm">
                              <Upload className="mr-2 h-4 w-4" />
                              {automationEnabled ? "Schedule Posts" : "Approve & Schedule"}
                            </Button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <Scissors className="h-12 w-12 mx-auto mb-4 text-muted-foreground/20" />
                        <p className="text-muted-foreground">
                          {isEditing
                            ? "Repurposing your content..."
                            : "Click 'Start Repurposing' to begin creating content variations"}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => setActiveTab(activeTab === "script" ? "script" : activeTab === "video" ? "script" : "video")}
        >
          Back
        </Button>
        <Button
          onClick={() =>
            setActiveTab(activeTab === "script" ? "video" : activeTab === "video" ? "repurpose" : "script")
          }
        >
          {activeTab === "repurpose" ? "Start New Project" : "Next Step"}
        </Button>
      </CardFooter>

      {/* Product Placement Dialog */}
      <Dialog open={productPlacementOpen} onOpenChange={setProductPlacementOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Product Placement</DialogTitle>
            <DialogDescription>
              Modify product placement in your content or regenerate scenes with different products.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="border rounded-md p-3">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium">Current Product Placements</h4>
                <Badge variant="outline">3 products</Badge>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between border-b pb-2">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-muted rounded flex items-center justify-center mr-3">
                      <ShoppingBag className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">Travel Backpack Pro</p>
                      <p className="text-xs text-muted-foreground">Scene: Opening shot</p>
                    </div>
                  </div>
                  <Button size="sm" variant="ghost">
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-muted rounded flex items-center justify-center mr-3">
                      <ShoppingBag className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">Eco Water Bottle</p>
                      <p className="text-xs text-muted-foreground">Scene: Location 2</p>
                    </div>
                  </div>
                  <Button size="sm" variant="ghost">
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-muted rounded flex items-center justify-center mr-3">
                      <ShoppingBag className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">Hiking Boots</p>
                      <p className="text-xs text-muted-foreground">Scene: Closing</p>
                    </div>
                  </div>
                  <Button size="sm" variant="ghost">
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Add New Product</Label>
              <div className="flex space-x-2">
                <Input placeholder="Product name" className="flex-1" />
                <Select defaultValue="opening">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select scene" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="opening">Opening Shot</SelectItem>
                    <SelectItem value="location1">Location 1</SelectItem>
                    <SelectItem value="location2">Location 2</SelectItem>
                    <SelectItem value="location3">Location 3</SelectItem>
                    <SelectItem value="closing">Closing</SelectItem>
                  </SelectContent>
                </Select>
                <Button>Add</Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Regenerate Scene</Label>
              <div className="flex space-x-2">
                <Select defaultValue="location2">
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="Select scene to regenerate" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="opening">Opening Shot</SelectItem>
                    <SelectItem value="location1">Location 1</SelectItem>
                    <SelectItem value="location2">Location 2</SelectItem>
                    <SelectItem value="location3">Location 3</SelectItem>
                    <SelectItem value="closing">Closing</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline">
                  <RotateCcw className="mr-2 h-4 w-4" />
                  Regenerate
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                This will create a new version of the selected scene with updated product placement.
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setProductPlacementOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                setProductPlacementOpen(false)
                toast({
                  title: "Product Placement Updated",
                  description: "Your content has been updated with the new product placements.",
                })
              }}
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Content Preview Dialog */}
      <Dialog open={previewDialogOpen} onOpenChange={setPreviewDialogOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {getPlatformIcon(previewPlatform)}
              <span>
                {getPlatformName(previewPlatform)}{" "}
                {previewType === "video" ? "Video" : previewType === "image" ? "Image" : "Content"} Preview
              </span>
            </DialogTitle>
            <DialogDescription>
              Preview how your content will appear on {getPlatformName(previewPlatform)}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {previewType === "video" && (
              <div className="aspect-video bg-black rounded-md flex items-center justify-center">
                <div className="text-center text-white">
                  <Play className="h-16 w-16 mx-auto mb-4 text-white/20" />
                  <p className="text-lg font-medium">{previewContent}</p>
                  <p className="text-sm text-white/70">Video preview for {getPlatformName(previewPlatform)}</p>
                </div>
              </div>
            )}

            {previewType === "image" && (
              <div
                className={`${previewPlatform === "pinterest" ? "aspect-[2/3]" : "aspect-square"} bg-black rounded-md flex items-center justify-center`}
              >
                <div className="text-center text-white">
                  <ImageIcon className="h-16 w-16 mx-auto mb-4 text-white/20" />
                  <p className="text-lg font-medium">{previewContent}</p>
                  <p className="text-sm text-white/70">Image preview for {getPlatformName(previewPlatform)}</p>
                </div>
              </div>
            )}

            {previewType === "text" && (
              <div className="border rounded-md p-4 max-h-[400px] overflow-y-auto">
                <h3 className="text-lg font-medium mb-2">{videoTitle}</h3>
                {previewPlatform === "blog" ? (
                  <div className="prose prose-sm max-w-none">
                    <h2>Introduction</h2>
                    <p>
                      Welcome to our comprehensive guide on {videoTitle}. In this article, we'll explore everything you
                      need to know about this fascinating topic, from basic concepts to advanced strategies.
                    </p>
                    <h2>What You'll Learn</h2>
                    <ul>
                      <li>The fundamentals of {videoTitle}</li>
                      <li>Key strategies for implementation</li>
                      <li>Common mistakes to avoid</li>
                      <li>Expert tips and tricks</li>
                      <li>Resources for further learning</li>
                    </ul>
                    <h2>Getting Started</h2>
                    <p>
                      Before diving deep into {videoTitle}, it's important to understand the basic principles. This
                      foundation will help you grasp the more complex concepts we'll cover later in this guide.
                    </p>
                    <p>
                      The concept of {videoTitle} has evolved significantly over the years. What started as a niche idea
                      has now become mainstream, with applications across various industries and domains.
                    </p>
                    <h2>Key Strategies</h2>
                    <p>When implementing {videoTitle} in your workflow, consider these proven strategies:</p>
                    <ol>
                      <li>
                        <strong>Start small and iterate</strong>: Begin with a minimal implementation and improve based
                        on feedback and results.
                      </li>
                      <li>
                        <strong>Focus on quality over quantity</strong>: It's better to have a few well-executed
                        elements than many mediocre ones.
                      </li>
                      <li>
                        <strong>Measure and analyze</strong>: Use data to inform your decisions and track progress over
                        time.
                      </li>
                    </ol>
                    <h2>Conclusion</h2>
                    <p>
                      By now, you should have a solid understanding of {videoTitle} and how to apply it effectively.
                      Remember that mastery comes with practice, so don't be afraid to experiment and learn from your
                      experiences.
                    </p>
                  </div>
                ) : (
                  <p className="text-sm">
                    {previewPlatform === "twitter" &&
                      "Check out our latest video on " +
                        videoTitle +
                        "! We cover everything you need to know about this topic, including key insights and actionable tips. #ContentCreation #DigitalMarketing"}
                    {previewPlatform === "facebook" &&
                      "We're excited to share our latest video on " +
                        videoTitle +
                        "! In this comprehensive guide, we explore the key aspects of this topic and provide practical tips you can implement right away."}
                    {previewPlatform === "linkedin" &&
                      "I'm excited to share our latest insights on " +
                        videoTitle +
                        ". In this comprehensive analysis, we break down the key components and provide actionable strategies for professionals in this space."}
                  </p>
                )}
              </div>
            )}

            <div className="space-y-2">
              <Label>Edit Caption/Description</Label>
              <Textarea
                placeholder="Enter caption or description"
                defaultValue={
                  previewPlatform === "twitter"
                    ? "Check out our latest video on " +
                      videoTitle +
                      "! We cover everything you need to know about this topic, including key insights and actionable tips. #ContentCreation #DigitalMarketing"
                    : previewPlatform === "facebook"
                      ? "We're excited to share our latest video on " +
                        videoTitle +
                        "! In this comprehensive guide, we explore the key aspects of this topic and provide practical tips you can implement right away."
                      : "I'm excited to share our latest insights on " +
                        videoTitle +
                        ". In this comprehensive analysis, we break down the key components and provide actionable strategies for professionals in this space."
                }
                className="min-h-[100px]"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setPreviewDialogOpen(false)}>
              Close
            </Button>
            <Button
              onClick={() => {
                setPreviewDialogOpen(false)
                toast({
                  title: "Content Updated",
                  description: "Your content has been updated with the new caption/description.",
                })
              }}
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar, Clock, Edit, Facebook, Instagram, Linkedin, MoreHorizontal, Twitter, Youtube } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export function ContentQueue() {
  return (
    <div className="space-y-5">
      <div className="flex items-center">
        <Avatar className="h-9 w-9">
          <AvatarImage src="/placeholder-user.jpg" alt="Avatar" />
          <AvatarFallback>TR</AvatarFallback>
        </Avatar>
        <div className="ml-4 space-y-1 flex-1">
          <div className="flex items-center">
            <p className="text-sm font-medium leading-none">Top 10 Travel Destinations for 2023</p>
            <Badge variant="outline" className="ml-2">
              Travel
            </Badge>
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="mr-1 h-3 w-3" />
            <span className="mr-2">May 6, 2023</span>
            <Clock className="mr-1 h-3 w-3" />
            <span>9:00 AM</span>
          </div>
        </div>
        <div className="flex items-center space-x-1">
          <Badge variant="secondary" className="flex items-center space-x-1">
            <Youtube className="h-3 w-3" />
            <span>Video</span>
          </Badge>
          <Badge variant="secondary" className="flex items-center space-x-1">
            <Instagram className="h-3 w-3" />
          </Badge>
          <Badge variant="secondary" className="flex items-center space-x-1">
            <Twitter className="h-3 w-3" />
          </Badge>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem>Reschedule</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">Delete</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="flex items-center">
        <Avatar className="h-9 w-9">
          <AvatarImage src="/placeholder-user.jpg" alt="Avatar" />
          <AvatarFallback>HM</AvatarFallback>
        </Avatar>
        <div className="ml-4 space-y-1 flex-1">
          <div className="flex items-center">
            <p className="text-sm font-medium leading-none">Minimalist Home Office Setup Guide</p>
            <Badge variant="outline" className="ml-2">
              Home
            </Badge>
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="mr-1 h-3 w-3" />
            <span className="mr-2">May 7, 2023</span>
            <Clock className="mr-1 h-3 w-3" />
            <span>10:30 AM</span>
          </div>
        </div>
        <div className="flex items-center space-x-1">
          <Badge variant="secondary" className="flex items-center space-x-1">
            <Youtube className="h-3 w-3" />
            <span>Video</span>
          </Badge>
          <Badge variant="secondary" className="flex items-center space-x-1">
            <Instagram className="h-3 w-3" />
          </Badge>
          <Badge variant="secondary" className="flex items-center space-x-1">
            <Facebook className="h-3 w-3" />
          </Badge>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem>Reschedule</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">Delete</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="flex items-center">
        <Avatar className="h-9 w-9">
          <AvatarImage src="/placeholder-user.jpg" alt="Avatar" />
          <AvatarFallback>HE</AvatarFallback>
        </Avatar>
        <div className="ml-4 space-y-1 flex-1">
          <div className="flex items-center">
            <p className="text-sm font-medium leading-none">5 Superfoods You Should Eat Daily</p>
            <Badge variant="outline" className="ml-2">
              Health
            </Badge>
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="mr-1 h-3 w-3" />
            <span className="mr-2">May 8, 2023</span>
            <Clock className="mr-1 h-3 w-3" />
            <span>8:15 AM</span>
          </div>
        </div>
        <div className="flex items-center space-x-1">
          <Badge variant="secondary" className="flex items-center space-x-1">
            <Youtube className="h-3 w-3" />
            <span>Video</span>
          </Badge>
          <Badge variant="secondary" className="flex items-center space-x-1">
            <Instagram className="h-3 w-3" />
          </Badge>
          <Badge variant="secondary" className="flex items-center space-x-1">
            <Linkedin className="h-3 w-3" />
          </Badge>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem>Reschedule</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">Delete</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  )
}

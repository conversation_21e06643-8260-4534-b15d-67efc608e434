"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Download, RefreshCw } from "lucide-react"

interface EngagementHeatmapProps {
  selectedAvatar: string
}

export function EngagementHeatmap({ selectedAvatar }: EngagementHeatmapProps) {
  const [timeRange, setTimeRange] = useState("30d")
  const [metricType, setMetricType] = useState("opens")
  const [activeTab, setActiveTab] = useState("daily")

  // Get color for heatmap cell based on value
  const getHeatmapColor = (value: number, max: number) => {
    const percentage = value / max

    if (percentage < 0.2) return "bg-sage-50 text-sage-700"
    if (percentage < 0.4) return "bg-sage-100 text-sage-700"
    if (percentage < 0.6) return "bg-sage-200 text-sage-700"
    if (percentage < 0.8) return "bg-sage-300 text-sage-700"
    return "bg-sage-400 text-sage-800"
  }

  // Filter heatmap data based on selected avatar
  const getFilteredHeatmapData = () => {
    if (selectedAvatar === "all") {
      return heatmapData
    }

    return heatmapData.filter((item) => item.avatar === selectedAvatar)
  }

  // Get maximum value for scaling
  const getMaxValue = () => {
    const filteredData = getFilteredHeatmapData()

    if (metricType === "opens") {
      return Math.max(...filteredData.map((item) => Math.max(...item.openRates)))
    } else {
      return Math.max(...filteredData.map((item) => Math.max(...item.clickRates)))
    }
  }

  // Get avatar badge color
  const getAvatarBadgeColor = (avatar: string) => {
    switch (avatar) {
      case "travel":
        return "bg-blue-100 text-blue-800"
      case "home":
        return "bg-green-100 text-green-800"
      case "health":
        return "bg-purple-100 text-purple-800"
      case "fitness":
        return "bg-red-100 text-red-800"
      case "entrepreneur":
        return "bg-amber-100 text-amber-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Engagement Heatmap</CardTitle>
            <CardDescription>Visualize open and click rates by day and time</CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Select time range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="12m">Last 12 months</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon">
              <RefreshCw className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-[400px]">
              <TabsList>
                <TabsTrigger value="daily">Daily</TabsTrigger>
                <TabsTrigger value="weekly">Weekly</TabsTrigger>
                <TabsTrigger value="monthly">Monthly</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="flex items-center space-x-2">
              <Select value={metricType} onValueChange={setMetricType}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Select metric" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="opens">Open Rates</SelectItem>
                  <SelectItem value="clicks">Click Rates</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <TabsContent value="daily" className="mt-0">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Daily Engagement Patterns</CardTitle>
                <CardDescription>
                  {metricType === "opens" ? "Email open" : "Email click"} rates by hour of day
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {getFilteredHeatmapData().map((item) => (
                    <div key={item.id} className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className={`capitalize ${getAvatarBadgeColor(item.avatar)}`}>
                          {item.avatar}
                        </Badge>
                        <span className="text-sm font-medium">{item.name}</span>
                      </div>

                      <div className="grid grid-cols-24 gap-1">
                        {Array.from({ length: 24 }).map((_, index) => (
                          <div
                            key={index}
                            className={`h-12 flex items-center justify-center rounded-md ${getHeatmapColor(
                              metricType === "opens" ? item.openRates[index] : item.clickRates[index],
                              getMaxValue(),
                            )}`}
                          >
                            <div className="text-xs font-medium">
                              {metricType === "opens" ? item.openRates[index] : item.clickRates[index]}%
                            </div>
                          </div>
                        ))}
                      </div>

                      <div className="grid grid-cols-24 gap-1">
                        {Array.from({ length: 24 }).map((_, index) => (
                          <div key={index} className="text-center">
                            <span className="text-xs text-muted-foreground">
                              {index === 0
                                ? "12am"
                                : index === 12
                                  ? "12pm"
                                  : index > 12
                                    ? `${index - 12}pm`
                                    : `${index}am`}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="weekly" className="mt-0">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Weekly Engagement Patterns</CardTitle>
                <CardDescription>
                  {metricType === "opens" ? "Email open" : "Email click"} rates by day of week
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {getFilteredHeatmapData().map((item) => (
                    <div key={item.id} className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className={`capitalize ${getAvatarBadgeColor(item.avatar)}`}>
                          {item.avatar}
                        </Badge>
                        <span className="text-sm font-medium">{item.name}</span>
                      </div>

                      <div className="grid grid-cols-7 gap-2">
                        {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day, index) => (
                          <div
                            key={index}
                            className={`h-20 flex flex-col items-center justify-center rounded-md ${getHeatmapColor(
                              metricType === "opens" ? item.weeklyOpenRates[index] : item.weeklyClickRates[index],
                              getMaxValue(),
                            )}`}
                          >
                            <div className="text-sm font-medium mb-1">
                              {metricType === "opens" ? item.weeklyOpenRates[index] : item.weeklyClickRates[index]}%
                            </div>
                            <div className="text-xs">{day}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="monthly" className="mt-0">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Monthly Engagement Patterns</CardTitle>
                <CardDescription>
                  {metricType === "opens" ? "Email open" : "Email click"} rates by month
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {getFilteredHeatmapData().map((item) => (
                    <div key={item.id} className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className={`capitalize ${getAvatarBadgeColor(item.avatar)}`}>
                          {item.avatar}
                        </Badge>
                        <span className="text-sm font-medium">{item.name}</span>
                      </div>

                      <div className="grid grid-cols-12 gap-2">
                        {["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"].map(
                          (month, index) => (
                            <div
                              key={index}
                              className={`h-20 flex flex-col items-center justify-center rounded-md ${getHeatmapColor(
                                metricType === "opens" ? item.monthlyOpenRates[index] : item.monthlyClickRates[index],
                                getMaxValue(),
                              )}`}
                            >
                              <div className="text-sm font-medium mb-1">
                                {metricType === "opens" ? item.monthlyOpenRates[index] : item.monthlyClickRates[index]}%
                              </div>
                              <div className="text-xs">{month}</div>
                            </div>
                          ),
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </CardContent>
    </Card>
  )
}

// Sample heatmap data
const heatmapData = [
  {
    id: "heatmap-1",
    name: "Travel Newsletter",
    avatar: "travel",
    // Hourly open rates (24 hours)
    openRates: [
      4.2, 3.1, 2.5, 1.8, 2.3, 5.7, 8.9, 15.6, 28.4, 32.1, 27.5, 24.8, 22.3, 19.7, 18.2, 17.5, 16.9, 18.4, 21.6, 24.3,
      19.8, 14.5, 9.2, 6.1,
    ],
    // Hourly click rates (24 hours)
    clickRates: [
      1.1, 0.8, 0.6, 0.4, 0.5, 1.2, 2.3, 4.1, 7.8, 8.9, 7.6, 6.9, 6.2, 5.4, 5.0, 4.8, 4.7, 5.1, 6.0, 6.7, 5.5, 4.0, 2.5,
      1.7,
    ],
    // Weekly open rates (7 days)
    weeklyOpenRates: [19.8, 24.5, 26.7, 25.3, 27.1, 28.4, 22.6],
    // Weekly click rates (7 days)
    weeklyClickRates: [5.5, 6.8, 7.4, 7.0, 7.5, 7.9, 6.3],
    // Monthly open rates (12 months)
    monthlyOpenRates: [23.4, 24.1, 25.7, 26.8, 27.9, 28.5, 29.1, 28.7, 27.5, 26.2, 25.1, 24.3],
    // Monthly click rates (12 months)
    monthlyClickRates: [6.5, 6.7, 7.1, 7.4, 7.7, 7.9, 8.1, 8.0, 7.6, 7.3, 7.0, 6.7],
  },
  {
    id: "heatmap-2",
    name: "Fitness Updates",
    avatar: "fitness",
    // Hourly open rates (24 hours)
    openRates: [
      3.8, 2.7, 2.1, 1.5, 1.9, 4.8, 9.7, 18.3, 31.5, 35.2, 30.1, 27.4, 24.6, 21.8, 20.1, 19.3, 18.6, 20.2, 23.8, 26.7,
      21.7, 15.9, 10.1, 6.7,
    ],
    // Hourly click rates (24 hours)
    clickRates: [
      1.0, 0.7, 0.5, 0.4, 0.5, 1.3, 2.7, 5.1, 8.7, 9.8, 8.3, 7.6, 6.8, 6.0, 5.6, 5.3, 5.2, 5.6, 6.6, 7.4, 6.0, 4.4, 2.8,
      1.9,
    ],
    // Weekly open rates (7 days)
    weeklyOpenRates: [21.7, 26.9, 29.4, 27.8, 29.8, 31.2, 24.9],
    // Weekly click rates (7 days)
    weeklyClickRates: [6.0, 7.5, 8.1, 7.7, 8.3, 8.7, 6.9],
    // Monthly open rates (12 months)
    monthlyOpenRates: [25.7, 26.5, 28.3, 29.5, 30.7, 31.4, 32.0, 31.6, 30.3, 28.8, 27.6, 26.7],
    // Monthly click rates (12 months)
    monthlyClickRates: [7.1, 7.3, 7.8, 8.2, 8.5, 8.7, 8.9, 8.8, 8.4, 8.0, 7.7, 7.4],
  },
  {
    id: "heatmap-3",
    name: "Health Newsletter",
    avatar: "health",
    // Hourly open rates (24 hours)
    openRates: [
      4.5, 3.3, 2.7, 2.0, 2.5, 6.2, 9.6, 16.9, 30.7, 34.8, 29.8, 26.9, 24.2, 21.4, 19.7, 19.0, 18.3, 19.9, 23.4, 26.3,
      21.4, 15.7, 10.0, 6.6,
    ],
    // Hourly click rates (24 hours)
    clickRates: [
      1.2, 0.9, 0.7, 0.5, 0.7, 1.7, 2.7, 4.7, 8.5, 9.6, 8.2, 7.4, 6.7, 5.9, 5.4, 5.2, 5.1, 5.5, 6.5, 7.3, 5.9, 4.3, 2.8,
      1.8,
    ],
    // Weekly open rates (7 days)
    weeklyOpenRates: [21.4, 26.5, 28.9, 27.4, 29.3, 30.7, 24.5],
    // Weekly click rates (7 days)
    weeklyClickRates: [5.9, 7.3, 8.0, 7.6, 8.1, 8.5, 6.8],
    // Monthly open rates (12 months)
    monthlyOpenRates: [25.3, 26.1, 27.8, 29.0, 30.2, 30.9, 31.5, 31.1, 29.8, 28.4, 27.2, 26.3],
    // Monthly click rates (12 months)
    monthlyClickRates: [7.0, 7.2, 7.7, 8.0, 8.4, 8.6, 8.7, 8.6, 8.2, 7.9, 7.5, 7.3],
  },
  {
    id: "heatmap-4",
    name: "Home Decor Tips",
    avatar: "home",
    // Hourly open rates (24 hours)
    openRates: [
      4.0, 2.9, 2.3, 1.7, 2.1, 5.3, 8.3, 14.5, 26.3, 29.8, 25.5, 23.0, 20.7, 18.3, 16.9, 16.2, 15.7, 17.1, 20.0, 22.5,
      18.3, 13.4, 8.5, 5.6,
    ],
    // Hourly click rates (24 hours)
    clickRates: [
      1.1, 0.8, 0.6, 0.5, 0.6, 1.5, 2.3, 4.0, 7.3, 8.2, 7.1, 6.4, 5.7, 5.1, 4.7, 4.5, 4.3, 4.7, 5.5, 6.2, 5.1, 3.7, 2.4,
      1.6,
    ],
    // Weekly open rates (7 days)
    weeklyOpenRates: [18.3, 22.7, 24.7, 23.4, 25.1, 26.3, 20.9],
    // Weekly click rates (7 days)
    weeklyClickRates: [5.1, 6.3, 6.8, 6.5, 6.9, 7.3, 5.8],
    // Monthly open rates (12 months)
    monthlyOpenRates: [21.7, 22.3, 23.8, 24.8, 25.8, 26.4, 26.9, 26.6, 25.5, 24.3, 23.2, 22.5],
    // Monthly click rates (12 months)
    monthlyClickRates: [6.0, 6.2, 6.6, 6.9, 7.1, 7.3, 7.5, 7.4, 7.1, 6.7, 6.4, 6.2],
  },
  {
    id: "heatmap-5",
    name: "Entrepreneur Insights",
    avatar: "entrepreneur",
    // Hourly open rates (24 hours)
    openRates: [
      4.7, 3.5, 2.8, 2.1, 2.6, 6.4, 10.0, 17.6, 32.0, 36.3, 31.1, 28.0, 25.2, 22.3, 20.5, 19.8, 19.1, 20.7, 24.4, 27.4,
      22.3, 16.4, 10.4, 6.9,
    ],
    // Hourly click rates (24 hours)
    clickRates: [
      1.3, 1.0, 0.8, 0.6, 0.7, 1.8, 2.8, 4.9, 8.9, 10.0, 8.6, 7.7, 7.0, 6.2, 5.7, 5.5, 5.3, 5.7, 6.8, 7.6, 6.2, 4.5,
      2.9, 1.9,
    ],
    // Weekly open rates (7 days)
    weeklyOpenRates: [22.3, 27.6, 30.1, 28.5, 30.5, 31.9, 25.5],
    // Weekly click rates (7 days)
    weeklyClickRates: [6.2, 7.6, 8.3, 7.9, 8.4, 8.8, 7.1],
    // Monthly open rates (12 months)
    monthlyOpenRates: [26.4, 27.2, 29.0, 30.3, 31.5, 32.2, 32.8, 32.4, 31.0, 29.5, 28.3, 27.4],
    // Monthly click rates (12 months)
    monthlyClickRates: [7.3, 7.5, 8.0, 8.4, 8.7, 8.9, 9.1, 9.0, 8.6, 8.2, 7.8, 7.6],
  },
]

"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { toast } from "@/components/ui/use-toast"
import {
  Loader2,
  Sparkles,
  Send,
  Video,
  Scissors,
  RefreshCw,
  Download,
  Check,
  ImageIcon,
  FileText,
  Play,
  Edit,
  RotateCcw,
  ShoppingBag,
  Eye,
  Facebook,
  Twitter,
  Instagram,
  Youtube,
  Linkedin,
  PenTool,
  Layers,
  QrCode,
  LinkIcon,
} from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

export function AIContentAssistantComplementary() {
  // All the existing state variables from the original component
  const [activeTab, setActiveTab] = useState("script")
  const [isGenerating, setIsGenerating] = useState(false)
  const [scriptPrompt, setScriptPrompt] = useState("")
  const [generatedScript, setGeneratedScript] = useState("")
  const [selectedTrend, setSelectedTrend] = useState("")
  const [selectedAvatar, setSelectedAvatar] = useState("")
  const [isExporting, setIsExporting] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [editProgress, setEditProgress] = useState(0)
  const [videoReady, setVideoReady] = useState(false)
  const [automationEnabled, setAutomationEnabled] = useState(true)
  const [pinterestApiKey, setPinterestApiKey] = useState("")
  const [showApiKeyInput, setShowApiKeyInput] = useState(false)
  const [videoTitle, setVideoTitle] = useState("Untitled Content")
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(["youtube", "instagram", "twitter", "facebook"])
  const [productPlacementOpen, setProductPlacementOpen] = useState(false)
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false)
  const [previewType, setPreviewType] = useState<"video" | "image" | "text">("video")
  const [previewPlatform, setPreviewPlatform] = useState("instagram")
  const [previewContent, setPreviewContent] = useState("")

  // New state variables for complementary products
  const [selectedProduct, setSelectedProduct] = useState<string>("Premium Travel Backpack")
  const [complementaryProductsOpen, setComplementaryProductsOpen] = useState(false)
  const [selectedComplementaryProducts, setSelectedComplementaryProducts] = useState<string[]>([
    "Travel Packing Cubes",
    "Compact Travel Adapter",
    "Waterproof Toiletry Bag",
  ])
  const [qrCodePreviewOpen, setQrCodePreviewOpen] = useState(false)
  const [linkPreviewOpen, setLinkPreviewOpen] = useState(false)

  // All the existing functions from the original component
  // ... (keeping the same functions as in the original component)

  // New functions for complementary products
  const handleProductChange = (product: string) => {
    setSelectedProduct(product)
    // Update complementary products based on selected product
    switch (product) {
      case "Premium Travel Backpack":
        setSelectedComplementaryProducts(["Travel Packing Cubes", "Compact Travel Adapter", "Waterproof Toiletry Bag"])
        break
      case "Smart Home Lighting Kit":
        setSelectedComplementaryProducts(["Smart Motion Sensor", "Voice Control Hub", "Smart Power Strip"])
        break
      case "Organic Superfood Blend":
        setSelectedComplementaryProducts(["Stainless Steel Shaker", "Digital Nutrition Scale", "Recipe eBook"])
        break
      case "Adjustable Kettlebell Set":
        setSelectedComplementaryProducts(["Workout Mat", "Resistance Bands Set", "Fitness Tracker"])
        break
      case "Business Productivity Course":
        setSelectedComplementaryProducts([
          "Premium Planner System",
          "Goal Setting Workbook",
          "Time Management Masterclass",
        ])
        break
      default:
        setSelectedComplementaryProducts([])
    }
  }

  const openComplementaryProductsDialog = () => {
    setComplementaryProductsOpen(true)
  }

  const saveComplementaryProducts = () => {
    setComplementaryProductsOpen(false)
    toast({
      title: "Complementary Products Updated",
      description: "Your content will include the selected complementary products.",
    })
  }

  const openQrCodePreview = () => {
    setQrCodePreviewOpen(true)
  }

  const openLinkPreview = () => {
    setLinkPreviewOpen(true)
  }

  // Mock function to simulate video editing process
  const startVideoEditing = async () => {
    if (!videoReady) {
      toast({
        title: "Video Not Ready",
        description: "Please wait for the video to be ready from LTX Studio.",
        variant: "destructive",
      })
      return
    }

    setIsEditing(true)
    setEditProgress(0)

    // Simulate progress updates
    const interval = setInterval(() => {
      setEditProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsEditing(false)
          toast({
            title: "Video Editing Complete",
            description: "Your content has been repurposed and is ready for distribution.",
          })
          return 100
        }
        return prev + 10
      })
    }, 800)
  }

  const handlePlatformToggle = (platform: string) => {
    setSelectedPlatforms((current) =>
      current.includes(platform) ? current.filter((p) => p !== platform) : [...current, platform],
    )
  }

  const handlePinterestApiSubmit = () => {
    if (!pinterestApiKey) {
      toast({
        title: "API Key Required",
        description: "Please enter your Pinterest API key.",
        variant: "destructive",
      })
      return
    }

    toast({
      title: "Pinterest API Connected",
      description: "Your Pinterest API key has been saved and connected.",
    })

    setShowApiKeyInput(false)
    handlePlatformToggle("pinterest")
  }

  const openPreviewDialog = (type: "video" | "image" | "text", platform: string, content: string) => {
    setPreviewType(type)
    setPreviewPlatform(platform)
    setPreviewContent(content)
    setPreviewDialogOpen(true)
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "youtube":
        return <Youtube className="h-4 w-4" />
      case "instagram":
        return <Instagram className="h-4 w-4" />
      case "twitter":
        return <Twitter className="h-4 w-4" />
      case "facebook":
        return <Facebook className="h-4 w-4" />
      case "linkedin":
        return <Linkedin className="h-4 w-4" />
      case "pinterest":
        return <PenTool className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getPlatformName = (platform: string) => {
    switch (platform) {
      case "youtube":
        return "YouTube"
      case "instagram":
        return "Instagram"
      case "twitter":
        return "Twitter"
      case "facebook":
        return "Facebook"
      case "linkedin":
        return "LinkedIn"
      case "pinterest":
        return "Pinterest"
      default:
        return platform
    }
  }

  return (
    <Card className="col-span-full">
      <CardHeader>
        <CardTitle>AI Content Creation Assistant</CardTitle>
        <CardDescription>
          Generate scripts with AI, send to LTX Studio, and repurpose content - all in one place
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="script">Script Generation</TabsTrigger>
            <TabsTrigger value="video">Video Production</TabsTrigger>
            <TabsTrigger value="repurpose">Content Repurposing</TabsTrigger>
          </TabsList>

          <TabsContent value="script" className="space-y-4">
            {/* Script Generation Tab Content - Same as original component */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-1 space-y-4">
                <div className="space-y-2">
                  <Label>Select Trending Topic</Label>
                  <Select value={selectedTrend} onValueChange={setSelectedTrend}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a trending topic" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Sustainable Travel in 2023">Sustainable Travel in 2023</SelectItem>
                      <SelectItem value="Minimalist Home Office Setup">Minimalist Home Office Setup</SelectItem>
                      <SelectItem value="Gut Health Foods and Recipes">Gut Health Foods and Recipes</SelectItem>
                      <SelectItem value="HIIT Workouts for Busy People">HIIT Workouts for Busy People</SelectItem>
                      <SelectItem value="Passive Income Strategies">Passive Income Strategies</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Or Enter Custom Prompt</Label>
                  <Textarea
                    placeholder="Describe the content you want to create..."
                    value={scriptPrompt}
                    onChange={(e) => setScriptPrompt(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Select Avatar</Label>
                  <div className="grid grid-cols-5 gap-2">
                    {["travel", "home", "health", "fitness", "entrepreneur"].map((avatar) => (
                      <div key={avatar} className="flex flex-col items-center">
                        <Avatar
                          className={`h-12 w-12 cursor-pointer transition-all ${selectedAvatar === avatar ? "ring-2 ring-primary ring-offset-2" : ""}`}
                          onClick={() => setSelectedAvatar(avatar)}
                        >
                          <AvatarImage src="/placeholder-user.jpg" alt={`${avatar} Avatar`} />
                          <AvatarFallback>{avatar.substring(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <span className="text-xs mt-1 capitalize">{avatar}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Script Options</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="hook" defaultChecked />
                      <label htmlFor="hook" className="text-sm">
                        Include attention-grabbing hook
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="cta" defaultChecked />
                      <label htmlFor="cta" className="text-sm">
                        Include call-to-action
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="keywords" defaultChecked />
                      <label htmlFor="keywords" className="text-sm">
                        Optimize for SEO keywords
                      </label>
                    </div>
                  </div>
                </div>

                <Button onClick={() => {}} disabled={isGenerating} className="w-full">
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating Script...
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" />
                      Generate Script with Poppy AI
                    </>
                  )}
                </Button>
              </div>

              <div className="md:col-span-2">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Generated Script</Label>
                    {generatedScript && (
                      <Button variant="outline" size="sm" onClick={() => {}} disabled={isExporting}>
                        {isExporting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Send className="mr-2 h-4 w-4" />
                            Send to LTX Studio
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                  <div className="border rounded-md p-4 min-h-[400px] bg-muted/30">
                    {isGenerating ? (
                      <div className="flex flex-col items-center justify-center h-full space-y-4">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        <p className="text-sm text-muted-foreground">Poppy AI is crafting your script...</p>
                      </div>
                    ) : generatedScript ? (
                      <Textarea
                        value={generatedScript}
                        onChange={(e) => setGeneratedScript(e.target.value)}
                        className="min-h-[380px] bg-transparent border-0 focus-visible:ring-0 resize-none"
                      />
                    ) : (
                      <div className="flex flex-col items-center justify-center h-full space-y-4 text-muted-foreground">
                        <Sparkles className="h-12 w-12 opacity-20" />
                        <p className="text-center max-w-md">
                          Select a trending topic or enter a custom prompt, then click "Generate Script" to create
                          content with Poppy AI.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="video" className="space-y-4">
            {/* Video Production Tab Content - Same as original component */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-1 space-y-4">
                <div className="space-y-2">
                  <Label>LTX Studio Status</Label>
                  <div className="border rounded-md p-4 space-y-4">
                    <div className="flex items-center space-x-2">
                      <Badge variant={videoReady ? "default" : "outline"}>
                        {videoReady ? "Video Ready" : "Processing"}
                      </Badge>
                      {!videoReady && <RefreshCw className="h-4 w-4 animate-spin" />}
                    </div>

                    <div className="space-y-1">
                      <div className="text-sm flex justify-between">
                        <span>Script Imported</span>
                        <Check className="h-4 w-4 text-green-500" />
                      </div>
                      <div className="text-sm flex justify-between">
                        <span>Voice Generation</span>
                        <Check className="h-4 w-4 text-green-500" />
                      </div>
                      <div className="text-sm flex justify-between">
                        <span>Video Production</span>
                        {videoReady ? (
                          <Check className="h-4 w-4 text-green-500" />
                        ) : (
                          <RefreshCw className="h-4 w-4 animate-spin" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Video Metadata</Label>
                  <div className="space-y-2">
                    <div className="space-y-1">
                      <Label htmlFor="video-title" className="text-xs">
                        Title
                      </Label>
                      <Input
                        id="video-title"
                        value={videoTitle}
                        onChange={(e) => setVideoTitle(e.target.value)}
                        placeholder="Enter video title"
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="video-description" className="text-xs">
                        Description
                      </Label>
                      <Textarea id="video-description" placeholder="Enter video description" className="min-h-[80px]" />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Video Settings</Label>
                  <div className="space-y-2">
                    <Select defaultValue="16:9">
                      <SelectTrigger>
                        <SelectValue placeholder="Aspect Ratio" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="16:9">16:9 - Landscape</SelectItem>
                        <SelectItem value="9:16">9:16 - Portrait</SelectItem>
                        <SelectItem value="1:1">1:1 - Square</SelectItem>
                        <SelectItem value="4:5">4:5 - Instagram</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select defaultValue="1080p">
                      <SelectTrigger>
                        <SelectValue placeholder="Resolution" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="720p">720p HD</SelectItem>
                        <SelectItem value="1080p">1080p Full HD</SelectItem>
                        <SelectItem value="4k">4K Ultra HD</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Button className="w-full" disabled={!videoReady} onClick={() => setActiveTab("repurpose")}>
                  <Video className="mr-2 h-4 w-4" />
                  {videoReady ? "Continue to Repurposing" : "Waiting for Video..."}
                </Button>
              </div>

              <div className="md:col-span-2">
                <div className="space-y-2">
                  <Label>Video Preview</Label>
                  <div className="border rounded-md aspect-video bg-black flex items-center justify-center">
                    {videoReady ? (
                      <div className="text-center">
                        <Video className="h-16 w-16 mx-auto mb-4 text-white/20" />
                        <p className="text-white">{videoTitle}</p>
                        <Button variant="outline" className="mt-4 bg-white/10 text-white hover:bg-white/20">
                          <Download className="mr-2 h-4 w-4" />
                          Download Preview
                        </Button>
                      </div>
                    ) : (
                      <div className="text-center">
                        <Loader2 className="h-16 w-16 mx-auto mb-4 animate-spin text-white/20" />
                        <p className="text-white">LTX Studio is processing your video...</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="repurpose" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-1 space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>Automation</Label>
                    <div className="flex items-center space-x-2">
                      <Switch checked={automationEnabled} onCheckedChange={setAutomationEnabled} id="automation-mode" />
                      <Label htmlFor="automation-mode" className="text-xs">
                        {automationEnabled ? "Auto" : "Manual"}
                      </Label>
                    </div>
                  </div>

                  <div className="border rounded-md p-3 bg-muted/30">
                    <div className="flex items-center space-x-2 text-sm">
                      {automationEnabled ? (
                        <>
                          <Badge
                            variant="outline"
                            className="bg-green-500/10 text-green-500 hover:bg-green-500/20 border-green-500/20"
                          >
                            Automation On
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            Content will be automatically processed and scheduled
                          </span>
                        </>
                      ) : (
                        <>
                          <Badge
                            variant="outline"
                            className="bg-amber-500/10 text-amber-500 hover:bg-amber-500/20 border-amber-500/20"
                          >
                            Manual Review
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            Content requires manual approval before publishing
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Featured Product</Label>
                  <Select value={selectedProduct} onValueChange={handleProductChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select product to feature" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Premium Travel Backpack">Premium Travel Backpack</SelectItem>
                      <SelectItem value="Smart Home Lighting Kit">Smart Home Lighting Kit</SelectItem>
                      <SelectItem value="Organic Superfood Blend">Organic Superfood Blend</SelectItem>
                      <SelectItem value="Adjustable Kettlebell Set">Adjustable Kettlebell Set</SelectItem>
                      <SelectItem value="Business Productivity Course">Business Productivity Course</SelectItem>
                    </SelectContent>
                  </Select>
                  <div className="flex justify-between">
                    <Button variant="outline" size="sm" onClick={openComplementaryProductsDialog}>
                      <Layers className="mr-2 h-4 w-4" />
                      Complementary Products
                    </Button>
                    <div className="space-x-1">
                      <Button variant="outline" size="sm" onClick={openQrCodePreview}>
                        <QrCode className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={openLinkPreview}>
                        <LinkIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Platforms</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="youtube"
                        checked={selectedPlatforms.includes("youtube")}
                        onCheckedChange={() => handlePlatformToggle("youtube")}
                      />
                      <Label htmlFor="youtube" className="flex items-center gap-1 cursor-pointer">
                        <Youtube className="h-4 w-4" />
                        <span>YouTube</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="instagram"
                        checked={selectedPlatforms.includes("instagram")}
                        onCheckedChange={() => handlePlatformToggle("instagram")}
                      />
                      <Label htmlFor="instagram" className="flex items-center gap-1 cursor-pointer">
                        <Instagram className="h-4 w-4" />
                        <span>Instagram</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="twitter"
                        checked={selectedPlatforms.includes("twitter")}
                        onCheckedChange={() => handlePlatformToggle("twitter")}
                      />
                      <Label htmlFor="twitter" className="flex items-center gap-1 cursor-pointer">
                        <Twitter className="h-4 w-4" />
                        <span>Twitter</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="facebook"
                        checked={selectedPlatforms.includes("facebook")}
                        onCheckedChange={() => handlePlatformToggle("facebook")}
                      />
                      <Label htmlFor="facebook" className="flex items-center gap-1 cursor-pointer">
                        <Facebook className="h-4 w-4" />
                        <span>Facebook</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="linkedin"
                        checked={selectedPlatforms.includes("linkedin")}
                        onCheckedChange={() => handlePlatformToggle("linkedin")}
                      />
                      <Label htmlFor="linkedin" className="flex items-center gap-1 cursor-pointer">
                        <Linkedin className="h-4 w-4" />
                        <span>LinkedIn</span>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="pinterest"
                        checked={selectedPlatforms.includes("pinterest")}
                        onCheckedChange={() => {
                          if (!selectedPlatforms.includes("pinterest")) {
                            setShowApiKeyInput(true)
                          } else {
                            handlePlatformToggle("pinterest")
                          }
                        }}
                      />
                      <Label htmlFor="pinterest" className="flex items-center gap-1 cursor-pointer">
                        <PenTool className="h-4 w-4" />
                        <span>Pinterest + Blog</span>
                      </Label>
                    </div>

                    {showApiKeyInput && (
                      <div className="mt-2 p-3 border rounded-md bg-muted/30 space-y-2">
                        <Label htmlFor="pinterest-api" className="text-xs">
                          Pinterest API Key
                        </Label>
                        <div className="flex space-x-2">
                          <Input
                            id="pinterest-api"
                            type="password"
                            placeholder="Enter your Pinterest API key"
                            value={pinterestApiKey}
                            onChange={(e) => setPinterestApiKey(e.target.value)}
                            className="flex-1"
                          />
                          <Button size="sm" onClick={handlePinterestApiSubmit}>
                            Connect
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">Required for pingenerator.com integration</p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Repurposing Options</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="shorts" defaultChecked />
                      <label htmlFor="shorts" className="text-sm">
                        Create Short-form Clips (9:16)
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="highlights" defaultChecked />
                      <label htmlFor="highlights" className="text-sm">
                        Extract Key Highlights
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="audiogram" />
                      <label htmlFor="audiogram" className="text-sm">
                        Generate Audiogram
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="transcript" defaultChecked />
                      <label htmlFor="transcript" className="text-sm">
                        Create Transcript for Blog
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="pins" defaultChecked={selectedPlatforms.includes("pinterest")} />
                      <label htmlFor="pins" className="text-sm">
                        Generate Pinterest Pins
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="include-products" defaultChecked />
                      <label htmlFor="include-products" className="text-sm">
                        Include Product Bundle in Links/QR
                      </label>
                    </div>
                  </div>
                </div>

                <Button
                  className="w-full"
                  onClick={startVideoEditing}
                  disabled={isEditing || !videoReady || selectedPlatforms.length === 0}
                >
                  {isEditing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Scissors className="mr-2 h-4 w-4" />
                      Start Repurposing
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => setProductPlacementOpen(true)}
                  disabled={isEditing || !videoReady}
                >
                  <ShoppingBag className="mr-2 h-4 w-4" />
                  Edit Product Placement
                </Button>
              </div>

              <div className="md:col-span-2">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Content Preview</Label>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">{videoTitle}</span>
                    </div>
                  </div>

                  {isEditing && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Processing content for selected platforms...</span>
                        <span>{editProgress}%</span>
                      </div>
                      <Progress value={editProgress} className="h-2" />
                    </div>
                  )}

                  <div className="border rounded-md p-4 space-y-4">
                    {editProgress === 100 ? (
                      <div className="space-y-6">
                        <div className="text-center pb-2">
                          <Check className="h-8 w-8 mx-auto mb-2 text-green-500" />
                          <h3 className="font-medium">Repurposing Complete!</h3>
                          <p className="text-sm text-muted-foreground">Your content has been successfully repurposed</p>
                        </div>

                        <Accordion type="single" collapsible className="w-full">
                          {selectedPlatforms.includes("youtube") && (
                            <AccordionItem value="youtube">
                              <AccordionTrigger className="hover:no-underline">
                                <div className="flex items-center space-x-2">
                                  <Youtube className="h-4 w-4" />
                                  <span>YouTube</span>
                                  <Badge variant="outline" className="ml-2">
                                    3 items
                                  </Badge>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="grid grid-cols-3 gap-3">
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-video bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "youtube", "Main Video")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">{videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Main Video • 10:24</div>
                                    <div className="flex items-center gap-1">
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <Layers className="h-3 w-3 mr-1" />
                                        Bundle
                                      </Badge>
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <QrCode className="h-3 w-3 mr-1" />
                                        QR
                                      </Badge>
                                    </div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-video bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "youtube", "Highlights")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Key Highlights: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Highlights • 3:45</div>
                                    <div className="flex items-center gap-1">
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <Layers className="h-3 w-3 mr-1" />
                                        Bundle
                                      </Badge>
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <LinkIcon className="h-3 w-3 mr-1" />
                                        Link
                                      </Badge>
                                    </div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-video bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "youtube", "Teaser")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Teaser: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Teaser • 0:15</div>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          )}

                          {selectedPlatforms.includes("instagram") && (
                            <AccordionItem value="instagram">
                              <AccordionTrigger className="hover:no-underline">
                                <div className="flex items-center space-x-2">
                                  <Instagram className="h-4 w-4" />
                                  <span>Instagram</span>
                                  <Badge variant="outline" className="ml-2">
                                    3 items
                                  </Badge>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="grid grid-cols-3 gap-3">
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-square bg-black rounded flex items-center justify-center relative group">
                                      <ImageIcon className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("image", "instagram", "Main Post")}
                                        >
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Main Post: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Image • Feed</div>
                                    <div className="flex items-center gap-1">
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <Layers className="h-3 w-3 mr-1" />
                                        Bundle
                                      </Badge>
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <QrCode className="h-3 w-3 mr-1" />
                                        QR
                                      </Badge>
                                    </div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-video bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "instagram", "Reels")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Reels: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Video • Reels</div>
                                    <div className="flex items-center gap-1">
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <Layers className="h-3 w-3 mr-1" />
                                        Bundle
                                      </Badge>
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <LinkIcon className="h-3 w-3 mr-1" />
                                        Link
                                      </Badge>
                                    </div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-square bg-black rounded flex items-center justify-center relative group">
                                      <ImageIcon className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("image", "instagram", "Story")}
                                        >
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Story: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Image • Story</div>
                                    <div className="flex items-center gap-1">
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <Layers className="h-3 w-3 mr-1" />
                                        Bundle
                                      </Badge>
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <LinkIcon className="h-3 w-3 mr-1" />
                                        Link
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          )}

                          {selectedPlatforms.includes("twitter") && (
                            <AccordionItem value="twitter">
                              <AccordionTrigger className="hover:no-underline">
                                <div className="flex items-center space-x-2">
                                  <Twitter className="h-4 w-4" />
                                  <span>Twitter</span>
                                  <Badge variant="outline" className="ml-2">
                                    2 items
                                  </Badge>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="grid grid-cols-2 gap-3">
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-video bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "twitter", "Main Tweet")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Main Tweet: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Video • Tweet</div>
                                    <div className="flex items-center gap-1">
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <Layers className="h-3 w-3 mr-1" />
                                        Bundle
                                      </Badge>
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <QrCode className="h-3 w-3 mr-1" />
                                        QR
                                      </Badge>
                                    </div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-square bg-black rounded flex items-center justify-center relative group">
                                      <ImageIcon className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("image", "twitter", "Image Tweet")}
                                        >
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Image Tweet: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Image • Tweet</div>
                                    <div className="flex items-center gap-1">
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <Layers className="h-3 w-3 mr-1" />
                                        Bundle
                                      </Badge>
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <LinkIcon className="h-3 w-3 mr-1" />
                                        Link
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          )}

                          {selectedPlatforms.includes("facebook") && (
                            <AccordionItem value="facebook">
                              <AccordionTrigger className="hover:no-underline">
                                <div className="flex items-center space-x-2">
                                  <Facebook className="h-4 w-4" />
                                  <span>Facebook</span>
                                  <Badge variant="outline" className="ml-2">
                                    2 items
                                  </Badge>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="grid grid-cols-2 gap-3">
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-video bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "facebook", "Main Post")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Main Post: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Video • Post</div>
                                    <div className="flex items-center gap-1">
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <Layers className="h-3 w-3 mr-1" />
                                        Bundle
                                      </Badge>
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <QrCode className="h-3 w-3 mr-1" />
                                        QR
                                      </Badge>
                                    </div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-square bg-black rounded flex items-center justify-center relative group">
                                      <ImageIcon className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("image", "facebook", "Image Post")}
                                        >
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Image Post: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Image • Post</div>
                                    <div className="flex items-center gap-1">
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <Layers className="h-3 w-3 mr-1" />
                                        Bundle
                                      </Badge>
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <LinkIcon className="h-3 w-3 mr-1" />
                                        Link
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          )}

                          {selectedPlatforms.includes("linkedin") && (
                            <AccordionItem value="linkedin">
                              <AccordionTrigger className="hover:no-underline">
                                <div className="flex items-center space-x-2">
                                  <Linkedin className="h-4 w-4" />
                                  <span>LinkedIn</span>
                                  <Badge variant="outline" className="ml-2">
                                    2 items
                                  </Badge>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="grid grid-cols-2 gap-3">
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-video bg-black rounded flex items-center justify-center relative group">
                                      <Video className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("video", "linkedin", "Main Post")}
                                        >
                                          <Play className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Main Post: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Video • Post</div>
                                    <div className="flex items-center gap-1">
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <Layers className="h-3 w-3 mr-1" />
                                        Bundle
                                      </Badge>
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <QrCode className="h-3 w-3 mr-1" />
                                        QR
                                      </Badge>
                                    </div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-square bg-black rounded flex items-center justify-center relative group">
                                      <ImageIcon className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("image", "linkedin", "Image Post")}
                                        >
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Image Post: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Image • Post</div>
                                    <div className="flex items-center gap-1">
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <Layers className="h-3 w-3 mr-1" />
                                        Bundle
                                      </Badge>
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <LinkIcon className="h-3 w-3 mr-1" />
                                        Link
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          )}

                          {selectedPlatforms.includes("pinterest") && (
                            <AccordionItem value="pinterest">
                              <AccordionTrigger className="hover:no-underline">
                                <div className="flex items-center space-x-2">
                                  <PenTool className="h-4 w-4" />
                                  <span>Pinterest + Blog</span>
                                  <Badge variant="outline" className="ml-2">
                                    2 items
                                  </Badge>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="grid grid-cols-2 gap-3">
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-[2/3] bg-black rounded flex items-center justify-center relative group">
                                      <ImageIcon className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("image", "pinterest", "Pinterest Pin")}
                                        >
                                          <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Pinterest Pin: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Image • Pin</div>
                                    <div className="flex items-center gap-1">
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <Layers className="h-3 w-3 mr-1" />
                                        Bundle
                                      </Badge>
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <LinkIcon className="h-3 w-3 mr-1" />
                                        Link
                                      </Badge>
                                    </div>
                                  </div>
                                  <div className="border rounded-md p-2 space-y-2">
                                    <div className="aspect-video bg-black rounded flex items-center justify-center relative group">
                                      <FileText className="h-8 w-8 text-white/20" />
                                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                          onClick={() => openPreviewDialog("text", "pinterest", "Blog Post")}
                                        >
                                          <FileText className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          size="icon"
                                          variant="ghost"
                                          className="h-8 w-8 rounded-full bg-white/20 text-white hover:bg-white/30"
                                        >
                                          <Edit className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                    <div className="text-xs font-medium truncate">Blog Post: {videoTitle}</div>
                                    <div className="text-xs text-muted-foreground">Text • Blog</div>
                                    <div className="flex items-center gap-1">
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <Layers className="h-3 w-3 mr-1" />
                                        Bundle
                                      </Badge>
                                      <Badge variant="outline" className="text-xs bg-sage-50 text-sage-700">
                                        <LinkIcon className="h-3 w-3 mr-1" />
                                        Link
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          )}
                        </Accordion>
                      </div>
                    ) : (
                      <div className="text-center">
                        <RotateCcw className="h-8 w-8 mx-auto mb-2 animate-spin text-muted-foreground" />
                        <h3 className="font-medium">Repurposing in Progress</h3>
                        <p className="text-sm text-muted-foreground">Please wait while we repurpose your content</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* Dialogs */}
      <Dialog open={productPlacementOpen} onOpenChange={setProductPlacementOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Product Placement</DialogTitle>
            <DialogDescription>Configure where and when your products appear in the video.</DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
            <div className="space-y-2">
              <Label>Product</Label>
              <Select value={selectedProduct} onValueChange={handleProductChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select product to feature" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Premium Travel Backpack">Premium Travel Backpack</SelectItem>
                  <SelectItem value="Smart Home Lighting Kit">Smart Home Lighting Kit</SelectItem>
                  <SelectItem value="Organic Superfood Blend">Organic Superfood Blend</SelectItem>
                  <SelectItem value="Adjustable Kettlebell Set">Adjustable Kettlebell Set</SelectItem>
                  <SelectItem value="Business Productivity Course">Business Productivity Course</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Placement Time</Label>
              <Input type="time" defaultValue="00:15" />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit">Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={complementaryProductsOpen} onOpenChange={setComplementaryProductsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select Complementary Products</DialogTitle>
            <DialogDescription>
              Choose products that complement the featured product to create a bundle.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Accordion type="multiple" collapsible>
              {selectedComplementaryProducts.map((product) => (
                <AccordionItem key={product} value={product}>
                  <AccordionTrigger className="hover:no-underline">{product}</AccordionTrigger>
                  <AccordionContent>
                    <p className="text-sm text-muted-foreground">Description of {product}. Add more details here.</p>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
          <DialogFooter>
            <Button type="submit" onClick={saveComplementaryProducts}>
              Save Bundle
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={qrCodePreviewOpen} onOpenChange={setQrCodePreviewOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>QR Code Preview</DialogTitle>
            <DialogDescription>Preview the QR code that will be generated for the product bundle.</DialogDescription>
          </DialogHeader>
          <div className="py-4 flex items-center justify-center">
            <img src="/placeholder-qr.png" alt="QR Code Preview" className="max-w-xs" />
          </div>
          <DialogFooter>
            <Button type="button" onClick={() => setQrCodePreviewOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={linkPreviewOpen} onOpenChange={setLinkPreviewOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Link Preview</DialogTitle>
            <DialogDescription>Preview the link that will be generated for the product bundle.</DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              Example Link:{" "}
              <a href="#" className="text-blue-500">
                https://example.com/product-bundle
              </a>
            </p>
          </div>
          <DialogFooter>
            <Button type="button" onClick={() => setLinkPreviewOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={previewDialogOpen} onOpenChange={setPreviewDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{getPlatformName(previewPlatform)} Preview</DialogTitle>
            <DialogDescription>Preview of the content for {getPlatformName(previewPlatform)}.</DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {previewType === "video" && (
              <div className="aspect-video bg-black rounded flex items-center justify-center">
                <Video className="h-16 w-16 text-white/20" />
              </div>
            )}
            {previewType === "image" && (
              <div className="aspect-square bg-black rounded flex items-center justify-center">
                <ImageIcon className="h-16 w-16 text-white/20" />
              </div>
            )}
            {previewType === "text" && (
              <div className="p-4 border rounded-md bg-muted/30">
                <p className="text-sm text-muted-foreground">
                  This is a preview of the text content for {getPlatformName(previewPlatform)}.
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button type="button" onClick={() => setPreviewDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}

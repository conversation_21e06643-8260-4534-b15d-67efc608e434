"use client"

import type React from "react"

import { useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { LayoutGrid, LayoutList, Instagram, Youtube, Twitter, Facebook, TrendingUp, DollarSign } from "lucide-react"

export function ProductGrid() {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Avatar Products</h3>
        <div className="flex items-center border rounded-md p-1">
          <Button
            variant={viewMode === "list" ? "secondary" : "ghost"}
            size="sm"
            className="h-8 px-2"
            onClick={() => setViewMode("list")}
          >
            <LayoutList className="h-4 w-4" />
            <span className="sr-only">List View</span>
          </Button>
          <Button
            variant={viewMode === "grid" ? "secondary" : "ghost"}
            size="sm"
            className="h-8 px-2"
            onClick={() => setViewMode("grid")}
          >
            <LayoutGrid className="h-4 w-4" />
            <span className="sr-only">Grid View</span>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="travel">
        <TabsList className="mb-4">
          <TabsTrigger value="travel">Travel</TabsTrigger>
          <TabsTrigger value="home">Home</TabsTrigger>
          <TabsTrigger value="health">Health</TabsTrigger>
          <TabsTrigger value="fitness">Fitness</TabsTrigger>
          <TabsTrigger value="entrepreneur">Entrepreneur</TabsTrigger>
        </TabsList>

        <TabsContent value="travel">
          <div className={viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" : "space-y-4"}>
            {travelProducts.map((product, index) => (
              <ProductCard key={index} product={product} viewMode={viewMode} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="home">
          <div className={viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" : "space-y-4"}>
            {homeProducts.map((product, index) => (
              <ProductCard key={index} product={product} viewMode={viewMode} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="health">
          <div className={viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" : "space-y-4"}>
            {healthProducts.map((product, index) => (
              <ProductCard key={index} product={product} viewMode={viewMode} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="fitness">
          <div className={viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" : "space-y-4"}>
            {fitnessProducts.map((product, index) => (
              <ProductCard key={index} product={product} viewMode={viewMode} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="entrepreneur">
          <div className={viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" : "space-y-4"}>
            {entrepreneurProducts.map((product, index) => (
              <ProductCard key={index} product={product} viewMode={viewMode} />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

interface Product {
  name: string
  type: "Affiliate" | "Dropship"
  category: string
  avatar: string
  avatarInitials: string
  revenue: string
  sales: number
  conversionRate: string
  platforms: {
    name: string
    icon: React.ReactNode
    videoThumbnail: string
  }[]
}

interface ProductCardProps {
  product: Product
  viewMode: "grid" | "list"
}

function ProductCard({ product, viewMode }: ProductCardProps) {
  if (viewMode === "list") {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-4">
              <Avatar className="h-12 w-12">
                <AvatarImage src={product.avatar || "/placeholder.svg"} alt={`${product.category} Avatar`} />
                <AvatarFallback>{product.avatarInitials}</AvatarFallback>
              </Avatar>
            </div>
            <div className="flex-grow">
              <div className="flex items-center justify-between mb-2">
                <div>
                  <h4 className="font-semibold">{product.name}</h4>
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Badge variant={product.type === "Affiliate" ? "outline" : "secondary"}>{product.type}</Badge>
                    <span>•</span>
                    <span>{product.category}</span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center">
                    <DollarSign className="h-4 w-4 mr-1 text-muted-foreground" />
                    <span className="font-semibold">{product.revenue}</span>
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    <span>{product.conversionRate} conversion</span>
                  </div>
                </div>
              </div>
              <div className="mt-4">
                <h5 className="text-sm font-medium mb-2">Platform Videos</h5>
                <div className="flex flex-wrap gap-3">
                  {product.platforms.map((platform, index) => (
                    <div key={index} className="relative group">
                      <div className="w-24 h-16 rounded-md overflow-hidden bg-muted">
                        <img
                          src={platform.videoThumbnail || "/placeholder.svg?height=64&width=96"}
                          alt={`${platform.name} video thumbnail`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="absolute top-1 right-1 bg-background/80 rounded-full p-1">{platform.icon}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Avatar className="h-8 w-8 mr-2">
              <AvatarImage src={product.avatar || "/placeholder.svg"} alt={`${product.category} Avatar`} />
              <AvatarFallback>{product.avatarInitials}</AvatarFallback>
            </Avatar>
            <CardTitle className="text-base">{product.name}</CardTitle>
          </div>
          <Badge variant={product.type === "Affiliate" ? "outline" : "secondary"}>{product.type}</Badge>
        </div>
        <CardDescription className="mt-1">{product.category}</CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="grid grid-cols-2 gap-2 mb-4">
          <div className="flex flex-col">
            <span className="text-xs text-muted-foreground">Revenue</span>
            <span className="font-semibold">{product.revenue}</span>
          </div>
          <div className="flex flex-col">
            <span className="text-xs text-muted-foreground">Conversion</span>
            <span className="font-semibold">{product.conversionRate}</span>
          </div>
        </div>
        <div>
          <h5 className="text-xs font-medium mb-2">Platform Videos</h5>
          <div className="grid grid-cols-3 gap-2">
            {product.platforms.map((platform, index) => (
              <div key={index} className="relative group">
                <div className="w-full aspect-video rounded-md overflow-hidden bg-muted">
                  <img
                    src={platform.videoThumbnail || "/placeholder.svg?height=64&width=96"}
                    alt={`${platform.name} video thumbnail`}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="absolute top-1 right-1 bg-background/80 rounded-full p-1">{platform.icon}</div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="ghost" size="sm" className="w-full">
          View Details
        </Button>
      </CardFooter>
    </Card>
  )
}

// Sample data
const travelProducts: Product[] = [
  {
    name: "Premium Travel Backpack",
    type: "Affiliate",
    category: "Travel Gear",
    avatar: "/placeholder-user.jpg",
    avatarInitials: "TR",
    revenue: "$4,250",
    sales: 85,
    conversionRate: "4.2%",
    platforms: [
      {
        name: "YouTube",
        icon: <Youtube className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Instagram",
        icon: <Instagram className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Twitter",
        icon: <Twitter className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
    ],
  },
  {
    name: "Compact Travel Adapter",
    type: "Dropship",
    category: "Travel Accessories",
    avatar: "/placeholder-user.jpg",
    avatarInitials: "TR",
    revenue: "$2,890",
    sales: 145,
    conversionRate: "3.8%",
    platforms: [
      {
        name: "YouTube",
        icon: <Youtube className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Instagram",
        icon: <Instagram className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Facebook",
        icon: <Facebook className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
    ],
  },
  {
    name: "Noise-Cancelling Headphones",
    type: "Affiliate",
    category: "Travel Electronics",
    avatar: "/placeholder-user.jpg",
    avatarInitials: "TR",
    revenue: "$5,670",
    sales: 63,
    conversionRate: "5.1%",
    platforms: [
      {
        name: "YouTube",
        icon: <Youtube className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Instagram",
        icon: <Instagram className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Twitter",
        icon: <Twitter className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
    ],
  },
]

const homeProducts: Product[] = [
  {
    name: "Minimalist Desk Organizer",
    type: "Dropship",
    category: "Home Office",
    avatar: "/placeholder-user.jpg",
    avatarInitials: "HM",
    revenue: "$3,450",
    sales: 172,
    conversionRate: "4.8%",
    platforms: [
      {
        name: "YouTube",
        icon: <Youtube className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Instagram",
        icon: <Instagram className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Facebook",
        icon: <Facebook className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
    ],
  },
  {
    name: "Smart Home Lighting Kit",
    type: "Affiliate",
    category: "Smart Home",
    avatar: "/placeholder-user.jpg",
    avatarInitials: "HM",
    revenue: "$6,780",
    sales: 113,
    conversionRate: "5.2%",
    platforms: [
      {
        name: "YouTube",
        icon: <Youtube className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Instagram",
        icon: <Instagram className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Twitter",
        icon: <Twitter className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
    ],
  },
]

const healthProducts: Product[] = [
  {
    name: "Organic Superfood Blend",
    type: "Affiliate",
    category: "Nutrition",
    avatar: "/placeholder-user.jpg",
    avatarInitials: "HE",
    revenue: "$4,120",
    sales: 206,
    conversionRate: "4.5%",
    platforms: [
      {
        name: "YouTube",
        icon: <Youtube className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Instagram",
        icon: <Instagram className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Facebook",
        icon: <Facebook className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
    ],
  },
  {
    name: "Sleep Tracking Device",
    type: "Dropship",
    category: "Wellness",
    avatar: "/placeholder-user.jpg",
    avatarInitials: "HE",
    revenue: "$5,340",
    sales: 89,
    conversionRate: "3.9%",
    platforms: [
      {
        name: "YouTube",
        icon: <Youtube className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Instagram",
        icon: <Instagram className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Twitter",
        icon: <Twitter className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
    ],
  },
]

const fitnessProducts: Product[] = [
  {
    name: "Adjustable Kettlebell Set",
    type: "Dropship",
    category: "Home Fitness",
    avatar: "/placeholder-user.jpg",
    avatarInitials: "FI",
    revenue: "$7,890",
    sales: 157,
    conversionRate: "5.7%",
    platforms: [
      {
        name: "YouTube",
        icon: <Youtube className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Instagram",
        icon: <Instagram className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Facebook",
        icon: <Facebook className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
    ],
  },
  {
    name: "Smart Fitness Tracker",
    type: "Affiliate",
    category: "Wearables",
    avatar: "/placeholder-user.jpg",
    avatarInitials: "FI",
    revenue: "$9,450",
    sales: 210,
    conversionRate: "6.2%",
    platforms: [
      {
        name: "YouTube",
        icon: <Youtube className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Instagram",
        icon: <Instagram className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Twitter",
        icon: <Twitter className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
    ],
  },
]

const entrepreneurProducts: Product[] = [
  {
    name: "Business Productivity Course",
    type: "Affiliate",
    category: "Online Education",
    avatar: "/placeholder-user.jpg",
    avatarInitials: "EN",
    revenue: "$12,780",
    sales: 85,
    conversionRate: "7.8%",
    platforms: [
      {
        name: "YouTube",
        icon: <Youtube className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Instagram",
        icon: <Instagram className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Twitter",
        icon: <Twitter className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
    ],
  },
  {
    name: "Premium Planner System",
    type: "Dropship",
    category: "Productivity",
    avatar: "/placeholder-user.jpg",
    avatarInitials: "EN",
    revenue: "$5,670",
    sales: 189,
    conversionRate: "4.9%",
    platforms: [
      {
        name: "YouTube",
        icon: <Youtube className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Instagram",
        icon: <Instagram className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
      {
        name: "Facebook",
        icon: <Facebook className="h-3 w-3" />,
        videoThumbnail: "/placeholder.svg?height=64&width=96",
      },
    ],
  },
]

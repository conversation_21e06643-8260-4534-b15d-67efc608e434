"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/chart"
import { ResponsiveContainer, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Line, Pie, Cell } from "recharts"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DollarSign, TrendingUp } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

const avatarData = [
  {
    name: "Travel Avatar",
    monthlyCost: 3500,
    monthlyRevenue: 12500,
    roi: 3.57,
    followers: 245000,
    engagement: 4.8,
    contentCount: 45,
    costPerContent: 77.78,
    platforms: ["Instagram", "YouTube", "TikTok"],
    trend: "up",
  },
  {
    name: "Fitness Avatar",
    monthlyCost: 4200,
    monthlyRevenue: 18500,
    roi: 4.4,
    followers: 312000,
    engagement: 5.2,
    contentCount: 60,
    costPerContent: 70.0,
    platforms: ["Instagram", "YouTube", "TikTok", "Twitter"],
    trend: "up",
  },
  {
    name: "Home Avatar",
    monthlyCost: 2800,
    monthlyRevenue: 8400,
    roi: 3.0,
    followers: 187000,
    engagement: 3.9,
    contentCount: 35,
    costPerContent: 80.0,
    platforms: ["Instagram", "Pinterest", "YouTube"],
    trend: "stable",
  },
  {
    name: "Health Avatar",
    monthlyCost: 3800,
    monthlyRevenue: 15200,
    roi: 4.0,
    followers: 278000,
    engagement: 4.5,
    contentCount: 50,
    costPerContent: 76.0,
    platforms: ["Instagram", "YouTube", "TikTok", "Twitter"],
    trend: "up",
  },
  {
    name: "Entrepreneur Avatar",
    monthlyCost: 4500,
    monthlyRevenue: 13500,
    roi: 3.0,
    followers: 267000,
    engagement: 3.8,
    contentCount: 40,
    costPerContent: 112.5,
    platforms: ["Instagram", "LinkedIn", "YouTube", "Twitter"],
    trend: "down",
  },
]

const monthlyTrendData = [
  {
    name: "Jan",
    Travel: 2.8,
    Fitness: 3.5,
    Home: 2.5,
    Health: 3.2,
    Entrepreneur: 3.4,
  },
  {
    name: "Feb",
    Travel: 3.0,
    Fitness: 3.8,
    Home: 2.7,
    Health: 3.5,
    Entrepreneur: 3.2,
  },
  {
    name: "Mar",
    Travel: 3.2,
    Fitness: 4.0,
    Home: 2.8,
    Health: 3.7,
    Entrepreneur: 3.1,
  },
  {
    name: "Apr",
    Travel: 3.4,
    Fitness: 4.2,
    Home: 2.9,
    Health: 3.9,
    Entrepreneur: 3.0,
  },
  {
    name: "May",
    Travel: 3.5,
    Fitness: 4.3,
    Home: 3.0,
    Health: 4.0,
    Entrepreneur: 2.9,
  },
  {
    name: "Jun",
    Travel: 3.57,
    Fitness: 4.4,
    Home: 3.0,
    Health: 4.0,
    Entrepreneur: 3.0,
  },
]

const costBreakdownData = [
  { name: "Content Creation", value: 40 },
  { name: "Editing", value: 25 },
  { name: "Promotion", value: 15 },
  { name: "Tools & Software", value: 10 },
  { name: "Miscellaneous", value: 10 },
]

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"]

export function AvatarCostReturn() {
  const [sortBy, setSortBy] = useState("roi")
  const [sortOrder, setSortOrder] = useState("desc")
  const [selectedAvatar, setSelectedAvatar] = useState("all")

  const filteredData = selectedAvatar === "all" ? avatarData : avatarData.filter((item) => item.name === selectedAvatar)

  const sortedData = [...filteredData].sort((a, b) => {
    const factor = sortOrder === "asc" ? 1 : -1
    return factor * (a[sortBy] - b[sortBy])
  })

  const totalMonthlyCost = filteredData.reduce((sum, item) => sum + item.monthlyCost, 0)
  const totalMonthlyRevenue = filteredData.reduce((sum, item) => sum + item.monthlyRevenue, 0)
  const totalROI = totalMonthlyRevenue / totalMonthlyCost
  const totalFollowers = filteredData.reduce((sum, item) => sum + item.followers, 0)
  const avgEngagement = filteredData.reduce((sum, item) => sum + item.engagement, 0) / filteredData.length

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortBy(field)
      setSortOrder("desc")
    }
  }

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Monthly Cost</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalMonthlyCost.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Across {filteredData.length} avatars</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Monthly Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalMonthlyRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Across {filteredData.length} avatars</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average ROI</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalROI.toFixed(2)}x</div>
            <p className="text-xs text-muted-foreground">Return on investment</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Followers</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(totalFollowers / 1000).toFixed(0)}K</div>
            <p className="text-xs text-muted-foreground">Across all platforms</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Engagement</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgEngagement.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">Across all avatars</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="trends">ROI Trends</TabsTrigger>
            <TabsTrigger value="breakdown">Cost Breakdown</TabsTrigger>
          </TabsList>
          <div className="flex items-center space-x-2">
            <Select value={selectedAvatar} onValueChange={setSelectedAvatar}>
              <SelectTrigger className="w-[180px] h-8">
                <SelectValue placeholder="Select avatar" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Avatars</SelectItem>
                {avatarData.map((avatar, index) => (
                  <SelectItem key={index} value={avatar.name}>
                    {avatar.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              Export Data
            </Button>
          </div>
        </div>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Avatar</TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("monthlyCost")}>
                      Monthly Cost {sortBy === "monthlyCost" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("monthlyRevenue")}>
                      Monthly Revenue {sortBy === "monthlyRevenue" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("roi")}>
                      ROI {sortBy === "roi" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("followers")}>
                      Followers {sortBy === "followers" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("engagement")}>
                      Engagement {sortBy === "engagement" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("contentCount")}>
                      Content/Month {sortBy === "contentCount" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("costPerContent")}>
                      Cost/Content {sortBy === "costPerContent" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead>Platforms</TableHead>
                    <TableHead>Trend</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedData.map((avatar, i) => (
                    <TableRow key={i}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src="/placeholder-user.jpg" alt={avatar.name} />
                            <AvatarFallback>{avatar.name.substring(0, 2)}</AvatarFallback>
                          </Avatar>
                          <span>{avatar.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>${avatar.monthlyCost.toLocaleString()}</TableCell>
                      <TableCell>${avatar.monthlyRevenue.toLocaleString()}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className={avatar.roi >= 4 ? "text-green-500 font-medium" : ""}>
                            {avatar.roi.toFixed(2)}x
                          </span>
                          {avatar.trend === "up" && <TrendingUp className="h-4 w-4 text-green-500" />}
                          {avatar.trend === "down" && <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />}
                        </div>
                      </TableCell>
                      <TableCell>{(avatar.followers / 1000).toFixed(0)}K</TableCell>
                      <TableCell>{avatar.engagement}%</TableCell>
                      <TableCell>{avatar.contentCount}</TableCell>
                      <TableCell>${avatar.costPerContent.toFixed(2)}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {avatar.platforms.map((platform, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {platform}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            avatar.trend === "up" ? "success" : avatar.trend === "down" ? "destructive" : "secondary"
                          }
                        >
                          {avatar.trend === "up" ? "Rising" : avatar.trend === "down" ? "Falling" : "Stable"}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>ROI Trends by Avatar</CardTitle>
              <CardDescription>Return on investment over the past 6 months</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={monthlyTrendData}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="Travel" stroke="#8884d8" activeDot={{ r: 8 }} />
                    <Line type="monotone" dataKey="Fitness" stroke="#82ca9d" />
                    <Line type="monotone" dataKey="Home" stroke="#ff7300" />
                    <Line type="monotone" dataKey="Health" stroke="#0088FE" />
                    <Line type="monotone" dataKey="Entrepreneur" stroke="#FFBB28" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="breakdown" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cost Breakdown</CardTitle>
              <CardDescription>Distribution of costs by category</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={costBreakdownData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={150}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {costBreakdownData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
            <CardFooter>
              <div className="grid grid-cols-2 gap-4 w-full">
                {costBreakdownData.map((item, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    ></div>
                    <span className="text-sm">
                      {item.name}: {item.value}%
                    </span>
                  </div>
                ))}
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

'use client'

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Image as ImageIcon,
  Video,
  File,
  Trash2,
  Eye,
  Download,
  Star,
  Calendar,
  HardDrive,
  FileType,
  AlertCircle,
  Copy,
  ExternalLink,
  Play
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { mediaService, type Media, type ProductMediaStats } from "@/lib/api/media"
import { type Product } from "@/lib/api/products"
import { getImageUrl } from "@/lib/url-utils"
import { MediaStats } from "@/components/media-stats"

interface ProductMediaDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  product: Product | null
  onMediaSelect?: (media: Media) => void
}

export function ProductMediaDialog({ 
  open, 
  onOpenChange, 
  product,
  onMediaSelect 
}: ProductMediaDialogProps) {
  const [media, setMedia] = useState<Media[]>([])
  const [loading, setLoading] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [mediaToDelete, setMediaToDelete] = useState<Media | null>(null)
  const [deleting, setDeleting] = useState(false)
  const { toast } = useToast()

  const copyToClipboard = async (text: string, label: string = 'URL') => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard.`,
      })
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
      toast({
        title: "Copy Failed",
        description: "Could not copy to clipboard. Please copy manually.",
        variant: "destructive",
      })
    }
  }

  const openInNewTab = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  useEffect(() => {
    if (open && product) {
      loadProductMedia()
    }
  }, [open, product])

  const loadProductMedia = async () => {
    if (!product) return

    try {
      setLoading(true)
      const response = await mediaService.getProductMedia(product.id)
      setMedia(response.data || [])
    } catch (error) {
      console.error('Failed to load product media:', error)
      setMedia([])

      toast({
        title: "Error",
        description: "Failed to load product media.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }



  const handleDeleteMedia = async () => {
    if (!mediaToDelete) return

    try {
      setDeleting(true)
      await mediaService.deleteMedia(mediaToDelete.id)
      
      // Remove from local state
      setMedia(prev => prev.filter(m => m.id !== mediaToDelete.id))

      toast({
        title: "Media Deleted",
        description: "Media has been deleted successfully.",
      })
    } catch (error) {
      console.error('Failed to delete media:', error)

      toast({
        title: "Error",
        description: "Failed to delete media.",
        variant: "destructive",
      })
    } finally {
      setDeleting(false)
      setDeleteDialogOpen(false)
      setMediaToDelete(null)
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getMediaIcon = (mediaType: string) => {
    switch (mediaType) {
      case 'image':
        return <ImageIcon className="h-5 w-5" />
      case 'video':
        return <Video className="h-5 w-5" />
      default:
        return <File className="h-5 w-5" />
    }
  }

  const getMediaPreview = (mediaItem: Media) => {
    const mediaUrl = mediaItem.media_url

    if (mediaItem.media_type === 'image') {
      return (
        <div className="aspect-video bg-muted rounded-lg overflow-hidden relative group cursor-pointer">
          <img
            src={mediaUrl || '/placeholder.jpg'}
            alt={`${mediaItem.media_type} - ${mediaItem.format || 'Unknown'}`}
            className="w-full h-full object-cover transition-transform group-hover:scale-105"
            onClick={() => mediaUrl && openInNewTab(mediaUrl)}
            onError={(e) => {
              (e.target as HTMLImageElement).src = '/placeholder.jpg'
            }}
          />
          {/* Overlay with actions */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="secondary"
                onClick={(e) => {
                  e.stopPropagation()
                  if (mediaUrl) openInNewTab(mediaUrl)
                }}
              >
                <ExternalLink className="h-4 w-4 mr-1" />
                Open
              </Button>
              <Button
                size="sm"
                variant="secondary"
                onClick={(e) => {
                  e.stopPropagation()
                  if (mediaUrl) copyToClipboard(mediaUrl, 'Image URL')
                }}
              >
                <Copy className="h-4 w-4 mr-1" />
                Copy
              </Button>
            </div>
          </div>
        </div>
      )
    } else if (mediaItem.media_type === 'video') {
      return (
        <div className="aspect-video bg-black rounded-lg relative group overflow-hidden">
          {mediaUrl && mediaUrl !== '/placeholder.jpg' ? (
            <video
              className="w-full h-full object-cover"
              controls
              preload="metadata"
              poster={mediaItem.thumbnail_url || undefined}
            >
              <source src={mediaUrl} type={`video/${mediaItem.format || 'mp4'}`} />
              Your browser does not support the video tag.
            </video>
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Video className="h-12 w-12 text-white/50" />
            </div>
          )}
          {/* Overlay with actions */}
          <div className="absolute top-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              size="sm"
              variant="secondary"
              onClick={() => mediaUrl && openInNewTab(mediaUrl)}
              disabled={!mediaUrl}
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Open
            </Button>
            <Button
              size="sm"
              variant="secondary"
              onClick={() => mediaUrl && copyToClipboard(mediaUrl, 'Video URL')}
              disabled={!mediaUrl}
            >
              <Copy className="h-3 w-3 mr-1" />
              Copy
            </Button>
          </div>
        </div>
      )
    } else {
      return (
        <div className="aspect-video bg-muted rounded-lg flex items-center justify-center relative group cursor-pointer">
          <File className="h-12 w-12 text-muted-foreground" />
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="secondary"
                onClick={() => mediaUrl && openInNewTab(mediaUrl)}
                disabled={!mediaUrl}
              >
                <ExternalLink className="h-4 w-4 mr-1" />
                Open
              </Button>
              <Button
                size="sm"
                variant="secondary"
                onClick={() => mediaUrl && copyToClipboard(mediaUrl, 'File URL')}
                disabled={!mediaUrl}
              >
                <Copy className="h-4 w-4 mr-1" />
                Copy
              </Button>
            </div>
          </div>
        </div>
      )
    }
  }

  if (!product) return null

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="w-[95vw] max-w-[900px] max-h-[95vh] rounded-2xl overflow-auto border-0 shadow-2xl bg-gradient-to-br from-background via-background to-muted/20 sm:mx-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <ImageIcon className="h-5 w-5" />
              <span>Media for {product.name}</span>
            </DialogTitle>
            <DialogDescription>
              View and manage media files for this product
            </DialogDescription>
          </DialogHeader>

          {/* Stats Section */}
          <div className="mb-6">
            <MediaStats productId={product.id} />
          </div>

          {/* Media Grid */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[...Array(6)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-4">
                    <Skeleton className="aspect-video w-full mb-3" />
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-3 w-1/2" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : media.length === 0 ? (
            <div className="text-center py-12">
              <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">No media found</h3>
              <p className="text-muted-foreground">
                This product doesn't have any media files yet.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {media.map((mediaItem) => (
                <Card key={mediaItem.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-4">
                    {getMediaPreview(mediaItem)}
                    
                    <div className="mt-3 space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {getMediaIcon(mediaItem.media_type)}
                          <span className="font-medium text-sm truncate">
                            {mediaItem.format ? mediaItem.format.toUpperCase() : 'Unknown'} {mediaItem.media_type}
                          </span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {mediaItem.status}
                        </Badge>
                      </div>

                      <p className="text-xs text-muted-foreground">
                        {formatFileSize(mediaItem.file_size)} • {mediaItem.format || 'Unknown format'}
                        {mediaItem.duration && ` • ${mediaItem.duration}s`}
                      </p>

                      <div className="text-xs text-muted-foreground">
                        <p>Quality: {((mediaItem.metadata?.quality_score || 0) * 100).toFixed(0)}%</p>
                        {mediaItem.width && mediaItem.height && (
                          <p>{mediaItem.width} × {mediaItem.height}</p>
                        )}
                      </div>
                      
                      <div className="flex justify-between items-center pt-2">
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onMediaSelect?.(mediaItem)}
                            title="View Details"
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => mediaItem.media_url && copyToClipboard(mediaItem.media_url, 'Media URL')}
                            title="Copy URL"
                            disabled={!mediaItem.media_url}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            asChild
                            title="Download"
                          >
                            <a
                              href={mediaItem.media_url || '#'}
                              download={`${mediaItem.media_type}.${mediaItem.format || 'unknown'}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              onClick={(e) => !mediaItem.media_url && e.preventDefault()}
                              style={{ pointerEvents: mediaItem.media_url ? 'auto' : 'none', opacity: mediaItem.media_url ? 1 : 0.5 }}
                            >
                              <Download className="h-3 w-3" />
                            </a>
                          </Button>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setMediaToDelete(mediaItem)
                            setDeleteDialogOpen(true)
                          }}
                          className="text-destructive hover:text-destructive"
                          title="Delete"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              <span>Delete Media</span>
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this media file? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteMedia}
              disabled={deleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

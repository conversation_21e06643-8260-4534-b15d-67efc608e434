"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/chart"
import { ResponsiveContainer, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Line, Pie, Cell } from "recharts"
import { Badge } from "@/components/ui/badge"
import { Clock, DollarSign, AlertTriangle, Users, Bot } from "lucide-react"

const overviewData = [
  {
    name: "Jan",
    "Human Hours": 120,
    "AI Hours": 80,
    Cost: 4500,
  },
  {
    name: "Feb",
    "Human Hours": 110,
    "AI Hours": 90,
    Cost: 4300,
  },
  {
    name: "<PERSON>",
    "Human Hours": 95,
    "AI Hours": 105,
    Cost: 4100,
  },
  {
    name: "Apr",
    "Human Hours": 85,
    "AI Hours": 115,
    Cost: 3900,
  },
  {
    name: "May",
    "Human Hours": 75,
    "AI Hours": 125,
    Cost: 3800,
  },
  {
    name: "<PERSON>",
    "Human Hours": 65,
    "AI Hours": 135,
    Cost: 3700,
  },
]

const efficiencyData = [
  {
    name: "Content Creation",
    efficiency: 85,
  },
  {
    name: "Social Media",
    efficiency: 92,
  },
  {
    name: "Email Marketing",
    efficiency: 78,
  },
  {
    name: "Video Production",
    efficiency: 65,
  },
  {
    name: "Analytics",
    efficiency: 95,
  },
]

const resourceAllocationData = [
  { name: "Content Creation", value: 35 },
  { name: "Social Media", value: 25 },
  { name: "Email Marketing", value: 15 },
  { name: "Video Production", value: 20 },
  { name: "Analytics", value: 5 },
]

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"]

export function OperationsDashboard() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card className="col-span-4">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle>Operations Overview</CardTitle>
            <CardDescription>Human vs AI resource allocation and costs</CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              Human
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Bot className="h-3 w-3" />
              AI
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <DollarSign className="h-3 w-3" />
              Cost
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={overviewData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip />
                <Legend />
                <Line yAxisId="left" type="monotone" dataKey="Human Hours" stroke="#8884d8" activeDot={{ r: 8 }} />
                <Line yAxisId="left" type="monotone" dataKey="AI Hours" stroke="#82ca9d" />
                <Line yAxisId="right" type="monotone" dataKey="Cost" stroke="#ff7300" />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Human Hours</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">65</div>
          <p className="text-xs text-muted-foreground">-15.3% from last month</p>
          <div className="mt-4 h-1 w-full bg-gray-200 rounded-full overflow-hidden">
            <div className="bg-blue-500 h-1 rounded-full" style={{ width: "35%" }}></div>
          </div>
          <p className="text-xs text-muted-foreground mt-1">35% of total resource allocation</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total AI Hours</CardTitle>
          <Bot className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">135</div>
          <p className="text-xs text-muted-foreground">+7.4% from last month</p>
          <div className="mt-4 h-1 w-full bg-gray-200 rounded-full overflow-hidden">
            <div className="bg-green-500 h-1 rounded-full" style={{ width: "65%" }}></div>
          </div>
          <p className="text-xs text-muted-foreground mt-1">65% of total resource allocation</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Monthly Burn Rate</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">$3,700</div>
          <p className="text-xs text-muted-foreground">-2.6% from last month</p>
          <div className="mt-4 h-1 w-full bg-gray-200 rounded-full overflow-hidden">
            <div className="bg-yellow-500 h-1 rounded-full" style={{ width: "45%" }}></div>
          </div>
          <p className="text-xs text-muted-foreground mt-1">45% of monthly budget</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active Bottlenecks</CardTitle>
          <AlertTriangle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">3</div>
          <p className="text-xs text-muted-foreground">-1 from last week</p>
          <div className="mt-4 space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs">Video Production</span>
              <Badge variant="destructive">Critical</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs">Email Marketing</span>
              <Badge variant="outline">Medium</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs">Content Approval</span>
              <Badge variant="outline">Low</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="col-span-2">
        <CardHeader>
          <CardTitle>Efficiency by Department</CardTitle>
          <CardDescription>Resource utilization efficiency scores</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={efficiencyData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis domain={[0, 100]} />
                <Tooltip />
                <Legend />
                <Bar dataKey="efficiency" fill="#8884d8" name="Efficiency %" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card className="col-span-2">
        <CardHeader>
          <CardTitle>Resource Allocation</CardTitle>
          <CardDescription>Distribution of resources across departments</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={resourceAllocationData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {resourceAllocationData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

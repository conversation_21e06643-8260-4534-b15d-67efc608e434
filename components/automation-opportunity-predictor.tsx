"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Slider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import {
  Brain,
  TrendingUp,
  Clock,
  DollarSign,
  BarChart2,
  Download,
  Share2,
  Sliders,
  Search,
  Plus,
  ArrowUpRight,
  Lightbulb,
  Gauge,
  FileSpreadsheet,
  ShoppingCart,
  Users,
  Truck,
  Headphones,
  Server,
  CalendarClock,
  FileText,
  Settings,
  Workflow,
  Database,
  BarChart3,
  Clipboard,
  MessageSquare,
  File<PERSON>heck,
  <PERSON><PERSON>l,
} from "lucide-react"

// Types for our data models
type ProcessMetrics = {
  id: string
  name: string
  department: string
  category: string
  frequency: string
  volume: number // transactions per month
  manualTime: number // minutes per transaction
  complexity: number // 1-10 scale
  repetitiveness: number // 1-10 scale
  errorRate: number // percentage
  dataStructured: number // 1-10 scale (how structured is the data)
  decisionComplexity: number // 1-10 scale (how complex are the decisions)
  systemIntegration: number // 1-10 scale (how many systems need to be integrated)
  regulatoryRequirements: number // 1-10 scale (regulatory complexity)
  stakeholderImpact: number // 1-10 scale (impact on stakeholders)
  predictedAutomationScore: number // 0-100 scale
  predictedTimeSavings: number // hours per month
  predictedCostSavings: number // $ per month
  predictedROI: number // percentage
  predictedImplementationTime: number // months
  predictedImplementationCost: number // $
  suggestedTechnology: string[]
  riskFactors: string[]
  dataPoints: number // number of data points analyzed
  confidenceScore: number // 0-100 scale
}

type DepartmentMetrics = {
  department: string
  processCount: number
  averageAutomationScore: number
  totalTimeSavings: number
  totalCostSavings: number
  averageROI: number
  icon: React.ReactNode
}

type TechnologyRecommendation = {
  name: string
  description: string
  suitableFor: string[]
  averageImplementationTime: number
  averageCost: number
  maturityScore: number // 1-10 scale
  vendorOptions: string[]
  strengthAreas: string[]
  limitationAreas: string[]
}

// Sample data for processes that could be automated
const processData: ProcessMetrics[] = [
  {
    id: "1",
    name: "Customer Data Entry",
    department: "Sales",
    category: "Data Entry",
    frequency: "Daily",
    volume: 450,
    manualTime: 8, // minutes per transaction
    complexity: 3,
    repetitiveness: 9,
    errorRate: 7.5,
    dataStructured: 8,
    decisionComplexity: 2,
    systemIntegration: 3,
    regulatoryRequirements: 4,
    stakeholderImpact: 6,
    predictedAutomationScore: 92,
    predictedTimeSavings: 60, // hours per month
    predictedCostSavings: 3000, // $ per month
    predictedROI: 380,
    predictedImplementationTime: 2.5,
    predictedImplementationCost: 15000,
    suggestedTechnology: ["RPA", "OCR", "Form Automation"],
    riskFactors: ["Data Quality", "System Changes"],
    dataPoints: 1250,
    confidenceScore: 94,
  },
  {
    id: "2",
    name: "Invoice Processing",
    department: "Finance",
    category: "Document Processing",
    frequency: "Daily",
    volume: 320,
    manualTime: 12,
    complexity: 5,
    repetitiveness: 8,
    errorRate: 6.2,
    dataStructured: 7,
    decisionComplexity: 4,
    systemIntegration: 5,
    regulatoryRequirements: 7,
    stakeholderImpact: 7,
    predictedAutomationScore: 88,
    predictedTimeSavings: 64,
    predictedCostSavings: 3200,
    predictedROI: 350,
    predictedImplementationTime: 3,
    predictedImplementationCost: 18000,
    suggestedTechnology: ["Intelligent Document Processing", "RPA", "AI Classification"],
    riskFactors: ["Vendor Invoice Formats", "Approval Workflows"],
    dataPoints: 980,
    confidenceScore: 92,
  },
  {
    id: "3",
    name: "Employee Onboarding",
    department: "HR",
    category: "Workflow",
    frequency: "Weekly",
    volume: 40,
    manualTime: 180,
    complexity: 6,
    repetitiveness: 7,
    errorRate: 5.5,
    dataStructured: 6,
    decisionComplexity: 5,
    systemIntegration: 7,
    regulatoryRequirements: 8,
    stakeholderImpact: 9,
    predictedAutomationScore: 78,
    predictedTimeSavings: 120,
    predictedCostSavings: 6000,
    predictedROI: 280,
    predictedImplementationTime: 4,
    predictedImplementationCost: 35000,
    suggestedTechnology: ["Workflow Automation", "Digital Forms", "Identity Management"],
    riskFactors: ["HR System Integration", "Compliance Requirements"],
    dataPoints: 520,
    confidenceScore: 86,
  },
  {
    id: "4",
    name: "Inventory Reconciliation",
    department: "Operations",
    category: "Data Reconciliation",
    frequency: "Weekly",
    volume: 60,
    manualTime: 90,
    complexity: 7,
    repetitiveness: 8,
    errorRate: 8.5,
    dataStructured: 6,
    decisionComplexity: 6,
    systemIntegration: 8,
    regulatoryRequirements: 5,
    stakeholderImpact: 8,
    predictedAutomationScore: 82,
    predictedTimeSavings: 90,
    predictedCostSavings: 4500,
    predictedROI: 320,
    predictedImplementationTime: 3.5,
    predictedImplementationCost: 28000,
    suggestedTechnology: ["RPA", "Data Integration", "Analytics"],
    riskFactors: ["Data Source Reliability", "Process Variations"],
    dataPoints: 780,
    confidenceScore: 88,
  },
  {
    id: "5",
    name: "Customer Support Ticket Routing",
    department: "Support",
    category: "Workflow",
    frequency: "Daily",
    volume: 280,
    manualTime: 5,
    complexity: 4,
    repetitiveness: 9,
    errorRate: 12,
    dataStructured: 5,
    decisionComplexity: 7,
    systemIntegration: 6,
    regulatoryRequirements: 3,
    stakeholderImpact: 8,
    predictedAutomationScore: 86,
    predictedTimeSavings: 23.3,
    predictedCostSavings: 1165,
    predictedROI: 310,
    predictedImplementationTime: 2,
    predictedImplementationCost: 12000,
    suggestedTechnology: ["AI Classification", "NLP", "Workflow Automation"],
    riskFactors: ["Language Complexity", "Ticket Categorization Accuracy"],
    dataPoints: 1450,
    confidenceScore: 90,
  },
  {
    id: "6",
    name: "Report Generation",
    department: "Finance",
    category: "Reporting",
    frequency: "Monthly",
    volume: 25,
    manualTime: 240,
    complexity: 6,
    repetitiveness: 10,
    errorRate: 4.5,
    dataStructured: 9,
    decisionComplexity: 3,
    systemIntegration: 5,
    regulatoryRequirements: 8,
    stakeholderImpact: 7,
    predictedAutomationScore: 94,
    predictedTimeSavings: 100,
    predictedCostSavings: 5000,
    predictedROI: 420,
    predictedImplementationTime: 2,
    predictedImplementationCost: 20000,
    suggestedTechnology: ["RPA", "BI Tools", "Scheduled Reporting"],
    riskFactors: ["Data Source Changes", "Report Format Requirements"],
    dataPoints: 350,
    confidenceScore: 96,
  },
  {
    id: "7",
    name: "Social Media Content Scheduling",
    department: "Marketing",
    category: "Content Management",
    frequency: "Daily",
    volume: 30,
    manualTime: 20,
    complexity: 4,
    repetitiveness: 8,
    errorRate: 5,
    dataStructured: 7,
    decisionComplexity: 5,
    systemIntegration: 4,
    regulatoryRequirements: 2,
    stakeholderImpact: 6,
    predictedAutomationScore: 85,
    predictedTimeSavings: 10,
    predictedCostSavings: 500,
    predictedROI: 300,
    predictedImplementationTime: 1.5,
    predictedImplementationCost: 5000,
    suggestedTechnology: ["Content Management System", "Social Media API", "Scheduling Tools"],
    riskFactors: ["Platform API Changes", "Content Approval Workflows"],
    dataPoints: 620,
    confidenceScore: 89,
  },
  {
    id: "8",
    name: "IT Service Desk Ticket Management",
    department: "IT",
    category: "Workflow",
    frequency: "Daily",
    volume: 150,
    manualTime: 15,
    complexity: 5,
    repetitiveness: 7,
    errorRate: 6,
    dataStructured: 6,
    decisionComplexity: 6,
    systemIntegration: 7,
    regulatoryRequirements: 4,
    stakeholderImpact: 7,
    predictedAutomationScore: 80,
    predictedTimeSavings: 37.5,
    predictedCostSavings: 1875,
    predictedROI: 290,
    predictedImplementationTime: 3,
    predictedImplementationCost: 22000,
    suggestedTechnology: ["ITSM Automation", "Chatbots", "Knowledge Base Integration"],
    riskFactors: ["System Integration Complexity", "User Adoption"],
    dataPoints: 890,
    confidenceScore: 85,
  },
  {
    id: "9",
    name: "Purchase Order Processing",
    department: "Operations",
    category: "Document Processing",
    frequency: "Daily",
    volume: 85,
    manualTime: 25,
    complexity: 5,
    repetitiveness: 9,
    errorRate: 7,
    dataStructured: 8,
    decisionComplexity: 4,
    systemIntegration: 6,
    regulatoryRequirements: 6,
    stakeholderImpact: 7,
    predictedAutomationScore: 89,
    predictedTimeSavings: 35.4,
    predictedCostSavings: 1770,
    predictedROI: 340,
    predictedImplementationTime: 2.5,
    predictedImplementationCost: 16000,
    suggestedTechnology: ["RPA", "Document Processing", "ERP Integration"],
    riskFactors: ["Approval Workflow Complexity", "Vendor Management"],
    dataPoints: 720,
    confidenceScore: 91,
  },
  {
    id: "10",
    name: "Customer Email Response",
    department: "Support",
    category: "Communication",
    frequency: "Daily",
    volume: 320,
    manualTime: 10,
    complexity: 6,
    repetitiveness: 6,
    errorRate: 4,
    dataStructured: 4,
    decisionComplexity: 8,
    systemIntegration: 5,
    regulatoryRequirements: 5,
    stakeholderImpact: 9,
    predictedAutomationScore: 75,
    predictedTimeSavings: 53.3,
    predictedCostSavings: 2665,
    predictedROI: 260,
    predictedImplementationTime: 4,
    predictedImplementationCost: 32000,
    suggestedTechnology: ["NLP", "Email Automation", "AI Response Generation"],
    riskFactors: ["Response Accuracy", "Customer Satisfaction", "Complex Queries"],
    dataPoints: 1580,
    confidenceScore: 82,
  },
  {
    id: "11",
    name: "Employee Expense Processing",
    department: "Finance",
    category: "Document Processing",
    frequency: "Weekly",
    volume: 120,
    manualTime: 15,
    complexity: 4,
    repetitiveness: 9,
    errorRate: 8,
    dataStructured: 7,
    decisionComplexity: 5,
    systemIntegration: 6,
    regulatoryRequirements: 7,
    stakeholderImpact: 6,
    predictedAutomationScore: 87,
    predictedTimeSavings: 30,
    predictedCostSavings: 1500,
    predictedROI: 330,
    predictedImplementationTime: 2,
    predictedImplementationCost: 14000,
    suggestedTechnology: ["Mobile App", "OCR", "Expense Management Software"],
    riskFactors: ["Receipt Quality", "Policy Compliance"],
    dataPoints: 840,
    confidenceScore: 90,
  },
  {
    id: "12",
    name: "Candidate Resume Screening",
    department: "HR",
    category: "Document Processing",
    frequency: "Daily",
    volume: 60,
    manualTime: 20,
    complexity: 7,
    repetitiveness: 7,
    errorRate: 5,
    dataStructured: 4,
    decisionComplexity: 8,
    systemIntegration: 5,
    regulatoryRequirements: 6,
    stakeholderImpact: 8,
    predictedAutomationScore: 76,
    predictedTimeSavings: 20,
    predictedCostSavings: 1000,
    predictedROI: 250,
    predictedImplementationTime: 3.5,
    predictedImplementationCost: 25000,
    suggestedTechnology: ["AI Resume Parsing", "NLP", "ATS Integration"],
    riskFactors: ["Bias in Screening", "Candidate Experience", "Resume Format Variations"],
    dataPoints: 720,
    confidenceScore: 83,
  },
  {
    id: "13",
    name: "Product Catalog Updates",
    department: "Marketing",
    category: "Data Entry",
    frequency: "Weekly",
    volume: 150,
    manualTime: 12,
    complexity: 4,
    repetitiveness: 8,
    errorRate: 6,
    dataStructured: 8,
    decisionComplexity: 3,
    systemIntegration: 7,
    regulatoryRequirements: 3,
    stakeholderImpact: 7,
    predictedAutomationScore: 88,
    predictedTimeSavings: 30,
    predictedCostSavings: 1500,
    predictedROI: 320,
    predictedImplementationTime: 2,
    predictedImplementationCost: 15000,
    suggestedTechnology: ["PIM Systems", "Data Integration", "Catalog Management Software"],
    riskFactors: ["Data Source Variations", "Image Quality"],
    dataPoints: 680,
    confidenceScore: 89,
  },
  {
    id: "14",
    name: "Software License Management",
    department: "IT",
    category: "Asset Management",
    frequency: "Monthly",
    volume: 40,
    manualTime: 60,
    complexity: 6,
    repetitiveness: 7,
    errorRate: 9,
    dataStructured: 7,
    decisionComplexity: 5,
    systemIntegration: 8,
    regulatoryRequirements: 6,
    stakeholderImpact: 6,
    predictedAutomationScore: 81,
    predictedTimeSavings: 40,
    predictedCostSavings: 2000,
    predictedROI: 280,
    predictedImplementationTime: 3,
    predictedImplementationCost: 22000,
    suggestedTechnology: ["License Management Software", "RPA", "Asset Management System"],
    riskFactors: ["License Agreement Complexity", "Software Vendor Changes"],
    dataPoints: 480,
    confidenceScore: 86,
  },
  {
    id: "15",
    name: "Quality Control Reporting",
    department: "Operations",
    category: "Reporting",
    frequency: "Daily",
    volume: 45,
    manualTime: 30,
    complexity: 7,
    repetitiveness: 8,
    errorRate: 5,
    dataStructured: 8,
    decisionComplexity: 6,
    systemIntegration: 7,
    regulatoryRequirements: 9,
    stakeholderImpact: 8,
    predictedAutomationScore: 83,
    predictedTimeSavings: 22.5,
    predictedCostSavings: 1125,
    predictedROI: 290,
    predictedImplementationTime: 3,
    predictedImplementationCost: 18000,
    suggestedTechnology: ["Quality Management System", "Data Integration", "Automated Reporting"],
    riskFactors: ["Regulatory Compliance", "Data Accuracy Requirements"],
    dataPoints: 560,
    confidenceScore: 87,
  },
  {
    id: "16",
    name: "Contract Review",
    department: "Legal",
    category: "Document Processing",
    frequency: "Weekly",
    volume: 25,
    manualTime: 180,
    complexity: 9,
    repetitiveness: 5,
    errorRate: 3,
    dataStructured: 4,
    decisionComplexity: 9,
    systemIntegration: 5,
    regulatoryRequirements: 9,
    stakeholderImpact: 9,
    predictedAutomationScore: 68,
    predictedTimeSavings: 75,
    predictedCostSavings: 3750,
    predictedROI: 210,
    predictedImplementationTime: 5,
    predictedImplementationCost: 45000,
    suggestedTechnology: ["Contract Analysis AI", "NLP", "Legal Document Management"],
    riskFactors: ["Legal Accuracy", "Complex Clauses", "Regulatory Changes"],
    dataPoints: 380,
    confidenceScore: 76,
  },
  {
    id: "17",
    name: "Customer Feedback Analysis",
    department: "Marketing",
    category: "Data Analysis",
    frequency: "Weekly",
    volume: 200,
    manualTime: 10,
    complexity: 7,
    repetitiveness: 6,
    errorRate: 4,
    dataStructured: 5,
    decisionComplexity: 8,
    systemIntegration: 6,
    regulatoryRequirements: 4,
    stakeholderImpact: 8,
    predictedAutomationScore: 79,
    predictedTimeSavings: 33.3,
    predictedCostSavings: 1665,
    predictedROI: 270,
    predictedImplementationTime: 3,
    predictedImplementationCost: 24000,
    suggestedTechnology: ["Sentiment Analysis", "NLP", "Customer Feedback Platform"],
    riskFactors: ["Language Nuances", "Context Understanding"],
    dataPoints: 920,
    confidenceScore: 84,
  },
  {
    id: "18",
    name: "Sales Forecasting",
    department: "Sales",
    category: "Data Analysis",
    frequency: "Monthly",
    volume: 15,
    manualTime: 240,
    complexity: 8,
    repetitiveness: 7,
    errorRate: 6,
    dataStructured: 7,
    decisionComplexity: 9,
    systemIntegration: 7,
    regulatoryRequirements: 5,
    stakeholderImpact: 9,
    predictedAutomationScore: 77,
    predictedTimeSavings: 60,
    predictedCostSavings: 3000,
    predictedROI: 240,
    predictedImplementationTime: 4,
    predictedImplementationCost: 35000,
    suggestedTechnology: ["Predictive Analytics", "ML Forecasting", "BI Tools"],
    riskFactors: ["Market Volatility", "Data Quality", "Model Accuracy"],
    dataPoints: 420,
    confidenceScore: 82,
  },
  {
    id: "19",
    name: "IT System Monitoring",
    department: "IT",
    category: "Monitoring",
    frequency: "Daily",
    volume: 100,
    manualTime: 15,
    complexity: 6,
    repetitiveness: 10,
    errorRate: 7,
    dataStructured: 9,
    decisionComplexity: 6,
    systemIntegration: 8,
    regulatoryRequirements: 5,
    stakeholderImpact: 7,
    predictedAutomationScore: 91,
    predictedTimeSavings: 25,
    predictedCostSavings: 1250,
    predictedROI: 360,
    predictedImplementationTime: 2,
    predictedImplementationCost: 12000,
    suggestedTechnology: ["Monitoring Tools", "Alert Systems", "Automated Remediation"],
    riskFactors: ["False Positives", "System Complexity"],
    dataPoints: 850,
    confidenceScore: 93,
  },
  {
    id: "20",
    name: "Supplier Performance Tracking",
    department: "Operations",
    category: "Data Analysis",
    frequency: "Monthly",
    volume: 50,
    manualTime: 90,
    complexity: 7,
    repetitiveness: 8,
    errorRate: 5,
    dataStructured: 7,
    decisionComplexity: 7,
    systemIntegration: 6,
    regulatoryRequirements: 6,
    stakeholderImpact: 8,
    predictedAutomationScore: 82,
    predictedTimeSavings: 75,
    predictedCostSavings: 3750,
    predictedROI: 300,
    predictedImplementationTime: 3,
    predictedImplementationCost: 28000,
    suggestedTechnology: ["Supplier Management System", "Data Integration", "Analytics Dashboard"],
    riskFactors: ["Data Source Reliability", "Metric Definition Changes"],
    dataPoints: 620,
    confidenceScore: 88,
  },
]

// Technology recommendations
const technologyRecommendations: TechnologyRecommendation[] = [
  {
    name: "Robotic Process Automation (RPA)",
    description:
      "Software robots that mimic human actions interacting with digital systems and software to execute tasks.",
    suitableFor: [
      "Data Entry",
      "Document Processing",
      "Reporting",
      "Data Reconciliation",
      "Form Processing",
      "System Integration",
    ],
    averageImplementationTime: 2.5,
    averageCost: 20000,
    maturityScore: 9,
    vendorOptions: ["UiPath", "Automation Anywhere", "Blue Prism", "Microsoft Power Automate"],
    strengthAreas: [
      "Structured data processing",
      "Rule-based tasks",
      "Cross-system integration",
      "High volume repetitive work",
    ],
    limitationAreas: ["Unstructured data", "Complex decision making", "Highly variable processes"],
  },
  {
    name: "Optical Character Recognition (OCR)",
    description: "Technology that recognizes text within digital images of physical documents.",
    suitableFor: ["Document Processing", "Data Entry", "Form Processing", "Invoice Processing"],
    averageImplementationTime: 2,
    averageCost: 15000,
    maturityScore: 8,
    vendorOptions: ["ABBYY", "Kofax", "Microsoft Azure Form Recognizer", "Google Document AI"],
    strengthAreas: ["Document digitization", "Form extraction", "Structured document processing"],
    limitationAreas: ["Handwritten text", "Poor quality images", "Complex layouts"],
  },
  {
    name: "Natural Language Processing (NLP)",
    description: "AI technology that gives computers the ability to understand text and spoken words.",
    suitableFor: ["Communication", "Document Processing", "Customer Support", "Content Analysis"],
    averageImplementationTime: 3.5,
    averageCost: 30000,
    maturityScore: 7,
    vendorOptions: ["IBM Watson", "Google Cloud NLP", "Microsoft Azure Language", "Amazon Comprehend"],
    strengthAreas: ["Text classification", "Sentiment analysis", "Entity extraction", "Language understanding"],
    limitationAreas: ["Domain-specific terminology", "Contextual understanding", "Sarcasm and nuance"],
  },
  {
    name: "Machine Learning Forecasting",
    description: "Predictive models that learn from historical data to make future predictions.",
    suitableFor: ["Data Analysis", "Forecasting", "Demand Planning", "Risk Assessment"],
    averageImplementationTime: 4,
    averageCost: 35000,
    maturityScore: 7,
    vendorOptions: ["DataRobot", "H2O.ai", "Amazon Forecast", "Google Cloud AI"],
    strengthAreas: ["Pattern recognition", "Trend analysis", "Complex correlations", "Continuous learning"],
    limitationAreas: ["Data quality dependencies", "Black box decision making", "Requires large datasets"],
  },
  {
    name: "Workflow Automation",
    description: "Software that automates business processes according to defined workflows.",
    suitableFor: ["Workflow", "Approval Processes", "Task Management", "Process Orchestration"],
    averageImplementationTime: 3,
    averageCost: 25000,
    maturityScore: 8,
    vendorOptions: ["ServiceNow", "Pega", "Kissflow", "Nintex"],
    strengthAreas: ["Process standardization", "Task routing", "Status tracking", "Integration capabilities"],
    limitationAreas: ["Highly customized processes", "Complex decision trees", "Legacy system integration"],
  },
  {
    name: "Intelligent Document Processing",
    description: "AI-powered solution that can extract, classify and process data from various document formats.",
    suitableFor: ["Document Processing", "Contract Management", "Invoice Processing", "Form Processing"],
    averageImplementationTime: 3,
    averageCost: 28000,
    maturityScore: 8,
    vendorOptions: ["Kofax", "ABBYY", "Hyperscience", "Automation Hero"],
    strengthAreas: ["Unstructured document handling", "Multi-format processing", "Data extraction accuracy"],
    limitationAreas: ["Very complex documents", "Highly variable layouts", "Low quality scans"],
  },
  {
    name: "Chatbots & Virtual Assistants",
    description: "Conversational AI systems that can interact with users and perform tasks.",
    suitableFor: ["Customer Support", "Employee Support", "Information Retrieval", "Simple Transactions"],
    averageImplementationTime: 3,
    averageCost: 25000,
    maturityScore: 7,
    vendorOptions: ["IBM Watson Assistant", "Microsoft Bot Framework", "Dialogflow", "Amazon Lex"],
    strengthAreas: ["24/7 availability", "Consistent responses", "Scalable interactions", "Multi-channel support"],
    limitationAreas: ["Complex conversations", "Emotional intelligence", "Highly specialized knowledge"],
  },
  {
    name: "Business Intelligence Automation",
    description: "Tools that automate data collection, analysis, and visualization for business insights.",
    suitableFor: ["Reporting", "Data Analysis", "Dashboard Creation", "KPI Monitoring"],
    averageImplementationTime: 2.5,
    averageCost: 22000,
    maturityScore: 9,
    vendorOptions: ["Tableau", "Power BI", "Qlik", "Looker"],
    strengthAreas: ["Data visualization", "Scheduled reporting", "Interactive dashboards", "Data integration"],
    limitationAreas: ["Very complex analysis", "Real-time processing needs", "Unstructured data sources"],
  },
]

// Calculate department metrics
const calculateDepartmentMetrics = (): DepartmentMetrics[] => {
  const departments = [
    { name: "Finance", icon: <FileSpreadsheet className="h-4 w-4" /> },
    { name: "Sales", icon: <ShoppingCart className="h-4 w-4" /> },
    { name: "Marketing", icon: <BarChart2 className="h-4 w-4" /> },
    { name: "Operations", icon: <Truck className="h-4 w-4" /> },
    { name: "HR", icon: <Users className="h-4 w-4" /> },
    { name: "Support", icon: <Headphones className="h-4 w-4" /> },
    { name: "IT", icon: <Server className="h-4 w-4" /> },
    { name: "Legal", icon: <FileText className="h-4 w-4" /> },
  ]

  return departments
    .map((dept) => {
      const departmentProcesses = processData.filter((process) => process.department === dept.name)
      const processCount = departmentProcesses.length

      if (processCount === 0) {
        return {
          department: dept.name,
          processCount: 0,
          averageAutomationScore: 0,
          totalTimeSavings: 0,
          totalCostSavings: 0,
          averageROI: 0,
          icon: dept.icon,
        }
      }

      const averageAutomationScore =
        departmentProcesses.reduce((sum, process) => sum + process.predictedAutomationScore, 0) / processCount
      const totalTimeSavings = departmentProcesses.reduce((sum, process) => sum + process.predictedTimeSavings, 0)
      const totalCostSavings = departmentProcesses.reduce((sum, process) => sum + process.predictedCostSavings, 0)
      const averageROI = departmentProcesses.reduce((sum, process) => sum + process.predictedROI, 0) / processCount

      return {
        department: dept.name,
        processCount,
        averageAutomationScore,
        totalTimeSavings,
        totalCostSavings,
        averageROI,
        icon: dept.icon,
      }
    })
    .filter((dept) => dept.processCount > 0)
}

// Calculate overall metrics
const calculateOverallMetrics = () => {
  const totalProcesses = processData.length
  const highPotentialProcesses = processData.filter((process) => process.predictedAutomationScore >= 85).length
  const mediumPotentialProcesses = processData.filter(
    (process) => process.predictedAutomationScore >= 70 && process.predictedAutomationScore < 85,
  ).length
  const lowPotentialProcesses = processData.filter((process) => process.predictedAutomationScore < 70).length

  const totalTimeSavings = processData.reduce((sum, process) => sum + process.predictedTimeSavings, 0)
  const totalCostSavings = processData.reduce((sum, process) => sum + process.predictedCostSavings, 0)
  const averageROI = processData.reduce((sum, process) => sum + process.predictedROI, 0) / totalProcesses
  const averageImplementationTime =
    processData.reduce((sum, process) => sum + process.predictedImplementationTime, 0) / totalProcesses
  const totalImplementationCost = processData.reduce((sum, process) => sum + process.predictedImplementationCost, 0)

  const averageAutomationScore =
    processData.reduce((sum, process) => sum + process.predictedAutomationScore, 0) / totalProcesses
  const averageConfidenceScore = processData.reduce((sum, process) => sum + process.confidenceScore, 0) / totalProcesses

  return {
    totalProcesses,
    highPotentialProcesses,
    mediumPotentialProcesses,
    lowPotentialProcesses,
    totalTimeSavings,
    totalCostSavings,
    averageROI,
    averageImplementationTime,
    totalImplementationCost,
    averageAutomationScore,
    averageConfidenceScore,
    annualTimeSavings: totalTimeSavings * 12, // monthly to annual
    annualCostSavings: totalCostSavings * 12, // monthly to annual
  }
}

// Prepare data for charts
const prepareAutomationFactorsData = () => {
  return [
    { factor: "Repetitiveness", score: processData.reduce((sum, p) => sum + p.repetitiveness, 0) / processData.length },
    { factor: "Volume", score: 7.8 }, // Normalized score for volume across processes
    { factor: "Error Rate", score: processData.reduce((sum, p) => sum + p.errorRate / 10, 0) / processData.length },
    {
      factor: "Data Structure",
      score: processData.reduce((sum, p) => sum + p.dataStructured, 0) / processData.length,
    },
    {
      factor: "Decision Complexity",
      score: 10 - processData.reduce((sum, p) => sum + p.decisionComplexity, 0) / processData.length, // Inverted
    },
    {
      factor: "System Integration",
      score: 10 - processData.reduce((sum, p) => sum + p.systemIntegration, 0) / processData.length, // Inverted
    },
    {
      factor: "Regulatory Requirements",
      score: 10 - processData.reduce((sum, p) => sum + p.regulatoryRequirements, 0) / processData.length, // Inverted
    },
  ]
}

const prepareCategoryData = () => {
  const categories = Array.from(new Set(processData.map((process) => process.category)))
  return categories.map((category) => {
    const categoryProcesses = processData.filter((process) => process.category === category)
    const averageAutomationScore =
      categoryProcesses.reduce((sum, process) => sum + process.predictedAutomationScore, 0) / categoryProcesses.length
    const totalTimeSavings = categoryProcesses.reduce((sum, process) => sum + process.predictedTimeSavings, 0)
    const totalCostSavings = categoryProcesses.reduce((sum, process) => sum + process.predictedCostSavings, 0)
    const processCount = categoryProcesses.length

    return {
      category,
      averageAutomationScore,
      totalTimeSavings,
      totalCostSavings,
      processCount,
    }
  })
}

const prepareImplementationData = () => {
  // Group processes by implementation time (quarters)
  const q1Processes = processData.filter((p) => p.predictedImplementationTime <= 1.5)
  const q2Processes = processData.filter(
    (p) => p.predictedImplementationTime > 1.5 && p.predictedImplementationTime <= 3,
  )
  const q3Processes = processData.filter(
    (p) => p.predictedImplementationTime > 3 && p.predictedImplementationTime <= 4.5,
  )
  const q4Processes = processData.filter((p) => p.predictedImplementationTime > 4.5)

  return [
    {
      quarter: "Q1",
      processes: q1Processes.length,
      timeSavings: q1Processes.reduce((sum, p) => sum + p.predictedTimeSavings, 0),
      costSavings: q1Processes.reduce((sum, p) => sum + p.predictedCostSavings, 0),
      implementationCost: q1Processes.reduce((sum, p) => sum + p.predictedImplementationCost, 0),
    },
    {
      quarter: "Q2",
      processes: q2Processes.length,
      timeSavings: q2Processes.reduce((sum, p) => sum + p.predictedTimeSavings, 0),
      costSavings: q2Processes.reduce((sum, p) => sum + p.predictedCostSavings, 0),
      implementationCost: q2Processes.reduce((sum, p) => sum + p.predictedImplementationCost, 0),
    },
    {
      quarter: "Q3",
      processes: q3Processes.length,
      timeSavings: q3Processes.reduce((sum, p) => sum + p.predictedTimeSavings, 0),
      costSavings: q3Processes.reduce((sum, p) => sum + p.predictedCostSavings, 0),
      implementationCost: q3Processes.reduce((sum, p) => sum + p.predictedImplementationCost, 0),
    },
    {
      quarter: "Q4",
      processes: q4Processes.length,
      timeSavings: q4Processes.reduce((sum, p) => sum + p.predictedTimeSavings, 0),
      costSavings: q4Processes.reduce((sum, p) => sum + p.predictedCostSavings, 0),
      implementationCost: q4Processes.reduce((sum, p) => sum + p.predictedImplementationCost, 0),
    },
  ]
}

const prepareScatterData = () => {
  return processData.map((process) => ({
    id: process.id,
    name: process.name,
    x: process.predictedROI,
    y: process.predictedImplementationTime,
    z: process.predictedTimeSavings,
    department: process.department,
    automationScore: process.predictedAutomationScore,
  }))
}

const COLORS = ["#4CAF50", "#FFC107", "#2196F3", "#FF5722", "#9C27B0", "#607D8B", "#E91E63", "#795548"]

export function AutomationOpportunityPredictor() {
  const [selectedProcess, setSelectedProcess] = useState<ProcessMetrics | null>(null)
  const [departmentFilter, setDepartmentFilter] = useState("All")
  const [categoryFilter, setCategoryFilter] = useState("All")
  const [scoreFilter, setScoreFilter] = useState("All")
  const [searchQuery, setSearchQuery] = useState("")
  const [timeRange, setTimeRange] = useState("all")
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [thresholds, setThresholds] = useState({
    automationScore: 70,
    roi: 200,
    implementationTime: 4,
  })

  // Calculate metrics
  const overallMetrics = calculateOverallMetrics()
  const departmentMetrics = calculateDepartmentMetrics()
  const automationFactorsData = prepareAutomationFactorsData()
  const categoryData = prepareCategoryData()
  const implementationData = prepareImplementationData()
  const scatterData = prepareScatterData()

  // Filter processes based on selected filters
  const filteredProcesses = processData.filter((process) => {
    if (departmentFilter !== "All" && process.department !== departmentFilter) return false
    if (categoryFilter !== "All" && process.category !== categoryFilter) return false
    if (
      scoreFilter === "High" &&
      process.predictedAutomationScore < 85
    )
      return false
    if (
      scoreFilter === "Medium" &&
      (process.predictedAutomationScore < 70 || process.predictedAutomationScore >= 85)
    )
      return false
    if (scoreFilter === "Low" && process.predictedAutomationScore >= 70) return false
    if (searchQuery && !process.name.toLowerCase().includes(searchQuery.toLowerCase())) return false
    
    if (showAdvancedFilters) {
      if (process.predictedAutomationScore < thresholds.automationScore) return false
      if (process.predictedROI < thresholds.roi) return false
      if (process.predictedImplementationTime > thresholds.implementationTime) return false
    }
    
    return true
  })

  // Sort processes by automation score (descending)
  const sortedProcesses = [...filteredProcesses].sort((a, b) => b.predictedAutomationScore - a.predictedAutomationScore)

  // Get recommended technologies for selected process
  const getRecommendedTechnologies = (process: ProcessMetrics) => {
    if (!process) return []
    return technologyRecommendations.filter((tech) => process.suggestedTechnology.includes(tech.name))
  }

  // Get automation score color
  const getAutomationScoreColor = (score: number) => {
    if (score >= 85) return "bg-green-100 text-green-800"
    if (score >= 70) return "bg-amber-100 text-amber-800"
    return "bg-gray-100 text-gray-800"
  }

  // Get ROI color
  const getROIColor = (roi: number) => {
    if (roi >= 300) return "bg-green-100 text-green-800"
    if (roi >= 200) return "bg-amber-100 text-amber-800"
    return "bg-gray-100 text-gray-800"
  }

  // Get implementation time color
  const getImplementationTimeColor = (time: number) => {
    if (time <= 2) return "bg-green-100 text-green-800"
    if (time <= 4) return "bg-amber-100 text-amber-800"
    return "bg-red-100 text-red-800"
  }

  // Get department icon
  const getDepartmentIcon = (department: string) => {
    switch (department) {
      case "Finance":
        return <FileSpreadsheet className="h-4 w-4" />
      case "Sales":
        return <ShoppingCart className="h-4 w-4" />
      case "Marketing":
        return <BarChart2 className="h-4 w-4" />
      case "Operations":
        return <Truck className="h-4 w-4" />
      case "HR":
        return <Users className="h-4 w-4" />
      case "Support":
        return <Headphones className="h-4 w-4" />
      case "IT":
        return <Server className="h-4 w-4" />
      case "Legal":
        return <FileText className="h-4 w-4" />
      default:
        return <Lightbulb className="h-4 w-4" />
    }
  }

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Data Entry":
        return <Pencil className="h-4 w-4" />
      case "Document Processing":
        return <FileCheck className="h-4 w-4" />
      case "Workflow":
        return <Workflow className="h-4 w-4" />
      case "Data Reconciliation":
        return <FileSpreadsheet className="h-4 w-4" />
      case "Reporting":
        return <BarChart3 className="h-4 w-4" />
      case "Content Management":
        return <Clipboard className="h-4 w-4" />
      case "Communication":
        return <MessageSquare className="h-4 w-4" />
      case "Data Analysis":
        return <BarChart2 className="h-4 w-4" />
      case "Asset Management":
        return <Database className="h-4 w-4" />
      case "Monitoring":
        return <Gauge className="h-4 w-4" />
      default:
        return <Settings className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Automation Opportunity Predictor</h2>
          <p className="text-muted-foreground">
            AI-powered analysis to identify and prioritize future automation opportunities
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Share2 className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Process
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Automation Potential</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallMetrics.totalProcesses}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <div className="flex items-center mr-2">
                <div className="h-2 w-2 rounded-full bg-green-500 mr-1"></div>
                <span>High: {overallMetrics.highPotentialProcesses}</span>
              </div>
              <div className="flex items-center mr-2">
                <div className="h-2 w-2 rounded-full bg-amber-500 mr-1"></div>
                <span>Medium: {overallMetrics.mediumPotentialProcesses}</span>
              </div>
              <div className="flex items-center">
                <div className="h-2 w-2 rounded-full bg-gray-400 mr-1"></div>
                <span>Low: {overallMetrics.lowPotentialProcesses}</span>
              </div>
            </div>
            <div className="mt-3">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>Avg. Score: {overallMetrics.averageAutomationScore.toFixed(1)}%</span>
                <span>Confidence: {overallMetrics.averageConfidenceScore.toFixed(1)}%</span>
              </div>
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-500 rounded-full"
                  style={{ width: `${overallMetrics.averageAutomationScore}%` }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Projected Time Savings</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallMetrics.totalTimeSavings.toFixed(0)} hrs/month</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <ArrowUpRight className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500 font-medium">
                {overallMetrics.annualTimeSavings.toFixed(0)} hours
              </span>
              <span className="ml-1">annually</span>
            </div>
            <div className="mt-3">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>Avg. per process: {(overallMetrics.totalTimeSavings / overallMetrics.totalProcesses).toFixed(1)} hrs</span>
                <span>FTE equivalent: {(overallMetrics.annualTimeSavings / 2080).toFixed(1)}</span>
              </div>
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-500 rounded-full"
                  style={{ width: "75%" }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Projected Cost Savings</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${overallMetrics.totalCostSavings.toLocaleString()}/month</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <ArrowUpRight className="mr-1 h-4 w-4 text-green-500" />
              <span className="text-green-500 font-medium">
                ${overallMetrics.annualCostSavings.toLocaleString()}
              </span>
              <span className="ml-1">annually</span>
            </div>
            <div className="mt-3">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>Avg. ROI: {overallMetrics.averageROI.toFixed(0)}%</span>
                <span>Implementation: ${overallMetrics.totalImplementationCost.toLocaleString()}</span>
              </div>
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-500 rounded-full"
                  style={{ width: `${Math.min(overallMetrics.averageROI / 5, 100)}%` }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Implementation Timeline</CardTitle>
            <CalendarClock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallMetrics.averageImplementationTime.toFixed(1)} months</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="mr-1 h-4 w-4 text-amber-500" />
              <span className="text-amber-500 font-medium">
                {(overallMetrics.totalCostSavings / overallMetrics.totalImplementationCost).toFixed(1)}x
              </span>
              <span className="ml-1">monthly return vs. cost</span>
            </div>
            <div className="mt-3">
              <div className="flex justify-between text-xs text-muted-foreground mb-1">
                <span>Payback: {(overallMetrics.totalImplementationCost / overallMetrics.totalCostSavings).toFixed(1)} months</span>
                <span>5-Year ROI: {((overallMetrics.annualCostSavings * 5 - overallMetrics.totalImplementationCost) / overallMetrics.totalImplementationCost * 100).toFixed(0)}%</span>
              </div>
              <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-amber-500 rounded-full"
                  style={{ width: `${Math.min((overallMetrics.averageImplementationTime / 6) * 100, 100)}%` }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="opportunities" className="space-y-4">
        <TabsList>
          <TabsTrigger value="opportunities">Opportunities</TabsTrigger>
          <TabsTrigger value="analysis">Analysis</TabsTrigger>
          <TabsTrigger value="technologies">Technologies</TabsTrigger>
          <TabsTrigger value="implementation">Implementation Plan</TabsTrigger>
        </TabsList>

        <TabsContent value="opportunities" className="space-y-4">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div className="flex flex-col gap-2 md:flex-row md:items-center">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search processes..."
                  className="w-full md:w-[200px] pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All Departments</SelectItem>
                  {Array.from(new Set(processData.map((p) => p.department))).map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="Process Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All Categories</SelectItem>
                  {Array.from(new Set(processData.map((p) => p.category))).map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={scoreFilter} onValueChange={setScoreFilter}>
                <SelectTrigger className="w-full md:w-[180px]">
                  <SelectValue placeholder="Automation Potential" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All Potential</SelectItem>
                  <SelectItem value="High">High Potential (85%+)</SelectItem>
                  <SelectItem value="Medium">Medium Potential (70-84%)</SelectItem>
                  <SelectItem value="Low">Low Potential (Below 70%)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                className="flex items-center gap-1"
              >
                <Sliders className="h-4 w-4" />
                {showAdvancedFilters ? "Hide Filters" : "Advanced Filters"}
              </Button>
              <Button variant="outline" size="sm" onClick={() => {
                setDepartmentFilter("All")
                setCategoryFilter("All")
                setScoreFilter("All")
                setSearchQuery("")
                setShowAdvancedFilters(false)
                setThresholds({
                  automationScore: 70,
                  roi: 200,
                  implementationTime: 4,
                })
              }}>
                Reset Filters
              </Button>
            </div>
          </div>

          {showAdvancedFilters && (
            <Card>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label>Min. Automation Score: {thresholds.automationScore}%</Label>
                      <span className="text-xs text-muted-foreground">Higher is better</span>
                    </div>
                    <Slider
                      value={[thresholds.automationScore]}
                      min={0}
                      max={100}
                      step={5}
                      onValueChange={(value) => setThresholds({ ...thresholds, automationScore: value[0] })}
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label>Min. ROI: {thresholds.roi}%</Label>
                      <span className="text-xs text-muted-foreground">Higher is better</span>
                    </div>
                    <Slider
                      value={[thresholds.roi]}
                      min={0}
                      max={500}
                      step={10}
                      onValueChange={(value) => setThresholds({ ...thresholds, roi: value[0] })}
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label>Max. Implementation Time: {thresholds.implementationTime} months</Label>
                      <span className="text-xs text-muted-foreground">Lower is better</span>
                    </div>
                    <Slider
                      value={[thresholds.implementationTime]}
                      min={1}
                      max={6}
                      step={0.5}
                      onValueChange={(value) => setThresholds({ ...thresholds, implementationTime: value[0] })}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Process Name</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Automation Score</TableHead>
                  <TableHead>Time Savings</TableHead>
                  <TableHead>Cost Savings</TableHead>
                  <TableHead>ROI</TableHead>
                  <TableHead>Implementation</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedProcesses.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="h-24 text-center">
                      No processes found matching your filters.
                    </TableCell>
                  </TableRow>
                ) : (
                  sortedProcesses.map((process) => (
                    <TableRow
                      key={process.id}
                      className="cursor-pointer"
                      onClick={() => setSelectedProcess(process)}
                    >
                      <TableCell className="font-medium">{process.name}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {getD

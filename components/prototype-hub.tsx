"use client"

import React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from "@/components/ui/dialog"
import PrototypeBuilder from "@/components/prototype-builder"

interface PrototypeHubProps {
  productId: string
  analysesId?: string
  onBack: () => void
}

export default function PrototypeHub({ productId, analysesId, onBack }: PrototypeHubProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Prototype Workspace</h3>
        <Button variant="outline" size="sm" onClick={onBack}>Back to Analyses</Button>
      </div>
      <Tabs defaultValue="new" className="w-full">
        <TabsList>
          <TabsTrigger value="new">New Prototype</TabsTrigger>
          <TabsTrigger value="prompts">Prompt List</TabsTrigger>
          <TabsTrigger value="media">Media</TabsTrigger>
        </TabsList>

        <TabsContent value="new" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>New Prototype</CardTitle>
              <CardDescription>Open the builder to configure and submit a prototype.</CardDescription>
            </CardHeader>
            <CardContent>
              <Dialog>
                <DialogTrigger asChild>
                  <Button>Open Prototype Builder</Button>
                </DialogTrigger>
                <DialogContent className="max-w-6xl w-[1200px] max-h-[90vh] overflow-hidden">
                  <DialogHeader>
                    <DialogTitle>Prototype Builder</DialogTitle>
                    <DialogDescription>Define content options and enter a prompt.</DialogDescription>
                  </DialogHeader>
                  <div className="h-[72vh] pr-2 overflow-auto">
                    <PrototypeBuilder productId={productId} analysesId={analysesId} />
                  </div>
                </DialogContent>
              </Dialog>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="prompts" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Prompt List</CardTitle>
              <CardDescription>Saved or recommended prompts will appear here.</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">No prompts yet.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="media" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Media</CardTitle>
              <CardDescription>Generated or attached media will appear here.</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">No media yet.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

interface PerformanceData {
  month: string
  year: number
  count: number
}

interface OverviewProps {
  performanceData?: PerformanceData[]
}

// Fallback data for when no performance data is available
const fallbackData = [
  {
    name: "<PERSON>",
    count: 4000,
  },
  {
    name: "Feb",
    count: 3000,
  },
  {
    name: "<PERSON>",
    count: 2000,
  },
  {
    name: "Apr",
    count: 2780,
  },
  {
    name: "May",
    count: 1890,
  },
  {
    name: "<PERSON>",
    count: 2390,
  },
  {
    name: "Jul",
    count: 3490,
  },
]

export function Overview({ performanceData }: OverviewProps) {
  // Transform the API data to chart format
  const chartData = performanceData && performanceData.length > 0
    ? performanceData.map(item => ({
        name: item.month,
        count: item.count,
      }))
    : fallbackData

  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart data={chartData}>
        <XAxis
          dataKey="name"
          stroke="#888888"
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        <YAxis
          stroke="#888888"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => `${value}`}
        />
        <Bar
          dataKey="count"
          fill="#8b5cf6"
          radius={[4, 4, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  )
}

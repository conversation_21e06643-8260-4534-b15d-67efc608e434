"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DialogClose } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { RefreshCw, Wand2 } from "lucide-react"
import { ContentTypeStyleCard, ContentTypeStyleValues } from "@/components/content-type-style-card"
import { useToast } from "@/hooks/use-toast"
import { productService } from "@/lib/api/products"

interface PrototypeBuilderProps {
  productId: string
  analysesId?: string
  editingPrototype?: any
  onSuccess?: () => void
  productData?: any
  analysisData?: any
  inDialog?: boolean
}

export default function PrototypeBuilder({ productId, analysesId, editingPrototype, onSuccess, productData, analysisData, inDialog = false }: PrototypeBuilderProps) {
  const { toast } = useToast()
  const [contentType, setContentType] = useState<string>("")
  const [mediaType, setMediaType] = useState<"video" | "image" | "text" | "">("")
  const [videoType, setVideoType] = useState<string>("")
  const [duration, setDuration] = useState<number | null>(null)
  const [cameraAngle, setCameraAngle] = useState<string>("")
  const [cameraMovement, setCameraMovement] = useState<string>("")
  const [lightingStyle, setLightingStyle] = useState<string>("")
  const [imageType, setImageType] = useState<string>("")
  const [imageDimensions, setImageDimensions] = useState<string>("")
  const [style, setStyle] = useState<string>("")
  const [tone, setTone] = useState<string>("")
  const [voiceTone, setVoiceTone] = useState<string>("")
  const [aiGenerationType, setAiGenerationType] = useState<string>("")
  const [targetAudience, setTargetAudience] = useState<string>("")
  const [prompt, setPrompt] = useState<string>("")
  const [generateFile, setGenerateFile] = useState<"yes" | "no">("no")
  const [submitting, setSubmitting] = useState(false)
  const [existingPrototype, setExistingPrototype] = useState<any>(null)
  const [showUpdateOption, setShowUpdateOption] = useState(false)
  const [generatingPrompt, setGeneratingPrompt] = useState(false)

  // Local UI state for extra options
  const [formData, setFormData] = useState<any>({
    content_options: {},
    include_product_info: false,
    include_pricing: false,
    include_call_to_action: false,
    platforms: [],
  })

  // Dynamic content options based on media type
  const getContentOptions = () => {
    const baseOptions = {
      include_captions: formData.content_options?.include_captions || false,
      include_product_info: formData.include_product_info || false,
      include_pricing: formData.include_pricing || false,
      include_call_to_action: formData.include_call_to_action || false,
    }

    if (mediaType === "video") {
      return {
        ...baseOptions,
        // Video-specific options
        include_subtitles: formData.content_options?.include_subtitles || false,
        include_background_music: formData.content_options?.include_background_music || true,
        include_transitions: formData.content_options?.include_transitions || true,
      }
    }

    if (mediaType === "image") {
      return {
        ...baseOptions,
        // Image-specific options
        include_text_overlay: formData.content_options?.include_text_overlay || false,
        include_logo: formData.content_options?.include_logo || true,
      }
    }

    return baseOptions
  }

  const updateFormData = (key: string, value: any) => {
    setFormData((prev: any) => {
      if (key === 'content_options') {
        return { ...prev, content_options: { ...(prev?.content_options || {}), ...(value || {}) } }
      }
      return { ...prev, [key]: value }
    })
  }

  const togglePlatform = (id: string) => {
    setFormData((prev: any) => {
      const exists = (prev.platforms || []).includes(id)
      return {
        ...prev,
        platforms: exists ? prev.platforms.filter((p: string) => p !== id) : [...(prev.platforms || []), id],
      }
    })
  }

  const handleGenerate = async () => {
    if (submitting) return
    setSubmitting(true)
    try {
      const isEditing = !!editingPrototype
      // Build dynamic payload based on media type
      const basePayload: any = {
        ...(analysesId ? { analyses_id: analysesId } : {}),
        custom_prompt: prompt,
        generate_file: generateFile === 'yes',
        content_type: contentType || undefined,
        media_type: mediaType || undefined,
        style: style || undefined,
        tone: tone || undefined,
        target_audience: targetAudience || "all",
        content_options: getContentOptions(),
      }

      // Add media-specific fields dynamically
      if (mediaType === "video") {
        Object.assign(basePayload, {
          video_type: videoType || "tutorial",
          duration: duration ?? 15,
          camera_angle: cameraAngle || "top_down",
          camera_movement: cameraMovement || "static",
          lighting_style: lightingStyle || "soft",
          voice_tone: voiceTone || undefined,
          ai_generation_type: aiGenerationType || undefined,
        })
      } else if (mediaType === "image") {
        Object.assign(basePayload, {
          image_type: imageType || undefined,
          image_dimensions: imageDimensions || undefined,
          ai_generation_type: aiGenerationType || undefined,
        })
      }

      // Add platform distribution if platforms are selected
      if (formData.platforms && formData.platforms.length > 0) {
        const platformDistribution: any = {}
        formData.platforms.forEach((platform: string) => {
          platformDistribution[platform] = {
            optimize_for_retention: false,
            include_hashtags: platform === 'tiktok' || platform === 'instagram',
            aspect_ratio: mediaType === "video" ? "portrait" : "landscape"
          }
        })
        basePayload.platform_distribution = platformDistribution
      }

      const payload = basePayload

      // Use update API if editing, create API if new
      const res: any = isEditing
        ? await productService.updatePrototype(editingPrototype.id, payload)
        : await productService.createPrototype(productId, payload)

      // Handle the response
      if (res && res.success === false) {
        // Check if it's an existing prototype case
        if (res.data && res.data.existing_prototype && res.data.can_update) {
          setExistingPrototype(res.data)
          setShowUpdateOption(true)
          toast({
            title: 'Prototype Already Exists',
            description: res.message,
            variant: 'destructive',
          })
        } else {
          toast({
            title: 'Request not submitted',
            description: res.message || 'The server reported a problem.',
            variant: 'destructive',
          })
        }
        return
      }

      toast({
        title: isEditing ? 'Prototype Updated' : 'Prototype Requested',
        description: isEditing
          ? 'Your prototype has been updated successfully.'
          : 'Your prototype generation has been submitted successfully.'
      })

      // Call success callback if provided
      if (onSuccess) {
        onSuccess()
      }
    } catch (e: any) {
      console.error(e)

      // Handle authentication errors specifically
      if (e.isAuthError) {
        toast({
          title: 'Authentication Required',
          description: e.message || 'Your session has expired. Please login again.',
          variant: 'destructive'
        })

        // Redirect to login page after a short delay
        setTimeout(() => {
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login?expired=true'
          }
        }, 2000)
      } else {
        toast({
          title: 'Failed to submit',
          description: e?.message || 'Something went wrong',
          variant: 'destructive'
        })
      }
    } finally {
      setSubmitting(false)
    }
  }

  const handleUpdateExisting = async () => {
    if (!existingPrototype || submitting) return
    setSubmitting(true)
    try {
      // Build the same payload as for creation
      const basePayload: any = {
        ...(analysesId ? { analyses_id: analysesId } : {}),
        custom_prompt: prompt,
        generate_file: generateFile === 'yes',
        content_type: contentType || undefined,
        media_type: mediaType || undefined,
        style: style || undefined,
        tone: tone || undefined,
        target_audience: targetAudience || "all",
        content_options: getContentOptions(),
      }

      // Add media-specific fields dynamically
      if (mediaType === "video") {
        Object.assign(basePayload, {
          video_type: videoType || "tutorial",
          duration: duration ?? 15,
          camera_angle: cameraAngle || "top_down",
          camera_movement: cameraMovement || "static",
          lighting_style: lightingStyle || "soft",
          voice_tone: voiceTone || undefined,
          ai_generation_type: aiGenerationType || undefined,
        })
      } else if (mediaType === "image") {
        Object.assign(basePayload, {
          image_type: imageType || undefined,
          image_dimensions: imageDimensions || undefined,
          ai_generation_type: aiGenerationType || undefined,
        })
      }

      // Add platform distribution if platforms are selected
      if (formData.platforms && formData.platforms.length > 0) {
        const platformDistribution: any = {}
        formData.platforms.forEach((platform: string) => {
          platformDistribution[platform] = {
            optimize_for_retention: false,
            include_hashtags: platform === 'tiktok' || platform === 'instagram',
            aspect_ratio: mediaType === "video" ? "portrait" : "landscape"
          }
        })
        basePayload.platform_distribution = platformDistribution
      }

      // Use the productService to update the prototype
      const res: any = await productService.updatePrototype(existingPrototype.existing_prototype.id, basePayload)

      if (res && res.success === false) {
        toast({
          title: 'Update Failed',
          description: res.message || 'Failed to update the existing prototype.',
          variant: 'destructive',
        })
        return
      }

      toast({
        title: 'Prototype Updated',
        description: 'The existing prototype has been updated successfully.'
      })

      // Reset the update state
      setShowUpdateOption(false)
      setExistingPrototype(null)

    } catch (e: any) {
      console.error(e)

      // Handle authentication errors specifically
      if (e.isAuthError) {
        toast({
          title: 'Authentication Required',
          description: e.message || 'Your session has expired. Please login again.',
          variant: 'destructive'
        })

        // Redirect to login page after a short delay
        setTimeout(() => {
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login?expired=true'
          }
        }, 2000)
      } else {
        toast({
          title: 'Update Failed',
          description: e?.message || 'Something went wrong while updating the prototype',
          variant: 'destructive'
        })
      }
    } finally {
      setSubmitting(false)
    }
  }

  const handleCancelUpdate = () => {
    setShowUpdateOption(false)
    setExistingPrototype(null)
  }

  // Pre-fill form when editing a prototype
  React.useEffect(() => {
    if (editingPrototype) {
      setContentType(editingPrototype.content_type || "")
      setMediaType(editingPrototype.media_type || "")
      setVideoType(editingPrototype.video_type || "")
      setDuration(editingPrototype.duration || null)
      setCameraAngle(editingPrototype.camera_angle || "")
      setCameraMovement(editingPrototype.camera_movement || "")
      setLightingStyle(editingPrototype.lighting_style || "")
      setImageType(editingPrototype.image_type || "")
      setImageDimensions(editingPrototype.image_dimensions || "")
      setStyle(editingPrototype.style || "")
      setTone(editingPrototype.tone || "")
      setVoiceTone(editingPrototype.voice_tone || "")
      setAiGenerationType(editingPrototype.ai_generation_type || "")
      setTargetAudience(editingPrototype.target_audience || "")
      setPrompt(editingPrototype.custom_prompt || "")
      setGenerateFile(editingPrototype.generate_file ? "yes" : "no")

      // Set content options
      if (editingPrototype.content_options) {
        setFormData(prev => ({
          ...prev,
          content_options: editingPrototype.content_options,
          include_product_info: editingPrototype.content_options.include_product_info || false,
          include_pricing: editingPrototype.content_options.include_pricing || false,
          include_call_to_action: editingPrototype.content_options.include_call_to_action || false,
        }))
      }

      // Set platform distribution
      if (editingPrototype.platform_distribution) {
        const platforms = Object.keys(editingPrototype.platform_distribution)
        setFormData(prev => ({
          ...prev,
          platforms: platforms,
        }))
      }
    }
  }, [editingPrototype])

  // Handle AI prompt generation
  const handleAskAI = async () => {
    if (generatingPrompt) return

    setGeneratingPrompt(true)
    try {
      // Extract analysis summary from analyzed_data
      let analysisSummary = ""
      if (analysisData?.analyzed_data) {
        // Try to extract meaningful summary from analyzed_data
        const analyzedData = analysisData.analyzed_data
        if (analyzedData.data_summary) {
          analysisSummary = analyzedData.data_summary
        } else if (analyzedData.product_name) {
          analysisSummary = `Product analysis for ${analyzedData.product_name}`
        } else {
          analysisSummary = "Product analysis data available"
        }
      }

      // Use actual product and analysis data
      const payload = {
        product_title: productData?.name || productData?.title || "Product",
        media_type: mediaType || "video",
        analysis_summary: analysisSummary || undefined // Optional field
      }

      console.log('Ask AI - Product Data:', productData)
      console.log('Ask AI - Analysis Data:', analysisData)
      console.log('Ask AI - Media Type:', mediaType)
      console.log('Ask AI - Payload:', payload)

      const response = await productService.generateAIPrompt(payload)

      if (response.success && response.data?.generated_prompt) {
        setPrompt(response.data.generated_prompt)
        toast({
          title: 'AI Prompt Generated',
          description: 'AI has generated a prompt for your prototype.',
        })
      } else {
        throw new Error(response.message || 'Failed to generate prompt')
      }
    } catch (error: any) {
      console.error('Failed to generate AI prompt:', error)
      toast({
        title: 'Failed to generate prompt',
        description: error?.message || 'Could not generate AI prompt',
        variant: 'destructive'
      })
    } finally {
      setGeneratingPrompt(false)
    }
  }

  return (
    <div className="grid gap-6 md:grid-cols-2">
      <div>
        <ContentTypeStyleCard
          values={{
            content_type: contentType,
            media_type: mediaType,
            video_type: videoType,
            duration: duration,
            camera_angle: cameraAngle,
            camera_movement: cameraMovement,
            lighting_style: lightingStyle,
            image_type: imageType,
            image_dimensions: imageDimensions,
            style: style,
            tone: tone,
            voice_tone: voiceTone,
            ai_generation_type: aiGenerationType,
          }}
          onChange={(key, value) => {
            switch (key as keyof ContentTypeStyleValues) {
              case 'content_type': setContentType(value); break
              case 'media_type': setMediaType(value); break
              case 'video_type': setVideoType(value); break
              case 'duration': setDuration(value); break
              case 'camera_angle': setCameraAngle(value); break
              case 'camera_movement': setCameraMovement(value); break
              case 'lighting_style': setLightingStyle(value); break
              case 'image_type': setImageType(value); break
              case 'image_dimensions': setImageDimensions(value); break
              case 'style': setStyle(value); break
              case 'tone': setTone(value); break
              case 'voice_tone': setVoiceTone(value); break
              case 'ai_generation_type': setAiGenerationType(value); break
              default: break
            }
          }}
        />
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Wand2 className="h-5 w-5" />
              <span>AI Content Prompt</span>
            </CardTitle>
            <CardDescription>Describe the content you want to generate</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <Label>Target Audience</Label>
              <Select value={targetAudience} onValueChange={setTargetAudience}>
                <SelectTrigger>
                  <SelectValue placeholder="Select target audience..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">General Audience</SelectItem>
                  <SelectItem value="designers_developers">Designers & Developers</SelectItem>
                  <SelectItem value="business_professionals">Business Professionals</SelectItem>
                  <SelectItem value="content_creators">Content Creators</SelectItem>
                  <SelectItem value="students">Students</SelectItem>
                  <SelectItem value="entrepreneurs">Entrepreneurs</SelectItem>
                  <SelectItem value="tech_enthusiasts">Tech Enthusiasts</SelectItem>
                  <SelectItem value="marketers">Marketers</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <Label>Content Options</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include_captions"
                    checked={(formData.content_options as any)?.include_captions || false}
                    onCheckedChange={(checked) => {
                      updateFormData('content_options', { ...(formData.content_options || {}), include_captions: checked })
                    }}
                  />
                  <Label htmlFor="include_captions" className="text-sm">Include captions</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include_product_info"
                    checked={formData.include_product_info}
                    onCheckedChange={(checked) => {
                      updateFormData('include_product_info', checked)
                      updateFormData('content_options', { ...(formData.content_options || {}), include_product_info: checked })
                    }}
                  />
                  <Label htmlFor="include_product_info" className="text-sm">Include product information</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include_pricing"
                    checked={formData.include_pricing}
                    onCheckedChange={(checked) => {
                      updateFormData('include_pricing', checked)
                      updateFormData('content_options', { ...(formData.content_options || {}), include_pricing: checked })
                    }}
                  />
                  <Label htmlFor="include_pricing" className="text-sm">Include pricing information</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include_call_to_action"
                    checked={formData.include_call_to_action}
                    onCheckedChange={(checked) => {
                      updateFormData('include_call_to_action', checked)
                      updateFormData('content_options', { ...(formData.content_options || {}), include_call_to_action: checked })
                    }}
                  />
                  <Label htmlFor="include_call_to_action" className="text-sm">Include call-to-action</Label>
                </div>

                {/* Video-specific options */}
                {mediaType === "video" && (
                  <>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_subtitles"
                        checked={(formData.content_options as any)?.include_subtitles || false}
                        onCheckedChange={(checked) => {
                          updateFormData('content_options', { ...(formData.content_options || {}), include_subtitles: checked })
                        }}
                      />
                      <Label htmlFor="include_subtitles" className="text-sm">Include subtitles</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_background_music"
                        checked={(formData.content_options as any)?.include_background_music !== false}
                        onCheckedChange={(checked) => {
                          updateFormData('content_options', { ...(formData.content_options || {}), include_background_music: checked })
                        }}
                      />
                      <Label htmlFor="include_background_music" className="text-sm">Include background music</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_transitions"
                        checked={(formData.content_options as any)?.include_transitions !== false}
                        onCheckedChange={(checked) => {
                          updateFormData('content_options', { ...(formData.content_options || {}), include_transitions: checked })
                        }}
                      />
                      <Label htmlFor="include_transitions" className="text-sm">Include transitions</Label>
                    </div>
                  </>
                )}

                {/* Image-specific options */}
                {mediaType === "image" && (
                  <>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_text_overlay"
                        checked={(formData.content_options as any)?.include_text_overlay || false}
                        onCheckedChange={(checked) => {
                          updateFormData('content_options', { ...(formData.content_options || {}), include_text_overlay: checked })
                        }}
                      />
                      <Label htmlFor="include_text_overlay" className="text-sm">Include text overlay</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_logo"
                        checked={(formData.content_options as any)?.include_logo !== false}
                        onCheckedChange={(checked) => {
                          updateFormData('content_options', { ...(formData.content_options || {}), include_logo: checked })
                        }}
                      />
                      <Label htmlFor="include_logo" className="text-sm">Include logo</Label>
                    </div>
                  </>
                )}
              </div>
            </div>

            <div className="space-y-3">
              <Label>Target Platforms</Label>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { id: 'instagram', name: 'Instagram', icon: '📷' },
                  { id: 'tiktok', name: 'TikTok', icon: '🎵' },
                  { id: 'youtube', name: 'YouTube', icon: '📺' },
                  { id: 'facebook', name: 'Facebook', icon: '👥' },
                  { id: 'twitter', name: 'Twitter/X', icon: '🐦' },
                  { id: 'linkedin', name: 'LinkedIn', icon: '💼' },
                ].map((platform) => (
                  <div key={platform.id} className="flex items-center space-x-2">
                    <Checkbox id={platform.id} checked={formData.platforms.includes(platform.id)} onCheckedChange={() => togglePlatform(platform.id)} />
                    <Label htmlFor={platform.id} className="text-sm flex items-center space-x-1">
                      <span>{platform.icon}</span>
                      <span>{platform.name}</span>
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Prompt</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAskAI}
                  disabled={generatingPrompt}
                  className="flex items-center gap-2"
                >
                  {generatingPrompt ? (
                    <>
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-4 w-4" />
                      Ask AI
                    </>
                  )}
                </Button>
              </div>
              <Textarea rows={6} placeholder="Type your prompt..." value={prompt} onChange={(e) => setPrompt(e.target.value)} />
            </div>

            <div className="space-y-2">
              <Label>Generate File</Label>
              <Select value={generateFile} onValueChange={(v: any) => setGenerateFile(v)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select an option" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="yes">Yes</SelectItem>
                  <SelectItem value="no">No</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Show existing prototype information and update options */}
        {showUpdateOption && existingPrototype && (
          <Card className="mb-4 border-orange-200 bg-orange-50">
            <CardHeader>
              <CardTitle className="text-orange-800 flex items-center space-x-2">
                <RefreshCw className="h-5 w-5" />
                <span>Existing Prototype Found</span>
              </CardTitle>
              <CardDescription className="text-orange-700">
                A prototype already exists for this product and analysis combination in draft status.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-white rounded-lg p-3 space-y-2">
                <div className="text-sm">
                  <span className="font-medium">Existing Prompt:</span> {existingPrototype.existing_prototype.custom_prompt}
                </div>
                <div className="text-sm">
                  <span className="font-medium">Media Type:</span> {existingPrototype.existing_prototype.media_type}
                </div>
                <div className="text-sm">
                  <span className="font-medium">Content Type:</span> {existingPrototype.existing_prototype.content_type}
                </div>
                <div className="text-sm">
                  <span className="font-medium">Status:</span>
                  <span className="ml-1 px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">
                    {existingPrototype.existing_prototype.status}
                  </span>
                </div>
              </div>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  disabled={submitting}
                  onClick={handleUpdateExisting}
                  aria-busy={submitting}
                >
                  {submitting ? (
                    <><RefreshCw className="h-4 w-4 animate-spin mr-2" /> Updating...</>
                  ) : (
                    'Update Existing Prototype'
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancelUpdate}
                  disabled={submitting}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="flex justify-end space-x-2 pt-2">
          {inDialog ? (
            <DialogClose asChild>
              <Button variant="outline" size="sm">Cancel</Button>
            </DialogClose>
          ) : (
            <Button variant="outline" size="sm" onClick={onSuccess}>Cancel</Button>
          )}
          <Button
            size="sm"
            disabled={submitting || showUpdateOption}
            onClick={handleGenerate}
            aria-busy={submitting}
          >
            {submitting ? (
              <><RefreshCw className="h-4 w-4 animate-spin mr-2" />
                {editingPrototype ? 'Updating...' : 'Generating...'}
              </>
            ) : (
              editingPrototype ? 'Update Prototype' : 'Generate'
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}

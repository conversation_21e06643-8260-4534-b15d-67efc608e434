"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  AlertTriangle,
  Clock,
  Database,
  HardDrive,
  Save,
  Trash2,
  FileText,
  BarChart3,
  UserCircle,
  MessageSquare,
  ShoppingCart,
  Link,
  RefreshCw,
  CheckCircle2,
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area"
import { Slider } from "@/components/ui/slider"

// Sample data retention policies
const dataRetentionPolicies = [
  {
    id: "policy-1",
    dataType: "user_data",
    displayName: "User Data",
    description: "Personal information about your users and subscribers",
    retentionPeriod: 24, // months
    autoDelete: true,
    lastUpdated: "2023-04-15T10:30:00Z",
    status: "active",
    dataSize: 1.2, // GB
    icon: <UserCircle className="h-5 w-5" />,
    examples: ["Email addresses", "Names", "Subscription preferences", "Login history"],
    legalBasis: "Consent",
    dataLocation: "Primary Database",
  },
  {
    id: "policy-2",
    dataType: "analytics_data",
    displayName: "Analytics Data",
    description: "Website and content performance metrics",
    retentionPeriod: 36, // months
    autoDelete: true,
    lastUpdated: "2023-03-22T14:45:00Z",
    status: "active",
    dataSize: 8.7, // GB
    icon: <BarChart3 className="h-5 w-5" />,
    examples: ["Page views", "Click rates", "Engagement metrics", "Conversion data"],
    legalBasis: "Legitimate Interest",
    dataLocation: "Analytics Database",
  },
  {
    id: "policy-3",
    dataType: "content_data",
    displayName: "Content Data",
    description: "Blog posts, videos, and other content",
    retentionPeriod: 0, // indefinite
    autoDelete: false,
    lastUpdated: "2023-02-10T09:15:00Z",
    status: "active",
    dataSize: 45.3, // GB
    icon: <FileText className="h-5 w-5" />,
    examples: ["Blog posts", "Videos", "Images", "Audio files"],
    legalBasis: "Contract",
    dataLocation: "Content Management System",
  },
  {
    id: "policy-4",
    dataType: "comment_data",
    displayName: "Comment Data",
    description: "User comments and interactions",
    retentionPeriod: 12, // months
    autoDelete: true,
    lastUpdated: "2023-05-05T16:20:00Z",
    status: "active",
    dataSize: 3.4, // GB
    icon: <MessageSquare className="h-5 w-5" />,
    examples: ["Post comments", "Video comments", "Reactions", "Shares"],
    legalBasis: "Consent",
    dataLocation: "Engagement Database",
  },
  {
    id: "policy-5",
    dataType: "transaction_data",
    displayName: "Transaction Data",
    description: "Purchase and affiliate transaction records",
    retentionPeriod: 84, // months (7 years)
    autoDelete: false,
    lastUpdated: "2023-01-18T11:30:00Z",
    status: "active",
    dataSize: 2.1, // GB
    icon: <ShoppingCart className="h-5 w-5" />,
    examples: ["Purchase records", "Affiliate commissions", "Payment information", "Refunds"],
    legalBasis: "Legal Obligation",
    dataLocation: "Financial Database",
  },
  {
    id: "policy-6",
    dataType: "link_data",
    displayName: "Link & Referral Data",
    description: "Tracking links and referral information",
    retentionPeriod: 18, // months
    autoDelete: true,
    lastUpdated: "2023-04-30T13:45:00Z",
    status: "active",
    dataSize: 5.6, // GB
    icon: <Link className="h-5 w-5" />,
    examples: ["Affiliate links", "UTM parameters", "Referral sources", "Click tracking"],
    legalBasis: "Legitimate Interest",
    dataLocation: "Marketing Database",
  },
  {
    id: "policy-7",
    dataType: "system_logs",
    displayName: "System Logs",
    description: "System and error logs",
    retentionPeriod: 6, // months
    autoDelete: true,
    lastUpdated: "2023-05-10T09:00:00Z",
    status: "active",
    dataSize: 12.8, // GB
    icon: <HardDrive className="h-5 w-5" />,
    examples: ["Error logs", "System performance data", "API request logs", "Security logs"],
    legalBasis: "Legitimate Interest",
    dataLocation: "Log Storage",
  },
]

// Sample data deletion logs
const dataDeletionLogs = [
  {
    id: "deletion-1",
    dataType: "user_data",
    displayName: "User Data",
    deletionDate: "2023-05-15T10:30:00Z",
    recordsDeleted: 1245,
    dataSize: "0.3 GB",
    deletionType: "automatic",
    status: "completed",
  },
  {
    id: "deletion-2",
    dataType: "analytics_data",
    displayName: "Analytics Data",
    deletionDate: "2023-05-10T14:45:00Z",
    recordsDeleted: 15678,
    dataSize: "1.2 GB",
    deletionType: "automatic",
    status: "completed",
  },
  {
    id: "deletion-3",
    dataType: "comment_data",
    displayName: "Comment Data",
    deletionDate: "2023-05-05T09:15:00Z",
    recordsDeleted: 3421,
    dataSize: "0.5 GB",
    deletionType: "automatic",
    status: "completed",
  },
  {
    id: "deletion-4",
    dataType: "system_logs",
    displayName: "System Logs",
    deletionDate: "2023-05-01T16:20:00Z",
    recordsDeleted: 45678,
    dataSize: "2.8 GB",
    deletionType: "automatic",
    status: "completed",
  },
  {
    id: "deletion-5",
    dataType: "user_data",
    displayName: "User Data (GDPR Request)",
    deletionDate: "2023-04-28T11:30:00Z",
    recordsDeleted: 1,
    dataSize: "< 0.1 GB",
    deletionType: "manual",
    status: "completed",
  },
]

export function DataRetentionSettings() {
  const [activeTab, setActiveTab] = useState("policies")
  const [selectedPolicy, setSelectedPolicy] = useState<string | null>(null)
  const [isEditingPolicy, setIsEditingPolicy] = useState(false)
  const [editedPolicy, setEditedPolicy] = useState<any>(null)
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false)
  const [policyToDelete, setPolicyToDelete] = useState<string | null>(null)
  const [isManualDeletionOpen, setIsManualDeletionOpen] = useState(false)
  const [manualDeletionOptions, setManualDeletionOptions] = useState({
    dataType: "user_data",
    olderThan: 12, // months
    reason: "",
  })

  // Handle policy edit
  const handleEditPolicy = (policy: any) => {
    setEditedPolicy({ ...policy })
    setIsEditingPolicy(true)
  }

  // Handle policy update
  const handleUpdatePolicy = () => {
    if (!editedPolicy) return

    toast({
      title: "Policy Updated",
      description: `Data retention policy for ${editedPolicy.displayName} has been updated.`,
    })

    setIsEditingPolicy(false)
    setEditedPolicy(null)
  }

  // Handle policy deletion confirmation
  const handleConfirmDelete = (policyId: string) => {
    setPolicyToDelete(policyId)
    setIsConfirmDeleteOpen(true)
  }

  // Handle policy deletion
  const handleDeletePolicy = () => {
    if (!policyToDelete) return

    toast({
      title: "Policy Deleted",
      description: "The data retention policy has been deleted.",
    })

    setIsConfirmDeleteOpen(false)
    setPolicyToDelete(null)
  }

  // Handle manual data deletion
  const handleManualDeletion = () => {
    if (!manualDeletionOptions.reason) {
      toast({
        title: "Missing Information",
        description: "Please provide a reason for the manual deletion.",
        variant: "destructive",
      })
      return
    }

    toast({
      title: "Manual Deletion Started",
      description: `Manual deletion of ${
        dataRetentionPolicies.find((p) => p.dataType === manualDeletionOptions.dataType)?.displayName
      } data older than ${manualDeletionOptions.olderThan} months has been initiated.`,
    })

    setIsManualDeletionOpen(false)
    setManualDeletionOptions({
      dataType: "user_data",
      olderThan: 12,
      reason: "",
    })
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  // Get retention period display
  const getRetentionPeriodDisplay = (months: number) => {
    if (months === 0) return "Indefinite"
    if (months < 12) return `${months} months`
    const years = Math.floor(months / 12)
    const remainingMonths = months % 12
    if (remainingMonths === 0) return `${years} ${years === 1 ? "year" : "years"}`
    return `${years} ${years === 1 ? "year" : "years"}, ${remainingMonths} ${remainingMonths === 1 ? "month" : "months"}`
  }

  // Calculate total data size
  const totalDataSize = dataRetentionPolicies.reduce((total, policy) => total + policy.dataSize, 0)

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Data Retention Settings</CardTitle>
              <CardDescription>Control how long different types of data are stored in your system</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={() => setIsManualDeletionOpen(true)}>
                <Trash2 className="mr-2 h-4 w-4" />
                Manual Deletion
              </Button>
              <Button>
                <Save className="mr-2 h-4 w-4" />
                Save All Changes
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Data Stored</CardTitle>
                  <Database className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{totalDataSize.toFixed(1)} GB</div>
                  <p className="text-xs text-muted-foreground">Across {dataRetentionPolicies.length} data types</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Auto-Delete Enabled</CardTitle>
                  <RefreshCw className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {dataRetentionPolicies.filter((policy) => policy.autoDelete).length}
                  </div>
                  <p className="text-xs text-muted-foreground">Out of {dataRetentionPolicies.length} data types</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Data Deleted (30 days)</CardTitle>
                  <Trash2 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">4.8 GB</div>
                  <p className="text-xs text-muted-foreground">{dataDeletionLogs.length} deletion operations</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Next Scheduled Deletion</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">2 days</div>
                  <p className="text-xs text-muted-foreground">System logs (May 18, 2023)</p>
                </CardContent>
              </Card>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList>
                <TabsTrigger value="policies">Retention Policies</TabsTrigger>
                <TabsTrigger value="deletions">Deletion Logs</TabsTrigger>
              </TabsList>

              <TabsContent value="policies">
                <div className="space-y-4">
                  {dataRetentionPolicies.map((policy) => (
                    <Card key={policy.id}>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className="text-muted-foreground">{policy.icon}</div>
                            <CardTitle className="text-base">{policy.displayName}</CardTitle>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge
                              variant={policy.autoDelete ? "default" : "outline"}
                              className={
                                policy.autoDelete
                                  ? "bg-green-100 text-green-800 hover:bg-green-200"
                                  : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                              }
                            >
                              {policy.autoDelete ? "Auto-Delete On" : "Auto-Delete Off"}
                            </Badge>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleEditPolicy(policy)}
                            >
                              <span className="sr-only">Edit</span>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="h-4 w-4"
                              >
                                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                                <path d="m15 5 4 4" />
                              </svg>
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleConfirmDelete(policy.id)}
                            >
                              <span className="sr-only">Delete</span>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        <CardDescription>{policy.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <p className="text-sm font-medium">Retention Period</p>
                            <p className="text-sm text-muted-foreground">
                              {getRetentionPeriodDisplay(policy.retentionPeriod)}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Data Size</p>
                            <p className="text-sm text-muted-foreground">{policy.dataSize} GB</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Last Updated</p>
                            <p className="text-sm text-muted-foreground">{formatDate(policy.lastUpdated)}</p>
                          </div>
                        </div>
                        <div className="mt-4">
                          <p className="text-sm font-medium">Examples</p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {policy.examples.map((example, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {example}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm font-medium">Legal Basis</p>
                            <p className="text-sm text-muted-foreground">{policy.legalBasis}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Data Location</p>
                            <p className="text-sm text-muted-foreground">{policy.dataLocation}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="deletions">
                <Card>
                  <CardHeader>
                    <CardTitle>Data Deletion History</CardTitle>
                    <CardDescription>Record of all data deletion operations</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border">
                      <ScrollArea className="h-[400px]">
                        <div className="space-y-4 p-4">
                          {dataDeletionLogs.map((log) => (
                            <div key={log.id} className="border-b pb-4 last:border-0 last:pb-0">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <Trash2 className="h-4 w-4 text-muted-foreground" />
                                  <span className="font-medium">{log.displayName}</span>
                                </div>
                                <Badge
                                  variant="outline"
                                  className={
                                    log.deletionType === "automatic"
                                      ? "bg-blue-50 text-blue-800"
                                      : "bg-amber-50 text-amber-800"
                                  }
                                >
                                  {log.deletionType === "automatic" ? "Automatic" : "Manual"}
                                </Badge>
                              </div>
                              <div className="mt-2 grid grid-cols-1 md:grid-cols-4 gap-2">
                                <div>
                                  <p className="text-xs text-muted-foreground">Date</p>
                                  <p className="text-sm">{formatDate(log.deletionDate)}</p>
                                </div>
                                <div>
                                  <p className="text-xs text-muted-foreground">Records Deleted</p>
                                  <p className="text-sm">{log.recordsDeleted.toLocaleString()}</p>
                                </div>
                                <div>
                                  <p className="text-xs text-muted-foreground">Data Size</p>
                                  <p className="text-sm">{log.dataSize}</p>
                                </div>
                                <div>
                                  <p className="text-xs text-muted-foreground">Status</p>
                                  <div className="flex items-center space-x-1">
                                    <CheckCircle2 className="h-3 w-3 text-green-600" />
                                    <p className="text-sm capitalize">{log.status}</p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* Edit Policy Dialog */}
      <Dialog open={isEditingPolicy} onOpenChange={setIsEditingPolicy}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Data Retention Policy</DialogTitle>
            <DialogDescription>Update the retention settings for {editedPolicy?.displayName}</DialogDescription>
          </DialogHeader>
          {editedPolicy && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="policy-name" className="text-right">
                  Name
                </Label>
                <Input
                  id="policy-name"
                  value={editedPolicy.displayName}
                  onChange={(e) => setEditedPolicy({ ...editedPolicy, displayName: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="policy-description" className="text-right">
                  Description
                </Label>
                <Input
                  id="policy-description"
                  value={editedPolicy.description}
                  onChange={(e) => setEditedPolicy({ ...editedPolicy, description: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="retention-period" className="text-right">
                  Retention Period
                </Label>
                <div className="col-span-3 space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">
                      {editedPolicy.retentionPeriod === 0 ? "Indefinite" : `${editedPolicy.retentionPeriod} months`}
                    </span>
                    <Select
                      value={
                        editedPolicy.retentionPeriod === 0 ? "indefinite" : editedPolicy.retentionPeriod.toString()
                      }
                      onValueChange={(value) =>
                        setEditedPolicy({
                          ...editedPolicy,
                          retentionPeriod: value === "indefinite" ? 0 : Number.parseInt(value),
                        })
                      }
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select period" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="3">3 months</SelectItem>
                        <SelectItem value="6">6 months</SelectItem>
                        <SelectItem value="12">1 year</SelectItem>
                        <SelectItem value="24">2 years</SelectItem>
                        <SelectItem value="36">3 years</SelectItem>
                        <SelectItem value="60">5 years</SelectItem>
                        <SelectItem value="84">7 years</SelectItem>
                        <SelectItem value="indefinite">Indefinite</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Slider
                    disabled={editedPolicy.retentionPeriod === 0}
                    min={1}
                    max={84}
                    step={1}
                    value={[editedPolicy.retentionPeriod === 0 ? 84 : editedPolicy.retentionPeriod]}
                    onValueChange={(value) => setEditedPolicy({ ...editedPolicy, retentionPeriod: value[0] })}
                  />
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="auto-delete" className="text-right">
                  Auto-Delete
                </Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <Switch
                    id="auto-delete"
                    checked={editedPolicy.autoDelete}
                    onCheckedChange={(checked) => setEditedPolicy({ ...editedPolicy, autoDelete: checked })}
                    disabled={editedPolicy.retentionPeriod === 0}
                  />
                  <Label htmlFor="auto-delete">
                    {editedPolicy.autoDelete
                      ? "Automatically delete data after retention period"
                      : "Keep data after retention period (manual deletion required)"}
                  </Label>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="legal-basis" className="text-right">
                  Legal Basis
                </Label>
                <Select
                  value={editedPolicy.legalBasis}
                  onValueChange={(value) => setEditedPolicy({ ...editedPolicy, legalBasis: value })}
                >
                  <SelectTrigger id="legal-basis" className="col-span-3">
                    <SelectValue placeholder="Select legal basis" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Consent">Consent</SelectItem>
                    <SelectItem value="Contract">Contract</SelectItem>
                    <SelectItem value="Legal Obligation">Legal Obligation</SelectItem>
                    <SelectItem value="Vital Interests">Vital Interests</SelectItem>
                    <SelectItem value="Public Task">Public Task</SelectItem>
                    <SelectItem value="Legitimate Interest">Legitimate Interest</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="data-location" className="text-right">
                  Data Location
                </Label>
                <Input
                  id="data-location"
                  value={editedPolicy.dataLocation}
                  onChange={(e) => setEditedPolicy({ ...editedPolicy, dataLocation: e.target.value })}
                  className="col-span-3"
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditingPolicy(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdatePolicy}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirm Delete Dialog */}
      <Dialog open={isConfirmDeleteOpen} onOpenChange={setIsConfirmDeleteOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this data retention policy? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="rounded-md bg-amber-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-5 w-5 text-amber-400" aria-hidden="true" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-amber-800">Warning</h3>
                  <div className="mt-2 text-sm text-amber-700">
                    <p>
                      Deleting this policy will not delete any existing data. It will only remove the retention
                      settings. If you want to delete the actual data, please use the Manual Deletion option.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsConfirmDeleteOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeletePolicy}>
              Delete Policy
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Manual Deletion Dialog */}
      <Dialog open={isManualDeletionOpen} onOpenChange={setIsManualDeletionOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Manual Data Deletion</DialogTitle>
            <DialogDescription>
              Delete data manually based on type and age. This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="data-type" className="text-right">
                Data Type
              </Label>
              <Select
                value={manualDeletionOptions.dataType}
                onValueChange={(value) => setManualDeletionOptions({ ...manualDeletionOptions, dataType: value })}
              >
                <SelectTrigger id="data-type" className="col-span-3">
                  <SelectValue placeholder="Select data type" />
                </SelectTrigger>
                <SelectContent>
                  {dataRetentionPolicies.map((policy) => (
                    <SelectItem key={policy.dataType} value={policy.dataType}>
                      {policy.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="older-than" className="text-right">
                Older Than
              </Label>
              <div className="col-span-3 space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">{manualDeletionOptions.olderThan} months</span>
                </div>
                <Slider
                  min={1}
                  max={84}
                  step={1}
                  value={[manualDeletionOptions.olderThan]}
                  onValueChange={(value) => setManualDeletionOptions({ ...manualDeletionOptions, olderThan: value[0] })}
                />
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="deletion-reason" className="text-right">
                Reason
              </Label>
              <Input
                id="deletion-reason"
                placeholder="e.g., Cleanup of old data, GDPR request"
                className="col-span-3"
                value={manualDeletionOptions.reason}
                onChange={(e) => setManualDeletionOptions({ ...manualDeletionOptions, reason: e.target.value })}
              />
            </div>
          </div>
          <div className="rounded-md bg-amber-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-5 w-5 text-amber-400" aria-hidden="true" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-amber-800">Warning</h3>
                <div className="mt-2 text-sm text-amber-700">
                  <p>
                    This action will permanently delete data and cannot be undone. Make sure you have appropriate
                    backups before proceeding.
                  </p>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsManualDeletionOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleManualDeletion}>
              Delete Data
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

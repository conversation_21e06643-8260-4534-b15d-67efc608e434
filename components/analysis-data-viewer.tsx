'use client'

import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  BarChart3,
  Search,
  Target,
  RefreshCw,
  AlertCircle,
  Eye,
  FileText,
  Database,
  Activity,
  DollarSign,
  ArrowLeft,
  Upload,
  ShoppingCart,
  DivideCircle,
} from "lucide-react"
import { ProductAnalysis } from "@/lib/api/products"

// Import the viewer components from the main dialog file
import {
  AnalyzedDataViewer,
  DataSummaryViewer,
  SubmittedDataViewer,
  AnalysisResultViewer,
  OrganicResultsViewer,
  ShoppingResultsViewer,
  KnowledgeGraphViewer,
  RelatedSearchesViewer,
  RelatedQuestionsViewer,
  AIOverviewViewer,
  InlineVideosViewer,
  RelatedProductsViewer,
} from "./product-analysis-dialog"

// Add custom CSS for hiding inactive tab content
const customStyles = `
  .cus-tab-content [data-state="inactive"] {
    display: none;
  }
`

interface AnalysisDataViewerProps {
  analyses: ProductAnalysis[]
  selectedAnalysis: ProductAnalysis | null
  loadingResult: boolean
  loadingData: boolean
  activeTab: string
  onTabChange: (tab: string) => void
  onBack: () => void
  onLoadAnalysisResult: (analysis: ProductAnalysis) => void
  onLoadAnalyzedData: (analysis: ProductAnalysis) => void
  getStatusColor: (status: string) => string
}

export function AnalysisDataViewer({
  analyses,
  selectedAnalysis,
  loadingResult,
  loadingData,
  activeTab,
  onTabChange,
  onBack,
  onLoadAnalysisResult,
  onLoadAnalyzedData,
  getStatusColor
}: AnalysisDataViewerProps) {

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: customStyles }} />
      <div className="flex-1 flex flex-col min-h-0 overflow-auto">
      <div className="flex items-start gap-2 md:items-center md:flex-row flex-col justify-between mb-4 flex-shrink-0">
        <Button
          variant="outline"
          size="sm"
          onClick={onBack}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Overview</span>
        </Button>
        <h3 className="text-lg font-semibold">Analysis Data Viewer</h3>
      </div>

      <Tabs value={activeTab} onValueChange={onTabChange} className="flex-1 flex flex-col min-h-0">
        <TabsList className="grid w-full grid-cols-7 h-12 p-1 bg-gray-100 flex-shrink-0">
          <TabsTrigger value="analyzed_data" className="flex items-center gap-1.5 px-2 py-1.5 text-xs font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <Database className="h-3.5 w-3.5" />
            <span className="hidden lg:inline">Analyzed Data</span>
            <span className="lg:hidden hidden sm:inline">Data</span>
          </TabsTrigger>
          <TabsTrigger value="data_summary" className="flex items-center gap-1.5 px-2 py-1.5 text-xs font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <FileText className="h-3.5 w-3.5" />
            <span className="hidden lg:inline">Data Summary</span>
            <span className="lg:hidden hidden sm:inline">Summary</span>
          </TabsTrigger>
          <TabsTrigger value="submitted_data" className="flex items-center gap-1.5 px-2 py-1.5 text-xs font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <Upload className="h-3.5 w-3.5" />
            <span className="hidden lg:inline">Submitted Data</span>
            <span className="lg:hidden hidden sm:inline">Submit</span>
          </TabsTrigger>
          <TabsTrigger value="analysis_result" className="flex items-center gap-1.5 px-2 py-1.5 text-xs font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <BarChart3 className="h-3.5 w-3.5" />
            <span className="hidden lg:inline">Analysis Result</span>
            <span className="lg:hidden hidden sm:inline">Result</span>
          </TabsTrigger>
          <TabsTrigger value="pricing" className="flex items-center gap-1.5 px-2 py-1.5 text-xs font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <DollarSign className="h-3.5 w-3.5" />
            <span className="hidden lg:inline">Price Analysis</span>
            <span className="lg:hidden hidden sm:inline">Price</span>
          </TabsTrigger>
          <TabsTrigger value="organic_results" className="flex items-center gap-1.5 px-2 py-1.5 text-xs font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <Search className="h-3.5 w-3.5" />
            <span className="hidden lg:inline">Organic Results</span>
            <span className="lg:hidden hidden sm:inline">Organic</span>
          </TabsTrigger>
          <TabsTrigger value="shopping_results" className="flex items-center gap-1.5 px-2 py-1.5 text-xs font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <ShoppingCart className="h-3.5 w-3.5" />
            <span className="hidden lg:inline">Shopping Results</span>
            <span className="lg:hidden hidden sm:inline">Shop</span>
          </TabsTrigger>
        </TabsList>

        <TabsList className="grid w-full grid-cols-6 mt-2 h-12 p-1 bg-gray-100 flex-shrink-0">
          <TabsTrigger value="knowledge_graph" className="flex items-center gap-1.5 px-2 py-1.5 text-xs font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <Target className="h-3.5 w-3.5" />
            <span className="hidden lg:inline">Knowledge Graph</span>
            <span className="lg:hidden hidden sm:inline">Graph</span>
          </TabsTrigger>
          <TabsTrigger value="related_searches" className="flex items-center gap-1.5 px-2 py-1.5 text-xs font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <Search className="h-3.5 w-3.5" />
            <span className="hidden lg:inline">Related Searches</span>
            <span className="lg:hidden hidden sm:inline">Searches</span>
          </TabsTrigger>
          <TabsTrigger value="related_questions" className="flex items-center gap-1.5 px-2 py-1.5 text-xs font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <AlertCircle className="h-3.5 w-3.5" />
            <span className="hidden lg:inline">Related Questions</span>
            <span className="lg:hidden hidden sm:inline">Questions</span>
          </TabsTrigger>
          <TabsTrigger value="ai_overview" className="flex items-center gap-1.5 px-2 py-1.5 text-xs font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <BarChart3 className="h-3.5 w-3.5" />
            <span className="hidden lg:inline">AI Overview</span>
            <span className="lg:hidden hidden sm:inline">AI</span>
          </TabsTrigger>
          <TabsTrigger value="inline_videos" className="flex items-center gap-1.5 px-2 py-1.5 text-xs font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <Eye className="h-3.5 w-3.5" />
            <span className="hidden lg:inline">Inline Videos</span>
            <span className="lg:hidden hidden sm:inline">Videos</span>
          </TabsTrigger>
          <TabsTrigger value="related_products" className="flex items-center gap-1.5 px-2 py-1.5 text-xs font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <Activity className="h-3.5 w-3.5" />
            <span className="hidden lg:inline">Related Products</span>
            <span className="lg:hidden hidden sm:inline">Products</span>
          </TabsTrigger>
        </TabsList>

        <div className="mt-4 flex-1 flex flex-col min-h-0 cus-tab-content">
          <TabsContent value="analyzed_data" className="space-y-4 flex-1 flex flex-col min-h-0">
            <div className="flex items-center justify-between flex-shrink-0">
              <h3 className="text-lg font-semibold">Analyzed Data</h3>
            </div>

            <div className="flex-1 min-h-0">
              <div className="pr-4">
                {selectedAnalysis ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Database className="h-5 w-5" />
                        <span>Analysis #{selectedAnalysis.external_job_id}</span>
                        <Badge className={getStatusColor(selectedAnalysis.status)}>
                          {selectedAnalysis.status}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {selectedAnalysis.analyzed_data ? (
                        <AnalyzedDataViewer data={selectedAnalysis.analyzed_data} />
                      ) : (
                        <div className="text-center py-8">
                          <AlertCircle className="mx-auto h-8 w-8 text-muted-foreground" />
                          <p className="text-muted-foreground mt-2">
                            No analyzed data available
                          </p>
                          {selectedAnalysis.external_job_id && (
                            <Button
                              size="sm"
                              variant="outline"
                              className="mt-2"
                              onClick={() => onLoadAnalyzedData(selectedAnalysis)}
                              disabled={loadingData}
                            >
                              {loadingData ? (
                                <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                              ) : (
                                <Database className="h-3 w-3 mr-1" />
                              )}
                              Load Analyzed Data
                            </Button>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ) : (
                  <div className="text-center py-12">
                    <Database className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-semibold">No analysis selected</h3>
                    <p className="text-muted-foreground">
                      Select an analysis to view analyzed data
                    </p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="data_summary" className="space-y-4 flex-1 flex flex-col min-h-0">
            <div className="flex items-center justify-between flex-shrink-0">
              <h3 className="text-lg font-semibold">Data Summary</h3>
            </div>

            <div className="flex-1 min-h-0">
              <div className="pr-4">
                {selectedAnalysis ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <FileText className="h-5 w-5" />
                        <span>Analysis #{selectedAnalysis.external_job_id}</span>
                        <Badge className={getStatusColor(selectedAnalysis.status)}>
                          {selectedAnalysis.status}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {selectedAnalysis.data_summary ? (
                        <DataSummaryViewer data={selectedAnalysis.data_summary} />
                      ) : (
                        <div className="text-center py-8">
                          <FileText className="mx-auto h-8 w-8 text-muted-foreground" />
                          <p className="text-muted-foreground mt-2">
                            No data summary available
                          </p>
                          {selectedAnalysis.external_job_id && (
                            <Button
                              size="sm"
                              variant="outline"
                              className="mt-2"
                              onClick={() => onLoadAnalyzedData(selectedAnalysis)}
                              disabled={loadingData}
                            >
                              {loadingData ? (
                                <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                              ) : (
                                <Database className="h-3 w-3 mr-1" />
                              )}
                              Load Data Summary
                            </Button>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ) : (
                  <div className="text-center py-12">
                    <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-semibold">No analysis selected</h3>
                    <p className="text-muted-foreground">
                      Select an analysis to view data summary
                    </p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="submitted_data" className="space-y-4 flex-1 flex flex-col min-h-0">
            <div className="flex items-center justify-between flex-shrink-0">
              <h3 className="text-lg font-semibold">Submitted Data</h3>
            </div>
            <div className="flex-1 min-h-0">
              <div className="pr-4">
                {selectedAnalysis ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <FileText className="h-5 w-5" />
                        <span>Analysis #{selectedAnalysis.external_job_id}</span>
                        <Badge className={getStatusColor(selectedAnalysis.status)}>
                          {selectedAnalysis.status}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {selectedAnalysis.submitted_data ? (
                        <SubmittedDataViewer data={selectedAnalysis.submitted_data} />
                      ) : (
                        <div className="text-center py-8">
                          <FileText className="mx-auto h-8 w-8 text-muted-foreground" />
                          <p className="text-muted-foreground mt-2">
                            No submitted data available
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ) : (
                  <div className="text-center py-12">
                    <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-semibold">No analysis selected</h3>
                    <p className="text-muted-foreground">
                      Select an analysis to view submitted data
                    </p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="analysis_result" className="space-y-4 flex-1 flex flex-col min-h-0">
            <div className="flex items-center justify-between flex-shrink-0">
              <h3 className="text-lg font-semibold">Analysis Result</h3>
            </div>
            <div className="flex-1 min-h-0">
              <div className="pr-4">
                {selectedAnalysis ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <BarChart3 className="h-5 w-5" />
                        <span>Analysis #{selectedAnalysis.external_job_id}</span>
                        <Badge className={getStatusColor(selectedAnalysis.status)}>
                          {selectedAnalysis.status}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {selectedAnalysis.analysis_result ? (
                        <AnalysisResultViewer data={selectedAnalysis.analysis_result} />
                      ) : (
                        <div className="text-center py-8">
                          <BarChart3 className="mx-auto h-8 w-8 text-muted-foreground" />
                          <p className="text-muted-foreground mt-2">
                            No analysis result available
                          </p>
                          {selectedAnalysis.external_job_id && (
                            <Button
                              size="sm"
                              variant="outline"
                              className="mt-2"
                              onClick={() => onLoadAnalysisResult(selectedAnalysis)}
                              disabled={loadingResult}
                            >
                              {loadingResult ? (
                                <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                              ) : (
                                <BarChart3 className="h-3 w-3 mr-1" />
                              )}
                              Load Analysis Result
                            </Button>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ) : (
                  <div className="text-center py-12">
                    <BarChart3 className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-semibold">No analysis selected</h3>
                    <p className="text-muted-foreground">
                      Select an analysis to view analysis result
                    </p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="pricing" className="space-y-4 flex-1 flex flex-col min-h-0">
            <div className="flex items-center justify-between flex-shrink-0">
              <h3 className="text-lg font-semibold">Price Analysis</h3>
            </div>
            <div className="flex-1 min-h-0">
              <div className="pr-4">
                {selectedAnalysis ? (
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">
                          Price Analysis - {new Date(selectedAnalysis.created_at).toLocaleDateString()}
                        </CardTitle>
                        <Badge className={getStatusColor(selectedAnalysis.status)}>
                          {selectedAnalysis.status}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {selectedAnalysis.price_analysis ? (
                        <div className="space-y-6">
                          {/* Product Name */}
                          {selectedAnalysis.price_analysis.product_name && (
                            <div className="text-center p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border">
                              <h3 className="text-lg font-semibold text-gray-800 mb-1">Product Analysis</h3>
                              <p className="text-sm text-gray-600">{selectedAnalysis.price_analysis.product_name}</p>
                            </div>
                          )}

                          {/* Price Overview */}
                          {selectedAnalysis.price_analysis.price_analysis && (
                            <div>
                              <h4 className="font-semibold mb-3 flex items-center">
                                <DollarSign className="h-4 w-4 mr-2" />
                                Price Overview
                              </h4>
                              <div className="grid gap-4 md:grid-cols-4">
                                <div className="text-center p-4 bg-green-50 rounded-lg border">
                                  <div className="text-2xl font-bold text-green-600">
                                    ${selectedAnalysis.price_analysis.price_analysis.min_price?.toFixed(2) || 'N/A'}
                                  </div>
                                  <div className="text-sm text-muted-foreground">Minimum Price</div>
                                </div>
                                <div className="text-center p-4 bg-blue-50 rounded-lg border">
                                  <div className="text-2xl font-bold text-blue-600">
                                    ${selectedAnalysis.price_analysis.price_analysis.average_price?.toFixed(2) ||
                                      ((selectedAnalysis.price_analysis.price_analysis.min_price + selectedAnalysis.price_analysis.price_analysis.max_price) / 2).toFixed(2) || 'N/A'}
                                  </div>
                                  <div className="text-sm text-muted-foreground">Average Price</div>
                                </div>
                                <div className="text-center p-4 bg-purple-50 rounded-lg border">
                                  <div className="text-2xl font-bold text-purple-600">
                                    ${selectedAnalysis.price_analysis.price_analysis.median_price?.toFixed(2) || 'N/A'}
                                  </div>
                                  <div className="text-sm text-muted-foreground">Median Price</div>
                                </div>
                                <div className="text-center p-4 bg-red-50 rounded-lg border">
                                  <div className="text-2xl font-bold text-red-600">
                                    ${selectedAnalysis.price_analysis.price_analysis.max_price?.toFixed(2) || 'N/A'}
                                  </div>
                                  <div className="text-sm text-muted-foreground">Maximum Price</div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="text-center py-8">
                          <DollarSign className="mx-auto h-8 w-8 text-muted-foreground" />
                          <p className="mt-2 text-sm text-muted-foreground">No price analysis available</p>
                          {selectedAnalysis.external_job_id && (
                            <div className="mt-4 space-y-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => onLoadAnalyzedData(selectedAnalysis)}
                                disabled={loadingData}
                              >
                                {loadingData ? (
                                  <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                                ) : (
                                  <Database className="h-3 w-3 mr-1" />
                                )}
                                Load Price Analysis Data
                              </Button>
                              <p className="text-xs text-muted-foreground">
                                Click to load price analysis data from the server
                              </p>
                            </div>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ) : (
                  <div className="text-center py-12">
                    <DollarSign className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-semibold">No analysis selected</h3>
                    <p className="text-muted-foreground">
                      Select an analysis to view price analysis
                    </p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="organic_results" className="space-y-4 flex-1 flex flex-col min-h-0">
            <div className="flex items-center justify-between flex-shrink-0">
              <h3 className="text-lg font-semibold">Organic Results</h3>
            </div>
            <div className="flex-1 min-h-0">
              <div className="pr-4">
                {selectedAnalysis ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Search className="h-5 w-5" />
                      <span>Analysis #{selectedAnalysis.external_job_id}</span>
                      <Badge className={getStatusColor(selectedAnalysis.status)}>
                        {selectedAnalysis.status}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {selectedAnalysis.organic_results && selectedAnalysis.organic_results.length > 0 ? (
                      <OrganicResultsViewer data={selectedAnalysis.organic_results} />
                    ) : (
                      <div className="text-center py-8">
                        <Search className="mx-auto h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground mt-2">
                          No organic results available
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <div className="text-center py-12">
                  <Search className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-semibold">No analysis selected</h3>
                  <p className="text-muted-foreground">
                    Select an analysis to view organic results
                  </p>
                </div>
              )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="shopping_results" className="space-y-4 flex-1 flex flex-col min-h-0">
            <div className="flex items-center justify-between flex-shrink-0">
              <h3 className="text-lg font-semibold">Shopping Results</h3>
            </div>
            <div className="flex-1 min-h-0">
              <div className="pr-4">
                {selectedAnalysis ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <ShoppingCart className="h-5 w-5" />
                      <span>Analysis #{selectedAnalysis.external_job_id}</span>
                      <Badge className={getStatusColor(selectedAnalysis.status)}>
                        {selectedAnalysis.status}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {selectedAnalysis.shopping_results && selectedAnalysis.shopping_results.length > 0 ? (
                      <ShoppingResultsViewer data={selectedAnalysis.shopping_results} />
                    ) : (
                      <div className="text-center py-8">
                        <ShoppingCart className="mx-auto h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground mt-2">
                          No shopping results available
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <div className="text-center py-12">
                  <ShoppingCart className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-semibold">No analysis selected</h3>
                  <p className="text-muted-foreground">
                    Select an analysis to view shopping results
                  </p>
                </div>
              )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="knowledge_graph" className="space-y-4 flex-1 flex flex-col min-h-0">
            <div className="flex items-center justify-between flex-shrink-0">
              <h3 className="text-lg font-semibold">Knowledge Graph</h3>
            </div>
            <div className="flex-1 min-h-0">
              <div className="pr-4">
                {selectedAnalysis ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Target className="h-5 w-5" />
                        <span>Analysis #{selectedAnalysis.external_job_id}</span>
                        <Badge className={getStatusColor(selectedAnalysis.status)}>
                          {selectedAnalysis.status}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {(selectedAnalysis.analyzed_data as any)?.knowledge_graph ? (
                        <KnowledgeGraphViewer data={(selectedAnalysis.analyzed_data as any).knowledge_graph} />
                      ) : (
                        <div className="text-center py-8">
                          <Target className="mx-auto h-8 w-8 text-muted-foreground" />
                          <p className="text-muted-foreground mt-2">
                            No knowledge graph available
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ) : (
                  <div className="text-center py-12">
                    <Target className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-semibold">No analysis selected</h3>
                    <p className="text-muted-foreground">
                      Select an analysis to view knowledge graph
                    </p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="related_searches" className="space-y-4 flex-1 flex flex-col min-h-0">
            <div className="flex items-center justify-between flex-shrink-0">
              <h3 className="text-lg font-semibold">Related Searches</h3>
            </div>
            <div className="flex-1 min-h-0">
              <div className="pr-4">
                {selectedAnalysis ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Search className="h-5 w-5" />
                      <span>Analysis #{selectedAnalysis.external_job_id}</span>
                      <Badge className={getStatusColor(selectedAnalysis.status)}>
                        {selectedAnalysis.status}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {selectedAnalysis.related_searches && selectedAnalysis.related_searches.length > 0 ? (
                      <RelatedSearchesViewer data={selectedAnalysis.related_searches} />
                    ) : (
                      <div className="text-center py-8">
                        <Search className="mx-auto h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground mt-2">
                          No related searches available
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <div className="text-center py-12">
                  <Search className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-semibold">No analysis selected</h3>
                  <p className="text-muted-foreground">
                    Select an analysis to view related searches
                  </p>
                </div>
              )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="related_questions" className="space-y-4 flex-1 flex flex-col min-h-0">
            <div className="flex items-center justify-between flex-shrink-0">
              <h3 className="text-lg font-semibold">Related Questions</h3>
            </div>
            <div className="flex-1 min-h-0">
              <div className="pr-4">
                {selectedAnalysis ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <AlertCircle className="h-5 w-5" />
                      <span>Analysis #{selectedAnalysis.external_job_id}</span>
                      <Badge className={getStatusColor(selectedAnalysis.status)}>
                        {selectedAnalysis.status}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {selectedAnalysis.related_questions && selectedAnalysis.related_questions.length > 0 ? (
                      <RelatedQuestionsViewer data={selectedAnalysis.related_questions} />
                    ) : (
                      <div className="text-center py-8">
                        <AlertCircle className="mx-auto h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground mt-2">
                          No related questions available
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <div className="text-center py-12">
                  <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-semibold">No analysis selected</h3>
                  <p className="text-muted-foreground">
                    Select an analysis to view related questions
                  </p>
                </div>
              )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="ai_overview" className="space-y-4 flex-1 flex flex-col min-h-0">
            <div className="flex items-center justify-between flex-shrink-0">
              <h3 className="text-lg font-semibold">AI Overview</h3>
            </div>
            <div className="flex-1 min-h-0">
              <div className="pr-4">
                {selectedAnalysis ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <BarChart3 className="h-5 w-5" />
                        <span>Analysis #{selectedAnalysis.external_job_id}</span>
                        <Badge className={getStatusColor(selectedAnalysis.status)}>
                          {selectedAnalysis.status}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {(selectedAnalysis.analyzed_data as any)?.ai_overview ? (
                        <AIOverviewViewer data={(selectedAnalysis.analyzed_data as any).ai_overview} />
                      ) : (
                        <div className="text-center py-8">
                          <BarChart3 className="mx-auto h-8 w-8 text-muted-foreground" />
                          <p className="text-muted-foreground mt-2">
                            No AI overview available
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ) : (
                  <div className="text-center py-12">
                    <BarChart3 className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-semibold">No analysis selected</h3>
                    <p className="text-muted-foreground">
                      Select an analysis to view AI overview
                    </p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="inline_videos" className="space-y-4 flex-1 flex flex-col min-h-0">
            <div className="flex items-center justify-between flex-shrink-0">
              <h3 className="text-lg font-semibold">Inline Videos</h3>
            </div>
            <div className="flex-1 min-h-0">
              <div className="pr-4">
                {selectedAnalysis ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Eye className="h-5 w-5" />
                      <span>Analysis #{selectedAnalysis.external_job_id}</span>
                      <Badge className={getStatusColor(selectedAnalysis.status)}>
                        {selectedAnalysis.status}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {selectedAnalysis.inline_videos && selectedAnalysis.inline_videos.length > 0 ? (
                      <InlineVideosViewer data={selectedAnalysis.inline_videos} />
                    ) : (
                      <div className="text-center py-8">
                        <Eye className="mx-auto h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground mt-2">
                          No inline videos available
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <div className="text-center py-12">
                  <Eye className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-semibold">No analysis selected</h3>
                  <p className="text-muted-foreground">
                    Select an analysis to view inline videos
                  </p>
                </div>
              )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="related_products" className="space-y-4 flex-1 flex flex-col min-h-0">
            <div className="flex items-center justify-between flex-shrink-0">
              <h3 className="text-lg font-semibold">Related Products</h3>
            </div>
            <div className="flex-1 min-h-0">
              <div className="pr-4">
                {selectedAnalysis ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Activity className="h-5 w-5" />
                      <span>Analysis #{selectedAnalysis.external_job_id}</span>
                      <Badge className={getStatusColor(selectedAnalysis.status)}>
                        {selectedAnalysis.status}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {selectedAnalysis.related_products && selectedAnalysis.related_products.length > 0 ? (
                      <RelatedProductsViewer data={selectedAnalysis.related_products} />
                    ) : (
                      <div className="text-center py-8">
                        <Activity className="mx-auto h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground mt-2">
                          No related products available
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <div className="text-center py-12">
                  <Activity className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-semibold">No analysis selected</h3>
                  <p className="text-muted-foreground">
                    Select an analysis to view related products
                  </p>
                </div>
              )}
              </div>
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
    </>
  )
}

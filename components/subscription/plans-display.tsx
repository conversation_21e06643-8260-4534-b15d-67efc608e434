"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import {
  Crown,
  Star,
  Check,
  X,
  Zap,
  Shield,
  BarChart3,
  Package,
  Image,
  Video,
  FileText,
  Users,
  Settings,
  TrendingUp,
  RefreshCw,
} from 'lucide-react'
import { SubscriptionPlan } from '@/lib/api/user-subscriptions'
import { useSubscriptionInterval } from '@/hooks/use-subscription-interval'

interface PlansDisplayProps {
  plans: SubscriptionPlan[]
  currentPlanId?: string | null
  hasActiveSubscription?: boolean
  onSelectPlan: (plan: SubscriptionPlan, interval: 'monthly' | 'annual') => void
  onComparePlans: (plans: SubscriptionPlan[]) => void
  isLoading?: boolean
}

const featureIcons: Record<string, React.ReactNode> = {
  api_access: <Settings className="h-4 w-4" />,
  product_manage: <Package className="h-4 w-4" />,
  ai_agents_access: <Zap className="h-4 w-4" />,
  analytics_access: <BarChart3 className="h-4 w-4" />,
  image_generation: <Image className="h-4 w-4" />,
  video_generation: <Video className="h-4 w-4" />,
  priority_support: <Shield className="h-4 w-4" />,
  product_analysis: <TrendingUp className="h-4 w-4" />,
  advanced_analytics: <BarChart3 className="h-4 w-4" />,
  prototype_pipeline: <Settings className="h-4 w-4" />,
  production_pipeline: <Settings className="h-4 w-4" />,
  brog_management: <FileText className="h-4 w-4" />,
}

const featureLabels: Record<string, string> = {
  api_access: 'API Access',
  product_manage: 'Product Management',
  ai_agents_access: 'AI Agents Access',
  analytics_access: 'Analytics Access',
  image_generation: 'Image Generation',
  video_generation: 'Video Generation',
  priority_support: 'Priority Support',
  product_analysis: 'Product Analysis',
  advanced_analytics: 'Advanced Analytics',
  prototype_pipeline: 'Prototype Pipeline',
  production_pipeline: 'Production Pipeline',
  brog_management: 'Blog Management',
}

export function PlansDisplay({
  plans,
  currentPlanId,
  hasActiveSubscription = false,
  onSelectPlan,
  onComparePlans,
  isLoading = false
}: PlansDisplayProps) {
  const [selectedPlans, setSelectedPlans] = useState<string[]>([])
  const { currentInterval, isLoading: intervalLoading, switchInterval, isAnnual } = useSubscriptionInterval()

  // Filter out Trial Plan and sort by price
  const sortedPlans = [...plans]
    .filter(plan => plan.slug !== 'trial') // Hide Trial Plan
    .sort((a, b) => {
      const priceA = isAnnual ? parseFloat(a.price_annual) : parseFloat(a.price_monthly)
      const priceB = isAnnual ? parseFloat(b.price_annual) : parseFloat(b.price_monthly)
      return priceA - priceB
    })

  const handleSelectPlan = (plan: SubscriptionPlan) => {
    // Use the current interval from the hook instead of local state
    onSelectPlan(plan, currentInterval)
  }

  const handleComparePlans = () => {
    const plansToCompare = plans.filter(plan => selectedPlans.includes(plan.id))
    onComparePlans(plansToCompare)
  }

  const togglePlanSelection = (planId: string) => {
    setSelectedPlans(prev => 
      prev.includes(planId) 
        ? prev.filter(id => id !== planId)
        : [...prev, planId]
    )
  }

  const calculateSavings = (plan: SubscriptionPlan) => {
    const monthlyTotal = parseFloat(plan.price_monthly) * 12
    const annualPrice = parseFloat(plan.price_annual)
    return Math.round(((monthlyTotal - annualPrice) / monthlyTotal) * 100)
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-full"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="space-y-2">
                {[1, 2, 3, 4].map((j) => (
                  <div key={j} className="h-4 bg-gray-200 rounded w-full"></div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Billing Toggle */}
      <div className="flex items-center justify-center space-x-4">
        <span className={`text-sm ${!isAnnual ? 'font-semibold' : 'text-muted-foreground'}`}>
          Monthly
        </span>
        <Switch
          checked={isAnnual}
          onCheckedChange={(checked) => switchInterval(checked ? 'annual' : 'monthly')}
          className="data-[state=checked]:bg-primary"
        />
        <span className={`text-sm ${isAnnual ? 'font-semibold' : 'text-muted-foreground'}`}>
          Annual
        </span>
        {isAnnual && (
          <Badge variant="secondary" className="ml-2">
            Save up to 20%
          </Badge>
        )}
      </div>

      {/* Compare Plans Button */}
      {selectedPlans.length > 1 && (
        <div className="flex justify-center">
          <Button onClick={handleComparePlans} variant="outline">
            Compare {selectedPlans.length} Plans
          </Button>
        </div>
      )}

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sortedPlans.map((plan) => {
          const isCurrentPlan = currentPlanId === plan.id
          const price = isAnnual ? parseFloat(plan.price_annual) : parseFloat(plan.price_monthly)
          const savings = calculateSavings(plan)
          const isSelected = selectedPlans.includes(plan.id)

          return (
            <Card 
              key={plan.id} 
              className={`relative transition-all duration-200 hover:shadow-lg ${
                plan.is_popular ? 'ring-2 ring-primary' : ''
              } ${isCurrentPlan ? 'bg-primary/5 border-primary' : ''} ${
                isSelected ? 'ring-2 ring-blue-500' : ''
              }`}
            >
              {plan.is_popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-primary text-primary-foreground">
                    <Star className="h-3 w-3 mr-1" />
                    Most Popular
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center pb-4">
                <div className="flex items-center justify-between">
                  <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={() => togglePlanSelection(plan.id)}
                    className="rounded border-gray-300"
                  />
                  {isCurrentPlan && (
                    <Badge variant="outline">
                      <Crown className="h-3 w-3 mr-1" />
                      Current
                    </Badge>
                  )}
                </div>
                
                <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                <CardDescription className="text-sm">
                  {plan.description}
                </CardDescription>
                
                <div className="space-y-1">
                  <div className="text-4xl font-bold">
                    ${price}
                    <span className="text-lg font-normal text-muted-foreground">
                      /{isAnnual ? 'year' : 'month'}
                    </span>
                  </div>
                  {isAnnual && savings > 0 && (
                    <div className="text-sm text-green-600 font-medium">
                      Save {savings}% annually
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Key Features */}
                <div className="space-y-3">
                  <h4 className="font-semibold text-sm">Features</h4>
                  <div className="space-y-2">
                    {Object.entries(plan.features).map(([key, value]) => {
                      if (key === 'limits') return null
                      
                      return (
                        <div key={key} className="flex items-center space-x-2 text-sm">
                          {value ? (
                            <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                          ) : (
                            <X className="h-4 w-4 text-gray-400 flex-shrink-0" />
                          )}
                          <div className="flex items-center space-x-2">
                            {featureIcons[key]}
                            <span className={value ? '' : 'text-muted-foreground'}>
                              {featureLabels[key] || key}
                            </span>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>

                <Separator />

                {/* Usage Limits */}
                <div className="space-y-3">
                  <h4 className="font-semibold text-sm">Usage Limits</h4>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex justify-between">
                      <span>Products:</span>
                      <span className="font-medium">
                        {plan.features.limits.products_created === -1 ? 'Unlimited' : plan.features.limits.products_created}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Images:</span>
                      <span className="font-medium">
                        {plan.features.limits.image_generations === -1 ? 'Unlimited' : plan.features.limits.image_generations}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Videos:</span>
                      <span className="font-medium">
                        {plan.features.limits.video_generations === -1 ? 'Unlimited' : plan.features.limits.video_generations}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Analysis:</span>
                      <span className="font-medium">
                        {plan.features.limits.analysis_requests === -1 ? 'Unlimited' : plan.features.limits.analysis_requests}
                      </span>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Action Button */}
                <Button
                  className="w-full"
                  variant={isCurrentPlan ? "outline" : "default"}
                  onClick={() => handleSelectPlan(plan)}
                  disabled={isCurrentPlan || isLoading}
                >
                  {isCurrentPlan ? (
                    <>
                      <Crown className="h-4 w-4 mr-2" />
                      Current Plan
                    </>
                  ) : isLoading ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Zap className="h-4 w-4 mr-2" />
                      {hasActiveSubscription ? 'Upgrade' : 'Pay Now'}
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}

"use client";

import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  BarChart3,
  TrendingUp,
  Package,
  Image,
  Video,
  FileText,
  AlertTriangle,
  CheckCircle,
  Clock,
  Calendar,
} from "lucide-react";
import {
  SubscriptionStatusData,
  UsageApiResponse,
} from "@/lib/api/user-subscriptions";

interface UsageAnalyticsProps {
  data: SubscriptionStatusData | null;
  usageData: UsageApiResponse | null;
  isLoading?: boolean;
}

const getUsageStatus = (percentage: number) => {
  if (percentage >= 90)
    return {
      color: "text-red-600",
      bg: "bg-red-500",
      status: "critical",
      icon: AlertTriangle,
    };
  if (percentage >= 75)
    return {
      color: "text-yellow-600",
      bg: "bg-yellow-500",
      status: "warning",
      icon: Clock,
    };
  return {
    color: "text-green-600",
    bg: "bg-green-500",
    status: "good",
    icon: CheckCircle,
  };
};

const usageItems = [
  {
    key: "products_created",
    label: "Products Created",
    icon: Package,
    description: "Number of products you've created this month",
  },
  {
    key: "analysis_requests",
    label: "Analysis Requests",
    icon: BarChart3,
    description: "Product analysis requests made this month",
  },
  {
    key: "image_generations",
    label: "Image Generations",
    icon: Image,
    description: "AI-generated images created this month",
  },
  {
    key: "video_generations",
    label: "Video Generations",
    icon: Video,
    description: "AI-generated videos created this month",
  },
  {
    key: "prototype_image_generations",
    label: "Prototype Images",
    icon: Image,
    description: "Prototype images generated this month",
  },
  {
    key: "prototype_video_generations",
    label: "Prototype Videos",
    icon: Video,
    description: "Prototype videos generated this month",
  },
  {
    key: "brogs_created",
    label: "Blog Posts",
    icon: FileText,
    description: "Blog posts created this month",
  },
];

export function UsageAnalytics({
  data,
  usageData,
  isLoading = false,
}: UsageAnalyticsProps) {
  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 animate-pulse" />
              <span>Loading Usage Analytics...</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="space-y-3 animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-2 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!data && !usageData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Usage Analytics</span>
          </CardTitle>
          <CardDescription>No subscription data available</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Subscribe to a plan to track your usage and access detailed
              analytics.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Use usage data from the new API if available, otherwise fall back to old structure
  const counters = usageData?.counters;
  const period = usageData?.period;
  const subscription = usageData?.subscription || data?.subscription;
  const plan = data?.plan;

  // Calculate overall usage status from new API structure
  const overallUsage = counters
    ? Object.values(counters).reduce(
        (sum, counter) => sum + counter.percentage_used,
        0
      ) / Object.values(counters).length
    : 0;
  const overallStatus = getUsageStatus(overallUsage);

  // Get renewal date
  const renewalDate = period?.end
    ? new Date(period.end).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    : subscription?.current_period_end
    ? new Date(subscription.current_period_end).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    : "N/A";

  return (
    <div className="space-y-6">
      {/* Usage Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Usage Analytics</span>
            </div>
            <Badge variant="outline" className={overallStatus.color}>
              <overallStatus.icon className="h-3 w-3 mr-1" />
              {Math.round(overallUsage)}% Used
            </Badge>
          </CardTitle>
          <CardDescription>
            Current usage for your {plan?.name || "current"} plan • Resets on{" "}
            {renewalDate}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {usageItems.map((item) => {
              // Get data from new API structure if available
              const counter = counters?.[item.key as keyof typeof counters];
              const usageValue = counter?.used || 0;
              const limitValue = counter?.limit || 0;
              const percentage = counter?.percentage_used || 0;
              const status = getUsageStatus(percentage);
              const IconComponent = item.icon;

              return (
                <div key={item.key} className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <IconComponent className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{item.label}</span>
                    </div>
                    <Badge
                      variant="outline"
                      className={`text-xs ${status.color}`}
                    >
                      {Math.round(percentage)}%
                    </Badge>
                  </div>

                  <div className="space-y-1">
                    <Progress
                      value={percentage}
                      className="h-2"
                      style={
                        {
                          "--progress-background": status.bg,
                        } as React.CSSProperties
                      }
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>{usageValue} used</span>
                      <span>
                        {counter?.unlimited
                          ? "unlimited"
                          : `${limitValue} limit`}
                      </span>
                    </div>
                  </div>

                  <p className="text-xs text-muted-foreground">
                    {item.description}
                  </p>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Usage Alerts */}
      <div className="space-y-3">
        {usageItems
          .map((item) => {
            const counter = counters?.[item.key as keyof typeof counters];
            const percentage = counter?.percentage_used || 0;
            const usageValue = counter?.used || 0;
            const limitValue = counter?.limit || 0;

            if (percentage >= 90) {
              return (
                <Alert key={item.key} variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>{item.label}</strong> usage is at{" "}
                    {Math.round(percentage)}% ({usageValue}/
                    {counter?.unlimited ? "unlimited" : limitValue}).
                    {!counter?.unlimited &&
                      " Consider upgrading your plan to avoid hitting limits."}
                  </AlertDescription>
                </Alert>
              );
            } else if (percentage >= 75) {
              return (
                <Alert key={item.key}>
                  <Clock className="h-4 w-4" />
                  <AlertDescription>
                    <strong>{item.label}</strong> usage is at{" "}
                    {Math.round(percentage)}% ({usageValue}/
                    {counter?.unlimited ? "unlimited" : limitValue}).
                    {!counter?.unlimited &&
                      " You're approaching your monthly limit."}
                  </AlertDescription>
                </Alert>
              );
            }
            return null;
          })
          .filter(Boolean)}
      </div>

      {/* Usage Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span>Most Used</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              if (!counters) {
                return (
                  <div className="text-sm text-muted-foreground">
                    No usage data
                  </div>
                );
              }

              const maxUsage = Math.max(
                ...Object.values(counters).map((c) => c.percentage_used)
              );
              const maxUsageKey = Object.keys(counters).find(
                (key) =>
                  counters[key as keyof typeof counters].percentage_used ===
                  maxUsage
              );
              const maxUsageItem = usageItems.find(
                (item) => item.key === maxUsageKey
              );

              return maxUsageItem ? (
                <div className="space-y-1">
                  <div className="font-semibold">{maxUsageItem.label}</div>
                  <div className="text-2xl font-bold">
                    {Math.round(maxUsage)}%
                  </div>
                  <div className="text-xs text-muted-foreground">
                    of monthly limit
                  </div>
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  No usage data
                </div>
              );
            })()}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-blue-600" />
              <span>Billing Period</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <div className="font-semibold">Current Period</div>
              <div className="text-2xl font-bold">
                {period?.end
                  ? Math.ceil(
                      (new Date(period.end).getTime() - new Date().getTime()) /
                        (1000 * 60 * 60 * 24)
                    )
                  : subscription?.current_period_end
                  ? Math.ceil(
                      (new Date(subscription.current_period_end).getTime() -
                        new Date().getTime()) /
                        (1000 * 60 * 60 * 24)
                    )
                  : "--"}
              </div>
              <div className="text-xs text-muted-foreground">
                days remaining
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span>Plan Status</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <div className="font-semibold">
                {plan?.name || "Current Plan"}
              </div>
              <div className="text-2xl font-bold capitalize">
                {subscription?.status || "Unknown"}
              </div>
              <div className="text-xs text-muted-foreground">
                subscription status
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

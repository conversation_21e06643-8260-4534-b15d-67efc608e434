"use client";

import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Crown,
  Calendar,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  CreditCard,
  Settings,
  TrendingUp,
  Package,
  Image,
  Video,
  BarChart3,
  FileText,
  RefreshCw,
} from "lucide-react";
import {
  SubscriptionStatusData,
  UsageApiResponse,
} from "@/lib/api/user-subscriptions";

interface SubscriptionStatusProps {
  data: SubscriptionStatusData | null;
  usageData: UsageApiResponse | null;
  onManageSubscription: () => void;
  onViewBilling: () => void;
  onUpgrade: () => void;
  isLoading?: boolean;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "active":
      return "bg-green-100 text-green-800 border-green-200";
    case "trialing":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "past_due":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "canceled":
    case "inactive":
      return "bg-red-100 text-red-800 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case "active":
      return <CheckCircle className="h-4 w-4" />;
    case "trialing":
      return <Clock className="h-4 w-4" />;
    case "past_due":
      return <AlertTriangle className="h-4 w-4" />;
    case "canceled":
    case "inactive":
      return <XCircle className="h-4 w-4" />;
    default:
      return <Clock className="h-4 w-4" />;
  }
};

const formatDate = (dateString: string | Date | null | undefined) => {
  if (!dateString) return "N/A";
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

const getUsageColor = (percentage: number) => {
  if (percentage >= 90) return "bg-red-500";
  if (percentage >= 75) return "bg-yellow-500";
  return "bg-green-500";
};

export function SubscriptionStatus({
  data,
  usageData,
  onManageSubscription,
  onViewBilling,
  onUpgrade,
  isLoading = false,
}: SubscriptionStatusProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            <CardTitle>Loading Subscription Status...</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Crown className="h-5 w-5" />
            <span>No Active Subscription</span>
          </CardTitle>
          <CardDescription>
            You don't have an active subscription plan
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Subscribe to a plan to access premium features and increased usage
              limits.
            </AlertDescription>
          </Alert>
          <div className="mt-4">
            <Button onClick={onUpgrade} className="w-full">
              <Crown className="h-4 w-4 mr-2" />
              Choose a Plan
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { subscription, plan, usage, limits, usage_percentage } = data;
  const statusColor = getStatusColor(subscription.status);
  const statusIcon = getStatusIcon(subscription.status);

  // Usage items configuration for dynamic display
  const usageItems = [
    {
      key: "products_created",
      label: "Products Created",
      icon: Package,
    },
    {
      key: "analysis_requests",
      label: "Analysis Requests",
      icon: BarChart3,
    },
    {
      key: "image_generations",
      label: "Image Generations",
      icon: Image,
    },
    {
      key: "video_generations",
      label: "Video Generations",
      icon: Video,
    },
    {
      key: "prototype_image_generations",
      label: "Prototype Images",
      icon: Image,
    },
    {
      key: "prototype_video_generations",
      label: "Prototype Videos",
      icon: Video,
    },
    {
      key: "brogs_created",
      label: "Blog Posts",
      icon: FileText,
    },
  ];

  // Calculate days until renewal/expiry
  const currentPeriodEnd = subscription.current_period_end;
  const daysUntilRenewal = currentPeriodEnd
    ? Math.ceil(
        (new Date(currentPeriodEnd).getTime() - new Date().getTime()) /
          (1000 * 60 * 60 * 24)
      )
    : null;

  return (
    <div className="space-y-6">
      {/* Main Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Crown className="h-5 w-5" />
              <span>{plan.name} Plan</span>
            </div>
            <Badge className={statusColor}>
              {statusIcon}
              <span className="ml-1 capitalize">{subscription.status}</span>
            </Badge>
          </CardTitle>
          <CardDescription>{plan.description}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Subscription Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">
                ${parseFloat(plan.price_monthly).toFixed(2)}
              </div>
              <div className="text-sm text-muted-foreground">per month</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {daysUntilRenewal !== null ? daysUntilRenewal : "--"}
              </div>
              <div className="text-sm text-muted-foreground">
                days until{" "}
                {subscription.status === "canceled" ? "expiry" : "renewal"}
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {formatDate(subscription.current_period_end)}
              </div>
              <div className="text-sm text-muted-foreground">
                {subscription.status === "canceled"
                  ? "expires on"
                  : "renews on"}
              </div>
            </div>
          </div>

          {/* Alerts */}
          {subscription.status === "past_due" && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Your payment is past due. Please update your payment method to
                avoid service interruption.
              </AlertDescription>
            </Alert>
          )}

          {subscription.status === "canceled" && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Your subscription has been canceled and will expire on{" "}
                {formatDate(subscription.current_period_end)}. You can
                reactivate it anytime before the expiry date.
              </AlertDescription>
            </Alert>
          )}

          {subscription.status === "active" && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                <div className="flex items-center justify-between">
                  <span>
                    Your <strong>{plan.name}</strong> plan is active and working
                    perfectly!
                    {daysUntilRenewal !== null && daysUntilRenewal <= 7 && (
                      <span className="block mt-1 text-sm">
                        Will renew in {daysUntilRenewal} days on{" "}
                        {formatDate(subscription.current_period_end)}.
                      </span>
                    )}
                  </span>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {daysUntilRenewal !== null &&
            daysUntilRenewal <= 7 &&
            subscription.status === "active" && (
              <Alert>
                <Clock className="h-4 w-4" />
                <AlertDescription>
                  Your subscription will renew in {daysUntilRenewal} days on{" "}
                  {formatDate(subscription.current_period_end)}.
                </AlertDescription>
              </Alert>
            )}

          <Separator />

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              onClick={onManageSubscription}
              variant="outline"
              className="flex-1"
            >
              <Settings className="h-4 w-4 mr-2" />
              Manage Subscription
            </Button>
            <Button
              onClick={onViewBilling}
              variant="outline"
              className="flex-1"
            >
              <CreditCard className="h-4 w-4 mr-2" />
              Billing History
            </Button>
            <Button onClick={onUpgrade} className="flex-1">
              <TrendingUp className="h-4 w-4 mr-2" />
              {data?.plan?.slug === "trial" ? "Choose Plan" : "Upgrade Plan"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Usage Overview Card */}
      {/* <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Current Usage</span>
          </CardTitle>
          <CardDescription>Your usage this billing period</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {usageItems.map((item) => {
              // Get data from new API structure if available, otherwise fall back to old structure
              const counter =
                usageData?.counters?.[
                  item.key as keyof typeof usageData.counters
                ];
              const usageValue =
                counter?.used || usage?.[item.key as keyof typeof usage] || 0;
              const limitValue =
                counter?.limit ||
                limits?.[item.key as keyof typeof limits] ||
                0;
              const percentage =
                counter?.percentage_used ||
                usage_percentage?.[item.key as keyof typeof usage_percentage] ||
                0;
              const IconComponent = item.icon;

              // Only show items that have some usage or limit
              if (limitValue === 0 && usageValue === 0) return null;

              return (
                <div key={item.key} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <IconComponent className="h-4 w-4" />
                      <span className="text-sm font-medium">{item.label}</span>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {usageValue} /{" "}
                      {counter?.unlimited ? "unlimited" : limitValue}
                    </span>
                  </div>
                  <Progress
                    value={percentage}
                    className="h-2"
                    style={
                      {
                        "--progress-background": getUsageColor(percentage),
                      } as React.CSSProperties
                    }
                  />
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card> */}
    </div>
  );
}

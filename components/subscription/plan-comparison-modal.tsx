"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Crown,
  Star,
  Check,
  X,
  Zap,
  Shield,
  BarChart3,
  Package,
  Image,
  Video,
  FileText,
  Settings,
  TrendingUp,
  DollarSign,
  RefreshCw,
} from "lucide-react";
import { SubscriptionPlan } from "@/lib/api/user-subscriptions";
import { useSubscriptionInterval } from "@/hooks/use-subscription-interval";

interface PlanComparisonModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  plans: SubscriptionPlan[];
  currentPlanId?: string | null;
  hasActiveSubscription?: boolean;
  isLoading?: boolean;
  onSelectPlan: (
    plan: SubscriptionPlan,
    interval: "monthly" | "annual"
  ) => void;
}

const featureCategories = {
  "Core Features": [
    {
      key: "product_manage",
      label: "Product Management",
      icon: <Package className="h-4 w-4" />,
    },
    {
      key: "image_generation",
      label: "Image Generation",
      icon: <Image className="h-4 w-4" />,
    },
    {
      key: "video_generation",
      label: "Video Generation",
      icon: <Video className="h-4 w-4" />,
    },
    {
      key: "brog_management",
      label: "Blog Management",
      icon: <FileText className="h-4 w-4" />,
    },
  ],
  "Advanced Features": [
    {
      key: "ai_agents_access",
      label: "AI Agents Access",
      icon: <Zap className="h-4 w-4" />,
    },
    {
      key: "product_analysis",
      label: "Product Analysis",
      icon: <TrendingUp className="h-4 w-4" />,
    },
    {
      key: "analytics_access",
      label: "Analytics Access",
      icon: <BarChart3 className="h-4 w-4" />,
    },
    {
      key: "advanced_analytics",
      label: "Advanced Analytics",
      icon: <BarChart3 className="h-4 w-4" />,
    },
  ],
  "Professional Features": [
    {
      key: "api_access",
      label: "API Access",
      icon: <Settings className="h-4 w-4" />,
    },
    {
      key: "prototype_pipeline",
      label: "Prototype Pipeline",
      icon: <Settings className="h-4 w-4" />,
    },
    {
      key: "production_pipeline",
      label: "Production Pipeline",
      icon: <Settings className="h-4 w-4" />,
    },
    {
      key: "priority_support",
      label: "Priority Support",
      icon: <Shield className="h-4 w-4" />,
    },
  ],
};

const limitLabels = {
  products_created: "Products per month",
  analysis_requests: "Analysis requests per month",
  image_generations: "Image generations per month",
  video_generations: "Video generations per month",
  prototype_image_generations: "Prototype images per month",
  prototype_video_generations: "Prototype videos per month",
  brogs_created: "Blog posts per month",
};

export function PlanComparisonModal({
  open,
  onOpenChange,
  plans,
  currentPlanId,
  hasActiveSubscription = false,
  isLoading = false,
  onSelectPlan,
}: PlanComparisonModalProps) {
  const { currentInterval, switchInterval, isAnnual } = useSubscriptionInterval()

  if (plans.length === 0) {
    return null;
  }

  // Filter out Trial Plan and sort by price
  const sortedPlans = [...plans]
    .filter((plan) => plan.slug !== "trial") // Hide Trial Plan
    .sort((a, b) => {
      const priceA = isAnnual
        ? parseFloat(a.price_annual)
        : parseFloat(a.price_monthly);
      const priceB = isAnnual
        ? parseFloat(b.price_annual)
        : parseFloat(b.price_monthly);
      return priceA - priceB;
    });

  const calculateSavings = (plan: SubscriptionPlan) => {
    const monthlyTotal = parseFloat(plan.price_monthly) * 12;
    const annualPrice = parseFloat(plan.price_annual);
    return Math.round(((monthlyTotal - annualPrice) / monthlyTotal) * 100);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Crown className="h-5 w-5" />
            <span>Compare Plans</span>
          </DialogTitle>
          <DialogDescription>
            Compare features and pricing across different subscription plans
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[70vh] pr-4">
          <div className="space-y-6">
            {/* Billing Toggle */}
            <div className="flex items-center justify-center space-x-4">
              <span
                className={`text-sm ${
                  !isAnnual ? "font-semibold" : "text-muted-foreground"
                }`}
              >
                Monthly
              </span>
              <Switch
                checked={isAnnual}
                onCheckedChange={(checked) => switchInterval(checked ? 'annual' : 'monthly')}
                className="data-[state=checked]:bg-primary"
              />
              <span
                className={`text-sm ${
                  isAnnual ? "font-semibold" : "text-muted-foreground"
                }`}
              >
                Annual
              </span>
              {isAnnual && (
                <Badge variant="secondary" className="ml-2">
                  Save up to 20%
                </Badge>
              )}
            </div>

            {/* Plans Header */}
            <div
              className="grid grid-cols-1 gap-4"
              style={{
                gridTemplateColumns: `200px repeat(${sortedPlans.length}, 1fr)`,
              }}
            >
              <div></div>
              {sortedPlans.map((plan) => {
                const isCurrentPlan = currentPlanId === plan.id;
                const price = isAnnual
                  ? parseFloat(plan.price_annual)
                  : parseFloat(plan.price_monthly);
                const savings = calculateSavings(plan);

                return (
                  <div
                    key={plan.id}
                    className="text-center space-y-3 p-4 border rounded-lg relative"
                  >
                    {plan.is_popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <Badge className="bg-primary text-primary-foreground">
                          <Star className="h-3 w-3 mr-1" />
                          Popular
                        </Badge>
                      </div>
                    )}

                    <div>
                      <h3 className="font-bold text-lg">{plan.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {plan.description}
                      </p>
                    </div>

                    <div className="space-y-1">
                      <div className="text-3xl font-bold">
                        ${price}
                        <span className="text-sm font-normal text-muted-foreground">
                          /{isAnnual ? "year" : "month"}
                        </span>
                      </div>
                      {isAnnual && savings > 0 && (
                        <div className="text-xs text-green-600 font-medium">
                          Save {savings}%
                        </div>
                      )}
                    </div>

                    {isCurrentPlan && (
                      <Badge variant="outline">
                        <Crown className="h-3 w-3 mr-1" />
                        Current
                      </Badge>
                    )}

                    <Button
                      className="w-full"
                      variant={isCurrentPlan ? "outline" : "default"}
                      onClick={() =>
                        onSelectPlan(plan, currentInterval)
                      }
                      disabled={isCurrentPlan || isLoading}
                    >
                      {isCurrentPlan ? (
                        "Current Plan"
                      ) : isLoading ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Processing...
                        </>
                      ) : hasActiveSubscription ? (
                        "Upgrade"
                      ) : (
                        "Pay Now"
                      )}
                    </Button>
                  </div>
                );
              })}
            </div>

            <Separator />

            {/* Features Comparison */}
            {Object.entries(featureCategories).map(
              ([categoryName, features]) => (
                <div key={categoryName} className="space-y-3">
                  <h4 className="font-semibold text-lg">{categoryName}</h4>
                  <div className="space-y-2">
                    {features.map((feature) => (
                      <div
                        key={feature.key}
                        className="grid grid-cols-1 gap-4"
                        style={{
                          gridTemplateColumns: `200px repeat(${sortedPlans.length}, 1fr)`,
                        }}
                      >
                        <div className="flex items-center space-x-2 py-2">
                          {feature.icon}
                          <span className="text-sm font-medium">
                            {feature.label}
                          </span>
                        </div>
                        {sortedPlans.map((plan) => (
                          <div
                            key={plan.id}
                            className="flex justify-center py-2"
                          >
                            {plan.features[
                              feature.key as keyof typeof plan.features
                            ] ? (
                              <Check className="h-5 w-5 text-green-500" />
                            ) : (
                              <X className="h-5 w-5 text-gray-400" />
                            )}
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
              )
            )}

            <Separator />

            {/* Usage Limits */}
            <div className="space-y-3">
              <h4 className="font-semibold text-lg">Usage Limits</h4>
              <div className="space-y-2">
                {Object.entries(limitLabels).map(([key, label]) => (
                  <div
                    key={key}
                    className="grid grid-cols-1 gap-4"
                    style={{
                      gridTemplateColumns: `200px repeat(${sortedPlans.length}, 1fr)`,
                    }}
                  >
                    <div className="flex items-center py-2">
                      <span className="text-sm font-medium">{label}</span>
                    </div>
                    {sortedPlans.map((plan) => (
                      <div key={plan.id} className="flex justify-center py-2">
                        <span className="text-sm font-semibold">
                          {plan.features.limits[
                            key as keyof typeof plan.features.limits
                          ] === -1
                            ? "Unlimited"
                            : plan.features.limits[
                                key as keyof typeof plan.features.limits
                              ]}
                        </span>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Pricing Summary */}
            <div className="space-y-3">
              <h4 className="font-semibold text-lg">Pricing Summary</h4>
              <div className="space-y-2">
                <div
                  className="grid grid-cols-1 gap-4"
                  style={{
                    gridTemplateColumns: `200px repeat(${sortedPlans.length}, 1fr)`,
                  }}
                >
                  <div className="flex items-center py-2">
                    <span className="text-sm font-medium">Monthly Price</span>
                  </div>
                  {sortedPlans.map((plan) => (
                    <div key={plan.id} className="flex justify-center py-2">
                      <span className="text-sm font-semibold">
                        ${plan.price_monthly}
                      </span>
                    </div>
                  ))}
                </div>
                <div
                  className="grid grid-cols-1 gap-4"
                  style={{
                    gridTemplateColumns: `200px repeat(${sortedPlans.length}, 1fr)`,
                  }}
                >
                  <div className="flex items-center py-2">
                    <span className="text-sm font-medium">Annual Price</span>
                  </div>
                  {sortedPlans.map((plan) => (
                    <div key={plan.id} className="flex justify-center py-2">
                      <div className="text-center">
                        <div className="text-sm font-semibold">
                          ${plan.price_annual}
                        </div>
                        <div className="text-xs text-green-600">
                          Save {calculateSavings(plan)}%
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}

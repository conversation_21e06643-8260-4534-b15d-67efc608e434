"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  SubscriptionNotifications,
  ActivePlanStatus,
} from "./subscription-notifications";

// Demo component to test the notification system
export function SubscriptionDemo() {
  const [showUpgradeNotification, setShowUpgradeNotification] = useState(false);
  const [showDowngradeNotification, setShowDowngradeNotification] =
    useState(false);
  const [showActivePlanStatus, setShowActivePlanStatus] = useState(false);
  const [showUpgradeNoPeriod, setShowUpgradeNoPeriod] = useState(false);
  const [showUpgradeNoSubscription, setShowUpgradeNoSubscription] =
    useState(false);

  const upgradeData = {
    subscription: {
      id: "458c0258-fd05-418b-81ad-4c06dec75dae",
      plan_id: "b32be5f7-f187-4fc0-bd45-f7c129c4e52d",
      status: "active",
      current_period_start: "2025-09-03T07:24:44.746Z",
      current_period_end: "2025-10-03T07:24:44.746Z",
    },
    change_type: "upgrade" as const,
    message: "Subscription upgraded to Pro Plan successfully",
    new_period: {
      id: "15984476-a3b0-473e-97bd-4fa8021353fc",
      period_start: "2025-09-04T05:15:54.287Z",
      period_end: "2025-10-03T07:24:44.746Z",
      reset_reason: "upgrade",
    },
    scheduled_change: null,
  };

  // Example with missing period data (to test error handling)
  const upgradeDataNoPeriod = {
    subscription: {
      id: "458c0258-fd05-418b-81ad-4c06dec75dae",
      plan_id: "b32be5f7-f187-4fc0-bd45-f7c129c4e52d",
      status: "active",
      // No period data
    },
    change_type: "upgrade" as const,
    message: "Subscription upgraded to Pro Plan successfully",
    scheduled_change: null,
  };

  // Example with completely missing subscription data (to test error handling)
  const upgradeDataNoSubscription = {
    change_type: "upgrade" as const,
    message: "Subscription upgraded successfully",
    // No subscription object at all
  };

  const downgradeData = {
    subscription: {
      id: "458c0258-fd05-418b-81ad-4c06dec75dae",
      plan_id: "b32be5f7-f187-4fc0-bd45-f7c129c4e52d",
      status: "active",
      current_period_start: "2025-09-03T07:24:44.746Z",
      current_period_end: "2025-10-03T07:24:44.746Z",
    },
    change_type: "downgrade" as const,
    message: "Subscription downgraded to Creator Plan successfully",
    new_period: {
      id: "15984476-a3b0-473e-97bd-4fa8021353fc",
      period_start: "2025-09-04T05:15:54.287Z",
      period_end: "2025-10-03T07:24:44.746Z",
      reset_reason: "downgrade",
    },
    scheduled_change: null,
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Subscription Notifications Demo</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={() => setShowUpgradeNotification(true)}
              variant="default"
            >
              Show Upgrade Notification
            </Button>
            <Button
              onClick={() => setShowDowngradeNotification(true)}
              variant="outline"
            >
              Show Downgrade Notification
            </Button>
            <Button
              onClick={() => setShowUpgradeNoPeriod(true)}
              variant="outline"
            >
              Test No Period Data
            </Button>
            <Button
              onClick={() => setShowUpgradeNoSubscription(true)}
              variant="destructive"
            >
              Test No Subscription Data
            </Button>
            <Button
              onClick={() => setShowActivePlanStatus(true)}
              variant="secondary"
            >
              Show Active Plan Status
            </Button>
          </div>

          <div className="space-y-2">
            <Badge variant="outline">
              API Response Format:{" "}
              {JSON.stringify(upgradeData, null, 2).substring(0, 50)}...
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Active Plan Status Demo */}
      <ActivePlanStatus
        planName="Pro Plan"
        status="active"
        currentPeriodEnd="2025-10-03T07:24:44.746Z"
        isVisible={showActivePlanStatus}
      />

      {/* Upgrade Notification */}
      <SubscriptionNotifications
        changeData={upgradeData}
        planName="Pro Plan"
        isVisible={showUpgradeNotification}
        onDismiss={() => setShowUpgradeNotification(false)}
      />

      {/* Downgrade Notification */}
      <SubscriptionNotifications
        changeData={downgradeData}
        planName="Creator Plan"
        isVisible={showDowngradeNotification}
        onDismiss={() => setShowDowngradeNotification(false)}
      />

      {/* No Period Data Test */}
      <SubscriptionNotifications
        changeData={upgradeDataNoPeriod}
        planName="Pro Plan"
        isVisible={showUpgradeNoPeriod}
        onDismiss={() => setShowUpgradeNoPeriod(false)}
      />

      {/* No Subscription Data Test */}
      <SubscriptionNotifications
        changeData={upgradeDataNoSubscription}
        planName="Pro Plan"
        isVisible={showUpgradeNoSubscription}
        onDismiss={() => setShowUpgradeNoSubscription(false)}
      />
    </div>
  );
}

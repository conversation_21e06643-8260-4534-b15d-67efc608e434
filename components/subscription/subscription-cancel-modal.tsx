'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON>alogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import {
  AlertTriangle,
  X,
  Calendar,
  DollarSign,
  CheckCircle,
  Clock,
  Shield,
  HelpCircle
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { cancelUserSubscription } from '@/lib/api/user-subscriptions'
import { SubscriptionStatusData } from '@/lib/api/user-subscriptions'

interface SubscriptionCancelModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  subscriptionData: SubscriptionStatusData | null
  onCancelSuccess: () => void
}

const CANCELLATION_REASONS = [
  { value: 'too_expensive', label: 'Too expensive' },
  { value: 'not_using', label: 'Not using the service enough' },
  { value: 'missing_features', label: 'Missing features I need' },
  { value: 'technical_issues', label: 'Technical issues/bugs' },
  { value: 'poor_support', label: 'Poor customer support' },
  { value: 'trying_alternative', label: 'Trying a different service' },
  { value: 'temporary_pause', label: 'Temporary pause (will return)' },
  { value: 'business_closed', label: 'Business closed/changed' },
  { value: 'other', label: 'Other reason' }
]

export function SubscriptionCancelModal({
  open,
  onOpenChange,
  subscriptionData,
  onCancelSuccess
}: SubscriptionCancelModalProps) {
  const [step, setStep] = useState<'reason' | 'confirm' | 'processing' | 'success'>('reason')
  const [selectedReason, setSelectedReason] = useState<string>('')
  const [customReason, setCustomReason] = useState<string>('')
  const [cancelImmediately, setCancelImmediately] = useState<boolean>(false)
  const [confirmationChecked, setConfirmationChecked] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [cancellationData, setCancellationData] = useState<any>(null)

  const resetModal = () => {
    setStep('reason')
    setSelectedReason('')
    setCustomReason('')
    setCancelImmediately(false)
    setConfirmationChecked(false)
    setIsLoading(false)
    setCancellationData(null)
  }

  const handleClose = () => {
    resetModal()
    onOpenChange(false)
  }

  const handleReasonNext = () => {
    if (!selectedReason) {
      toast({
        title: 'Please select a reason',
        description: 'We need to know why you\'re cancelling to improve our service.',
        variant: 'destructive'
      })
      return
    }
    
    if (selectedReason === 'other' && !customReason.trim()) {
      toast({
        title: 'Please specify your reason',
        description: 'Please provide details about why you\'re cancelling.',
        variant: 'destructive'
      })
      return
    }

    setStep('confirm')
  }

  const handleCancelSubscription = async () => {
    if (!confirmationChecked) {
      toast({
        title: 'Please confirm cancellation',
        description: 'You must acknowledge that you understand the cancellation terms.',
        variant: 'destructive'
      })
      return
    }

    setIsLoading(true)
    setStep('processing')

    try {
      const reason = selectedReason === 'other' ? customReason : CANCELLATION_REASONS.find(r => r.value === selectedReason)?.label || ''
      
      const response = await cancelUserSubscription({
        reason: reason,
        cancel_immediately: cancelImmediately
      })

      if (response.success) {
        setCancellationData(response.data)
        setStep('success')
        
        toast({
          title: 'Subscription Cancelled',
          description: cancelImmediately 
            ? 'Your subscription has been cancelled immediately.' 
            : 'Your subscription will be cancelled at the end of the current billing period.',
        })

        // Call success callback after a short delay
        setTimeout(() => {
          onCancelSuccess()
        }, 2000)
      } else {
        throw new Error(response.message || 'Failed to cancel subscription')
      }
    } catch (error) {
      console.error('Error cancelling subscription:', error)
      toast({
        title: 'Cancellation Failed',
        description: error instanceof Error ? error.message : 'Failed to cancel subscription. Please try again.',
        variant: 'destructive'
      })
      setStep('confirm')
    } finally {
      setIsLoading(false)
    }
  }

  const getCurrentPeriodEnd = () => {
    if (subscriptionData?.subscription.current_period_end) {
      return new Date(subscriptionData.subscription.current_period_end).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }
    return 'end of current billing period'
  }

  const getPlanName = () => subscriptionData?.plan?.name || 'Current Plan'

  // Render different steps
  if (step === 'reason') {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2 text-2xl">
              <AlertTriangle className="h-6 w-6 text-amber-500" />
              <span>Cancel Subscription</span>
            </DialogTitle>
            <DialogDescription className="text-base">
              We're sorry to see you go! Please help us improve by telling us why you're cancelling.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Current Plan Info */}
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>Currently subscribed to: <strong>{getPlanName()}</strong></span>
                <Badge variant="secondary">{subscriptionData?.subscription.status}</Badge>
              </AlertDescription>
            </Alert>

            {/* Reason Selection */}
            <div className="space-y-4">
              <Label className="text-base font-semibold">Why are you cancelling?</Label>
              <RadioGroup value={selectedReason} onValueChange={setSelectedReason}>
                <div className="grid grid-cols-1 gap-3">
                  {CANCELLATION_REASONS.map((reason) => (
                    <div key={reason.value} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <RadioGroupItem value={reason.value} id={reason.value} />
                      <Label htmlFor={reason.value} className="flex-1 cursor-pointer">
                        {reason.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </div>

            {/* Custom Reason Input */}
            {selectedReason === 'other' && (
              <div className="space-y-2">
                <Label htmlFor="customReason">Please specify your reason</Label>
                <Textarea
                  id="customReason"
                  placeholder="Tell us more about why you're cancelling..."
                  value={customReason}
                  onChange={(e) => setCustomReason(e.target.value)}
                  className="min-h-[100px]"
                />
              </div>
            )}
          </div>

          <DialogFooter className="flex justify-between">
            <Button variant="outline" onClick={handleClose}>
              Keep Subscription
            </Button>
            <Button onClick={handleReasonNext} className="bg-amber-600 hover:bg-amber-700">
              Continue to Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }

  if (step === 'confirm') {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2 text-2xl">
              <AlertTriangle className="h-6 w-6 text-red-500" />
              <span>Confirm Cancellation</span>
            </DialogTitle>
            <DialogDescription>
              Please review the cancellation details before proceeding.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Cancellation Summary */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 space-y-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                <span className="font-semibold text-red-900">Cancellation Summary</span>
              </div>
              
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Plan:</span>
                  <span className="font-semibold">{getPlanName()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Reason:</span>
                  <span className="font-semibold">
                    {selectedReason === 'other' ? customReason : CANCELLATION_REASONS.find(r => r.value === selectedReason)?.label}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Access until:</span>
                  <span className="font-semibold">
                    {cancelImmediately ? 'Immediately' : getCurrentPeriodEnd()}
                  </span>
                </div>
              </div>
            </div>

            {/* Cancellation Options */}
            <div className="space-y-4">
              <Label className="text-base font-semibold">Cancellation Options</Label>
              
              <div className="space-y-3">
                <div className="flex items-start space-x-3 p-4 border rounded-lg">
                  <Checkbox
                    id="cancelImmediately"
                    checked={cancelImmediately}
                    onCheckedChange={(checked) => setCancelImmediately(checked as boolean)}
                  />
                  <div className="flex-1">
                    <Label htmlFor="cancelImmediately" className="font-medium cursor-pointer">
                      Cancel immediately
                    </Label>
                    <p className="text-sm text-gray-600 mt-1">
                      Your subscription will be cancelled right away and you'll lose access immediately. 
                      {cancelImmediately && (
                        <span className="text-red-600 font-medium"> No refund will be provided.</span>
                      )}
                    </p>
                  </div>
                </div>

                {!cancelImmediately && (
                  <Alert>
                    <Clock className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Recommended:</strong> Your subscription will remain active until {getCurrentPeriodEnd()}, 
                      giving you full access to all features until then.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>

            {/* Confirmation Checkbox */}
            <div className="border-t pt-4">
              <div className="flex items-start space-x-3">
                <Checkbox
                  id="confirmCancellation"
                  checked={confirmationChecked}
                  onCheckedChange={(checked) => setConfirmationChecked(checked as boolean)}
                />
                <Label htmlFor="confirmCancellation" className="text-sm cursor-pointer">
                  I understand that cancelling my subscription will remove access to all premium features
                  {cancelImmediately ? ' immediately' : ` after ${getCurrentPeriodEnd()}`} and that this action cannot be undone.
                </Label>
              </div>
            </div>
          </div>

          <DialogFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setStep('reason')}>
              Back
            </Button>
            <div className="space-x-2">
              <Button variant="outline" onClick={handleClose}>
                Keep Subscription
              </Button>
              <Button 
                onClick={handleCancelSubscription}
                disabled={!confirmationChecked}
                className="bg-red-600 hover:bg-red-700"
              >
                Cancel Subscription
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }

  if (step === 'processing') {
    return (
      <Dialog open={open} onOpenChange={() => {}}>
        <DialogContent className="max-w-md">
          <div className="flex flex-col items-center justify-center py-8 space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <h3 className="text-lg font-semibold">Processing Cancellation...</h3>
            <p className="text-gray-600 text-center">
              Please wait while we process your subscription cancellation.
            </p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  if (step === 'success') {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="max-w-lg">
          <div className="flex flex-col items-center justify-center py-8 space-y-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            
            <div className="text-center space-y-2">
              <h3 className="text-2xl font-bold text-gray-900">Subscription Cancelled</h3>
              <p className="text-gray-600">
                {cancelImmediately 
                  ? 'Your subscription has been cancelled immediately.'
                  : `Your subscription will remain active until ${getCurrentPeriodEnd()}.`
                }
              </p>
            </div>

            {cancellationData && (
              <div className="w-full bg-gray-50 rounded-lg p-4 space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <Badge variant="secondary">{cancellationData.status}</Badge>
                </div>
                {cancellationData.effective_date && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Effective Date:</span>
                    <span className="font-medium">
                      {new Date(cancellationData.effective_date).toLocaleDateString()}
                    </span>
                  </div>
                )}
                {cancellationData.refund_amount && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Refund Amount:</span>
                    <span className="font-medium text-green-600">
                      ${cancellationData.refund_amount}
                    </span>
                  </div>
                )}
              </div>
            )}

            <div className="text-center text-sm text-gray-600">
              <p>Thank you for using our service. We'd love to have you back anytime!</p>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={handleClose} className="w-full">
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }

  return null
}

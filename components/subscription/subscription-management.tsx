"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "@/hooks/use-toast";
import {
  Crown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Calendar,
  CreditCard,
  Settings,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Shield,
  Info,
} from "lucide-react";
import {
  SubscriptionPlan,
  SubscriptionStatusData,
  updateUserSubscription,
} from "@/lib/api/user-subscriptions";
import { SubscriptionCancelModal } from "./subscription-cancel-modal";

interface SubscriptionManagementProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentSubscription: SubscriptionStatusData | null;
  availablePlans: SubscriptionPlan[];
  onSubscriptionUpdated: () => void;
}

type ManagementAction =
  | "upgrade"
  | "downgrade"
  | "cancel"
  | "reactivate"
  | null;

export function SubscriptionManagement({
  open,
  onOpenChange,
  currentSubscription,
  availablePlans,
  onSubscriptionUpdated,
}: SubscriptionManagementProps) {
  const [selectedAction, setSelectedAction] = useState<ManagementAction>(null);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(
    null
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);

  const currentPlan = currentSubscription?.plan;
  const subscription = currentSubscription?.subscription;

  // Get available upgrade/downgrade options
  const getAvailableActions = (): { upgrades: SubscriptionPlan[]; downgrades: SubscriptionPlan[] } => {
    if (!currentPlan) return { upgrades: [], downgrades: [] };

    const currentPrice = parseFloat(currentPlan.price_monthly);
    const filteredPlans = availablePlans.filter(
      (plan) => plan.slug !== "trial"
    ); // Exclude Trial Plan

    const upgrades = filteredPlans.filter(
      (plan) =>
        parseFloat(plan.price_monthly) > currentPrice &&
        plan.id !== currentPlan.id
    );
    const downgrades = filteredPlans.filter(
      (plan) =>
        parseFloat(plan.price_monthly) < currentPrice &&
        plan.id !== currentPlan.id
    );

    return { upgrades, downgrades };
  };

  const { upgrades, downgrades } = getAvailableActions();

  const handlePlanChange = async (
    plan: SubscriptionPlan,
    action: "upgrade" | "downgrade"
  ) => {
    setIsProcessing(true);
    try {
      const response = await updateUserSubscription(plan.id, "monthly");

      if (response.success && response.data) {
        const { change_type, message } = response.data;

        // Enhanced success message based on change type
        const successTitle =
          change_type === "upgrade" ? "Plan Upgraded!" : "Plan Updated!";
        const successDescription =
          message || `Successfully ${action}d to ${plan.name} plan.`;

        toast({
          title: successTitle,
          description: successDescription,
        });

        // Show additional information for downgrades
        if (change_type === "downgrade") {
          setTimeout(() => {
            toast({
              title: "Important Notice",
              description:
                "Some features may no longer be available. You'll retain access to current usage until the next billing cycle.",
              variant: "default",
            });
          }, 2000);
        }

        // If payment is required, show payment notice
        if (response.requires_payment) {
          toast({
            title: "Payment Required",
            description:
              "Please complete the payment to activate your new plan.",
            variant: "default",
          });
        }

        onSubscriptionUpdated();
        onOpenChange(false);
      } else {
        throw new Error(response.message || "Failed to update subscription");
      }
    } catch (error) {
      console.error("Error updating subscription:", error);
      toast({
        title: "Update Failed",
        description:
          error instanceof Error
            ? error.message
            : "Failed to update subscription. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCancelSubscription = () => {
    setShowCancelModal(true);
  };

  const handleCancelSuccess = () => {
    setShowCancelModal(false);
    onSubscriptionUpdated();
    onOpenChange(false);
  };

  const formatDate = (dateString: string | Date | null | undefined) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (!currentSubscription || !currentPlan) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>No Active Subscription</DialogTitle>
            <DialogDescription>
              You don't have an active subscription to manage.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => onOpenChange(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Manage Subscription</span>
            </DialogTitle>
            <DialogDescription>
              Upgrade, downgrade, or cancel your subscription
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Current Plan Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Crown className="h-5 w-5" />
                    <span>Current Plan: {currentPlan.name}</span>
                  </div>
                  <Badge
                    variant={
                      subscription?.status === "active" ? "default" : "secondary"
                    }
                  >
                    {subscription?.status}
                  </Badge>
                </CardTitle>
                <CardDescription>{currentPlan.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">
                      ${parseFloat(currentPlan.price_monthly).toFixed(2)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      per month
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">
                      {formatDate(subscription?.current_period_end)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {subscription?.status === "canceled"
                        ? "expires on"
                        : "renews on"}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">
                      {subscription?.status === "canceled" ? (
                        <XCircle className="h-8 w-8 text-red-500 mx-auto" />
                      ) : (
                        <CheckCircle className="h-8 w-8 text-green-500 mx-auto" />
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {subscription?.status === "canceled"
                        ? "Canceled"
                        : "Active"}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Upgrade Options */}
            {upgrades.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                    <span>Upgrade Options</span>
                  </CardTitle>
                  <CardDescription>
                    Get more features and higher limits
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {upgrades.map((plan) => (
                      <div
                        key={plan.id}
                        className="border rounded-lg p-4 space-y-3"
                      >
                        <div className="flex items-center justify-between">
                          <h4 className="font-semibold">{plan.name}</h4>
                          {plan.is_popular && (
                            <Badge variant="secondary">Popular</Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {plan.description}
                        </p>
                        <div className="text-xl font-bold">
                          ${parseFloat(plan.price_monthly).toFixed(2)}/month
                        </div>
                        <Button
                          className="w-full"
                          onClick={() => handlePlanChange(plan, "upgrade")}
                          disabled={isProcessing}
                        >
                          {isProcessing ? (
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <TrendingUp className="h-4 w-4 mr-2" />
                          )}
                          Upgrade to {plan.name}
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Downgrade Options */}
            {downgrades.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingDown className="h-5 w-5 text-blue-600" />
                    <span>Downgrade Options</span>
                  </CardTitle>
                  <CardDescription>
                    Reduce costs with a lower-tier plan
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Alert className="mb-4">
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      Downgrading will take effect at the end of your current
                      billing period.
                    </AlertDescription>
                  </Alert>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {downgrades.map((plan) => (
                      <div
                        key={plan.id}
                        className="border rounded-lg p-4 space-y-3"
                      >
                        <h4 className="font-semibold">{plan.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {plan.description}
                        </p>
                        <div className="text-xl font-bold">
                          ${parseFloat(plan.price_monthly).toFixed(2)}/month
                        </div>
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={() => handlePlanChange(plan, "downgrade")}
                          disabled={isProcessing}
                        >
                          {isProcessing ? (
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <TrendingDown className="h-4 w-4 mr-2" />
                          )}
                          Downgrade to {plan.name}
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Cancel Subscription */}
            {subscription?.status !== "canceled" && (
              <Card className="border-red-200">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 text-red-600">
                    <XCircle className="h-5 w-5" />
                    <span>Cancel Subscription</span>
                  </CardTitle>
                  <CardDescription>
                    Cancel your subscription and stop future billing
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Alert variant="destructive" className="mb-4">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      You'll lose access to premium features at the end of your
                      billing period (
                      {formatDate(subscription?.current_period_end)}).
                    </AlertDescription>
                  </Alert>
                  <Button
                    variant="destructive"
                    onClick={() => setShowCancelModal(true)}
                    disabled={isProcessing}
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Cancel Subscription
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Subscription Cancellation Modal */}
      <SubscriptionCancelModal
        open={showCancelModal}
        onOpenChange={setShowCancelModal}
        subscriptionData={currentSubscription}
        onCancelSuccess={handleCancelSuccess}
      />
    </>
  );
}

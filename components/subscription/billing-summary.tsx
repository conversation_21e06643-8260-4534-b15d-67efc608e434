"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DollarSign,
  Calendar,
  CreditCard,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Download,
} from "lucide-react";
import { getUserOrders, type Order } from "@/lib/api/user-subscriptions";
import { toast } from "@/hooks/use-toast";

interface BillingSummaryProps {
  className?: string;
}

interface SummaryStats {
  totalSpent: number;
  totalOrders: number;
  paidOrders: number;
  pendingOrders: number;
  failedOrders: number;
  thisMonthSpent: number;
  lastMonthSpent: number;
  avgOrderValue: number;
}

export function BillingSummary({ className }: BillingSummaryProps) {
  const [stats, setStats] = useState<SummaryStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const calculateStats = (orders: Order[]): SummaryStats => {
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    const totalSpent = orders
      .filter(order => order.status === 'paid')
      .reduce((sum, order) => sum + (order.amount_total || 0), 0);

    const paidOrders = orders.filter(order => order.status === 'paid').length;
    const pendingOrders = orders.filter(order => order.status === 'pending').length;
    const failedOrders = orders.filter(order => order.status === 'failed').length;

    const thisMonthOrders = orders.filter(order => {
      const orderDate = new Date(order.created_at);
      return orderDate >= thisMonth && order.status === 'paid';
    });

    const lastMonthOrders = orders.filter(order => {
      const orderDate = new Date(order.created_at);
      return orderDate >= lastMonth && orderDate <= lastMonthEnd && order.status === 'paid';
    });

    const thisMonthSpent = thisMonthOrders.reduce((sum, order) => 
      sum + (order.amount_total || 0), 0);
    
    const lastMonthSpent = lastMonthOrders.reduce((sum, order) => 
      sum + (order.amount_total || 0), 0);

    const avgOrderValue = paidOrders > 0 ? totalSpent / paidOrders : 0;

    return {
      totalSpent,
      totalOrders: orders.length,
      paidOrders,
      pendingOrders,
      failedOrders,
      thisMonthSpent,
      lastMonthSpent,
      avgOrderValue,
    };
  };

  const loadBillingSummary = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load orders for summary calculation with reasonable limit
      const response = await getUserOrders({
        page: 1,
        limit: 50, // Use reasonable limit that should work
        type: 'subscription'
      });

      if (response.success && response.data) {
        const calculatedStats = calculateStats(response.data.orders);
        setStats(calculatedStats);
      } else {
        setError(response.message || "Failed to load billing summary");
        toast({
          title: "Error",
          description: response.message || "Failed to load billing summary",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error loading billing summary:", error);
      setError("Failed to load billing summary");
      toast({
        title: "Error",
        description: "Failed to load billing summary",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      // Load all orders for export with pagination
      let allOrders: any[] = [];
      let currentPage = 1;
      let hasMorePages = true;

      while (hasMorePages) {
        const response = await getUserOrders({
          page: currentPage,
          limit: 50, // Use reasonable limit
          type: 'subscription'
        });

        if (response.success && response.data) {
          allOrders = [...allOrders, ...response.data.orders];
          
          // Check if there are more pages
          const pagination = response.data.pagination;
          hasMorePages = pagination.has_next;
          currentPage++;
          
          // Safety check to prevent infinite loop
          if (currentPage > 20) break; // Max 1000 orders (20 * 50)
        } else {
          console.error("API Error:", response.message);
          throw new Error(response.message || "Failed to fetch orders");
        }
      }

      if (allOrders.length > 0) {
        // Create CSV content
        const csvHeader = "Order ID,Plan,Amount,Status,Date,Billing Interval,Currency\n";
        const csvContent = allOrders.map(order => 
          `${order.id},"${order.plan.name}","${order.amount_total_formatted}",${order.status},${new Date(order.created_at).toLocaleDateString()},${order.meta.interval},${order.currency.toUpperCase()}`
        ).join('\n');
        
        const fullCsv = csvHeader + csvContent;
        
        // Create and download the file
        const blob = new Blob([fullCsv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `billing-history-${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        toast({
          title: "Export Successful",
          description: `Exported ${allOrders.length} orders to CSV file`,
        });
      } else {
        toast({
          title: "No Orders Found",
          description: "No subscription orders available to export",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error exporting orders:", error);
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export billing data",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    // Only load on client side to avoid SSR issues
    if (typeof window !== 'undefined') {
      loadBillingSummary();
    }
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const calculateTrend = () => {
    if (!stats || stats.lastMonthSpent === 0) return null;
    
    const percentChange = ((stats.thisMonthSpent - stats.lastMonthSpent) / stats.lastMonthSpent) * 100;
    return {
      value: Math.abs(percentChange),
      isPositive: percentChange > 0,
    };
  };

  const trend = calculateTrend();

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            <CardTitle>Loading Billing Summary...</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !stats) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>Billing Summary</span>
          </CardTitle>
          <CardDescription>
            {error || "Failed to load billing information"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            variant="outline"
            onClick={loadBillingSummary}
            className="w-full"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>Billing Summary</span>
          </CardTitle>
          <CardDescription>
            Overview of your subscription billing and payments
          </CardDescription>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={loadBillingSummary}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Spent */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <CreditCard className="h-4 w-4 text-green-600" />
              <p className="text-sm font-medium text-muted-foreground">
                Total Spent
              </p>
            </div>
            <p className="text-2xl font-bold">
              {formatCurrency(stats.totalSpent)}
            </p>
          </div>

          {/* This Month */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-blue-600" />
                <p className="text-sm font-medium text-muted-foreground">
                  This Month
                </p>
              </div>
              {trend && (
                <Badge
                  variant="outline"
                  className={`flex items-center space-x-1 ${
                    trend.isPositive
                      ? "text-green-700 border-green-200 bg-green-50"
                      : "text-red-700 border-red-200 bg-red-50"
                  }`}
                >
                  {trend.isPositive ? (
                    <TrendingUp className="h-3 w-3" />
                  ) : (
                    <TrendingDown className="h-3 w-3" />
                  )}
                  <span>{trend.value.toFixed(1)}%</span>
                </Badge>
              )}
            </div>
            <p className="text-2xl font-bold">
              {formatCurrency(stats.thisMonthSpent)}
            </p>
            <p className="text-xs text-muted-foreground">
              vs {formatCurrency(stats.lastMonthSpent)} last month
            </p>
          </div>

          {/* Total Orders */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-purple-600" />
              <p className="text-sm font-medium text-muted-foreground">
                Total Orders
              </p>
            </div>
            <p className="text-2xl font-bold">{stats.totalOrders}</p>
            <div className="flex space-x-4 text-xs">
              <span className="text-green-600">
                {stats.paidOrders} paid
              </span>
              {stats.pendingOrders > 0 && (
                <span className="text-yellow-600">
                  {stats.pendingOrders} pending
                </span>
              )}
              {stats.failedOrders > 0 && (
                <span className="text-red-600">
                  {stats.failedOrders} failed
                </span>
              )}
            </div>
          </div>

          {/* Average Order Value */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-orange-600" />
              <p className="text-sm font-medium text-muted-foreground">
                Avg. Order
              </p>
            </div>
            <p className="text-2xl font-bold">
              {formatCurrency(stats.avgOrderValue)}
            </p>
            <p className="text-xs text-muted-foreground">
              per successful order
            </p>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mt-6 pt-4 border-t">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="text-center p-2 bg-green-50 rounded-lg border border-green-200">
              <p className="font-semibold text-green-800">Success Rate</p>
              <p className="text-green-600">
                {stats.totalOrders > 0
                  ? ((stats.paidOrders / stats.totalOrders) * 100).toFixed(1)
                  : 0}%
              </p>
            </div>
            <div className="text-center p-2 bg-blue-50 rounded-lg border border-blue-200">
              <p className="font-semibold text-blue-800">Active Since</p>
              <p className="text-blue-600">
                {stats.totalOrders > 0 ? "Multiple payments" : "No payments yet"}
              </p>
            </div>
            <div className="text-center p-2 bg-purple-50 rounded-lg border border-purple-200">
              <p className="font-semibold text-purple-800">Payment Method</p>
              <p className="text-purple-600">Stripe</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

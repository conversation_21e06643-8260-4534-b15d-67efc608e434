"use client";

import React, { useState, useEffect } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Info,
  X,
  Sparkles,
  Crown,
} from "lucide-react";

interface SubscriptionChangeData {
  subscription?: {
    id?: string;
    plan_id?: string;
    status?: string;
    current_period_start?: string;
    current_period_end?: string;
  };
  change_type: "upgrade" | "downgrade";
  message: string;
  new_period?: {
    id: string;
    period_start: string;
    period_end: string;
    reset_reason: string;
  };
  scheduled_change?: any;
}

interface SubscriptionNotificationsProps {
  changeData?: SubscriptionChangeData;
  planName?: string;
  isVisible: boolean;
  onDismiss: () => void;
  autoHide?: boolean;
  autoHideDelay?: number;
}

export function SubscriptionNotifications({
  changeData,
  planName,
  isVisible,
  onDismiss,
  autoHide = true,
  autoHideDelay = 8000,
}: SubscriptionNotificationsProps) {
  const [isShowing, setIsShowing] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setIsShowing(true);

      if (autoHide) {
        const timer = setTimeout(() => {
          setIsShowing(false);
          setTimeout(onDismiss, 300); // Allow fade out animation
        }, autoHideDelay);

        return () => clearTimeout(timer);
      }
    } else {
      setIsShowing(false);
    }
  }, [isVisible, autoHide, autoHideDelay, onDismiss]);

  if (!isVisible || !changeData) return null;

  // Safety check for required data
  if (!changeData.change_type || !changeData.message) {
    console.warn(
      "SubscriptionNotifications: Missing required change_type or message"
    );
    return null;
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getChangeIcon = () => {
    return changeData.change_type === "upgrade" ? (
      <TrendingUp className="h-5 w-5 text-green-600" />
    ) : (
      <TrendingDown className="h-5 w-5 text-blue-600" />
    );
  };

  const getChangeColor = () => {
    return changeData.change_type === "upgrade"
      ? "bg-green-50 border-green-200"
      : "bg-blue-50 border-blue-200";
  };

  const getChangeBadgeColor = () => {
    return changeData.change_type === "upgrade"
      ? "bg-green-100 text-green-800 border-green-200"
      : "bg-blue-100 text-blue-800 border-blue-200";
  };

  return (
    <div
      className={`fixed top-4 right-4 z-50 w-96 transition-all duration-300 transform ${
        isShowing
          ? "translate-x-0 opacity-100 scale-100"
          : "translate-x-full opacity-0 scale-95"
      }`}
    >
      <Card className={`shadow-lg ${getChangeColor()}`}>
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-2">
              {getChangeIcon()}
              <div>
                <CardTitle className="text-lg">
                  {changeData.change_type === "upgrade"
                    ? "Plan Upgraded!"
                    : "Plan Changed!"}
                </CardTitle>
                <CardDescription className="text-sm">
                  {changeData.message}
                </CardDescription>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setIsShowing(false);
                setTimeout(onDismiss, 300);
              }}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Plan Status */}
          <div className="flex items-center justify-between p-3 bg-white/50 rounded-lg">
            <div className="flex items-center space-x-2">
              <Crown className="h-4 w-4 text-amber-600" />
              <span className="font-medium">{planName || "New Plan"}</span>
            </div>
            <Badge className={getChangeBadgeColor()}>
              <CheckCircle className="h-3 w-3 mr-1" />
              Active
            </Badge>
          </div>

          {/* Period Information */}
          <div className="space-y-2">
            {changeData.subscription?.current_period_start &&
              changeData.subscription?.current_period_end && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Current Period:</span>
                  <span className="font-medium">
                    {formatDate(changeData.subscription.current_period_start)} -{" "}
                    {formatDate(changeData.subscription.current_period_end)}
                  </span>
                </div>
              )}

            {/* Show subscription status */}
            {changeData.subscription?.status && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Status:</span>
                <Badge variant="outline" className="text-xs capitalize">
                  {changeData.subscription.status}
                </Badge>
              </div>
            )}

            {changeData.new_period && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Reset Reason:</span>
                <Badge variant="outline" className="text-xs">
                  {changeData.new_period.reset_reason}
                </Badge>
              </div>
            )}
          </div>

          {/* Success Message */}
          <Alert className="border-green-200 bg-green-50">
            <Sparkles className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              Your subscription has been successfully updated and is now active.
              {changeData.change_type === "upgrade" &&
                " Enjoy your enhanced features!"}
            </AlertDescription>
          </Alert>

          {/* Additional Info for Downgrades */}
          {changeData.change_type === "downgrade" && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Your plan has been downgraded. Some features may no longer be
                available, but you'll retain access to your current usage until
                the next billing cycle.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Component for showing active plan status
interface ActivePlanStatusProps {
  planName: string;
  status: string;
  currentPeriodEnd: string;
  isVisible: boolean;
}

export function ActivePlanStatus({
  planName,
  status,
  currentPeriodEnd,
  isVisible,
}: ActivePlanStatusProps) {
  if (!isVisible || status !== "active") return null;

  const daysUntilRenewal = Math.ceil(
    (new Date(currentPeriodEnd).getTime() - new Date().getTime()) /
      (1000 * 60 * 60 * 24)
  );

  return (
    <Alert className="border-green-200 bg-green-50">
      <CheckCircle className="h-4 w-4 text-green-600" />
      <AlertDescription className="text-green-800">
        <div className="flex items-center justify-between">
          <span>
            Your <strong>{planName}</strong> plan is active and will renew in{" "}
            <strong>{daysUntilRenewal} days</strong>.
          </span>
          <Badge className="bg-green-100 text-green-800 border-green-200">
            <Crown className="h-3 w-3 mr-1" />
            Active
          </Badge>
        </div>
      </AlertDescription>
    </Alert>
  );
}

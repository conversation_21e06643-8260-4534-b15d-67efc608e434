"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Copy, Check, Code, Globe, Database, ExternalLink } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

export function WebsiteIntegrationGuide() {
  const [activeTab, setActiveTab] = useState("api")
  const [copied, setCopied] = useState<string | null>(null)

  const handleCopy = (text: string, id: string) => {
    navigator.clipboard.writeText(text)
    setCopied(id)
    setTimeout(() => setCopied(null), 2000)

    toast({
      title: "Copied to clipboard",
      description: "The code has been copied to your clipboard.",
    })
  }

  return (
    <Card className="bg-white">
      <CardHeader>
        <CardTitle>Website Integration Guide</CardTitle>
        <CardDescription>Connect your Command Center to your website with these simple steps</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="bg-sage-100">
            <TabsTrigger value="api" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
              API Integration
            </TabsTrigger>
            <TabsTrigger value="tracking" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
              Tracking Setup
            </TabsTrigger>
            <TabsTrigger value="embed" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
              Embed Components
            </TabsTrigger>
            <TabsTrigger value="cms" className="data-[state=active]:bg-sage-200 data-[state=active]:text-sage-900">
              CMS Integration
            </TabsTrigger>
          </TabsList>

          <TabsContent value="api" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Badge className="bg-sage-100 text-sage-800">Step 1</Badge>
                <h3 className="text-lg font-medium">Generate API Keys</h3>
              </div>

              <div className="space-y-2">
                <p className="text-sm">
                  First, you'll need to generate API keys to authenticate your website with the Command Center.
                </p>
                <div className="flex items-center space-x-2">
                  <Input
                    value="cc_api_key_12345abcdef67890ghijklmnopqrstuvwxyz"
                    readOnly
                    className="font-mono text-sm"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleCopy("cc_api_key_12345abcdef67890ghijklmnopqrstuvwxyz", "api-key")}
                  >
                    {copied === "api-key" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div className="flex items-center space-x-2 mt-6">
                <Badge className="bg-sage-100 text-sage-800">Step 2</Badge>
                <h3 className="text-lg font-medium">Install the SDK</h3>
              </div>

              <div className="space-y-2">
                <p className="text-sm">Install our JavaScript SDK using npm or yarn:</p>
                <div className="bg-black rounded-md p-4">
                  <div className="flex items-center justify-between">
                    <code className="text-white font-mono text-sm">npm install command-center-sdk</code>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:text-white hover:bg-white/10"
                      onClick={() => handleCopy("npm install command-center-sdk", "npm-install")}
                    >
                      {copied === "npm-install" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2 mt-6">
                <Badge className="bg-sage-100 text-sage-800">Step 3</Badge>
                <h3 className="text-lg font-medium">Initialize the SDK</h3>
              </div>

              <div className="space-y-2">
                <p className="text-sm">Add this code to your website to initialize the Command Center SDK:</p>
                <div className="bg-black rounded-md p-4">
                  <div className="flex items-start justify-between">
                    <pre className="text-white font-mono text-sm overflow-x-auto">
                      <code>{`import { CommandCenter } from 'command-center-sdk';

// Initialize the SDK
const commandCenter = new CommandCenter({
  apiKey: 'cc_api_key_12345abcdef67890ghijklmnopqrstuvwxyz',
  environment: 'production',
  avatars: ['travel', 'home', 'health', 'fitness', 'entrepreneur']
});

// Start tracking
commandCenter.init();`}</code>
                    </pre>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:text-white hover:bg-white/10 ml-2"
                      onClick={() =>
                        handleCopy(
                          `import { CommandCenter } from 'command-center-sdk';

// Initialize the SDK
const commandCenter = new CommandCenter({
  apiKey: 'cc_api_key_12345abcdef67890ghijklmnopqrstuvwxyz',
  environment: 'production',
  avatars: ['travel', 'home', 'health', 'fitness', 'entrepreneur']
});

// Start tracking
commandCenter.init();`,
                          "init-code",
                        )
                      }
                    >
                      {copied === "init-code" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2 mt-6">
                <Badge className="bg-sage-100 text-sage-800">Step 4</Badge>
                <h3 className="text-lg font-medium">Test the Connection</h3>
              </div>

              <div className="space-y-2">
                <p className="text-sm">Verify that your website is properly connected to the Command Center:</p>
                <div className="bg-black rounded-md p-4">
                  <div className="flex items-start justify-between">
                    <pre className="text-white font-mono text-sm overflow-x-auto">
                      <code>{`// Test the connection
commandCenter.testConnection()
  .then(response => {
    console.log('Connection successful!', response);
  })
  .catch(error => {
    console.error('Connection failed:', error);
  });`}</code>
                    </pre>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:text-white hover:bg-white/10 ml-2"
                      onClick={() =>
                        handleCopy(
                          `// Test the connection
commandCenter.testConnection()
  .then(response => {
    console.log('Connection successful!', response);
  })
  .catch(error => {
    console.error('Connection failed:', error);
  });`,
                          "test-code",
                        )
                      }
                    >
                      {copied === "test-code" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="tracking" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Badge className="bg-sage-100 text-sage-800">Step 1</Badge>
                <h3 className="text-lg font-medium">Set Up QR Code Tracking</h3>
              </div>

              <div className="space-y-2">
                <p className="text-sm">
                  Implement QR code tracking to monitor conversions from physical or digital QR codes:
                </p>
                <div className="bg-black rounded-md p-4">
                  <div className="flex items-start justify-between">
                    <pre className="text-white font-mono text-sm overflow-x-auto">
                      <code>{`// Generate a trackable QR code URL
const qrCodeUrl = commandCenter.generateQrCodeUrl({
  productId: 'premium-travel-backpack',
  avatarId: 'travel',
  campaignId: 'instagram-summer-2023',
  medium: 'qr-code'
});

// Later, track the conversion when purchase is completed
commandCenter.trackConversion({
  productId: 'premium-travel-backpack',
  revenue: 89.99,
  quantity: 1,
  trackingSource: 'qr-code'
});`}</code>
                    </pre>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:text-white hover:bg-white/10 ml-2"
                      onClick={() =>
                        handleCopy(
                          `// Generate a trackable QR code URL
const qrCodeUrl = commandCenter.generateQrCodeUrl({
  productId: 'premium-travel-backpack',
  avatarId: 'travel',
  campaignId: 'instagram-summer-2023',
  medium: 'qr-code'
});

// Later, track the conversion when purchase is completed
commandCenter.trackConversion({
  productId: 'premium-travel-backpack',
  revenue: 89.99,
  quantity: 1,
  trackingSource: 'qr-code'
});`,
                          "qr-code",
                        )
                      }
                    >
                      {copied === "qr-code" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2 mt-6">
                <Badge className="bg-sage-100 text-sage-800">Step 2</Badge>
                <h3 className="text-lg font-medium">Set Up Link Tracking</h3>
              </div>

              <div className="space-y-2">
                <p className="text-sm">
                  Implement link tracking to monitor conversions from links in comments, bio, or on-screen:
                </p>
                <div className="bg-black rounded-md p-4">
                  <div className="flex items-start justify-between">
                    <pre className="text-white font-mono text-sm overflow-x-auto">
                      <code>{`// Generate a trackable link URL
const linkUrl = commandCenter.generateLinkUrl({
  productId: 'smart-fitness-tracker',
  avatarId: 'fitness',
  campaignId: 'youtube-fitness-2023',
  medium: 'video-description',
  placement: 'on-screen' // or 'comments' or 'bio'
});

// Later, track the conversion when purchase is completed
commandCenter.trackConversion({
  productId: 'smart-fitness-tracker',
  revenue: 129.99,
  quantity: 1,
  trackingSource: 'link',
  placement: 'on-screen'
});`}</code>
                    </pre>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:text-white hover:bg-white/10 ml-2"
                      onClick={() =>
                        handleCopy(
                          `// Generate a trackable link URL
const linkUrl = commandCenter.generateLinkUrl({
  productId: 'smart-fitness-tracker',
  avatarId: 'fitness',
  campaignId: 'youtube-fitness-2023',
  medium: 'video-description',
  placement: 'on-screen' // or 'comments' or 'bio'
});

// Later, track the conversion when purchase is completed
commandCenter.trackConversion({
  productId: 'smart-fitness-tracker',
  revenue: 129.99,
  quantity: 1,
  trackingSource: 'link',
  placement: 'on-screen'
});`,
                          "link-tracking",
                        )
                      }
                    >
                      {copied === "link-tracking" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2 mt-6">
                <Badge className="bg-sage-100 text-sage-800">Step 3</Badge>
                <h3 className="text-lg font-medium">Implement Event Tracking</h3>
              </div>

              <div className="space-y-2">
                <p className="text-sm">Track important events on your website to gather more analytics data:</p>
                <div className="bg-black rounded-md p-4">
                  <div className="flex items-start justify-between">
                    <pre className="text-white font-mono text-sm overflow-x-auto">
                      <code>{`// Track product views
commandCenter.trackEvent({
  eventType: 'product_view',
  productId: 'organic-superfood-blend',
  avatarId: 'health',
  referrer: document.referrer
});

// Track add to cart
commandCenter.trackEvent({
  eventType: 'add_to_cart',
  productId: 'organic-superfood-blend',
  avatarId: 'health',
  quantity: 1,
  price: 49.99
});

// Track checkout steps
commandCenter.trackEvent({
  eventType: 'begin_checkout',
  products: [
    { id: 'organic-superfood-blend', quantity: 1, price: 49.99 }
  ],
  totalValue: 49.99
});`}</code>
                    </pre>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:text-white hover:bg-white/10 ml-2"
                      onClick={() =>
                        handleCopy(
                          `// Track product views
commandCenter.trackEvent({
  eventType: 'product_view',
  productId: 'organic-superfood-blend',
  avatarId: 'health',
  referrer: document.referrer
});

// Track add to cart
commandCenter.trackEvent({
  eventType: 'add_to_cart',
  productId: 'organic-superfood-blend',
  avatarId: 'health',
  quantity: 1,
  price: 49.99
});

// Track checkout steps
commandCenter.trackEvent({
  eventType: 'begin_checkout',
  products: [
    { id: 'organic-superfood-blend', quantity: 1, price: 49.99 }
  ],
  totalValue: 49.99
});`,
                          "event-tracking",
                        )
                      }
                    >
                      {copied === "event-tracking" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="embed" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Badge className="bg-sage-100 text-sage-800">Option 1</Badge>
                <h3 className="text-lg font-medium">Embed Product Widgets</h3>
              </div>

              <div className="space-y-2">
                <p className="text-sm">
                  Display products from your Command Center directly on your website with these embeddable widgets:
                </p>
                <div className="bg-black rounded-md p-4">
                  <div className="flex items-start justify-between">
                    <pre className="text-white font-mono text-sm overflow-x-auto">
                      <code>{`<!-- Add this HTML where you want to display products -->
<div id="command-center-products" data-avatar="fitness" data-limit="3"></div>

<!-- Add this script to your page -->
<script>
  window.commandCenterConfig = {
    apiKey: 'cc_api_key_12345abcdef67890ghijklmnopqrstuvwxyz',
    widgetTheme: 'sage-gold'
  };
</script>
<script src="https://cdn.commandcenter.app/widgets.js" async></script>`}</code>
                    </pre>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:text-white hover:bg-white/10 ml-2"
                      onClick={() =>
                        handleCopy(
                          `<!-- Add this HTML where you want to display products -->
<div id="command-center-products" data-avatar="fitness" data-limit="3"></div>

<!-- Add this script to your page -->
<script>
  window.commandCenterConfig = {
    apiKey: 'cc_api_key_12345abcdef67890ghijklmnopqrstuvwxyz',
    widgetTheme: 'sage-gold'
  };
</script>
<script src="https://cdn.commandcenter.app/widgets.js" async></script>`,
                          "product-widget",
                        )
                      }
                    >
                      {copied === "product-widget" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2 mt-6">
                <Badge className="bg-sage-100 text-sage-800">Option 2</Badge>
                <h3 className="text-lg font-medium">Embed Trending Content</h3>
              </div>

              <div className="space-y-2">
                <p className="text-sm">Display trending content from your Command Center on your website:</p>
                <div className="bg-black rounded-md p-4">
                  <div className="flex items-start justify-between">
                    <pre className="text-white font-mono text-sm overflow-x-auto">
                      <code>{`<!-- Add this HTML where you want to display trending content -->
<div id="command-center-trends" data-category="all" data-limit="5"></div>

<!-- The same script configuration works for all widgets -->
<script>
  window.commandCenterConfig = {
    apiKey: 'cc_api_key_12345abcdef67890ghijklmnopqrstuvwxyz',
    widgetTheme: 'sage-gold'
  };
</script>
<script src="https://cdn.commandcenter.app/widgets.js" async></script>`}</code>
                    </pre>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:text-white hover:bg-white/10 ml-2"
                      onClick={() =>
                        handleCopy(
                          `<!-- Add this HTML where you want to display trending content -->
<div id="command-center-trends" data-category="all" data-limit="5"></div>

<!-- The same script configuration works for all widgets -->
<script>
  window.commandCenterConfig = {
    apiKey: 'cc_api_key_12345abcdef67890ghijklmnopqrstuvwxyz',
    widgetTheme: 'sage-gold'
  };
</script>
<script src="https://cdn.commandcenter.app/widgets.js" async></script>`,
                          "trends-widget",
                        )
                      }
                    >
                      {copied === "trends-widget" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2 mt-6">
                <Badge className="bg-sage-100 text-sage-800">Option 3</Badge>
                <h3 className="text-lg font-medium">Custom React Components</h3>
              </div>

              <div className="space-y-2">
                <p className="text-sm">
                  If you're using React, you can use our React components for deeper integration:
                </p>
                <div className="bg-black rounded-md p-4">
                  <div className="flex items-start justify-between">
                    <pre className="text-white font-mono text-sm overflow-x-auto">
                      <code>{`import { CommandCenterProvider, ProductGrid, TrendingContent } from 'command-center-react';

function MyWebsite() {
  return (
    <CommandCenterProvider apiKey="cc_api_key_12345abcdef67890ghijklmnopqrstuvwxyz">
      <div className="my-page">
        <h2>Featured Products</h2>
        <ProductGrid 
          avatar="fitness" 
          limit={3} 
          theme="sage-gold" 
        />
        
        <h2>Trending Content</h2>
        <TrendingContent 
          category="all" 
          limit={5} 
          theme="sage-gold" 
        />
      </div>
    </CommandCenterProvider>
  );
}`}</code>
                    </pre>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:text-white hover:bg-white/10 ml-2"
                      onClick={() =>
                        handleCopy(
                          `import { CommandCenterProvider, ProductGrid, TrendingContent } from 'command-center-react';

function MyWebsite() {
  return (
    <CommandCenterProvider apiKey="cc_api_key_12345abcdef67890ghijklmnopqrstuvwxyz">
      <div className="my-page">
        <h2>Featured Products</h2>
        <ProductGrid 
          avatar="fitness" 
          limit={3} 
          theme="sage-gold" 
        />
        
        <h2>Trending Content</h2>
        <TrendingContent 
          category="all" 
          limit={5} 
          theme="sage-gold" 
        />
      </div>
    </CommandCenterProvider>
  );
}`,
                          "react-components",
                        )
                      }
                    >
                      {copied === "react-components" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="cms" className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Badge className="bg-sage-100 text-sage-800">WordPress</Badge>
                <h3 className="text-lg font-medium">WordPress Integration</h3>
              </div>

              <div className="space-y-2">
                <p className="text-sm">If you're using WordPress, you can install our plugin for easy integration:</p>
                <ol className="list-decimal list-inside space-y-2 text-sm ml-4">
                  <li>Go to your WordPress admin dashboard</li>
                  <li>Navigate to Plugins → Add New</li>
                  <li>Search for "Command Center for WordPress"</li>
                  <li>Click "Install Now" and then "Activate"</li>
                  <li>Go to Settings → Command Center</li>
                  <li>Enter your API key and configure your settings</li>
                </ol>
                <div className="mt-2">
                  <Button variant="outline" size="sm" className="gap-1">
                    <ExternalLink className="h-3.5 w-3.5" />
                    <span>Download WordPress Plugin</span>
                  </Button>
                </div>
              </div>

              <div className="flex items-center space-x-2 mt-6">
                <Badge className="bg-sage-100 text-sage-800">Shopify</Badge>
                <h3 className="text-lg font-medium">Shopify Integration</h3>
              </div>

              <div className="space-y-2">
                <p className="text-sm">If you're using Shopify, you can install our app from the Shopify App Store:</p>
                <ol className="list-decimal list-inside space-y-2 text-sm ml-4">
                  <li>Go to your Shopify admin dashboard</li>
                  <li>Navigate to Apps → Visit Shopify App Store</li>
                  <li>Search for "Command Center for Shopify"</li>
                  <li>Click "Add app"</li>
                  <li>Follow the installation instructions</li>
                  <li>Enter your API key and configure your settings</li>
                </ol>
                <div className="mt-2">
                  <Button variant="outline" size="sm" className="gap-1">
                    <ExternalLink className="h-3.5 w-3.5" />
                    <span>Get Shopify App</span>
                  </Button>
                </div>
              </div>

              <div className="flex items-center space-x-2 mt-6">
                <Badge className="bg-sage-100 text-sage-800">Other CMS</Badge>
                <h3 className="text-lg font-medium">Other CMS Platforms</h3>
              </div>

              <div className="space-y-2">
                <p className="text-sm">For other CMS platforms, you can use our universal embed code:</p>
                <div className="bg-black rounded-md p-4">
                  <div className="flex items-start justify-between">
                    <pre className="text-white font-mono text-sm overflow-x-auto">
                      <code>{`<!-- Add this code to your site's header or before the closing </body> tag -->
<script>
  (function(c,o,m,d,n,t,r) {
    c[n]=c[n]||function(){(c[n].q=c[n].q||[]).push(arguments)};
    t=o.createElement(m);r=o.getElementsByTagName(m)[0];
    t.async=1;t.src=d;r.parentNode.insertBefore(t,r);
  })(window,document,'script','https://cdn.commandcenter.app/tracker.js','ccTracker');
  
  ccTracker('init', 'cc_api_key_12345abcdef67890ghijklmnopqrstuvwxyz');
  ccTracker('trackPageView');
</script>`}</code>
                    </pre>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:text-white hover:bg-white/10 ml-2"
                      onClick={() =>
                        handleCopy(
                          `<!-- Add this code to your site's header or before the closing </body> tag -->
<script>
  (function(c,o,m,d,n,t,r) {
    c[n]=c[n]||function(){(c[n].q=c[n].q||[]).push(arguments)};
    t=o.createElement(m);r=o.getElementsByTagName(m)[0];
    t.async=1;t.src=d;r.parentNode.insertBefore(t,r);
  })(window,document,'script','https://cdn.commandcenter.app/tracker.js','ccTracker');
  
  ccTracker('init', 'cc_api_key_12345abcdef67890ghijklmnopqrstuvwxyz');
  ccTracker('trackPageView');
</script>`,
                          "universal-embed",
                        )
                      }
                    >
                      {copied === "universal-embed" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" className="gap-1">
          <Globe className="h-4 w-4" />
          <span>View Documentation</span>
        </Button>
        <Button variant="outline" className="gap-1">
          <Code className="h-4 w-4" />
          <span>API Reference</span>
        </Button>
        <Button className="gap-1 bg-sage-700 hover:bg-sage-800 text-white">
          <Database className="h-4 w-4" />
          <span>Connect Website</span>
        </Button>
      </CardFooter>
    </Card>
  )
}

"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Bot,
  Search,
  Filter,
  MoreHorizontal,
  Play,
  Pause,
  RefreshCw,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Trash2,
  Edit,
  PlusCircle,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"

// Sample agent data
const agents = [
  {
    id: "agent-1",
    name: "Content Scout",
    avatar: "travel",
    function: "Content Research",
    status: "active",
    lastActivity: "2023-05-16T08:45:00Z",
    successRate: 98.2,
    tasksCompleted: 342,
    description: "Researches trending topics and content ideas for the Travel avatar",
    createdAt: "2023-01-10T09:15:00Z",
  },
  {
    id: "agent-2",
    name: "Engagement Bot",
    avatar: "fitness",
    function: "Community Management",
    status: "active",
    lastActivity: "2023-05-16T10:30:00Z",
    successRate: 95.7,
    tasksCompleted: 1204,
    description: "Responds to comments and messages for the Fitness avatar",
    createdAt: "2023-01-15T14:20:00Z",
  },
  {
    id: "agent-3",
    name: "Analytics Miner",
    avatar: "all",
    function: "Data Analysis",
    status: "paused",
    lastActivity: "2023-05-15T22:10:00Z",
    successRate: 99.1,
    tasksCompleted: 567,
    description: "Analyzes performance data across all avatars and generates reports",
    createdAt: "2023-02-05T11:30:00Z",
  },
  {
    id: "agent-4",
    name: "Content Repurposer",
    avatar: "entrepreneur",
    function: "Content Creation",
    status: "error",
    lastActivity: "2023-05-16T07:15:00Z",
    successRate: 89.3,
    tasksCompleted: 432,
    description: "Repurposes long-form content into social media posts for the Entrepreneur avatar",
    createdAt: "2023-02-20T16:45:00Z",
  },
  {
    id: "agent-5",
    name: "Hashtag Optimizer",
    avatar: "home",
    function: "SEO",
    status: "active",
    lastActivity: "2023-05-16T09:20:00Z",
    successRate: 97.5,
    tasksCompleted: 890,
    description: "Researches and optimizes hashtags for the Home avatar content",
    createdAt: "2023-03-01T10:00:00Z",
  },
  {
    id: "agent-6",
    name: "Affiliate Hunter",
    avatar: "health",
    function: "Affiliate Research",
    status: "maintenance",
    lastActivity: "2023-05-15T18:30:00Z",
    successRate: 94.8,
    tasksCompleted: 321,
    description: "Identifies new affiliate opportunities for the Health avatar",
    createdAt: "2023-03-15T13:20:00Z",
  },
  {
    id: "agent-7",
    name: "Scheduler Bot",
    avatar: "all",
    function: "Content Scheduling",
    status: "active",
    lastActivity: "2023-05-16T11:05:00Z",
    successRate: 99.7,
    tasksCompleted: 2145,
    description: "Optimizes posting schedules across all platforms and avatars",
    createdAt: "2023-04-01T09:30:00Z",
  },
]

export function AgentDirectory() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [avatarFilter, setAvatarFilter] = useState("all")
  const [functionFilter, setFunctionFilter] = useState("all")
  const [isAddAgentOpen, setIsAddAgentOpen] = useState(false)
  const [newAgent, setNewAgent] = useState({
    name: "",
    avatar: "all",
    function: "Content Research",
    description: "",
  })

  // Filter agents based on search and filters
  const filteredAgents = agents.filter((agent) => {
    const matchesSearch =
      agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      agent.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || agent.status === statusFilter
    const matchesAvatar = avatarFilter === "all" || agent.avatar === avatarFilter
    const matchesFunction = functionFilter === "all" || agent.function === functionFilter

    return matchesSearch && matchesStatus && matchesAvatar && matchesFunction
  })

  // Get status badge variant
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Active</Badge>
      case "paused":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
            Paused
          </Badge>
        )
      case "error":
        return <Badge variant="destructive">Error</Badge>
      case "maintenance":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-200">
            Maintenance
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle2 className="h-5 w-5 text-green-500" />
      case "paused":
        return <Pause className="h-5 w-5 text-yellow-500" />
      case "error":
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case "maintenance":
        return <RefreshCw className="h-5 w-5 text-blue-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  // Handle agent status change
  const handleStatusChange = (agentId: string, newStatus: string) => {
    // In a real app, this would update the agent status in the database
    toast({
      title: "Agent Status Updated",
      description: `Agent status has been changed to ${newStatus}.`,
    })
  }

  // Handle agent deletion
  const handleDeleteAgent = (agentId: string) => {
    // In a real app, this would delete the agent from the database
    toast({
      title: "Agent Deleted",
      description: "The agent has been deleted from the system.",
    })
  }

  // Handle adding a new agent
  const handleAddAgent = () => {
    if (!newAgent.name || !newAgent.description) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    // In a real app, this would add the agent to the database
    toast({
      title: "Agent Created",
      description: "Your new agent has been created successfully.",
    })

    setIsAddAgentOpen(false)
    setNewAgent({
      name: "",
      avatar: "all",
      function: "Content Research",
      description: "",
    })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Agent Directory</CardTitle>
            <CardDescription>Manage your AI agents and monitor their status</CardDescription>
          </div>
          <Button onClick={() => setIsAddAgentOpen(true)}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add Agent
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search agents..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex flex-1 items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="paused">Paused</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                  <SelectItem value="maintenance">Maintenance</SelectItem>
                </SelectContent>
              </Select>
              <Select value={avatarFilter} onValueChange={setAvatarFilter}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Filter by avatar" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Avatars</SelectItem>
                  <SelectItem value="travel">Travel</SelectItem>
                  <SelectItem value="fitness">Fitness</SelectItem>
                  <SelectItem value="health">Health</SelectItem>
                  <SelectItem value="home">Home</SelectItem>
                  <SelectItem value="entrepreneur">Entrepreneur</SelectItem>
                </SelectContent>
              </Select>
              <Select value={functionFilter} onValueChange={setFunctionFilter}>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Filter by function" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Functions</SelectItem>
                  <SelectItem value="Content Research">Content Research</SelectItem>
                  <SelectItem value="Community Management">Community Management</SelectItem>
                  <SelectItem value="Data Analysis">Data Analysis</SelectItem>
                  <SelectItem value="Content Creation">Content Creation</SelectItem>
                  <SelectItem value="SEO">SEO</SelectItem>
                  <SelectItem value="Affiliate Research">Affiliate Research</SelectItem>
                  <SelectItem value="Content Scheduling">Content Scheduling</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">Status</TableHead>
                  <TableHead>Agent Name</TableHead>
                  <TableHead>Avatar</TableHead>
                  <TableHead>Function</TableHead>
                  <TableHead>Last Activity</TableHead>
                  <TableHead className="text-right">Success Rate</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAgents.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      No agents found matching your filters.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredAgents.map((agent) => (
                    <TableRow key={agent.id}>
                      <TableCell>{getStatusIcon(agent.status)}</TableCell>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <Bot className="h-4 w-4 text-muted-foreground" />
                          <span>{agent.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {agent.avatar}
                        </Badge>
                      </TableCell>
                      <TableCell>{agent.function}</TableCell>
                      <TableCell>{formatDate(agent.lastActivity)}</TableCell>
                      <TableCell className="text-right">
                        <span
                          className={`font-medium ${agent.successRate > 95 ? "text-green-600" : agent.successRate > 90 ? "text-yellow-600" : "text-red-600"}`}
                        >
                          {agent.successRate}%
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => (window.location.href = `/agents/${agent.id}`)}>
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {agent.status === "active" ? (
                              <DropdownMenuItem onClick={() => handleStatusChange(agent.id, "paused")}>
                                <Pause className="mr-2 h-4 w-4" />
                                Pause Agent
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem onClick={() => handleStatusChange(agent.id, "active")}>
                                <Play className="mr-2 h-4 w-4" />
                                Activate Agent
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem onClick={() => handleStatusChange(agent.id, "maintenance")}>
                              <RefreshCw className="mr-2 h-4 w-4" />
                              Set to Maintenance
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => (window.location.href = `/agents/${agent.id}/edit`)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Agent
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteAgent(agent.id)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Agent
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Add Agent Dialog */}
        <Dialog open={isAddAgentOpen} onOpenChange={setIsAddAgentOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Add New Agent</DialogTitle>
              <DialogDescription>Create a new AI agent to automate tasks in your empire.</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="agent-name" className="text-right">
                  Name
                </Label>
                <Input
                  id="agent-name"
                  placeholder="e.g., Content Scout"
                  className="col-span-3"
                  value={newAgent.name}
                  onChange={(e) => setNewAgent({ ...newAgent, name: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="agent-avatar" className="text-right">
                  Avatar
                </Label>
                <Select value={newAgent.avatar} onValueChange={(value) => setNewAgent({ ...newAgent, avatar: value })}>
                  <SelectTrigger id="agent-avatar" className="col-span-3">
                    <SelectValue placeholder="Select avatar" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Avatars</SelectItem>
                    <SelectItem value="travel">Travel</SelectItem>
                    <SelectItem value="fitness">Fitness</SelectItem>
                    <SelectItem value="health">Health</SelectItem>
                    <SelectItem value="home">Home</SelectItem>
                    <SelectItem value="entrepreneur">Entrepreneur</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="agent-function" className="text-right">
                  Function
                </Label>
                <Select
                  value={newAgent.function}
                  onValueChange={(value) => setNewAgent({ ...newAgent, function: value })}
                >
                  <SelectTrigger id="agent-function" className="col-span-3">
                    <SelectValue placeholder="Select function" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Content Research">Content Research</SelectItem>
                    <SelectItem value="Community Management">Community Management</SelectItem>
                    <SelectItem value="Data Analysis">Data Analysis</SelectItem>
                    <SelectItem value="Content Creation">Content Creation</SelectItem>
                    <SelectItem value="SEO">SEO</SelectItem>
                    <SelectItem value="Affiliate Research">Affiliate Research</SelectItem>
                    <SelectItem value="Content Scheduling">Content Scheduling</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="agent-description" className="text-right">
                  Description
                </Label>
                <Input
                  id="agent-description"
                  placeholder="e.g., Researches trending topics for the Travel avatar"
                  className="col-span-3"
                  value={newAgent.description}
                  onChange={(e) => setNewAgent({ ...newAgent, description: e.target.value })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddAgentOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddAgent}>Create Agent</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}

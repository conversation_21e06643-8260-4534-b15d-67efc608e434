"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogT<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Facebook, Instagram, Twitter, Youtube } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { toast } from "@/components/ui/use-toast"

interface CreateEventDialogProps {
  onEventCreated?: (event: any) => void
  children?: React.ReactNode
}

export function CreateEventDialog({ onEventCreated, children }: CreateEventDialogProps) {
  const [open, setOpen] = useState(false)
  const [date, setDate] = useState<Date>()
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [category, setCategory] = useState("")
  const [avatar, setAvatar] = useState("")
  const [time, setTime] = useState("")
  const [platforms, setPlatforms] = useState<string[]>([])

  const handlePlatformToggle = (platform: string) => {
    setPlatforms((current) =>
      current.includes(platform) ? current.filter((p) => p !== platform) : [...current, platform],
    )
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Validate required fields
    if (!title || !date || !category || !avatar || !time || platforms.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields and select at least one platform.",
        variant: "destructive",
      })
      return
    }

    // Create event object
    const [hours, minutes] = time.split(":").map(Number)
    const eventDate = new Date(date)
    eventDate.setHours(hours, minutes)

    const newEvent = {
      title,
      description,
      date: eventDate.toISOString(),
      category,
      avatar,
      avatarName: getAvatarName(avatar),
      avatarInitials: getAvatarInitials(avatar),
      platforms,
      status: "scheduled" as const,
    }

    // Call the callback function if provided
    if (onEventCreated) {
      onEventCreated(newEvent)
    }

    // Show success message
    toast({
      title: "Event Created",
      description: `"${title}" has been scheduled for ${format(eventDate, "PPP")} at ${format(eventDate, "p")}`,
    })

    // Reset form and close dialog
    resetForm()
    setOpen(false)
  }

  const resetForm = () => {
    setTitle("")
    setDescription("")
    setDate(undefined)
    setCategory("")
    setAvatar("")
    setTime("")
    setPlatforms([])
  }

  const getAvatarName = (avatarCode: string): string => {
    switch (avatarCode) {
      case "travel":
        return "Travel Avatar"
      case "home":
        return "Home Avatar"
      case "health":
        return "Health Avatar"
      case "fitness":
        return "Fitness Avatar"
      case "entrepreneur":
        return "Entrepreneur Avatar"
      default:
        return "Avatar"
    }
  }

  const getAvatarInitials = (avatarCode: string): string => {
    switch (avatarCode) {
      case "travel":
        return "TR"
      case "home":
        return "HM"
      case "health":
        return "HE"
      case "fitness":
        return "FI"
      case "entrepreneur":
        return "EN"
      default:
        return "AV"
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children || <Button>Create New Event</Button>}</DialogTrigger>
      <DialogContent className="sm:max-w-[525px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Create New Content Event</DialogTitle>
            <DialogDescription>Add a new content event to your calendar. Fill in the details below.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title" className="required">
                Title
              </Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter event title"
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Enter event description"
                className="min-h-[80px]"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="category" className="required">
                  Category
                </Label>
                <Select value={category} onValueChange={setCategory} required>
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Travel">Travel</SelectItem>
                    <SelectItem value="Home">Home</SelectItem>
                    <SelectItem value="Health">Health</SelectItem>
                    <SelectItem value="Fitness">Fitness</SelectItem>
                    <SelectItem value="Entrepreneurship">Entrepreneurship</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="avatar" className="required">
                  Avatar
                </Label>
                <Select value={avatar} onValueChange={setAvatar} required>
                  <SelectTrigger id="avatar">
                    <SelectValue placeholder="Select avatar" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="travel">Travel Avatar</SelectItem>
                    <SelectItem value="home">Home Avatar</SelectItem>
                    <SelectItem value="health">Health Avatar</SelectItem>
                    <SelectItem value="fitness">Fitness Avatar</SelectItem>
                    <SelectItem value="entrepreneur">Entrepreneur Avatar</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label className="required">Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn("w-full justify-start text-left font-normal", !date && "text-muted-foreground")}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="time" className="required">
                  Time
                </Label>
                <Input id="time" type="time" value={time} onChange={(e) => setTime(e.target.value)} required />
              </div>
            </div>
            <div className="grid gap-2">
              <Label className="required">Platforms</Label>
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="youtube"
                    checked={platforms.includes("youtube")}
                    onCheckedChange={() => handlePlatformToggle("youtube")}
                  />
                  <Label htmlFor="youtube" className="flex items-center gap-1 cursor-pointer">
                    <Youtube className="h-4 w-4" />
                    <span>YouTube</span>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="instagram"
                    checked={platforms.includes("instagram")}
                    onCheckedChange={() => handlePlatformToggle("instagram")}
                  />
                  <Label htmlFor="instagram" className="flex items-center gap-1 cursor-pointer">
                    <Instagram className="h-4 w-4" />
                    <span>Instagram</span>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="twitter"
                    checked={platforms.includes("twitter")}
                    onCheckedChange={() => handlePlatformToggle("twitter")}
                  />
                  <Label htmlFor="twitter" className="flex items-center gap-1 cursor-pointer">
                    <Twitter className="h-4 w-4" />
                    <span>Twitter</span>
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="facebook"
                    checked={platforms.includes("facebook")}
                    onCheckedChange={() => handlePlatformToggle("facebook")}
                  />
                  <Label htmlFor="facebook" className="flex items-center gap-1 cursor-pointer">
                    <Facebook className="h-4 w-4" />
                    <span>Facebook</span>
                  </Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit">Create Event</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

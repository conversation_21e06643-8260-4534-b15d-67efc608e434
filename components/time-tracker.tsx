"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/ui/chart"
import { ResponsiveContainer, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Line } from "recharts"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Clock, Save, Users, Bot, ArrowUpDown } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"

const timeData = [
  {
    task: "Content Research",
    humanTime: 3.5,
    aiTime: 0.5,
    timeSaved: 3.0,
    efficiency: 85,
    department: "Content",
  },
  {
    task: "Script Writing",
    humanTime: 4.0,
    aiTime: 1.0,
    timeSaved: 3.0,
    efficiency: 75,
    department: "Content",
  },
  {
    task: "Video Editing",
    humanTime: 6.0,
    aiTime: 2.0,
    timeSaved: 4.0,
    efficiency: 67,
    department: "Production",
  },
  {
    task: "Social Media Posting",
    humanTime: 1.5,
    aiTime: 0.2,
    timeSaved: 1.3,
    efficiency: 87,
    department: "Marketing",
  },
  {
    task: "Email Campaign Creation",
    humanTime: 3.0,
    aiTime: 0.8,
    timeSaved: 2.2,
    efficiency: 73,
    department: "Marketing",
  },
  {
    task: "Analytics Review",
    humanTime: 4.0,
    aiTime: 0.5,
    timeSaved: 3.5,
    efficiency: 88,
    department: "Analytics",
  },
  {
    task: "Competitor Research",
    humanTime: 5.0,
    aiTime: 1.0,
    timeSaved: 4.0,
    efficiency: 80,
    department: "Strategy",
  },
  {
    task: "Audience Segmentation",
    humanTime: 3.0,
    aiTime: 0.5,
    timeSaved: 2.5,
    efficiency: 83,
    department: "Marketing",
  },
  {
    task: "Product Listing Creation",
    humanTime: 4.5,
    aiTime: 1.0,
    timeSaved: 3.5,
    efficiency: 78,
    department: "Products",
  },
  {
    task: "Customer Support",
    humanTime: 8.0,
    aiTime: 3.0,
    timeSaved: 5.0,
    efficiency: 63,
    department: "Support",
  },
]

const monthlyData = [
  {
    name: "Jan",
    "Human Hours": 180,
    "AI Hours": 40,
    "Time Saved": 140,
  },
  {
    name: "Feb",
    "Human Hours": 160,
    "AI Hours": 50,
    "Time Saved": 110,
  },
  {
    name: "Mar",
    "Human Hours": 140,
    "AI Hours": 60,
    "Time Saved": 80,
  },
  {
    name: "Apr",
    "Human Hours": 120,
    "AI Hours": 70,
    "Time Saved": 50,
  },
  {
    name: "May",
    "Human Hours": 100,
    "AI Hours": 80,
    "Time Saved": 20,
  },
  {
    name: "Jun",
    "Human Hours": 80,
    "AI Hours": 90,
    "Time Saved": -10,
  },
]

const departmentData = [
  {
    name: "Content",
    "Human Hours": 45,
    "AI Hours": 15,
    "Time Saved": 30,
  },
  {
    name: "Marketing",
    "Human Hours": 35,
    "AI Hours": 25,
    "Time Saved": 10,
  },
  {
    name: "Production",
    "Human Hours": 60,
    "AI Hours": 20,
    "Time Saved": 40,
  },
  {
    name: "Analytics",
    "Human Hours": 20,
    "AI Hours": 10,
    "Time Saved": 10,
  },
  {
    name: "Strategy",
    "Human Hours": 30,
    "AI Hours": 10,
    "Time Saved": 20,
  },
  {
    name: "Support",
    "Human Hours": 50,
    "AI Hours": 20,
    "Time Saved": 30,
  },
]

export function TimeTracker() {
  const [department, setDepartment] = useState("all")
  const [sortBy, setSortBy] = useState("timeSaved")
  const [sortOrder, setSortOrder] = useState("desc")

  const filteredData =
    department === "all"
      ? timeData
      : timeData.filter((item) => item.department.toLowerCase() === department.toLowerCase())

  const sortedData = [...filteredData].sort((a, b) => {
    const factor = sortOrder === "asc" ? 1 : -1
    return factor * (a[sortBy] - b[sortBy])
  })

  const totalHumanHours = filteredData.reduce((sum, item) => sum + item.humanTime, 0)
  const totalAIHours = filteredData.reduce((sum, item) => sum + item.aiTime, 0)
  const totalTimeSaved = filteredData.reduce((sum, item) => sum + item.timeSaved, 0)
  const averageEfficiency = Math.round(
    filteredData.reduce((sum, item) => sum + item.efficiency, 0) / filteredData.length,
  )

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortBy(field)
      setSortOrder("desc")
    }
  }

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Human Hours</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalHumanHours.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">Across {filteredData.length} tasks</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total AI Hours</CardTitle>
            <Bot className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalAIHours.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">Across {filteredData.length} tasks</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Time Saved</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalTimeSaved.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((totalTimeSaved / (totalHumanHours + totalTimeSaved)) * 100)}% reduction
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Efficiency</CardTitle>
            <Save className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageEfficiency}%</div>
            <Progress value={averageEfficiency} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="tasks" className="space-y-4">
        <TabsList>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="trends">Monthly Trends</TabsTrigger>
          <TabsTrigger value="departments">By Department</TabsTrigger>
        </TabsList>

        <TabsContent value="tasks" className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" className="h-8 gap-1" onClick={() => handleSort("timeSaved")}>
                <ArrowUpDown className="h-4 w-4" />
                Sort by {sortBy === "timeSaved" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
              </Button>
              <Select value={department} onValueChange={setDepartment}>
                <SelectTrigger className="w-[180px] h-8">
                  <SelectValue placeholder="Filter by department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  <SelectItem value="content">Content</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                  <SelectItem value="production">Production</SelectItem>
                  <SelectItem value="analytics">Analytics</SelectItem>
                  <SelectItem value="strategy">Strategy</SelectItem>
                  <SelectItem value="support">Support</SelectItem>
                  <SelectItem value="products">Products</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button variant="outline" size="sm">
              Export Data
            </Button>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Task</TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("humanTime")}>
                      Human Hours {sortBy === "humanTime" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("aiTime")}>
                      AI Hours {sortBy === "aiTime" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("timeSaved")}>
                      Time Saved {sortBy === "timeSaved" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort("efficiency")}>
                      Efficiency {sortBy === "efficiency" ? (sortOrder === "asc" ? "↑" : "↓") : ""}
                    </TableHead>
                    <TableHead>Department</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedData.map((task, i) => (
                    <TableRow key={i}>
                      <TableCell className="font-medium">{task.task}</TableCell>
                      <TableCell>{task.humanTime.toFixed(1)}</TableCell>
                      <TableCell>{task.aiTime.toFixed(1)}</TableCell>
                      <TableCell>{task.timeSaved.toFixed(1)}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Progress value={task.efficiency} className="w-[60px]" />
                          <span>{task.efficiency}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{task.department}</Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Time Allocation Trends</CardTitle>
              <CardDescription>Human vs AI hours over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={monthlyData}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="Human Hours" stroke="#8884d8" activeDot={{ r: 8 }} />
                    <Line type="monotone" dataKey="AI Hours" stroke="#82ca9d" />
                    <Line type="monotone" dataKey="Time Saved" stroke="#ff7300" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="departments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Time Allocation by Department</CardTitle>
              <CardDescription>Human vs AI hours across departments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={departmentData}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="Human Hours" fill="#8884d8" />
                    <Bar dataKey="AI Hours" fill="#82ca9d" />
                    <Bar dataKey="Time Saved" fill="#ff7300" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

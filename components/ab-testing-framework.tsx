"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  FlaskConical,
  Play,
  Pause,
  Square,
  TrendingUp,
  BarChart3,
  Target,
  CheckCircle2,
  AlertTriangle,
  Settings,
  Plus,
  Eye,
  Edit,
  Copy,
  Calendar,
  Activity,
  DollarSign,
  Gauge,
  ArrowRight,
} from "lucide-react"

interface ABTest {
  id: string
  name: string
  description: string
  hypothesis: string
  status: "draft" | "running" | "paused" | "completed" | "cancelled"
  type: "optimization" | "feature" | "workflow" | "ui"
  priority: "high" | "medium" | "low"
  variants: ABVariant[]
  metrics: ABMetric[]
  configuration: {
    trafficSplit: number[]
    duration: number // days
    minSampleSize: number
    confidenceLevel: number
    significanceThreshold: number
  }
  results: ABResults | null
  createdAt: string
  startedAt?: string
  endedAt?: string
  createdBy: string
  tags: string[]
  relatedSuggestionId?: string
}

interface ABVariant {
  id: string
  name: string
  description: string
  isControl: boolean
  trafficPercentage: number
  configuration: Record<string, any>
  metrics: {
    impressions: number
    conversions: number
    conversionRate: number
    averageValue: number
    totalValue: number
    bounceRate: number
    engagementRate: number
  }
}

interface ABMetric {
  id: string
  name: string
  type: "conversion" | "revenue" | "engagement" | "performance"
  description: string
  unit: string
  isPrimary: boolean
  targetDirection: "increase" | "decrease"
  currentValue?: number
  targetValue?: number
}

interface ABResults {
  winner: string | null
  confidence: number
  significance: number
  lift: number
  pValue: number
  summary: string
  recommendations: string[]
  detailedAnalysis: {
    statisticalSignificance: boolean
    practicalSignificance: boolean
    sampleSizeAdequate: boolean
    testDuration: number
    totalParticipants: number
  }
}

export function ABTestingFramework() {
  const [tests, setTests] = useState<ABTest[]>([])
  const [selectedTest, setSelectedTest] = useState<ABTest | null>(null)
  const [activeTab, setActiveTab] = useState("overview")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [newTest, setNewTest] = useState<Partial<ABTest>>({
    name: "",
    description: "",
    hypothesis: "",
    type: "optimization",
    priority: "medium",
    variants: [],
    metrics: [],
    configuration: {
      trafficSplit: [50, 50],
      duration: 14,
      minSampleSize: 1000,
      confidenceLevel: 95,
      significanceThreshold: 0.05,
    },
    tags: [],
  })

  // Mock data initialization
  useEffect(() => {
    const mockTests: ABTest[] = [
      {
        id: "test-001",
        name: "Parallel Content Generation",
        description: "Testing parallel processing vs sequential processing for content generation",
        hypothesis: "Parallel content generation will reduce processing time by 40% without affecting quality",
        status: "running",
        type: "optimization",
        priority: "high",
        variants: [
          {
            id: "control",
            name: "Sequential Processing",
            description: "Current sequential content generation approach",
            isControl: true,
            trafficPercentage: 50,
            configuration: { parallelism: false, batchSize: 1 },
            metrics: {
              impressions: 2450,
              conversions: 2380,
              conversionRate: 97.1,
              averageValue: 45.2,
              totalValue: 107556,
              bounceRate: 2.9,
              engagementRate: 94.2,
            },
          },
          {
            id: "variant-a",
            name: "Parallel Processing",
            description: "New parallel content generation with batch processing",
            isControl: false,
            trafficPercentage: 50,
            configuration: { parallelism: true, batchSize: 5 },
            metrics: {
              impressions: 2520,
              conversions: 2465,
              conversionRate: 97.8,
              averageValue: 27.1,
              totalValue: 66801,
              bounceRate: 2.2,
              engagementRate: 96.1,
            },
          },
        ],
        metrics: [
          {
            id: "processing-time",
            name: "Processing Time",
            type: "performance",
            description: "Average time to generate content",
            unit: "seconds",
            isPrimary: true,
            targetDirection: "decrease",
            currentValue: 45.2,
            targetValue: 27.0,
          },
          {
            id: "quality-score",
            name: "Content Quality Score",
            type: "engagement",
            description: "AI-generated quality rating",
            unit: "score",
            isPrimary: true,
            targetDirection: "increase",
            currentValue: 8.2,
            targetValue: 8.0,
          },
          {
            id: "error-rate",
            name: "Error Rate",
            type: "performance",
            description: "Percentage of failed generations",
            unit: "%",
            isPrimary: false,
            targetDirection: "decrease",
            currentValue: 2.9,
            targetValue: 3.0,
          },
        ],
        configuration: {
          trafficSplit: [50, 50],
          duration: 14,
          minSampleSize: 2000,
          confidenceLevel: 95,
          significanceThreshold: 0.05,
        },
        results: {
          winner: "variant-a",
          confidence: 97.3,
          significance: 0.027,
          lift: 40.1,
          pValue: 0.027,
          summary: "Parallel processing shows significant improvement in processing time with maintained quality",
          recommendations: [
            "Implement parallel processing for content generation",
            "Monitor quality metrics during rollout",
            "Consider increasing batch size for further optimization",
          ],
          detailedAnalysis: {
            statisticalSignificance: true,
            practicalSignificance: true,
            sampleSizeAdequate: true,
            testDuration: 7,
            totalParticipants: 4970,
          },
        },
        createdAt: "2024-01-15T10:00:00Z",
        startedAt: "2024-01-16T09:00:00Z",
        createdBy: "AI Optimization Engine",
        tags: ["performance", "content-generation", "high-impact"],
        relatedSuggestionId: "opt-001",
      },
      {
        id: "test-002",
        name: "Smart Caching Strategy",
        description: "Testing Redis caching vs in-memory caching for market research data",
        hypothesis: "Redis caching will improve response times by 60% compared to in-memory caching",
        status: "completed",
        type: "optimization",
        priority: "high",
        variants: [
          {
            id: "control",
            name: "In-Memory Caching",
            description: "Current in-memory caching approach",
            isControl: true,
            trafficPercentage: 50,
            configuration: { cacheType: "memory", ttl: 3600 },
            metrics: {
              impressions: 1850,
              conversions: 1798,
              conversionRate: 97.2,
              averageValue: 28.7,
              totalValue: 51604,
              bounceRate: 2.8,
              engagementRate: 95.1,
            },
          },
          {
            id: "variant-a",
            name: "Redis Caching",
            description: "Redis-based distributed caching",
            isControl: false,
            trafficPercentage: 50,
            configuration: { cacheType: "redis", ttl: 7200 },
            metrics: {
              impressions: 1920,
              conversions: 1885,
              conversionRate: 98.2,
              averageValue: 11.5,
              totalValue: 21678,
              bounceRate: 1.8,
              engagementRate: 97.3,
            },
          },
        ],
        metrics: [
          {
            id: "response-time",
            name: "Response Time",
            type: "performance",
            description: "Average API response time",
            unit: "seconds",
            isPrimary: true,
            targetDirection: "decrease",
            currentValue: 28.7,
            targetValue: 11.5,
          },
          {
            id: "cache-hit-rate",
            name: "Cache Hit Rate",
            type: "performance",
            description: "Percentage of requests served from cache",
            unit: "%",
            isPrimary: true,
            targetDirection: "increase",
            currentValue: 67.2,
            targetValue: 89.1,
          },
        ],
        configuration: {
          trafficSplit: [50, 50],
          duration: 10,
          minSampleSize: 1500,
          confidenceLevel: 95,
          significenceThreshold: 0.05,
        },
        results: {
          winner: "variant-a",
          confidence: 99.1,
          significance: 0.009,
          lift: 59.9,
          pValue: 0.009,
          summary: "Redis caching significantly outperforms in-memory caching with 60% improvement",
          recommendations: [
            "Migrate to Redis caching immediately",
            "Implement cache warming strategies",
            "Monitor memory usage and optimize TTL values",
          ],
          detailedAnalysis: {
            statisticalSignificance: true,
            practicalSignificance: true,
            sampleSizeAdequate: true,
            testDuration: 10,
            totalParticipants: 3770,
          },
        },
        createdAt: "2024-01-10T14:30:00Z",
        startedAt: "2024-01-11T09:00:00Z",
        endedAt: "2024-01-21T09:00:00Z",
        createdBy: "AI Optimization Engine",
        tags: ["performance", "caching", "market-research"],
        relatedSuggestionId: "opt-002",
      },
      {
        id: "test-003",
        name: "Quality Scoring Threshold",
        description: "Testing different quality score thresholds for content filtering",
        hypothesis: "Higher quality threshold (8.5) will reduce errors by 50% with minimal impact on throughput",
        status: "running",
        type: "optimization",
        priority: "medium",
        variants: [
          {
            id: "control",
            name: "Standard Threshold (7.0)",
            description: "Current quality threshold of 7.0",
            isControl: true,
            trafficPercentage: 33,
            configuration: { qualityThreshold: 7.0 },
            metrics: {
              impressions: 1200,
              conversions: 1152,
              conversionRate: 96.0,
              averageValue: 15.2,
              totalValue: 17510,
              bounceRate: 4.0,
              engagementRate: 92.1,
            },
          },
          {
            id: "variant-a",
            name: "Medium Threshold (8.0)",
            description: "Increased quality threshold to 8.0",
            isControl: false,
            trafficPercentage: 33,
            configuration: { qualityThreshold: 8.0 },
            metrics: {
              impressions: 1180,
              conversions: 1145,
              conversionRate: 97.0,
              averageValue: 16.8,
              totalValue: 19236,
              bounceRate: 3.0,
              engagementRate: 94.2,
            },
          },
          {
            id: "variant-b",
            name: "High Threshold (8.5)",
            description: "High quality threshold of 8.5",
            isControl: false,
            trafficPercentage: 34,
            configuration: { qualityThreshold: 8.5 },
            metrics: {
              impressions: 1220,
              conversions: 1195,
              conversionRate: 97.9,
              averageValue: 18.9,
              totalValue: 22586,
              bounceRate: 2.1,
              engagementRate: 96.8,
            },
          },
        ],
        metrics: [
          {
            id: "error-rate",
            name: "Content Error Rate",
            type: "performance",
            description: "Percentage of content with quality issues",
            unit: "%",
            isPrimary: true,
            targetDirection: "decrease",
            currentValue: 4.0,
            targetValue: 2.0,
          },
          {
            id: "throughput",
            name: "Content Throughput",
            type: "performance",
            description: "Content pieces generated per hour",
            unit: "pieces/hour",
            isPrimary: true,
            targetDirection: "increase",
            currentValue: 180,
            targetValue: 170,
          },
        ],
        configuration: {
          trafficSplit: [33, 33, 34],
          duration: 21,
          minSampleSize: 3000,
          confidenceLevel: 95,
          significanceThreshold: 0.05,
        },
        results: null,
        createdAt: "2024-01-20T11:15:00Z",
        startedAt: "2024-01-22T09:00:00Z",
        createdBy: "AI Optimization Engine",
        tags: ["quality", "content-generation", "filtering"],
        relatedSuggestionId: "opt-003",
      },
    ]

    setTests(mockTests)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "running":
        return "bg-green-100 text-green-800 border-green-200"
      case "completed":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "paused":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "draft":
        return "bg-gray-100 text-gray-800 border-gray-200"
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800 border-red-200"
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "low":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "running":
        return <Play className="h-4 w-4" />
      case "completed":
        return <CheckCircle2 className="h-4 w-4" />
      case "paused":
        return <Pause className="h-4 w-4" />
      case "draft":
        return <Edit className="h-4 w-4" />
      case "cancelled":
        return <Square className="h-4 w-4" />
      default:
        return <Settings className="h-4 w-4" />
    }
  }

  const calculateProgress = (test: ABTest) => {
    if (test.status === "completed") return 100
    if (test.status === "draft" || !test.startedAt) return 0

    const startDate = new Date(test.startedAt)
    const now = new Date()
    const elapsed = (now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
    const progress = Math.min((elapsed / test.configuration.duration) * 100, 100)
    return Math.round(progress)
  }

  const startTest = (testId: string) => {
    setTests((prev) =>
      prev.map((test) =>
        test.id === testId ? { ...test, status: "running", startedAt: new Date().toISOString() } : test,
      ),
    )
  }

  const pauseTest = (testId: string) => {
    setTests((prev) => prev.map((test) => (test.id === testId ? { ...test, status: "paused" } : test)))
  }

  const stopTest = (testId: string) => {
    setTests((prev) =>
      prev.map((test) =>
        test.id === testId ? { ...test, status: "completed", endedAt: new Date().toISOString() } : test,
      ),
    )
  }

  const duplicateTest = (testId: string) => {
    const originalTest = tests.find((t) => t.id === testId)
    if (originalTest) {
      const newTest: ABTest = {
        ...originalTest,
        id: `test-${Date.now()}`,
        name: `${originalTest.name} (Copy)`,
        status: "draft",
        results: null,
        createdAt: new Date().toISOString(),
        startedAt: undefined,
        endedAt: undefined,
      }
      setTests((prev) => [...prev, newTest])
    }
  }

  const deleteTest = (testId: string) => {
    setTests((prev) => prev.filter((test) => test.id !== testId))
  }

  const createTest = () => {
    if (!newTest.name || !newTest.description || !newTest.hypothesis) return

    const test: ABTest = {
      id: `test-${Date.now()}`,
      name: newTest.name,
      description: newTest.description,
      hypothesis: newTest.hypothesis,
      status: "draft",
      type: newTest.type || "optimization",
      priority: newTest.priority || "medium",
      variants: newTest.variants || [],
      metrics: newTest.metrics || [],
      configuration: newTest.configuration || {
        trafficSplit: [50, 50],
        duration: 14,
        minSampleSize: 1000,
        confidenceLevel: 95,
        significanceThreshold: 0.05,
      },
      results: null,
      createdAt: new Date().toISOString(),
      createdBy: "User",
      tags: newTest.tags || [],
      relatedSuggestionId: newTest.relatedSuggestionId,
    }

    setTests((prev) => [...prev, test])
    setIsCreateDialogOpen(false)
    setNewTest({
      name: "",
      description: "",
      hypothesis: "",
      type: "optimization",
      priority: "medium",
      variants: [],
      metrics: [],
      configuration: {
        trafficSplit: [50, 50],
        duration: 14,
        minSampleSize: 1000,
        confidenceLevel: 95,
        significanceThreshold: 0.05,
      },
      tags: [],
    })
  }

  const runningTests = tests.filter((t) => t.status === "running")
  const completedTests = tests.filter((t) => t.status === "completed")
  const draftTests = tests.filter((t) => t.status === "draft")

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FlaskConical className="h-5 w-5" />
                A/B Testing Framework
              </CardTitle>
              <CardDescription>Test and validate optimization suggestions with statistical confidence</CardDescription>
            </div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Test
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Create New A/B Test</DialogTitle>
                  <DialogDescription>Set up a new A/B test to validate optimization suggestions</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="test-name">Test Name</Label>
                      <Input
                        id="test-name"
                        value={newTest.name}
                        onChange={(e) => setNewTest({ ...newTest, name: e.target.value })}
                        placeholder="Enter test name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="test-type">Test Type</Label>
                      <Select
                        value={newTest.type}
                        onValueChange={(value) => setNewTest({ ...newTest, type: value as any })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="optimization">Optimization</SelectItem>
                          <SelectItem value="feature">Feature</SelectItem>
                          <SelectItem value="workflow">Workflow</SelectItem>
                          <SelectItem value="ui">UI/UX</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="test-description">Description</Label>
                    <Textarea
                      id="test-description"
                      value={newTest.description}
                      onChange={(e) => setNewTest({ ...newTest, description: e.target.value })}
                      placeholder="Describe what you're testing"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="test-hypothesis">Hypothesis</Label>
                    <Textarea
                      id="test-hypothesis"
                      value={newTest.hypothesis}
                      onChange={(e) => setNewTest({ ...newTest, hypothesis: e.target.value })}
                      placeholder="State your hypothesis (e.g., 'Changing X will improve Y by Z%')"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="test-priority">Priority</Label>
                      <Select
                        value={newTest.priority}
                        onValueChange={(value) => setNewTest({ ...newTest, priority: value as any })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="high">High</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="low">Low</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="test-duration">Duration (days)</Label>
                      <Input
                        id="test-duration"
                        type="number"
                        value={newTest.configuration?.duration}
                        onChange={(e) =>
                          setNewTest({
                            ...newTest,
                            configuration: {
                              ...newTest.configuration!,
                              duration: Number.parseInt(e.target.value),
                            },
                          })
                        }
                        placeholder="14"
                      />
                    </div>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={createTest}>Create Test</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="running">Running ({runningTests.length})</TabsTrigger>
          <TabsTrigger value="completed">Completed ({completedTests.length})</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Tests</CardTitle>
                <FlaskConical className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{tests.length}</div>
                <p className="text-xs text-muted-foreground">All time</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Running Tests</CardTitle>
                <Play className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{runningTests.length}</div>
                <p className="text-xs text-muted-foreground">Currently active</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {completedTests.length > 0
                    ? Math.round(
                        (completedTests.filter((t) => t.results?.winner && t.results.winner !== "control").length /
                          completedTests.length) *
                          100,
                      )
                    : 0}
                  %
                </div>
                <p className="text-xs text-muted-foreground">Tests with positive results</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Confidence</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {completedTests.length > 0
                    ? Math.round(
                        completedTests.reduce((acc, t) => acc + (t.results?.confidence || 0), 0) /
                          completedTests.length,
                      )
                    : 0}
                  %
                </div>
                <p className="text-xs text-muted-foreground">Statistical confidence</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recent Test Activity</CardTitle>
                <CardDescription>Latest A/B test updates and results</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[300px]">
                  <div className="space-y-4">
                    {tests
                      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                      .slice(0, 5)
                      .map((test) => (
                        <div key={test.id} className="flex items-start gap-3 p-3 border rounded-lg">
                          <div className="mt-1">{getStatusIcon(test.status)}</div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-medium text-sm">{test.name}</h4>
                              <Badge variant="outline" className={getStatusColor(test.status)}>
                                {test.status}
                              </Badge>
                            </div>
                            <p className="text-xs text-muted-foreground mb-2">{test.description}</p>
                            <div className="flex items-center gap-4 text-xs">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                <span>{new Date(test.createdAt).toLocaleDateString()}</span>
                              </div>
                              {test.status === "running" && (
                                <div className="flex items-center gap-1">
                                  <Activity className="h-3 w-3" />
                                  <span>{calculateProgress(test)}% complete</span>
                                </div>
                              )}
                              {test.results && (
                                <div className="flex items-center gap-1">
                                  <Target className="h-3 w-3" />
                                  <span>{test.results.confidence}% confidence</span>
                                </div>
                              )}
                            </div>
                          </div>
                          <Button size="sm" variant="outline" onClick={() => setSelectedTest(test)}>
                            View
                          </Button>
                        </div>
                      ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Performance Impact</CardTitle>
                <CardDescription>Cumulative improvements from completed tests</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                      <div className="flex items-center gap-2 mb-2">
                        <TrendingUp className="h-4 w-4 text-green-600" />
                        <h4 className="font-medium text-sm">Processing Time</h4>
                      </div>
                      <div className="text-2xl font-bold text-green-600">-40%</div>
                      <p className="text-xs text-green-700">Average improvement</p>
                    </div>
                    <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="flex items-center gap-2 mb-2">
                        <DollarSign className="h-4 w-4 text-blue-600" />
                        <h4 className="font-medium text-sm">Cost Savings</h4>
                      </div>
                      <div className="text-2xl font-bold text-blue-600">$1,200</div>
                      <p className="text-xs text-blue-700">Monthly savings</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Gauge className="h-4 w-4 text-purple-600" />
                        <h4 className="font-medium text-sm">Quality Score</h4>
                      </div>
                      <div className="text-2xl font-bold text-purple-600">+15%</div>
                      <p className="text-xs text-purple-700">Quality improvement</p>
                    </div>
                    <div className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Activity className="h-4 w-4 text-orange-600" />
                        <h4 className="font-medium text-sm">Throughput</h4>
                      </div>
                      <div className="text-2xl font-bold text-orange-600">+25%</div>
                      <p className="text-xs text-orange-700">Capacity increase</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="running" className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            {runningTests.map((test) => (
              <Card key={test.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(test.status)}
                        <h3 className="font-semibold">{test.name}</h3>
                      </div>
                      <Badge variant="outline" className={getPriorityColor(test.priority)}>
                        {test.priority}
                      </Badge>
                      <Badge variant="outline" className={getStatusColor(test.status)}>
                        {test.status}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline" onClick={() => pauseTest(test.id)}>
                        <Pause className="h-4 w-4 mr-1" />
                        Pause
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => stopTest(test.id)}>
                        <Square className="h-4 w-4 mr-1" />
                        Stop
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => setSelectedTest(test)}>
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>
                  <CardDescription>{test.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between text-sm">
                      <span>Progress</span>
                      <span>{calculateProgress(test)}% complete</span>
                    </div>
                    <Progress value={calculateProgress(test)} className="w-full" />

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Test Configuration</h4>
                        <div className="space-y-1 text-xs">
                          <div className="flex justify-between">
                            <span>Duration:</span>
                            <span>{test.configuration.duration} days</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Traffic Split:</span>
                            <span>{test.configuration.trafficSplit.join("/")}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Min Sample:</span>
                            <span>{test.configuration.minSampleSize.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Current Metrics</h4>
                        <div className="space-y-1 text-xs">
                          {test.variants.map((variant) => (
                            <div key={variant.id} className="flex justify-between">
                              <span>{variant.name}:</span>
                              <span>{variant.metrics.impressions.toLocaleString()} samples</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Performance</h4>
                        <div className="space-y-1 text-xs">
                          {test.variants.map((variant) => (
                            <div key={variant.id} className="flex justify-between">
                              <span>{variant.name}:</span>
                              <span className={variant.isControl ? "text-muted-foreground" : "text-green-600"}>
                                {variant.metrics.conversionRate.toFixed(1)}%
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
            {runningTests.length === 0 && (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Play className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Running Tests</h3>
                  <p className="text-muted-foreground text-center">
                    Start a test from your drafts or create a new one to begin optimization.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            {completedTests.map((test) => (
              <Card key={test.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(test.status)}
                        <h3 className="font-semibold">{test.name}</h3>
                      </div>
                      <Badge variant="outline" className={getPriorityColor(test.priority)}>
                        {test.priority}
                      </Badge>
                      <Badge variant="outline" className={getStatusColor(test.status)}>
                        {test.status}
                      </Badge>
                      {test.results?.winner && test.results.winner !== "control" && (
                        <Badge className="bg-green-100 text-green-800 border-green-200">Winner Found</Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline" onClick={() => duplicateTest(test.id)}>
                        <Copy className="h-4 w-4 mr-1" />
                        Duplicate
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => setSelectedTest(test)}>
                        <Eye className="h-4 w-4 mr-1" />
                        View Results
                      </Button>
                    </div>
                  </div>
                  <CardDescription>{test.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  {test.results && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">{test.results.confidence.toFixed(1)}%</div>
                          <p className="text-xs text-muted-foreground">Confidence</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            {test.results.lift > 0 ? "+" : ""}
                            {test.results.lift.toFixed(1)}%
                          </div>
                          <p className="text-xs text-muted-foreground">Lift</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">
                            {test.results.detailedAnalysis.totalParticipants.toLocaleString()}
                          </div>
                          <p className="text-xs text-muted-foreground">Participants</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-600">
                            {test.results.detailedAnalysis.testDuration}d
                          </div>
                          <p className="text-xs text-muted-foreground">Duration</p>
                        </div>
                      </div>

                      <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <h4 className="font-medium text-sm mb-2">Summary</h4>
                        <p className="text-sm text-blue-800">{test.results.summary}</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
            {completedTests.length === 0 && (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <CheckCircle2 className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Completed Tests</h3>
                  <p className="text-muted-foreground text-center">
                    Complete some tests to see results and insights here.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          {selectedTest ? (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {getStatusIcon(selectedTest.status)}
                      {selectedTest.name}
                    </CardTitle>
                    <CardDescription>{selectedTest.description}</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className={getStatusColor(selectedTest.status)}>
                      {selectedTest.status}
                    </Badge>
                    <Badge variant="outline" className={getPriorityColor(selectedTest.priority)}>
                      {selectedTest.priority}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <h4 className="font-medium">Test Hypothesis</h4>
                      <p className="text-sm text-muted-foreground">{selectedTest.hypothesis}</p>
                    </div>
                    <div className="space-y-3">
                      <h4 className="font-medium">Configuration</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="flex justify-between">
                          <span>Duration:</span>
                          <span>{selectedTest.configuration.duration} days</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Confidence:</span>
                          <span>{selectedTest.configuration.confidenceLevel}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Min Sample:</span>
                          <span>{selectedTest.configuration.minSampleSize.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Traffic Split:</span>
                          <span>{selectedTest.configuration.trafficSplit.join("/")}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">Variant Performance</h4>
                    <div className="grid grid-cols-1 gap-4">
                      {selectedTest.variants.map((variant) => (
                        <Card key={variant.id} className={variant.isControl ? "border-gray-300" : "border-blue-300"}>
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <h5 className="font-medium">{variant.name}</h5>
                                {variant.isControl && (
                                  <Badge variant="outline" className="bg-gray-100 text-gray-800">
                                    Control
                                  </Badge>
                                )}
                                {selectedTest.results?.winner === variant.id && (
                                  <Badge className="bg-green-100 text-green-800 border-green-200">Winner</Badge>
                                )}
                              </div>
                              <div className="text-sm text-muted-foreground">{variant.trafficPercentage}% traffic</div>
                            </div>
                            <p className="text-sm text-muted-foreground">{variant.description}</p>
                          </CardHeader>
                          <CardContent>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <p className="text-muted-foreground">Impressions</p>
                                <p className="font-medium">{variant.metrics.impressions.toLocaleString()}</p>
                              </div>
                              <div>
                                <p className="text-muted-foreground">Conversions</p>
                                <p className="font-medium">{variant.metrics.conversions.toLocaleString()}</p>
                              </div>
                              <div>
                                <p className="text-muted-foreground">Conversion Rate</p>
                                <p className="font-medium">{variant.metrics.conversionRate.toFixed(2)}%</p>
                              </div>
                              <div>
                                <p className="text-muted-foreground">Avg Value</p>
                                <p className="font-medium">{variant.metrics.averageValue.toFixed(1)}s</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>

                  {selectedTest.results && (
                    <>
                      <Separator />
                      <div className="space-y-4">
                        <h4 className="font-medium">Statistical Analysis</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-3">
                            <h5 className="font-medium text-sm">Key Metrics</h5>
                            <div className="grid grid-cols-2 gap-4">
                              <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                                <div className="text-2xl font-bold text-green-600">
                                  {selectedTest.results.confidence.toFixed(1)}%
                                </div>
                                <p className="text-xs text-green-700">Confidence Level</p>
                              </div>
                              <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                                <div className="text-2xl font-bold text-blue-600">
                                  {selectedTest.results.lift > 0 ? "+" : ""}
                                  {selectedTest.results.lift.toFixed(1)}%
                                </div>
                                <p className="text-xs text-blue-700">Performance Lift</p>
                              </div>
                            </div>
                          </div>
                          <div className="space-y-3">
                            <h5 className="font-medium text-sm">Test Validity</h5>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <span className="text-sm">Statistical Significance:</span>
                                <div className="flex items-center gap-1">
                                  {selectedTest.results.detailedAnalysis.statisticalSignificance ? (
                                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                                  ) : (
                                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                                  )}
                                  <span className="text-sm">
                                    {selectedTest.results.detailedAnalysis.statisticalSignificance ? "Yes" : "No"}
                                  </span>
                                </div>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-sm">Sample Size Adequate:</span>
                                <div className="flex items-center gap-1">
                                  {selectedTest.results.detailedAnalysis.sampleSizeAdequate ? (
                                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                                  ) : (
                                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                                  )}
                                  <span className="text-sm">
                                    {selectedTest.results.detailedAnalysis.sampleSizeAdequate ? "Yes" : "No"}
                                  </span>
                                </div>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-sm">Practical Significance:</span>
                                <div className="flex items-center gap-1">
                                  {selectedTest.results.detailedAnalysis.practicalSignificance ? (
                                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                                  ) : (
                                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                                  )}
                                  <span className="text-sm">
                                    {selectedTest.results.detailedAnalysis.practicalSignificance ? "Yes" : "No"}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <h5 className="font-medium text-sm">Summary & Recommendations</h5>
                        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                          <p className="text-sm text-blue-800 mb-3">{selectedTest.results.summary}</p>
                          <div className="space-y-2">
                            <h6 className="font-medium text-sm text-blue-900">Recommendations:</h6>
                            <ul className="space-y-1">
                              {selectedTest.results.recommendations.map((rec, index) => (
                                <li key={index} className="text-sm text-blue-800 flex items-start gap-2">
                                  <ArrowRight className="h-3 w-3 mt-0.5 text-blue-600" />
                                  {rec}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <BarChart3 className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Select a Test</h3>
                <p className="text-muted-foreground text-center">
                  Choose a test from the other tabs to view detailed results and analysis.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

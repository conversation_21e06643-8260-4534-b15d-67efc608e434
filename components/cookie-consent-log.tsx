"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Search, Filter, Download, Plus, Check, X, AlertTriangle, Globe, Cookie, Eye } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

// Sample cookie consent data
const cookieConsents = [
  {
    id: "consent-1",
    domain: "travel-avatar.com",
    consentType: "explicit",
    cookieTypes: ["necessary", "preferences", "analytics", "marketing"],
    lastUpdated: "2023-05-15T14:30:00Z",
    status: "compliant",
    platform: "web",
    bannerType: "modal",
    consentRate: 78,
  },
  {
    id: "consent-2",
    domain: "fitness-blog.com",
    consentType: "explicit",
    cookieTypes: ["necessary", "preferences", "analytics"],
    lastUpdated: "2023-04-22T09:15:00Z",
    status: "compliant",
    platform: "web",
    bannerType: "bottom-bar",
    consentRate: 82,
  },
  {
    id: "consent-3",
    domain: "health-tips.com",
    consentType: "implicit",
    cookieTypes: ["necessary", "analytics"],
    lastUpdated: "2023-03-10T11:45:00Z",
    status: "non-compliant",
    platform: "web",
    bannerType: "none",
    consentRate: 0,
  },
  {
    id: "consent-4",
    domain: "entrepreneur-podcast.com",
    consentType: "explicit",
    cookieTypes: ["necessary", "preferences", "analytics", "marketing"],
    lastUpdated: "2023-05-01T16:20:00Z",
    status: "warning",
    platform: "web",
    bannerType: "modal",
    consentRate: 65,
  },
  {
    id: "consent-5",
    domain: "home-decor-tips.com",
    consentType: "explicit",
    cookieTypes: ["necessary", "preferences", "analytics"],
    lastUpdated: "2023-04-15T10:30:00Z",
    status: "compliant",
    platform: "web",
    bannerType: "bottom-bar",
    consentRate: 75,
  },
]

// Sample cookie data
const cookieData = [
  {
    id: "cookie-1",
    domain: "travel-avatar.com",
    name: "_ga",
    type: "analytics",
    purpose: "Google Analytics tracking",
    duration: "2 years",
    thirdParty: true,
    necessary: false,
  },
  {
    id: "cookie-2",
    domain: "travel-avatar.com",
    name: "session_id",
    type: "necessary",
    purpose: "Session management",
    duration: "session",
    thirdParty: false,
    necessary: true,
  },
  {
    id: "cookie-3",
    domain: "fitness-blog.com",
    name: "_fbp",
    type: "marketing",
    purpose: "Facebook Pixel tracking",
    duration: "3 months",
    thirdParty: true,
    necessary: false,
  },
  {
    id: "cookie-4",
    domain: "health-tips.com",
    name: "preferences",
    type: "preferences",
    purpose: "Store user preferences",
    duration: "1 year",
    thirdParty: false,
    necessary: false,
  },
  {
    id: "cookie-5",
    domain: "entrepreneur-podcast.com",
    name: "_gid",
    type: "analytics",
    purpose: "Google Analytics tracking (24h)",
    duration: "24 hours",
    thirdParty: true,
    necessary: false,
  },
]

// Sample consent logs
const consentLogs = [
  {
    id: "log-1",
    domain: "travel-avatar.com",
    userIdentifier: "user123",
    consentGiven: true,
    cookiesAccepted: ["necessary", "preferences", "analytics"],
    timestamp: "2023-05-16T14:30:00Z",
    ipAddress: "***********",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  },
  {
    id: "log-2",
    domain: "fitness-blog.com",
    userIdentifier: "user456",
    consentGiven: true,
    cookiesAccepted: ["necessary", "preferences", "analytics", "marketing"],
    timestamp: "2023-05-16T10:15:00Z",
    ipAddress: "***********",
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
  },
  {
    id: "log-3",
    domain: "health-tips.com",
    userIdentifier: "user789",
    consentGiven: false,
    cookiesAccepted: ["necessary"],
    timestamp: "2023-05-15T09:45:00Z",
    ipAddress: "***********",
    userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15",
  },
  {
    id: "log-4",
    domain: "entrepreneur-podcast.com",
    userIdentifier: "user101",
    consentGiven: true,
    cookiesAccepted: ["necessary", "preferences"],
    timestamp: "2023-05-14T16:20:00Z",
    ipAddress: "***********",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  },
  {
    id: "log-5",
    domain: "home-decor-tips.com",
    userIdentifier: "user202",
    consentGiven: true,
    cookiesAccepted: ["necessary", "preferences", "analytics"],
    timestamp: "2023-05-13T11:30:00Z",
    ipAddress: "***********",
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
  },
]

export function CookieConsentLog() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [domainFilter, setDomainFilter] = useState("all")
  const [activeTab, setActiveTab] = useState("domains")
  const [selectedConsent, setSelectedConsent] = useState<string | null>(null)
  const [selectedCookie, setSelectedCookie] = useState<string | null>(null)

  // Filter consents based on search and filters
  const filteredConsents = cookieConsents.filter((consent) => {
    const matchesSearch = consent.domain.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || consent.status === statusFilter
    const matchesDomain = domainFilter === "all" || consent.domain === domainFilter

    return matchesSearch && matchesStatus && matchesDomain
  })

  // Filter cookies based on domain filter
  const filteredCookies = cookieData.filter((cookie) => {
    return domainFilter === "all" || cookie.domain === domainFilter
  })

  // Filter consent logs based on domain filter
  const filteredLogs = consentLogs.filter((log) => {
    return domainFilter === "all" || log.domain === domainFilter
  })

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "compliant":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Compliant</Badge>
      case "warning":
        return (
          <Badge variant="outline" className="bg-amber-100 text-amber-800 hover:bg-amber-200">
            Warning
          </Badge>
        )
      case "non-compliant":
        return <Badge variant="destructive">Non-Compliant</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  // Handle domain selection
  const handleDomainSelect = (domain: string) => {
    setDomainFilter(domain)
  }

  // Handle export
  const handleExport = () => {
    toast({
      title: "Export Started",
      description: "Your cookie consent data is being exported.",
    })
  }

  // View consent details
  const handleViewConsent = (id: string) => {
    setSelectedConsent(id)
  }

  // View cookie details
  const handleViewCookie = (id: string) => {
    setSelectedCookie(id)
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Cookie & Consent Log</CardTitle>
              <CardDescription>Track and manage cookie consent across all your domains and platforms</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={handleExport}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Domain
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search domains..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex flex-1 items-center space-x-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="compliant">Compliant</SelectItem>
                    <SelectItem value="warning">Warning</SelectItem>
                    <SelectItem value="non-compliant">Non-Compliant</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={domainFilter} onValueChange={setDomainFilter}>
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Filter by domain" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Domains</SelectItem>
                    {cookieConsents.map((consent) => (
                      <SelectItem key={consent.id} value={consent.domain}>
                        {consent.domain}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList>
                <TabsTrigger value="domains">Domains</TabsTrigger>
                <TabsTrigger value="cookies">Cookies</TabsTrigger>
                <TabsTrigger value="logs">Consent Logs</TabsTrigger>
              </TabsList>

              <TabsContent value="domains">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Domain</TableHead>
                        <TableHead>Consent Type</TableHead>
                        <TableHead>Banner Type</TableHead>
                        <TableHead>Cookie Types</TableHead>
                        <TableHead>Last Updated</TableHead>
                        <TableHead>Consent Rate</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredConsents.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="h-24 text-center">
                            No domains found matching your filters.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredConsents.map((consent) => (
                          <TableRow key={consent.id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center space-x-2">
                                <Globe className="h-4 w-4 text-muted-foreground" />
                                <span>{consent.domain}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="capitalize">
                                {consent.consentType}
                              </Badge>
                            </TableCell>
                            <TableCell>{consent.bannerType === "none" ? "None" : consent.bannerType}</TableCell>
                            <TableCell>
                              <div className="flex flex-wrap gap-1">
                                {consent.cookieTypes.map((type) => (
                                  <Badge key={type} variant="outline" className="text-xs capitalize">
                                    {type}
                                  </Badge>
                                ))}
                              </div>
                            </TableCell>
                            <TableCell>{formatDate(consent.lastUpdated)}</TableCell>
                            <TableCell>
                              <span
                                className={`font-medium ${
                                  consent.consentRate > 75
                                    ? "text-green-600"
                                    : consent.consentRate > 50
                                      ? "text-amber-600"
                                      : "text-red-600"
                                }`}
                              >
                                {consent.consentRate}%
                              </span>
                            </TableCell>
                            <TableCell>{getStatusBadge(consent.status)}</TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewConsent(consent.id)}
                                className="h-8 w-8 p-0"
                              >
                                <span className="sr-only">View details</span>
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>

              <TabsContent value="cookies">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Domain</TableHead>
                        <TableHead>Cookie Name</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Purpose</TableHead>
                        <TableHead>Duration</TableHead>
                        <TableHead>Third Party</TableHead>
                        <TableHead>Necessary</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredCookies.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="h-24 text-center">
                            No cookies found matching your filters.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredCookies.map((cookie) => (
                          <TableRow key={cookie.id}>
                            <TableCell>{cookie.domain}</TableCell>
                            <TableCell className="font-medium">
                              <div className="flex items-center space-x-2">
                                <Cookie className="h-4 w-4 text-muted-foreground" />
                                <span>{cookie.name}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant="outline"
                                className={`capitalize ${
                                  cookie.type === "necessary"
                                    ? "bg-blue-50 text-blue-800"
                                    : cookie.type === "preferences"
                                      ? "bg-green-50 text-green-800"
                                      : cookie.type === "analytics"
                                        ? "bg-amber-50 text-amber-800"
                                        : "bg-red-50 text-red-800"
                                }`}
                              >
                                {cookie.type}
                              </Badge>
                            </TableCell>
                            <TableCell>{cookie.purpose}</TableCell>
                            <TableCell>{cookie.duration}</TableCell>
                            <TableCell>
                              {cookie.thirdParty ? (
                                <Check className="h-4 w-4 text-green-600" />
                              ) : (
                                <X className="h-4 w-4 text-red-600" />
                              )}
                            </TableCell>
                            <TableCell>
                              {cookie.necessary ? (
                                <Check className="h-4 w-4 text-green-600" />
                              ) : (
                                <X className="h-4 w-4 text-red-600" />
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewCookie(cookie.id)}
                                className="h-8 w-8 p-0"
                              >
                                <span className="sr-only">View details</span>
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>

              <TabsContent value="logs">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Domain</TableHead>
                        <TableHead>User ID</TableHead>
                        <TableHead>Consent Given</TableHead>
                        <TableHead>Cookies Accepted</TableHead>
                        <TableHead>Timestamp</TableHead>
                        <TableHead>IP Address</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredLogs.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="h-24 text-center">
                            No consent logs found matching your filters.
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredLogs.map((log) => (
                          <TableRow key={log.id}>
                            <TableCell>{log.domain}</TableCell>
                            <TableCell className="font-medium">{log.userIdentifier}</TableCell>
                            <TableCell>
                              {log.consentGiven ? (
                                <Badge className="bg-green-100 text-green-800">Accepted</Badge>
                              ) : (
                                <Badge variant="outline" className="bg-red-100 text-red-800">
                                  Declined
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-wrap gap-1">
                                {log.cookiesAccepted.map((type) => (
                                  <Badge key={type} variant="outline" className="text-xs capitalize">
                                    {type}
                                  </Badge>
                                ))}
                              </div>
                            </TableCell>
                            <TableCell>{formatDate(log.timestamp)}</TableCell>
                            <TableCell>{log.ipAddress}</TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* Consent Details Dialog */}
      <Dialog open={selectedConsent !== null} onOpenChange={() => setSelectedConsent(null)}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Consent Configuration Details</DialogTitle>
            <DialogDescription>
              Detailed information about the cookie consent configuration for this domain
            </DialogDescription>
          </DialogHeader>
          {selectedConsent && (
            <div className="space-y-4 py-4">
              {(() => {
                const consent = cookieConsents.find((c) => c.id === selectedConsent)
                if (!consent) return null

                return (
                  <>
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">{consent.domain}</h3>
                      {getStatusBadge(consent.status)}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-muted-foreground">Consent Type</Label>
                        <p className="font-medium capitalize">{consent.consentType}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Banner Type</Label>
                        <p className="font-medium capitalize">{consent.bannerType}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Platform</Label>
                        <p className="font-medium capitalize">{consent.platform}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Consent Rate</Label>
                        <p className="font-medium">{consent.consentRate}%</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Last Updated</Label>
                        <p className="font-medium">{formatDate(consent.lastUpdated)}</p>
                      </div>
                    </div>

                    <div>
                      <Label className="text-muted-foreground">Cookie Types</Label>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {consent.cookieTypes.map((type) => (
                          <Badge key={type} className="capitalize">
                            {type}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {consent.status === "non-compliant" && (
                      <div className="rounded-md bg-red-50 p-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <AlertTriangle className="h-5 w-5 text-red-400" aria-hidden="true" />
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-red-800">Compliance Issues</h3>
                            <div className="mt-2 text-sm text-red-700">
                              <ul className="list-disc space-y-1 pl-5">
                                <li>No consent banner implemented</li>
                                <li>Implicit consent is not GDPR compliant</li>
                                <li>Missing proper documentation of consent</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {consent.status === "warning" && (
                      <div className="rounded-md bg-amber-50 p-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <AlertTriangle className="h-5 w-5 text-amber-400" aria-hidden="true" />
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-amber-800">Compliance Warnings</h3>
                            <div className="mt-2 text-sm text-amber-700">
                              <ul className="list-disc space-y-1 pl-5">
                                <li>Consent rate below recommended threshold (75%)</li>
                                <li>Some third-party cookies may be set before consent</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )
              })()}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setSelectedConsent(null)}>
              Close
            </Button>
            <Button>Edit Configuration</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Cookie Details Dialog */}
      <Dialog open={selectedCookie !== null} onOpenChange={() => setSelectedCookie(null)}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Cookie Details</DialogTitle>
            <DialogDescription>Detailed information about this cookie</DialogDescription>
          </DialogHeader>
          {selectedCookie && (
            <div className="space-y-4 py-4">
              {(() => {
                const cookie = cookieData.find((c) => c.id === selectedCookie)
                if (!cookie) return null

                return (
                  <>
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">{cookie.name}</h3>
                      <Badge
                        variant="outline"
                        className={`capitalize ${
                          cookie.type === "necessary"
                            ? "bg-blue-50 text-blue-800"
                            : cookie.type === "preferences"
                              ? "bg-green-50 text-green-800"
                              : cookie.type === "analytics"
                                ? "bg-amber-50 text-amber-800"
                                : "bg-red-50 text-red-800"
                        }`}
                      >
                        {cookie.type}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-muted-foreground">Domain</Label>
                        <p className="font-medium">{cookie.domain}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Duration</Label>
                        <p className="font-medium">{cookie.duration}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Third Party</Label>
                        <p className="font-medium">{cookie.thirdParty ? "Yes" : "No"}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Necessary</Label>
                        <p className="font-medium">{cookie.necessary ? "Yes" : "No"}</p>
                      </div>
                    </div>

                    <div>
                      <Label className="text-muted-foreground">Purpose</Label>
                      <p className="font-medium">{cookie.purpose}</p>
                    </div>

                    {cookie.thirdParty && !cookie.necessary && (
                      <div className="rounded-md bg-amber-50 p-4">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <AlertTriangle className="h-5 w-5 text-amber-400" aria-hidden="true" />
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-amber-800">Consent Required</h3>
                            <div className="mt-2 text-sm text-amber-700">
                              This is a third-party cookie that is not necessary for basic functionality. Explicit user
                              consent is required before setting this cookie.
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )
              })()}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setSelectedCookie(null)}>
              Close
            </Button>
            <Button>Edit Cookie</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Plus, Search, ArrowUpDown } from "lucide-react"

type Product = {
  id: string
  name: string
  category: string
  sales: number
  margin: number
  competition: "Low" | "Medium" | "High"
  score: number
}

export function WhiteLabelScoreboard() {
  const [products, setProducts] = useState<Product[]>([
    {
      id: "1",
      name: "Premium Yoga Mat",
      category: "Fitness",
      sales: 1250,
      margin: 65,
      competition: "Medium",
      score: 82,
    },
    {
      id: "2",
      name: "Bamboo Cutting Board",
      category: "Kitchen",
      sales: 980,
      margin: 72,
      competition: "High",
      score: 76,
    },
    {
      id: "3",
      name: "Organic Coffee Beans",
      category: "Food",
      sales: 1560,
      margin: 58,
      competition: "High",
      score: 71,
    },
    {
      id: "4",
      name: "Wireless Earbuds",
      category: "Electronics",
      sales: 2100,
      margin: 45,
      competition: "High",
      score: 68,
    },
    {
      id: "5",
      name: "Silicone Baking Mats",
      category: "Kitchen",
      sales: 850,
      margin: 78,
      competition: "Low",
      score: 88,
    },
  ])

  const [sortBy, setSortBy] = useState<keyof Product>("score")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
  const [searchTerm, setSearchTerm] = useState("")
  const [newProduct, setNewProduct] = useState<Omit<Product, "id" | "score">>({
    name: "",
    category: "",
    sales: 0,
    margin: 0,
    competition: "Medium",
  })
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const handleSort = (column: keyof Product) => {
    if (sortBy === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortBy(column)
      setSortDirection("desc")
    }
  }

  const sortedProducts = [...products]
    .filter(
      (product) =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.category.toLowerCase().includes(searchTerm.toLowerCase()),
    )
    .sort((a, b) => {
      const aValue = a[sortBy]
      const bValue = b[sortBy]

      if (typeof aValue === "number" && typeof bValue === "number") {
        return sortDirection === "asc" ? aValue - bValue : bValue - aValue
      }

      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortDirection === "asc" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue)
      }

      return 0
    })

  const calculateScore = (product: Omit<Product, "id" | "score">): number => {
    // Simple scoring algorithm based on sales, margin, and competition
    const salesScore = Math.min(product.sales / 20, 40) // Max 40 points
    const marginScore = product.margin * 0.5 // Max 50 points
    const competitionScore = product.competition === "Low" ? 10 : product.competition === "Medium" ? 5 : 0 // Max 10 points

    return Math.round(salesScore + marginScore + competitionScore)
  }

  const handleAddProduct = () => {
    const score = calculateScore(newProduct)
    const id = (products.length + 1).toString()

    setProducts([...products, { ...newProduct, id, score }])
    setNewProduct({
      name: "",
      category: "",
      sales: 0,
      margin: 0,
      competition: "Medium",
    })
    setIsDialogOpen(false)
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return "bg-green-100 text-green-800"
    if (score >= 70) return "bg-yellow-100 text-yellow-800"
    return "bg-red-100 text-red-800"
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>White Label Scoreboard</CardTitle>
            <CardDescription>Track potential products by sales, margin, and competition</CardDescription>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Product
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Product Opportunity</DialogTitle>
                <DialogDescription>Enter details about the potential white label product</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Product Name</Label>
                  <Input
                    id="name"
                    value={newProduct.name}
                    onChange={(e) => setNewProduct({ ...newProduct, name: e.target.value })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    value={newProduct.category}
                    onChange={(e) => setNewProduct({ ...newProduct, category: e.target.value })}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="sales">Monthly Sales</Label>
                    <Input
                      id="sales"
                      type="number"
                      value={newProduct.sales}
                      onChange={(e) => setNewProduct({ ...newProduct, sales: Number(e.target.value) })}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="margin">Margin %</Label>
                    <Input
                      id="margin"
                      type="number"
                      value={newProduct.margin}
                      onChange={(e) => setNewProduct({ ...newProduct, margin: Number(e.target.value) })}
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="competition">Competition Level</Label>
                  <select
                    id="competition"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={newProduct.competition}
                    onChange={(e) =>
                      setNewProduct({
                        ...newProduct,
                        competition: e.target.value as "Low" | "Medium" | "High",
                      })
                    }
                  >
                    <option value="Low">Low</option>
                    <option value="Medium">Medium</option>
                    <option value="High">High</option>
                  </select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddProduct}>Add Product</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex items-center gap-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search products..."
            className="h-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="cursor-pointer" onClick={() => handleSort("name")}>
                  <div className="flex items-center">
                    Product
                    {sortBy === "name" && <ArrowUpDown className="ml-2 h-4 w-4" />}
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => handleSort("category")}>
                  <div className="flex items-center">
                    Category
                    {sortBy === "category" && <ArrowUpDown className="ml-2 h-4 w-4" />}
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer text-right" onClick={() => handleSort("sales")}>
                  <div className="flex items-center justify-end">
                    Sales
                    {sortBy === "sales" && <ArrowUpDown className="ml-2 h-4 w-4" />}
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer text-right" onClick={() => handleSort("margin")}>
                  <div className="flex items-center justify-end">
                    Margin
                    {sortBy === "margin" && <ArrowUpDown className="ml-2 h-4 w-4" />}
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => handleSort("competition")}>
                  <div className="flex items-center">
                    Competition
                    {sortBy === "competition" && <ArrowUpDown className="ml-2 h-4 w-4" />}
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer text-right" onClick={() => handleSort("score")}>
                  <div className="flex items-center justify-end">
                    Score
                    {sortBy === "score" && <ArrowUpDown className="ml-2 h-4 w-4" />}
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedProducts.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    No products found.
                  </TableCell>
                </TableRow>
              ) : (
                sortedProducts.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">{product.name}</TableCell>
                    <TableCell>{product.category}</TableCell>
                    <TableCell className="text-right">{product.sales}</TableCell>
                    <TableCell className="text-right">{product.margin}%</TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={
                          product.competition === "Low"
                            ? "bg-green-100 text-green-800"
                            : product.competition === "Medium"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                        }
                      >
                        {product.competition}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Badge className={getScoreColor(product.score)}>{product.score}</Badge>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}

'use client'

import React, { useState, useEffect } from "react"
import { AppLayout } from "@/components/layouts/app-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { useToast } from "@/hooks/use-toast"
import {
  Search,
  Filter,
  Eye,
  Trash2,
  Download,
  Image as ImageIcon,
  Video,
  File,
  ChevronLeft,
  ChevronRight,
  RefreshCw,
  Calendar,
  User,
  HardDrive,
  Copy,
  Activity,
  RotateCcw,
  Wand2,
  Camera,
  FileText,
  Palette,
  Info,
  AlertCircle
} from "lucide-react"
import { mediaService, type Media, type MediaFilters } from "@/lib/api/media"
import { getImageUrl } from "@/lib/url-utils"

interface PaginationInfo {
  page: number
  limit: number
  totalPages: number
  totalItems: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

export default function MediaManagementPage() {
  const [media, setMedia] = useState<Media[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedMedia, setSelectedMedia] = useState<Media | null>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [mediaToDelete, setMediaToDelete] = useState<Media | null>(null)
  const [checkingStatus, setCheckingStatus] = useState<string | null>(null)
  const [syncingResult, setSyncingResult] = useState<string | null>(null)
  const [isProgressModalOpen, setIsProgressModalOpen] = useState(false)
  const [isFileInfoModalOpen, setIsFileInfoModalOpen] = useState(false)
  const [selectedFileInfo, setSelectedFileInfo] = useState<Media | null>(null)
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 10,
    totalPages: 1,
    totalItems: 0,
    hasNextPage: false,
    hasPrevPage: false
  })
  
  // Filters
  const [filters, setFilters] = useState<MediaFilters>({
    page: 1,
    limit: 10,
    media_type: undefined,
    status: undefined,
    search: '',
    product_id: undefined
  })
  
  const { toast } = useToast()

  useEffect(() => {
    loadMedia()
  }, [filters])

  const loadMedia = async () => {
    try {
      setLoading(true)
      const response = await mediaService.getAllMedia(filters)



      if (response.data && Array.isArray(response.data)) {
        setMedia(response.data)
        if (response.pagination) {
          // Map API pagination structure to frontend structure
          setPagination({
            page: response.pagination.currentPage || 1,
            limit: response.pagination.itemsPerPage || 10,
            totalPages: response.pagination.totalPages || 1,
            totalItems: response.pagination.totalItems || 0,
            hasNextPage: response.pagination.hasNextPage || false,
            hasPrevPage: response.pagination.hasPreviousPage || false
          })
        } else {
          // Fallback pagination if API doesn't provide it
          const totalItems = response.data.length
          const totalPages = Math.ceil(totalItems / (filters.limit || 10))
          setPagination({
            page: filters.page || 1,
            limit: filters.limit || 10,
            totalPages: totalPages,
            totalItems: totalItems,
            hasNextPage: (filters.page || 1) < totalPages,
            hasPrevPage: (filters.page || 1) > 1
          })
        }
      } else {
        console.error('Invalid media response:', response)
        setMedia([])
        // Reset pagination for empty results
        setPagination({
          page: 1,
          limit: filters.limit || 10,
          totalPages: 0,
          totalItems: 0,
          hasNextPage: false,
          hasPrevPage: false
        })
      }
    } catch (error) {
      console.error('Failed to load media:', error)
      toast({
        title: "Error",
        description: "Failed to load media files",
        variant: "destructive",
      })
      setMedia([])
      // Reset pagination on error
      setPagination({
        page: 1,
        limit: filters.limit || 10,
        totalPages: 0,
        totalItems: 0,
        hasNextPage: false,
        hasPrevPage: false
      })
    } finally {
      setLoading(false)
    }
  }

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      setFilters(prev => ({ ...prev, page: newPage }))
    }
  }

  const handleLimitChange = (newLimit: string) => {
    setFilters(prev => ({ ...prev, page: 1, limit: parseInt(newLimit) }))
  }

  const handleFilterChange = (key: keyof MediaFilters, value: string | undefined) => {
    setFilters(prev => ({ ...prev, page: 1, [key]: value }))
  }

  const handleSearch = (searchTerm: string) => {
    setFilters(prev => ({ ...prev, page: 1, search: searchTerm }))
  }

  const handleViewMedia = (mediaItem: Media) => {
    setSelectedMedia(mediaItem)
    setIsDetailDialogOpen(true)
  }

  const handleViewFileInfo = (mediaItem: Media) => {
    setSelectedFileInfo(mediaItem)
    setIsFileInfoModalOpen(true)
  }

  const handleDeleteMedia = async (mediaItem: Media) => {
    setMediaToDelete(mediaItem)
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!mediaToDelete) return

    try {
      await mediaService.deleteMedia(mediaToDelete.id)
      toast({
        title: "Success",
        description: "Media file deleted successfully",
      })
      loadMedia() // Reload the list
    } catch (error) {
      console.error('Failed to delete media:', error)
      toast({
        title: "Error",
        description: "Failed to delete media file",
        variant: "destructive",
      })
    } finally {
      setIsDeleteDialogOpen(false)
      setMediaToDelete(null)
    }
  }

  const getMediaTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'image':
        return <ImageIcon className="h-4 w-4 text-blue-500" />
      case 'video':
        return <Video className="h-4 w-4 text-purple-500" />
      default:
        return <File className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { variant: "default" as const, color: "bg-green-100 text-green-800" },
      processing: { variant: "secondary" as const, color: "bg-yellow-100 text-yellow-800" },
      failed: { variant: "destructive" as const, color: "bg-red-100 text-red-800" },
      pending: { variant: "outline" as const, color: "bg-gray-100 text-gray-800" }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    
    return (
      <Badge variant={config.variant} className={config.color}>
        {status}
      </Badge>
    )
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getProgressInfo = (status: string) => {
    const steps = [
      {
        key: 'pending',
        label: 'Upload',
        description: 'File uploaded to system',
        icon: '📁',
        shortDesc: 'Upload'
      },
      {
        key: 'processing',
        label: 'AI Processing',
        description: 'AI analyzing and enhancing content',
        icon: '🤖',
        shortDesc: 'AI Process'
      },
      {
        key: 'completed',
        label: 'Completed',
        description: 'File processed successfully',
        icon: '✅',
        shortDesc: 'Complete'
      },
      {
        key: 'failed',
        label: 'Failed',
        description: 'Processing failed',
        icon: '❌',
        shortDesc: 'Failed'
      }
    ]

    // Map status to progress percentage and step
    const statusMapping: Record<string, { progress: number, stepIndex: number }> = {
      'pending': { progress: 33, stepIndex: 0 },      // Step 1: Upload
      'processing': { progress: 66, stepIndex: 1 },   // Step 2: AI Processing
      'completed': { progress: 100, stepIndex: 2 },   // Step 3: Completed
      'failed': { progress: 100, stepIndex: 3 }       // Step 4: Failed
    }

    const mapping = statusMapping[status] || { progress: 0, stepIndex: 0 }

    return {
      steps,
      currentStepIndex: mapping.stepIndex,
      progress: mapping.progress,
      currentStep: steps[mapping.stepIndex] || steps[0]
    }
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getMediaUrl = (url: string | null): string | null => {
    if (!url || url.trim() === '') return null

    // If URL already has a domain, return as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url
    }

    // Get media domain from environment variable
    const MEDIA_DOMAIN = process.env.NEXT_PUBLIC_MEDIA_DOMAIN || process.env.MEDIA_DOMAIN || 'https://avatarai.luminousdemo.com'
    return `${MEDIA_DOMAIN}${url.startsWith('/') ? '' : '/'}${url}`
  }

  const copyJobId = async (jobId: string) => {
    try {
      await navigator.clipboard.writeText(jobId)
      toast({
        title: "Copied!",
        description: "Job ID copied to clipboard.",
      })
    } catch (error) {
      console.error('Failed to copy job ID:', error)
      toast({
        title: "Copy Failed",
        description: "Could not copy job ID to clipboard.",
        variant: "destructive",
      })
    }
  }

  const checkMediaStatus = async (mediaItem: Media) => {
    if (!mediaItem.external_job_id) {
      toast({
        title: "Error",
        description: "No job ID available for status check.",
        variant: "destructive",
      })
      return
    }

    setCheckingStatus(mediaItem.id)

    try {
      // Use the media service to check status
      const statusResponse = await mediaService.checkMediaStatus(mediaItem.external_job_id)

      console.log('Status check response:', statusResponse)

      // Extract status from the response with multiple fallbacks
      let status = 'Unknown'

      if (statusResponse) {
        // Try different possible response structures
        if (statusResponse.status) {
          status = statusResponse.status
        } else if (statusResponse.data?.status) {
          status = statusResponse.data.status
        } else if (statusResponse.data?.data?.status) {
          status = statusResponse.data.data.status
        } else if (statusResponse.metadata?.api_response?.status) {
          status = statusResponse.metadata.api_response.status
        } else if (typeof statusResponse === 'string') {
          status = statusResponse
        }
      }

      // Map status to user-friendly messages
      const statusMessages = {
        'pending': 'Pending - Content generation in queue',
        'processing': 'Processing - AI is generating your content',
        'completed': 'Completed - Content is ready',
        'failed': 'Failed - Content generation failed',
        'error': 'Error - Something went wrong',
        'success': 'Success - Content generated successfully',
        'queued': 'Queued - Waiting to start processing',
        'in_progress': 'In Progress - Currently generating',
        'finished': 'Finished - Generation complete'
      }

      const displayMessage = statusMessages[status.toLowerCase()] || `Status: ${status}`

      toast({
        title: "Status Updated",
        description: displayMessage,
        variant: status.toLowerCase() === 'failed' || status.toLowerCase() === 'error' ? 'destructive' : 'default'
      })

      // Reload the table to get the latest data
      await loadMedia()
    } catch (error) {
      console.error('Failed to check media status:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to check media status.'
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setCheckingStatus(null)
    }
  }

  const syncMediaResult = async (mediaItem: Media) => {
    if (!mediaItem.external_job_id) {
      toast({
        title: "Error",
        description: "No job ID available for sync result.",
        variant: "destructive",
      })
      return
    }

    setSyncingResult(mediaItem.id)

    try {
      // Use the media service to sync result
      const resultData = await mediaService.syncMediaResult(mediaItem.external_job_id)

      toast({
        title: "Sync Successful",
        description: "Sync data successfully",
      })

      // Reload the table to get the latest data
      await loadMedia()
    } catch (error) {
      console.error('Failed to sync media result:', error)
      toast({
        title: "Error",
        description: "Failed to sync media result.",
        variant: "destructive",
      })
    } finally {
      setSyncingResult(null)
    }
  }

  return (
    <AppLayout>
      <div className="flex-1 space-y-4 p-4 sm:p-6 lg:p-8 pt-4 sm:pt-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold ">Media Management</h1>
            <p className="text-muted-foreground text-sm sm:text-base">
              Manage and organize all media files across your products
            </p>
          </div>
          <Button onClick={loadMedia} variant="outline" size="sm" className="shrink-0">
            <RefreshCw className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Refresh</span>
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Filters</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 sm:p-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2 sm:col-span-2 lg:col-span-1">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search media files..."
                    value={filters.search || ''}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Media Type</label>
                <Select value={filters.media_type || "all"} onValueChange={(value) => handleFilterChange('media_type', value === "all" ? undefined : value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All types</SelectItem>
                    <SelectItem value="image">Images</SelectItem>
                    <SelectItem value="video">Videos</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select value={filters.status || "all"} onValueChange={(value) => handleFilterChange('status', value === "all" ? undefined : value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All statuses</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Per Page</label>
                <Select value={filters.limit?.toString() || '10'} onValueChange={handleLimitChange}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Media Table */}
        <Card>
          <CardHeader className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
              <CardTitle className="text-lg">Media Files</CardTitle>
              <div className="text-sm text-muted-foreground">
                {pagination.totalItems} total files
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0 sm:p-6 sm:pt-0">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                Loading media files...
              </div>
            ) : media.length === 0 ? (
              <div className="text-center py-8">
                <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No media files found</h3>
                <p className="text-muted-foreground">Try adjusting your filters or search terms.</p>
              </div>
            ) : (
              <>
                {/* Mobile Card View */}
                <div className="block sm:hidden space-y-4">
                  {media.map((item) => (
                    <div key={item.id} className="bg-white border rounded-lg p-4 space-y-3">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                          {getMediaTypeIcon(item.media_type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex md:items-center items-start md:flex-row flex-col space-x-2 mb-1">
                            <div className="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                              {item.media_type === 'image' ? (
                                (() => {
                                  const imageUrl = getMediaUrl(item.thumbnail_url || item.media_url)
                                  return imageUrl ? (
                                    <img
                                      src={imageUrl}
                                      alt="Preview"
                                      className="w-full h-full object-cover"
                                      onError={(e) => {
                                        const target = e.target as HTMLImageElement
                                        target.src = '/placeholder-image.png'
                                      }}
                                    />
                                  ) : (
                                    <div className="w-full h-full flex items-center justify-center">
                                      <ImageIcon className="h-4 w-4 text-muted-foreground" />
                                    </div>
                                  )
                                })()
                              ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                  <Video className="h-4 w-4 text-muted-foreground" />
                                </div>
                              )}
                            </div>
                            <div className="flex-1 ">
                              <div className="cursor-pointer " onClick={() => handleViewFileInfo(item)}>
                                <div className="font-medium text-sm truncate md:max-w-[400px] max-w-[270px]  hover:text-blue-600 transition-colors">
                                  {item.media_url ? (item.media_url.split('/').pop() || 'Unknown file') : 'No file URL'}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {item.format ? item.format.toUpperCase() : 'Unknown format'}
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-1 text-xs text-muted-foreground mb-2">
                            <div className="flex items-center justify-between">
                              <span>Product: {item.product?.name || `#${item.product_id.slice(-8)}`}</span>
                              <span>{formatDate(item.created_at)}</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span>Job ID: {item.external_job_id ? (
                                <button
                                  onClick={() => copyJobId(item.external_job_id)}
                                  className="font-mono text-blue-600 hover:text-blue-800 hover:underline cursor-pointer"
                                  title="Click to copy full Job ID"
                                >
                                  ...{item.external_job_id.slice(-6)}
                                </button>
                              ) : (
                                <span className="text-muted-foreground">No Job ID</span>
                              )}</span>
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            {getStatusBadge(item.status)}
                            <div className="flex items-center space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewMedia(item)}
                                title="View Details"
                                className="h-8 w-8 p-0"
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => checkMediaStatus(item)}
                                disabled={checkingStatus === item.id}
                                title="Status Check"
                                className="h-8 w-8 p-0"
                              >
                                {checkingStatus === item.id ? (
                                  <RefreshCw className="h-3 w-3 animate-spin" />
                                ) : (
                                  <Activity className="h-3 w-3" />
                                )}
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => syncMediaResult(item)}
                                disabled={syncingResult === item.id}
                                title="Sync Result"
                                className="h-8 w-8 p-0"
                              >
                                {syncingResult === item.id ? (
                                  <RefreshCw className="h-3 w-3 animate-spin" />
                                ) : (
                                  <RotateCcw className="h-3 w-3" />
                                )}
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteMedia(item)}
                                className="text-red-600 hover:text-red-700 h-8 w-8 p-0"
                                title="Delete"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Desktop Table View */}
                <div className="hidden sm:block overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-16">Type</TableHead>
                        <TableHead className="w-20">Preview</TableHead>
                        <TableHead className="min-w-[120px]">
                          <Button
                            variant="ghost"
                            className="h-auto p-0 font-medium hover:bg-transparent text-sm"
                            onClick={() => setIsProgressModalOpen(true)}
                          >
                            File Info
                          </Button>
                        </TableHead>
                        <TableHead className="min-w-[100px]">Product</TableHead>
                        <TableHead className="hidden md:table-cell min-w-[80px]">Job ID</TableHead>
                        <TableHead className="min-w-[80px]">Status</TableHead>
                        <TableHead className="hidden lg:table-cell min-w-[100px]">Created</TableHead>
                        <TableHead className="w-40">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {media.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell className="p-4">
                            {getMediaTypeIcon(item.media_type)}
                          </TableCell>
                          <TableCell className="p-4">
                            <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden">
                              {item.media_type === 'image' ? (
                                (() => {
                                  const imageUrl = getMediaUrl(item.thumbnail_url || item.media_url)
                                  return imageUrl ? (
                                    <img
                                      src={imageUrl}
                                      alt="Preview"
                                      className="w-full h-full object-cover"
                                      onError={(e) => {
                                        const target = e.target as HTMLImageElement
                                        target.src = '/placeholder-image.png'
                                      }}
                                    />
                                  ) : (
                                    <div className="w-full h-full flex items-center justify-center">
                                      <ImageIcon className="h-6 w-6 text-muted-foreground" />
                                    </div>
                                  )
                                })()
                              ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                  <Video className="h-6 w-6 text-muted-foreground" />
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="p-4">
                            <div className="cursor-pointer" onClick={() => handleViewFileInfo(item)}>
                              <div className="font-medium text-sm truncate md:max-w-[400px] max-w-[200px] hover:text-blue-600 transition-colors">
                                {item.media_url ? (item.media_url.split('/').pop() || 'Unknown file') : 'No file URL'}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {item.format ? item.format.toUpperCase() : 'Unknown format'}
                                {item.width && item.height && (
                                  <span> • {item.width}×{item.height}</span>
                                )}
                                {item.duration && (
                                  <span> • {item.duration}s</span>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="p-4">
                            <div className="text-sm">
                              <div className="font-medium">{item.product?.name || `Product #${item.product_id.slice(-8)}`}</div>
                              {item.product?.name && (
                                <div className="text-xs text-muted-foreground">#{item.product_id.slice(-8)}</div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="hidden md:table-cell p-4">
                            <div className="text-sm">
                              {item.external_job_id ? (
                                <button
                                  onClick={() => copyJobId(item.external_job_id)}
                                  className="font-mono text-blue-600 hover:text-blue-800 hover:underline cursor-pointer"
                                  title="Click to copy full Job ID"
                                >
                                  ...{item.external_job_id.slice(-6)}
                                </button>
                              ) : (
                                <span className="text-muted-foreground">No Job ID</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="p-4">
                            {getStatusBadge(item.status)}
                          </TableCell>
                          <TableCell className="hidden lg:table-cell p-4 text-sm text-muted-foreground">
                            {formatDate(item.created_at)}
                          </TableCell>
                          <TableCell className="p-2 sm:p-4">
                            <div className="flex items-center space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewMedia(item)}
                                title="View Details"
                                className="h-8 w-8 p-0 sm:h-9 sm:w-9"
                              >
                                <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => checkMediaStatus(item)}
                                disabled={checkingStatus === item.id}
                                title="Status Check"
                                className="h-8 w-8 p-0 sm:h-9 sm:w-9"
                              >
                                {checkingStatus === item.id ? (
                                  <RefreshCw className="h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                                ) : (
                                  <Activity className="h-3 w-3 sm:h-4 sm:w-4" />
                                )}
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => syncMediaResult(item)}
                                disabled={syncingResult === item.id}
                                title="Sync Result"
                                className="h-8 w-8 p-0 sm:h-9 sm:w-9"
                              >
                                {syncingResult === item.id ? (
                                  <RefreshCw className="h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                                ) : (
                                  <RotateCcw className="h-3 w-3 sm:h-4 sm:w-4" />
                                )}
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteMedia(item)}
                                className="text-red-600 hover:text-red-700 h-8 w-8 p-0 sm:h-9 sm:w-9"
                                title="Delete"
                              >
                                <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                {pagination && pagination.totalItems > 0 && (
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mt-6 px-4 sm:px-0">
                    <div className="text-xs sm:text-sm text-muted-foreground text-center sm:text-left">
                      {(() => {
                        const currentPage = pagination.page || 1
                        const limit = pagination.limit || 10
                        const totalItems = pagination.totalItems || 0

                        if (totalItems === 0) {
                          return "No files found"
                        }

                        const startItem = ((currentPage - 1) * limit) + 1
                        const endItem = Math.min(currentPage * limit, totalItems)

                        return (
                          <span>
                            <span className="hidden sm:inline">Showing {startItem} to {endItem} of {totalItems} files (Page {currentPage})</span>
                            <span className="sm:hidden">{startItem}-{endItem} of {totalItems} (Page {currentPage})</span>
                          </span>
                        )
                      })()}
                    </div>

                    <div className="flex items-center justify-center space-x-1 sm:space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange((pagination.page || 1) - 1)}
                        disabled={!(pagination.hasPrevPage || (pagination.page || 1) > 1)}
                        className="text-xs sm:text-sm px-2 sm:px-3"
                      >
                        <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                        <span className="hidden sm:inline">Previous</span>
                        <span className="sm:hidden">Prev</span>
                      </Button>

                      <div className="flex items-center space-x-1">
                        {(() => {
                          const pages = []
                          const currentPage = pagination.page || 1
                          const totalPages = pagination.totalPages || 1

                          if (totalPages <= 1) {
                            return null
                          }

                          // Show fewer pages on mobile
                          const isMobile = window.innerWidth < 640
                          const maxVisiblePages = isMobile ? 3 : 5
                          const halfVisible = Math.floor(maxVisiblePages / 2)

                          let startPage = Math.max(1, currentPage - halfVisible)
                          let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)

                          // Adjust if we're near the end
                          if (endPage - startPage + 1 < maxVisiblePages) {
                            startPage = Math.max(1, endPage - maxVisiblePages + 1)
                          }

                          for (let i = startPage; i <= endPage; i++) {
                            pages.push(
                              <Button
                                key={`pagination-page-${i}`}
                                variant={i === currentPage ? "default" : "outline"}
                                size="sm"
                                onClick={() => handlePageChange(i)}
                                className="w-7 h-7 sm:w-8 sm:h-8 p-0 text-xs sm:text-sm"
                              >
                                {i}
                              </Button>
                            )
                          }
                          return pages
                        })()}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange((pagination.page || 1) + 1)}
                        disabled={!(pagination.hasNextPage || (pagination.page || 1) < (pagination.totalPages || 1))}
                        className="text-xs sm:text-sm px-2 sm:px-3"
                      >
                        <span className="hidden sm:inline">Next</span>
                        <span className="sm:hidden">Next</span>
                        <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 ml-1" />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Media Detail Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="w-[95vw] max-w-[900px] max-h-[95vh] rounded-2xl overflow-auto border-0 shadow-2xl bg-gradient-to-br from-background via-background to-muted/20 sm:mx-auto">
          <DialogHeader className="pb-4">
            <DialogTitle className="flex items-center space-x-2 text-lg sm:text-xl">
              <Eye className="h-4 w-4 sm:h-5 sm:w-5" />
              <span>Media Details</span>
            </DialogTitle>
            <DialogDescription className="text-sm sm:text-base">
              View detailed information about this media file
            </DialogDescription>
          </DialogHeader>
          
          {selectedMedia && (
            <div className="space-y-4 sm:space-y-6">
              {/* Media Preview */}
              <div className="flex justify-center">
                {selectedMedia.media_type === 'image' ? (
                  (() => {
                    const imageUrl = getMediaUrl(selectedMedia.media_url)
                    return imageUrl ? (
                      <img
                        src={imageUrl}
                        alt="Media preview"
                        className="max-w-full max-h-64 sm:max-h-96 object-contain rounded-lg"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement
                          target.src = '/placeholder-image.png'
                        }}
                      />
                    ) : (
                      <div className="max-w-full max-h-64 sm:max-h-96 rounded-lg bg-gray-100 flex items-center justify-center p-4 sm:p-8">
                        <div className="text-center text-gray-500">
                          <ImageIcon className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-2" />
                          <p className="text-sm sm:text-base">No image URL available</p>
                        </div>
                      </div>
                    )
                  })()
                ) : (
                  (() => {
                    const videoUrl = getMediaUrl(selectedMedia.media_url)
                    return videoUrl ? (
                      <video
                        src={videoUrl}
                        controls
                        className="max-w-full max-h-64 sm:max-h-96 rounded-lg"
                      />
                    ) : (
                      <div className="max-w-full max-h-64 sm:max-h-96 rounded-lg bg-gray-100 flex items-center justify-center p-4 sm:p-8">
                        <div className="text-center text-gray-500">
                          <Video className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-2" />
                          <p className="text-sm sm:text-base">No video URL available</p>
                        </div>
                      </div>
                    )
                  })()
                )}
              </div>

              {/* Media Information */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                <div className="space-y-3 sm:space-y-4">
                  <h3 className="text-base sm:text-lg font-semibold">File Information</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-xs sm:text-sm font-medium">Type:</span>
                      <span className="text-xs sm:text-sm">{selectedMedia.media_type}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs sm:text-sm font-medium">Format:</span>
                      <span className="text-xs sm:text-sm">{selectedMedia.format ? selectedMedia.format.toUpperCase() : 'Unknown'}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs sm:text-sm font-medium">Size:</span>
                      <span className="text-xs sm:text-sm">{formatFileSize(selectedMedia.file_size)}</span>
                    </div>
                    {selectedMedia.width && selectedMedia.height && (
                      <div className="flex justify-between items-center">
                        <span className="text-xs sm:text-sm font-medium">Dimensions:</span>
                        <span className="text-xs sm:text-sm">{selectedMedia.width} × {selectedMedia.height}</span>
                      </div>
                    )}
                    {selectedMedia.duration && (
                      <div className="flex justify-between items-center">
                        <span className="text-xs sm:text-sm font-medium">Duration:</span>
                        <span className="text-xs sm:text-sm">{selectedMedia.duration} seconds</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-3 sm:space-y-4">
                  <h3 className="text-base sm:text-lg font-semibold">Status & Metadata</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-xs sm:text-sm font-medium">Status:</span>
                      {getStatusBadge(selectedMedia.status)}
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs sm:text-sm font-medium">Product ID:</span>
                      <span className="text-xs sm:text-sm font-mono break-all">{selectedMedia.product_id}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs sm:text-sm font-medium">Created:</span>
                      <span className="text-xs sm:text-sm">{formatDate(selectedMedia.created_at)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs sm:text-sm font-medium">Updated:</span>
                      <span className="text-xs sm:text-sm">{formatDate(selectedMedia.updated_at)}</span>
                    </div>
                    {selectedMedia.metadata?.quality_score && (
                      <div className="flex justify-between items-center">
                        <span className="text-xs sm:text-sm font-medium">Quality Score:</span>
                        <span className="text-xs sm:text-sm">{(selectedMedia.metadata.quality_score * 100).toFixed(1)}%</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Content Details Section */}
              <div className="space-y-4 sm:space-y-6">
                <div className="flex items-center space-x-2 sm:space-x-3 pb-2 border-b border-gray-200">
                  <div className="p-1.5 sm:p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
                    <Wand2 className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                  </div>
                  <h3 className="text-lg sm:text-xl font-bold text-gray-900">Content Details</h3>
                </div>

                <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                  {/* Content & Media Settings */}
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4 sm:p-6 border border-blue-100">
                    <div className="flex items-center space-x-2 mb-3 sm:mb-4">
                      <Video className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                      <h4 className="font-semibold text-blue-900 text-sm sm:text-base">Content & Media Settings</h4>
                    </div>
                    <div className="space-y-3 sm:space-y-4">
                      {selectedMedia.content_type && (
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between p-2 sm:p-3 bg-white/70 rounded-lg border border-blue-200 gap-2">
                          <span className="text-xs sm:text-sm font-medium text-gray-700">Content Type</span>
                          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 text-xs sm:text-sm">
                            {selectedMedia.content_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </Badge>
                        </div>
                      )}

                      {selectedMedia.video_type && (
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between p-2 sm:p-3 bg-white/70 rounded-lg border border-blue-200 gap-2">
                          <span className="text-xs sm:text-sm font-medium text-gray-700">Video Type</span>
                          <span className="text-xs sm:text-sm font-semibold text-blue-800 capitalize">{selectedMedia.video_type}</span>
                        </div>
                      )}

                      {selectedMedia.style && (
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between p-2 sm:p-3 bg-white/70 rounded-lg border border-blue-200 gap-2">
                          <span className="text-xs sm:text-sm font-medium text-gray-700">Style</span>
                          <span className="text-xs sm:text-sm font-semibold text-blue-800 capitalize">{selectedMedia.style}</span>
                        </div>
                      )}

                      {selectedMedia.tone && (
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between p-2 sm:p-3 bg-white/70 rounded-lg border border-blue-200 gap-2">
                          <span className="text-xs sm:text-sm font-medium text-gray-700">Tone</span>
                          <span className="text-xs sm:text-sm font-semibold text-blue-800 capitalize">{selectedMedia.tone}</span>
                        </div>
                      )}

                      {selectedMedia.voice_tone && (
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between p-2 sm:p-3 bg-white/70 rounded-lg border border-blue-200 gap-2">
                          <span className="text-xs sm:text-sm font-medium text-gray-700">Voice Tone</span>
                          <span className="text-xs sm:text-sm font-semibold text-blue-800 capitalize">{selectedMedia.voice_tone}</span>
                        </div>
                      )}

                      {selectedMedia.ai_generation_type && (
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between p-2 sm:p-3 bg-white/70 rounded-lg border border-blue-200 gap-2">
                          <span className="text-xs sm:text-sm font-medium text-gray-700">AI Generation Type</span>
                          <Badge className="bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border-purple-200 text-xs sm:text-sm">
                            {selectedMedia.ai_generation_type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </Badge>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Camera & Visual Settings */}
                  <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-xl p-4 sm:p-6 border border-emerald-100">
                    <div className="flex items-center space-x-2 mb-3 sm:mb-4">
                      <Camera className="h-4 w-4 sm:h-5 sm:w-5 text-emerald-600" />
                      <h4 className="font-semibold text-emerald-900 text-sm sm:text-base">Camera & Visual Settings</h4>
                    </div>
                    <div className="space-y-4">
                      {selectedMedia.camera_angle && (
                        <div className="flex items-center justify-between p-3 bg-white/70 rounded-lg border border-emerald-200">
                          <span className="text-sm font-medium text-gray-700">Camera Angle</span>
                          <span className="text-sm font-semibold text-emerald-800">
                            {selectedMedia.camera_angle.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </span>
                        </div>
                      )}

                      {selectedMedia.camera_movement && (
                        <div className="flex items-center justify-between p-3 bg-white/70 rounded-lg border border-emerald-200">
                          <span className="text-sm font-medium text-gray-700">Camera Movement</span>
                          <span className="text-sm font-semibold text-emerald-800">
                            {selectedMedia.camera_movement.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </span>
                        </div>
                      )}

                      {selectedMedia.lighting_style && (
                        <div className="flex items-center justify-between p-3 bg-white/70 rounded-lg border border-emerald-200">
                          <span className="text-sm font-medium text-gray-700">Lighting Style</span>
                          <span className="text-sm font-semibold text-emerald-800">
                            {selectedMedia.lighting_style.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </span>
                        </div>
                      )}

                      {selectedMedia.target_audience && (
                        <div className="flex items-center justify-between p-3 bg-white/70 rounded-lg border border-emerald-200">
                          <span className="text-sm font-medium text-gray-700">Target Audience</span>
                          <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200 capitalize">
                            {selectedMedia.target_audience}
                          </Badge>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Content Description */}
                {selectedMedia.content_description && (
                  <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl p-4 sm:p-6 border border-amber-200">
                    <div className="flex items-center space-x-2 mb-3">
                      <FileText className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600" />
                      <h4 className="font-semibold text-amber-900 text-sm sm:text-base">Content Description</h4>
                    </div>
                    <div className="bg-white/80 rounded-lg p-3 sm:p-4 border border-amber-200">
                      <p className="text-xs sm:text-sm text-gray-800 leading-relaxed">{selectedMedia.content_description}</p>
                    </div>
                  </div>
                )}

                {/* Custom AI Prompt */}
                {selectedMedia.custom_ai_prompt && selectedMedia.custom_ai_prompt !== selectedMedia.content_description && (
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 sm:p-6 border border-green-200">
                    <div className="flex items-center space-x-2 mb-3">
                      <Wand2 className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                      <h4 className="font-semibold text-green-900 text-sm sm:text-base">Custom AI Prompt</h4>
                    </div>
                    <div className="bg-white/80 rounded-lg p-3 sm:p-4 border border-green-200">
                      <p className="text-xs sm:text-sm text-gray-800 leading-relaxed">{selectedMedia.custom_ai_prompt}</p>
                    </div>
                  </div>
                )}

                {/* Content Options & Platform Distribution */}
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
                  {/* Content Options */}
                  {selectedMedia.content_options && (
                    <div className="bg-gradient-to-br from-slate-50 to-gray-50 rounded-xl p-6 border border-slate-200">
                      <div className="flex items-center space-x-2 mb-4">
                        <Activity className="h-5 w-5 text-slate-600" />
                        <h4 className="font-semibold text-slate-900">Content Options</h4>
                      </div>
                      <div className="bg-white rounded-lg p-4 border border-slate-200">
                        <div className="space-y-2">
                          {Object.entries(selectedMedia.content_options as Record<string, any>).map(([key, value]) => (
                            <div key={key} className="flex items-center justify-between p-2 bg-slate-50 rounded-md">
                              <span className="text-sm font-medium text-slate-700 capitalize">
                                {key.replace(/_/g, ' ')}
                              </span>
                              <Badge variant={value ? "default" : "secondary"} className="text-xs">
                                {value ? "✓ Enabled" : "✗ Disabled"}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Platform Distribution */}
                  {selectedMedia.platform_distribution && (
                    <div className="bg-gradient-to-br from-violet-50 to-purple-50 rounded-xl p-6 border border-violet-200">
                      <div className="flex items-center space-x-2 mb-4">
                        <Palette className="h-5 w-5 text-violet-600" />
                        <h4 className="font-semibold text-violet-900">Platform Distribution</h4>
                      </div>
                      <div className="bg-white rounded-lg p-4 border border-violet-200">
                        <div className="space-y-3">
                          {Object.entries(selectedMedia.platform_distribution as Record<string, any>).map(([platform, settings]) => (
                            <div key={platform} className="border border-violet-100 rounded-lg p-3">
                              <div className="flex items-center space-x-2 mb-2">
                                <Badge className="bg-violet-100 text-violet-800 capitalize text-xs">
                                  {platform}
                                </Badge>
                              </div>
                              <div className="space-y-1">
                                {Object.entries(settings as Record<string, any>).map(([key, value]) => (
                                  <div key={key} className="flex items-center justify-between text-xs">
                                    <span className="text-slate-600 capitalize">
                                      {key.replace(/_/g, ' ')}
                                    </span>
                                    <span className="font-medium text-violet-700">
                                      {typeof value === 'boolean' ? (value ? '✓' : '✗') : value}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Error Message */}
              {selectedMedia.error_message && (
                <div className="p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg">
                  <h4 className="text-xs sm:text-sm font-medium text-red-800 mb-2">Error Details</h4>
                  <p className="text-xs sm:text-sm text-red-700">{selectedMedia.error_message}</p>
                </div>
              )}

              {/* Actions */}
              <div className="flex flex-col sm:flex-row justify-end gap-2 sm:space-x-2 pt-4 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={() => {
                    const downloadUrl = getMediaUrl(selectedMedia.media_url)
                    if (downloadUrl) {
                      window.open(downloadUrl, '_blank')
                    }
                  }}
                  disabled={!selectedMedia.media_url || !getMediaUrl(selectedMedia.media_url)}
                  className="w-full sm:w-auto text-sm"
                >
                  <Download className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                  Download
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => {
                    setIsDetailDialogOpen(false)
                    handleDeleteMedia(selectedMedia)
                  }}
                  className="w-full sm:w-auto text-sm"
                >
                  <Trash2 className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent className="max-w-[95vw] sm:max-w-lg">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-base sm:text-lg">Delete Media File</AlertDialogTitle>
            <AlertDialogDescription className="text-sm sm:text-base">
              Are you sure you want to delete this media file? This action cannot be undone.
              {mediaToDelete && (
                <div className="mt-2 p-2 bg-gray-50 rounded text-xs sm:text-sm">
                  <strong>File:</strong> {mediaToDelete.media_url ? (mediaToDelete.media_url.split('/').pop() || 'Unknown file') : 'No file URL'}
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <AlertDialogCancel className="w-full sm:w-auto">Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700 w-full sm:w-auto">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* File Processing Information Modal */}
      <Dialog open={isProgressModalOpen} onOpenChange={setIsProgressModalOpen}>
        <DialogContent className="max-w-[95vw] sm:max-w-4xl max-h-[90vh] overflow-y-auto p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
              <div className="p-1.5 sm:p-2 bg-blue-100 rounded-lg">
                <Info className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
              </div>
              <div>
                <span className="text-lg sm:text-xl">File Processing Information</span>
                <p className="text-xs sm:text-sm text-muted-foreground font-normal mt-1">
                  Understanding how your media files are processed
                </p>
              </div>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6 sm:space-y-8">
            {/* Processing Workflow */}
            <div className="space-y-3 sm:space-y-4">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900">Processing Workflow</h3>
              <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                Every media file goes through a structured processing pipeline to ensure quality and compatibility.
                Here's how your files are handled from upload to completion.
              </p>

              {/* Horizontal Workflow Visualization */}
              <div className="bg-gray-50 p-4 sm:p-6 rounded-xl border">
                <div className="relative">
                  <div className="flex flex-col sm:flex-row items-center justify-between space-y-6 sm:space-y-0">
                    {[
                      { icon: '📁', title: 'Upload', desc: 'File uploaded to system' },
                      { icon: '🤖', title: 'AI Processing', desc: 'AI analyzing content' },
                      { icon: '✅', title: 'Completed', desc: 'Ready for use' }
                    ].map((step, index) => (
                      <div key={index} className="flex flex-col items-center relative z-10">
                        {/* Step Circle */}
                        <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full border-3 border-blue-500 bg-blue-100 flex items-center justify-center text-lg sm:text-2xl transition-all duration-300 hover:scale-110">
                          <span className="text-blue-600">{step.icon}</span>
                        </div>

                        {/* Step Label */}
                        <div className="mt-2 sm:mt-3 text-center">
                          <div className="text-xs sm:text-sm font-semibold text-gray-900">{step.title}</div>
                          <div className="text-xs text-gray-600 mt-1 max-w-20 sm:max-w-none">{step.desc}</div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Connection Lines - Hidden on mobile */}
                  <div className="hidden sm:block absolute top-8 left-8 right-8 h-1 bg-gray-300 -z-0 rounded-full">
                    <div className="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-1000"></div>
                  </div>
                </div>

                {/* Failed State Example */}
                <div className="mt-6 sm:mt-8 pt-4 sm:pt-6 border-t border-gray-200">
                  <div className="text-center">
                    <h4 className="text-xs sm:text-sm font-semibold text-gray-900 mb-2">If Processing Fails:</h4>
                    <div className="inline-flex flex-col sm:flex-row items-center space-y-1 sm:space-y-0 sm:space-x-2 px-3 sm:px-4 py-2 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <span className="text-base sm:text-lg">❌</span>
                        <span className="text-xs sm:text-sm font-medium text-red-700">Failed</span>
                      </div>
                      <span className="text-xs text-red-600">Error details will be shown</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* File Information Guide */}
            <div className="space-y-3 sm:space-y-4">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900">Understanding File Information</h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                <div className="space-y-3 sm:space-y-4">
                  <div className="p-3 sm:p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h4 className="font-semibold text-blue-900 mb-2 text-sm sm:text-base">📁 File Details</h4>
                    <ul className="text-xs sm:text-sm text-blue-800 space-y-1">
                      <li>• <strong>Filename:</strong> Original name of your uploaded file</li>
                      <li>• <strong>Format:</strong> File type (JPG, PNG, MP4, etc.)</li>
                      <li>• <strong>Dimensions:</strong> Width × Height in pixels</li>
                      <li>• <strong>Duration:</strong> Length for video files</li>
                    </ul>
                  </div>

                  <div className="p-3 sm:p-4 bg-purple-50 rounded-lg border border-purple-200">
                    <h4 className="font-semibold text-purple-900 mb-2 text-sm sm:text-base">🎯 Status Tracking</h4>
                    <ul className="text-xs sm:text-sm text-purple-800 space-y-1">
                      <li>• <strong>Real-time updates:</strong> Status changes automatically</li>
                      <li>• <strong>Progress indicators:</strong> Visual progress bars</li>
                      <li>• <strong>Error handling:</strong> Clear error messages if issues occur</li>
                      <li>• <strong>Completion alerts:</strong> Notifications when ready</li>
                    </ul>
                  </div>
                </div>

                <div className="space-y-3 sm:space-y-4">
                  <div className="p-3 sm:p-4 bg-green-50 rounded-lg border border-green-200">
                    <h4 className="font-semibold text-green-900 mb-2 text-sm sm:text-base">⚡ Processing Features</h4>
                    <ul className="text-xs sm:text-sm text-green-800 space-y-1">
                      <li>• <strong>AI Enhancement:</strong> Automatic quality improvements</li>
                      <li>• <strong>Format Optimization:</strong> Best format for your needs</li>
                      <li>• <strong>Compression:</strong> Reduced file size without quality loss</li>
                      <li>• <strong>Metadata Extraction:</strong> Automatic tagging and categorization</li>
                    </ul>
                  </div>

                  <div className="p-3 sm:p-4 bg-orange-50 rounded-lg border border-orange-200">
                    <h4 className="font-semibold text-orange-900 mb-2 text-sm sm:text-base">💡 Tips & Best Practices</h4>
                    <ul className="text-xs sm:text-sm text-orange-800 space-y-1">
                      <li>• <strong>Click on file names</strong> to view detailed information</li>
                      <li>• <strong>Use filters</strong> to find specific file types or statuses</li>
                      <li>• <strong>Check status regularly</strong> for processing updates</li>
                      <li>• <strong>Download completed files</strong> when ready</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="p-4 sm:p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3">Quick Actions</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                <Button variant="outline" className="justify-start h-auto p-3 sm:p-4">
                  <Eye className="h-4 w-4 sm:h-5 sm:w-5 mr-2 sm:mr-3 text-blue-600 shrink-0" />
                  <div className="text-left min-w-0">
                    <div className="font-medium text-sm sm:text-base">View Details</div>
                    <div className="text-xs text-muted-foreground">See comprehensive file information</div>
                  </div>
                </Button>
                <Button variant="outline" className="justify-start h-auto p-3 sm:p-4">
                  <Activity className="h-4 w-4 sm:h-5 sm:w-5 mr-2 sm:mr-3 text-green-600 shrink-0" />
                  <div className="text-left min-w-0">
                    <div className="font-medium text-sm sm:text-base">Check Status</div>
                    <div className="text-xs text-muted-foreground">Get real-time processing updates</div>
                  </div>
                </Button>
                <Button variant="outline" className="justify-start h-auto p-3 sm:p-4 sm:col-span-2 lg:col-span-1">
                  <Download className="h-4 w-4 sm:h-5 sm:w-5 mr-2 sm:mr-3 text-purple-600 shrink-0" />
                  <div className="text-left min-w-0">
                    <div className="font-medium text-sm sm:text-base">Download</div>
                    <div className="text-xs text-muted-foreground">Get your processed files</div>
                  </div>
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Individual File Information Modal */}
      <Dialog open={isFileInfoModalOpen} onOpenChange={setIsFileInfoModalOpen}>
        <DialogContent className="max-w-[95vw] sm:max-w-3xl max-h-[90vh] overflow-y-auto p-4 sm:p-6">
          {selectedFileInfo && (
            <>
              <DialogHeader>
                <DialogTitle className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                  <div className="p-1.5 sm:p-2 bg-blue-100 rounded-lg">
                    {getMediaTypeIcon(selectedFileInfo.media_type)}
                  </div>
                  <div>
                    <span className="text-lg sm:text-xl">File Information</span>
                    <p className="text-xs sm:text-sm text-muted-foreground font-normal mt-1">
                      Detailed information about this media file
                    </p>
                  </div>
                </DialogTitle>
              </DialogHeader>

              <div className="space-y-4 sm:space-y-6">
                {/* File Status Progress */}
                <div className="p-3 sm:p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-3 sm:mb-4 gap-2">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900">Processing Status</h3>
                    {getStatusBadge(selectedFileInfo.status)}
                  </div>

                  {(() => {
                    const progressInfo = getProgressInfo(selectedFileInfo.status)
                    return (
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Progress</span>
                            <span className="font-medium">{Math.round(progressInfo.progress)}%</span>
                          </div>
                          <Progress
                            value={progressInfo.progress}
                            className={`h-3 ${
                              selectedFileInfo.status === 'failed' ? '[&>div]:bg-red-500' :
                              selectedFileInfo.status === 'completed' ? '[&>div]:bg-green-500' :
                              selectedFileInfo.status === 'processing' ? '[&>div]:bg-yellow-500' :
                              '[&>div]:bg-blue-500'
                            }`}
                          />
                          <p className="text-sm text-gray-600">
                            {progressInfo.currentStep.description}
                          </p>
                        </div>

                        {/* Horizontal Workflow Steps */}
                        <div className="relative">
                          <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
                            {progressInfo.steps.slice(0, 3).map((step, index) => {
                              const isActive = index <= progressInfo.currentStepIndex && selectedFileInfo.status !== 'failed'
                              const isCurrent = index === progressInfo.currentStepIndex
                              const isFailed = selectedFileInfo.status === 'failed' && index === progressInfo.currentStepIndex

                              return (
                                <div key={step.key} className="flex flex-col items-center relative z-10">
                                  {/* Step Circle */}
                                  <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-full border-3 flex items-center justify-center text-base sm:text-lg transition-all duration-300 ${
                                    isFailed ? 'border-red-500 bg-red-100' :
                                    isActive ? 'border-blue-500 bg-blue-100' :
                                    'border-gray-300 bg-gray-100'
                                  }`}>
                                    {isCurrent && !isFailed && (
                                      <div className="absolute inset-0 rounded-full border-3 border-blue-500 animate-pulse"></div>
                                    )}
                                    <span className={`${
                                      isFailed ? 'text-red-600' :
                                      isActive ? 'text-blue-600' : 'text-gray-400'
                                    }`}>
                                      {step.icon}
                                    </span>
                                  </div>

                                  {/* Step Label */}
                                  <div className="mt-2 text-center">
                                    <div className={`text-xs font-medium ${
                                      isFailed ? 'text-red-700' :
                                      isActive ? 'text-blue-700' : 'text-gray-500'
                                    }`}>
                                      {step.shortDesc}
                                    </div>
                                    {isCurrent && (
                                      <div className="text-xs text-gray-500 mt-1 max-w-20 sm:max-w-none">
                                        {step.description}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )
                            })}

                            {/* Failed Step - Show separately if failed */}
                            {selectedFileInfo.status === 'failed' && (
                              <div className="flex flex-col items-center relative z-10">
                                <div className="w-12 h-12 rounded-full border-3 border-red-500 bg-red-100 flex items-center justify-center text-lg">
                                  <span className="text-red-600">❌</span>
                                </div>
                                <div className="mt-2 text-center">
                                  <div className="text-xs font-medium text-red-700">Failed</div>
                                  <div className="text-xs text-gray-500 mt-1">Processing failed</div>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Connection Lines - Hidden on mobile */}
                          <div className="hidden sm:block absolute top-6 left-6 right-6 h-0.5 bg-gray-200 -z-0">
                            <div
                              className={`h-full transition-all duration-500 ${
                                selectedFileInfo.status === 'failed' ? 'bg-red-500' :
                                selectedFileInfo.status === 'completed' ? 'bg-green-500' :
                                'bg-blue-500'
                              }`}
                              style={{
                                width: selectedFileInfo.status === 'failed' ? '100%' :
                                       selectedFileInfo.status === 'completed' ? '100%' :
                                       selectedFileInfo.status === 'processing' ? '50%' : '25%'
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    )
                  })()}
                </div>

                {/* File Details */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                  <div className="space-y-3 sm:space-y-4">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900">File Details</h3>
                    <div className="space-y-2 sm:space-y-3">
                      <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                          <span className="text-xs sm:text-sm font-medium text-gray-600">Filename</span>
                          <span className="text-xs sm:text-sm font-mono text-gray-900 ">
                            {selectedFileInfo.media_url ? (selectedFileInfo.media_url.split('/').pop() || 'Unknown file') : 'No file URL'}
                          </span>
                        </div>
                      </div>
                      <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                          <span className="text-xs sm:text-sm font-medium text-gray-600">Format</span>
                          <span className="text-xs sm:text-sm font-mono text-gray-900">
                            {selectedFileInfo.format ? selectedFileInfo.format.toUpperCase() : 'Unknown'}
                          </span>
                        </div>
                      </div>
                      <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                          <span className="text-xs sm:text-sm font-medium text-gray-600">File Size</span>
                          <span className="text-xs sm:text-sm font-mono text-gray-900">
                            {formatFileSize(selectedFileInfo.file_size || 0)}
                          </span>
                        </div>
                      </div>
                      {selectedFileInfo.width && selectedFileInfo.height && (
                        <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                            <span className="text-xs sm:text-sm font-medium text-gray-600">Dimensions</span>
                            <span className="text-xs sm:text-sm font-mono text-gray-900">
                              {selectedFileInfo.width} × {selectedFileInfo.height}
                            </span>
                          </div>
                        </div>
                      )}
                      {selectedFileInfo.duration && (
                        <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                            <span className="text-xs sm:text-sm font-medium text-gray-600">Duration</span>
                            <span className="text-xs sm:text-sm font-mono text-gray-900">
                              {selectedFileInfo.duration}s
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="space-y-3 sm:space-y-4">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900">Metadata</h3>
                    <div className="space-y-2 sm:space-y-3">
                      <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                          <span className="text-xs sm:text-sm font-medium text-gray-600">Product ID</span>
                          <span className="text-xs sm:text-sm font-mono text-gray-900">
                            #{selectedFileInfo.product_id.slice(-8)}
                          </span>
                        </div>
                      </div>
                      <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                          <span className="text-xs sm:text-sm font-medium text-gray-600">Job ID</span>
                          <span className="text-xs sm:text-sm font-mono text-gray-900 break-all">
                            {selectedFileInfo.external_job_id || 'N/A'}
                          </span>
                        </div>
                      </div>
                      <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                          <span className="text-xs sm:text-sm font-medium text-gray-600">Created</span>
                          <span className="text-xs sm:text-sm text-gray-900">
                            {formatDate(selectedFileInfo.created_at)}
                          </span>
                        </div>
                      </div>
                      <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                          <span className="text-xs sm:text-sm font-medium text-gray-600">Updated</span>
                          <span className="text-xs sm:text-sm text-gray-900">
                            {formatDate(selectedFileInfo.updated_at)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Error Message */}
                {selectedFileInfo.error_message && (
                  <div className="p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg">
                    <h4 className="text-xs sm:text-sm font-medium text-red-800 mb-2 flex items-center">
                      <AlertCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                      Error Details
                    </h4>
                    <p className="text-xs sm:text-sm text-red-700">{selectedFileInfo.error_message}</p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex flex-col sm:flex-row justify-end gap-2 sm:space-x-3 pt-4 border-t border-gray-200">
                  <Button
                    variant="outline"
                    onClick={() => checkMediaStatus(selectedFileInfo)}
                    disabled={checkingStatus === selectedFileInfo.id}
                    className="w-full sm:w-auto text-sm"
                  >
                    {checkingStatus === selectedFileInfo.id ? (
                      <RefreshCw className="h-3 w-3 sm:h-4 sm:w-4 mr-2 animate-spin" />
                    ) : (
                      <Activity className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                    )}
                    Check Status
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      const downloadUrl = getMediaUrl(selectedFileInfo.media_url)
                      if (downloadUrl) {
                        window.open(downloadUrl, '_blank')
                      }
                    }}
                    disabled={!selectedFileInfo.media_url || !getMediaUrl(selectedFileInfo.media_url)}
                    className="w-full sm:w-auto text-sm"
                  >
                    <Download className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                    Download
                  </Button>
                  <Button onClick={() => handleViewMedia(selectedFileInfo)} className="w-full sm:w-auto text-sm">
                    <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                    View Details
                  </Button>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </AppLayout>
  )
}

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { OperationsDashboard } from "@/components/operations-dashboard"
import { TimeTracker } from "@/components/time-tracker"
import { ResourceBurnRate } from "@/components/resource-burn-rate"
import { BottleneckDashboard } from "@/components/bottleneck-dashboard"
import { AvatarCostReturn } from "@/components/avatar-cost-return"
import { ResourceOptimizationWizard } from "@/components/resource-optimization-wizard"
import { CostReductionAnalyzer } from "@/components/cost-reduction-analyzer"
import { AutomationOpportunityFinder } from "@/components/automation-opportunity-finder"
import { AutomationSuccessMetrics } from "@/components/automation-success-metrics"
import { Wand2, Scissors, Bot, BarChart2 } from "lucide-react"
import { AppLayout } from "@/components/layouts/app-layout"

export default function OperationsPage() {
  return (
    <AppLayout>
      <div className="space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Ops & Resource Allocation Center</h2>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              Export Data
            </Button>
            <Button variant="outline" size="sm">
              Print Report
            </Button>
            <Button variant="outline">
              <Scissors className="mr-2 h-4 w-4" />
              Reduce Costs
            </Button>
            <Button variant="outline">
              <Bot className="mr-2 h-4 w-4" />
              Find Automation
            </Button>
            <Button variant="outline">
              <BarChart2 className="mr-2 h-4 w-4" />
              Track Success
            </Button>
            <Button>
              <Wand2 className="mr-2 h-4 w-4" />
              Optimize Resources
            </Button>
          </div>
        </div>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="time-tracker">Time Tracker</TabsTrigger>
            <TabsTrigger value="burn-rate">Burn Rate</TabsTrigger>
            <TabsTrigger value="bottlenecks">Bottlenecks</TabsTrigger>
            <TabsTrigger value="avatar-roi">Avatar ROI</TabsTrigger>
            <TabsTrigger value="optimization">Optimization Wizard</TabsTrigger>
            <TabsTrigger value="cost-reduction">Cost Reduction</TabsTrigger>
            <TabsTrigger value="automation">Automation Finder</TabsTrigger>
            <TabsTrigger value="success-metrics">Success Metrics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <OperationsDashboard />
          </TabsContent>

          <TabsContent value="time-tracker" className="space-y-4">
            <TimeTracker />
          </TabsContent>

          <TabsContent value="burn-rate" className="space-y-4">
            <ResourceBurnRate />
          </TabsContent>

          <TabsContent value="bottlenecks" className="space-y-4">
            <BottleneckDashboard />
          </TabsContent>

          <TabsContent value="avatar-roi" className="space-y-4">
            <AvatarCostReturn />
          </TabsContent>

          <TabsContent value="optimization" className="space-y-4">
            <ResourceOptimizationWizard />
          </TabsContent>

          <TabsContent value="cost-reduction" className="space-y-4">
            <CostReductionAnalyzer />
          </TabsContent>

          <TabsContent value="automation" className="space-y-4">
            <AutomationOpportunityFinder />
          </TabsContent>

          <TabsContent value="success-metrics" className="space-y-4">
            <AutomationSuccessMetrics />
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  )
}

'use client'

import React, { useState, useEffect } from "react"
import { useAuth } from "@/contexts/auth-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON>ertD<PERSON>og<PERSON>eader,
  AlertDialog<PERSON>itle,
} from "@/components/ui/alert-dialog"
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  MoreHorizontal,
  Eye,
  EyeOff,
  Power,
  PowerOff,
  ChevronLeft,
  ChevronRight,
  RefreshCw,
  Download
} from "lucide-react"
import { AppLayout } from "@/components/layouts/app-layout"
import { BlogCategoryCreateDialog } from "@/components/blog-category-create-dialog"
import { BlogCategoryEditDialog } from "@/components/blog-category-edit-dialog"
import { useToast } from "@/hooks/use-toast"
import { 
  blogCategoryService, 
  type BlogCategory, 
  type BlogCategoryFilters,
  type CreateBlogCategoryData 
} from "@/lib/api/blog-categories"

export default function BlogCategoriesPage() {
  const { isAuthenticated, user, isLoading: authLoading } = useAuth()
  const [categories, setCategories] = useState<BlogCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [pagination, setPagination] = useState({
    currentPage: 1,
    itemsPerPage: 10,
    totalPages: 1,
    totalItems: 0
  })
  
  // Filters and search
  const [searchTerm, setSearchTerm] = useState("")
  const [activeFilter, setActiveFilter] = useState<string>("all")
  const [frontendFilter, setFrontendFilter] = useState<string>("all")
  
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<BlogCategory | null>(null)
  
  // Delete confirmation
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
  const [categoryToDelete, setCategoryToDelete] = useState<BlogCategory | null>(null)
  
  const { toast } = useToast()

  useEffect(() => {
    if (!authLoading) {
      loadCategories()
    }
  }, [authLoading, pagination.currentPage, pagination.itemsPerPage, searchTerm, activeFilter, frontendFilter])

  const loadCategories = async () => {
    try {
      setLoading(true)

      const filters: BlogCategoryFilters = {
        page: pagination.currentPage,
        limit: pagination.itemsPerPage,
      }

      if (searchTerm.trim()) filters.search = searchTerm.trim()
      if (activeFilter !== "all") filters.is_active = activeFilter === "active"
      if (frontendFilter !== "all") filters.is_frontend_show = frontendFilter === "show"

      const response = await blogCategoryService.getAllBlogCategories(filters)

      setCategories(response.categories || [])
      setPagination({
        currentPage: response.pagination?.page || 1,
        itemsPerPage: response.pagination?.limit || 10,
        totalPages: response.pagination?.totalPages || 1,
        totalItems: response.pagination?.total || 0
      })
    } catch (error) {
      console.error('Failed to load blog categories:', error)
      toast({
        title: "Error",
        description: "Failed to load blog categories. Please try again.",
        variant: "destructive",
      })
      setCategories([])
    } finally {
      setLoading(false)
    }
  }

  const handleCreateCategory = async (data: CreateBlogCategoryData) => {
    try {
      await blogCategoryService.createBlogCategory(data)
      toast({
        title: "Success",
        description: "Blog category created successfully.",
      })
      setIsCreateDialogOpen(false)
      loadCategories()
    } catch (error) {
      console.error('Failed to create blog category:', error)
      toast({
        title: "Error",
        description: "Failed to create blog category. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleEditCategory = async (data: any) => {
    if (!selectedCategory) return
    
    try {
      await blogCategoryService.updateBlogCategory(selectedCategory.id, data)
      toast({
        title: "Success",
        description: "Blog category updated successfully.",
      })
      setIsEditDialogOpen(false)
      setSelectedCategory(null)
      loadCategories()
    } catch (error) {
      console.error('Failed to update blog category:', error)
      toast({
        title: "Error",
        description: "Failed to update blog category. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleDeleteCategory = async () => {
    if (!categoryToDelete) return
    
    try {
      await blogCategoryService.deleteBlogCategory(categoryToDelete.id)
      toast({
        title: "Success",
        description: "Blog category deleted successfully.",
      })
      setDeleteConfirmOpen(false)
      setCategoryToDelete(null)
      loadCategories()
    } catch (error) {
      console.error('Failed to delete blog category:', error)
      toast({
        title: "Error",
        description: "Failed to delete blog category. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleToggleActive = async (category: BlogCategory) => {
    try {
      await blogCategoryService.toggleActiveStatus(category.id)
      toast({
        title: "Success",
        description: `Category ${category.is_active ? 'deactivated' : 'activated'} successfully.`,
      })
      loadCategories()
    } catch (error) {
      console.error('Failed to toggle active status:', error)
      toast({
        title: "Error",
        description: "Failed to update category status. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleToggleFrontend = async (category: BlogCategory) => {
    try {
      await blogCategoryService.toggleFrontendShow(category.id)
      toast({
        title: "Success",
        description: `Category ${category.is_frontend_show ? 'hidden from' : 'shown on'} frontend successfully.`,
      })
      loadCategories()
    } catch (error) {
      console.error('Failed to toggle frontend visibility:', error)
      toast({
        title: "Error",
        description: "Failed to update frontend visibility. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleSelectCategory = (categoryId: string, checked: boolean) => {
    if (checked) {
      setSelectedCategories(prev => [...prev, categoryId])
    } else {
      setSelectedCategories(prev => prev.filter(id => id !== categoryId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCategories(categories.map(cat => cat.id))
    } else {
      setSelectedCategories([])
    }
  }

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }))
  }

  const handleLimitChange = (newLimit: string) => {
    setPagination(prev => ({ ...prev, itemsPerPage: parseInt(newLimit), currentPage: 1 }))
  }

  // Bulk operations
  const handleBulkActivate = async () => {
    try {
      await blogCategoryService.bulkToggleActive(selectedCategories, true)
      toast({
        title: "Success",
        description: `${selectedCategories.length} categories activated successfully.`,
      })
      setSelectedCategories([])
      loadCategories()
    } catch (error) {
      console.error('Failed to bulk activate categories:', error)
      toast({
        title: "Error",
        description: "Failed to activate categories. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleBulkDeactivate = async () => {
    try {
      await blogCategoryService.bulkToggleActive(selectedCategories, false)
      toast({
        title: "Success",
        description: `${selectedCategories.length} categories deactivated successfully.`,
      })
      setSelectedCategories([])
      loadCategories()
    } catch (error) {
      console.error('Failed to bulk deactivate categories:', error)
      toast({
        title: "Error",
        description: "Failed to deactivate categories. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleBulkShowFrontend = async () => {
    try {
      await blogCategoryService.bulkToggleFrontend(selectedCategories, true)
      toast({
        title: "Success",
        description: `${selectedCategories.length} categories are now visible on frontend.`,
      })
      setSelectedCategories([])
      loadCategories()
    } catch (error) {
      console.error('Failed to bulk show categories on frontend:', error)
      toast({
        title: "Error",
        description: "Failed to update frontend visibility. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleBulkHideFrontend = async () => {
    try {
      await blogCategoryService.bulkToggleFrontend(selectedCategories, false)
      toast({
        title: "Success",
        description: `${selectedCategories.length} categories are now hidden from frontend.`,
      })
      setSelectedCategories([])
      loadCategories()
    } catch (error) {
      console.error('Failed to bulk hide categories from frontend:', error)
      toast({
        title: "Error",
        description: "Failed to update frontend visibility. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleBulkDelete = async () => {
    if (!confirm(`Are you sure you want to delete ${selectedCategories.length} categories? This action cannot be undone.`)) {
      return
    }

    try {
      await blogCategoryService.bulkDelete(selectedCategories)
      toast({
        title: "Success",
        description: `${selectedCategories.length} categories deleted successfully.`,
      })
      setSelectedCategories([])
      loadCategories()
    } catch (error) {
      console.error('Failed to bulk delete categories:', error)
      toast({
        title: "Error",
        description: "Failed to delete categories. Please try again.",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Export/Import functions
  const handleExportCategories = () => {
    const exportData = categories.map(cat => ({
      title: cat.title,
      slug: cat.slug,
      is_active: cat.is_active,
      is_frontend_show: cat.is_frontend_show,
      created_at: cat.created_at,
      updated_at: cat.updated_at
    }))

    const dataStr = JSON.stringify(exportData, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)

    const exportFileDefaultName = `blog-categories-${new Date().toISOString().split('T')[0]}.json`

    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()

    toast({
      title: "Success",
      description: "Categories exported successfully.",
    })
  }



  if (authLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading...</p>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 lg:p-8 pt-4 sm:pt-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Blog Categories</h1>
            <p className="text-muted-foreground text-sm sm:text-base">
              Manage your blog categories and their visibility settings
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:space-x-2">
            <Button variant="outline" size="sm" onClick={handleExportCategories} className="w-full sm:w-auto">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button variant="outline" onClick={loadCategories} disabled={loading} className="w-full sm:w-auto">
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button onClick={() => setIsCreateDialogOpen(true)} className="w-full sm:w-auto">
              <Plus className="mr-2 h-4 w-4" />
              Add Category
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-3 sm:gap-4 grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium">Total Categories</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-lg sm:text-2xl font-bold">{pagination.totalItems}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium">Active Categories</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-lg sm:text-2xl font-bold text-green-600">
                {categories.filter(cat => cat.is_active).length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium">Frontend Visible</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-lg sm:text-2xl font-bold text-blue-600">
                {categories.filter(cat => cat.is_frontend_show).length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium">Selected</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-lg sm:text-2xl font-bold text-purple-600">
                {selectedCategories.length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base sm:text-lg">Filters & Search</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search categories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-2">
                <Select value={activeFilter} onValueChange={setActiveFilter}>
                  <SelectTrigger className="w-full ">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={frontendFilter} onValueChange={setFrontendFilter}>
                  <SelectTrigger className="w-full ">
                    <SelectValue placeholder="Frontend" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Frontend</SelectItem>
                    <SelectItem value="show">Visible</SelectItem>
                    <SelectItem value="hide">Hidden</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={pagination.itemsPerPage.toString()} onValueChange={handleLimitChange}>
                  <SelectTrigger className="w-full ">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 per page</SelectItem>
                    <SelectItem value="10">10 per page</SelectItem>
                    <SelectItem value="25">25 per page</SelectItem>
                    <SelectItem value="50">50 per page</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bulk Actions */}
        {selectedCategories.length > 0 && (
          <Card>
            <CardContent className="pt-4 sm:pt-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <span className="text-sm text-muted-foreground">
                  {selectedCategories.length} categories selected
                </span>
                <div className="flex flex-col sm:flex-row gap-2 sm:gap-2">
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button variant="outline" size="sm" onClick={handleBulkActivate} className="w-full sm:w-auto">
                      <Power className="mr-2 h-4 w-4" />
                      <span className="hidden sm:inline">Bulk </span>Activate
                    </Button>
                    <Button variant="outline" size="sm" onClick={handleBulkDeactivate} className="w-full sm:w-auto">
                      <PowerOff className="mr-2 h-4 w-4" />
                      <span className="hidden sm:inline">Bulk </span>Deactivate
                    </Button>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button variant="outline" size="sm" onClick={handleBulkShowFrontend} className="w-full sm:w-auto">
                      <Eye className="mr-2 h-4 w-4" />
                      Show Frontend
                    </Button>
                    <Button variant="outline" size="sm" onClick={handleBulkHideFrontend} className="w-full sm:w-auto">
                      <EyeOff className="mr-2 h-4 w-4" />
                      Hide Frontend
                    </Button>
                  </div>
                  <Button variant="destructive" size="sm" onClick={handleBulkDelete} className="w-full sm:w-auto">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Selected
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Categories Table */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base sm:text-lg">Categories</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-muted-foreground text-sm">Loading categories...</p>
                </div>
              </div>
            ) : categories.length === 0 ? (
              <div className="text-center py-8 sm:py-12">
                <h3 className="text-base sm:text-lg font-semibold">No categories found</h3>
                <p className="text-muted-foreground text-sm sm:text-base mt-2">
                  {searchTerm || activeFilter !== "all" || frontendFilter !== "all"
                    ? "Try adjusting your search or filters"
                    : "Get started by creating your first blog category"}
                </p>
                {!searchTerm && activeFilter === "all" && frontendFilter === "all" && (
                  <Button className="mt-4 w-full sm:w-auto" onClick={() => setIsCreateDialogOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add First Category
                  </Button>
                )}
              </div>
            ) : (
              <>
                {/* Desktop Table */}
                <div className="hidden lg:block">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedCategories.length === categories.length}
                            onCheckedChange={handleSelectAll}
                          />
                        </TableHead>
                        <TableHead>Title</TableHead>
                        <TableHead>Slug</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Frontend</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead>Updated</TableHead>
                        <TableHead className="w-12">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {categories.map((category) => (
                        <TableRow key={category.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedCategories.includes(category.id)}
                              onCheckedChange={(checked) => handleSelectCategory(category.id, checked as boolean)}
                            />
                          </TableCell>
                          <TableCell className="font-medium">{category.title}</TableCell>
                          <TableCell>
                            <code className="text-xs bg-muted px-2 py-1 rounded">{category.slug}</code>
                          </TableCell>
                          <TableCell>
                            <Badge variant={category.is_active ? "default" : "secondary"}>
                              {category.is_active ? "Active" : "Inactive"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={category.is_frontend_show ? "default" : "outline"}>
                              {category.is_frontend_show ? "Visible" : "Hidden"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {formatDate(category.created_at)}
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {formatDate(category.updated_at)}
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem
                                  onClick={() => {
                                    setSelectedCategory(category)
                                    setIsEditDialogOpen(true)
                                  }}
                                >
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleToggleActive(category)}>
                                  {category.is_active ? (
                                    <>
                                      <PowerOff className="mr-2 h-4 w-4" />
                                      Deactivate
                                    </>
                                  ) : (
                                    <>
                                      <Power className="mr-2 h-4 w-4" />
                                      Activate
                                    </>
                                  )}
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleToggleFrontend(category)}>
                                  {category.is_frontend_show ? (
                                    <>
                                      <EyeOff className="mr-2 h-4 w-4" />
                                      Hide from Frontend
                                    </>
                                  ) : (
                                    <>
                                      <Eye className="mr-2 h-4 w-4" />
                                      Show on Frontend
                                    </>
                                  )}
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-destructive"
                                  onClick={() => {
                                    setCategoryToDelete(category)
                                    setDeleteConfirmOpen(true)
                                  }}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Mobile Card Layout */}
                <div className="lg:hidden space-y-4">
                  {categories.map((category) => (
                    <Card key={category.id} className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Checkbox
                            checked={selectedCategories.includes(category.id)}
                            onCheckedChange={(checked) => handleSelectCategory(category.id, checked as boolean)}
                          />
                          <div>
                            <h3 className="font-medium text-sm">{category.title}</h3>
                            <code className="text-xs bg-muted px-2 py-1 rounded mt-1 inline-block">{category.slug}</code>
                          </div>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedCategory(category)
                                setIsEditDialogOpen(true)
                              }}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleToggleActive(category)}>
                              {category.is_active ? (
                                <>
                                  <PowerOff className="mr-2 h-4 w-4" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <Power className="mr-2 h-4 w-4" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleToggleFrontend(category)}>
                              {category.is_frontend_show ? (
                                <>
                                  <EyeOff className="mr-2 h-4 w-4" />
                                  Hide from Frontend
                                </>
                              ) : (
                                <>
                                  <Eye className="mr-2 h-4 w-4" />
                                  Show on Frontend
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-destructive"
                              onClick={() => {
                                setCategoryToDelete(category)
                                setDeleteConfirmOpen(true)
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <div className="flex flex-wrap gap-2 mb-3">
                        <Badge variant={category.is_active ? "default" : "secondary"} className="text-xs">
                          {category.is_active ? "Active" : "Inactive"}
                        </Badge>
                        <Badge variant={category.is_frontend_show ? "default" : "outline"} className="text-xs">
                          {category.is_frontend_show ? "Visible" : "Hidden"}
                        </Badge>
                      </div>

                      <div className="text-xs text-muted-foreground space-y-1">
                        <div>Created: {formatDate(category.created_at)}</div>
                        <div>Updated: {formatDate(category.updated_at)}</div>
                      </div>
                    </Card>
                  ))}
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <Card>
            <CardContent className="pt-4 sm:pt-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="text-xs sm:text-sm text-muted-foreground text-center sm:text-left">
                  {(() => {
                    const currentPage = pagination.currentPage || 1
                    const itemsPerPage = pagination.itemsPerPage || 10
                    const totalItems = pagination.totalItems || 0
                    const startItem = ((currentPage - 1) * itemsPerPage) + 1
                    const endItem = Math.min(currentPage * itemsPerPage, totalItems)

                    return `Showing ${startItem} to ${endItem} of ${totalItems} results`
                  })()}
                </div>
                <div className="flex flex-col sm:flex-row items-center gap-2 sm:space-x-2">
                  <div className="flex items-center space-x-2 w-full sm:w-auto">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.currentPage - 1)}
                      disabled={pagination.currentPage <= 1}
                      className="flex-1 sm:flex-none"
                    >
                      <ChevronLeft className="h-4 w-4 mr-1" />
                      <span className="hidden sm:inline">Previous</span>
                      <span className="sm:hidden">Prev</span>
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.currentPage + 1)}
                      disabled={pagination.currentPage >= pagination.totalPages}
                      className="flex-1 sm:flex-none"
                    >
                      <span className="hidden sm:inline">Next</span>
                      <span className="sm:hidden">Next</span>
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>

                  <div className="hidden sm:flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                      const pageNumber = i + 1
                      return (
                        <Button
                          key={pageNumber}
                          variant={pagination.currentPage === pageNumber ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(pageNumber)}
                          className="w-8 h-8 p-0"
                        >
                          {pageNumber}
                        </Button>
                      )
                    })}
                    {pagination.totalPages > 5 && (
                      <>
                        <span className="text-muted-foreground">...</span>
                        <Button
                          variant={pagination.currentPage === pagination.totalPages ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(pagination.totalPages)}
                          className="w-8 h-8 p-0"
                        >
                          {pagination.totalPages}
                        </Button>
                      </>
                    )}
                  </div>

                  {/* Mobile page indicator */}
                  <div className="sm:hidden text-xs text-muted-foreground">
                    Page {pagination.currentPage} of {pagination.totalPages}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Create Dialog */}
        <BlogCategoryCreateDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          onSubmit={handleCreateCategory}
        />

        {/* Edit Dialog */}
        <BlogCategoryEditDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          category={selectedCategory}
          onSubmit={handleEditCategory}
        />

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the blog category
                "{categoryToDelete?.title}" and remove it from our servers.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteCategory}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AppLayout>
  )
}

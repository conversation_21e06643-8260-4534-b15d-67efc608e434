import { But<PERSON> } from "@/components/ui/button"
import { AgentDirectory } from "@/components/agent-directory"
import { TaskLog } from "@/components/task-log"
import { AgentFeedback } from "@/components/agent-feedback"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Toaster } from "@/components/toaster"
import { AppLayout } from "@/components/layouts/app-layout"

export default function AgentsPage() {
  return (
    <AppLayout>
      <div className="space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Agent Management & Automation Control</h2>
          <div className="flex items-center space-x-2">
            <Button variant="outline">View Documentation</Button>
            <Button>Add New Agent</Button>
          </div>
        </div>

        <Tabs defaultValue="directory" className="space-y-4">
          <TabsList>
            <TabsTrigger value="directory">Agent Directory</TabsTrigger>
            <TabsTrigger value="tasks">Task Log</TabsTrigger>
            <TabsTrigger value="feedback">Feedback Loop</TabsTrigger>
            <TabsTrigger value="performance">Performance Metrics</TabsTrigger>
          </TabsList>

          <TabsContent value="directory">
            <AgentDirectory />
          </TabsContent>

          <TabsContent value="tasks">
            <TaskLog />
          </TabsContent>

          <TabsContent value="feedback">
            <AgentFeedback />
          </TabsContent>

          <TabsContent value="performance">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <div className="col-span-2">
                <h3 className="text-lg font-medium mb-4">Agent Performance Overview</h3>
                {/* Performance metrics will go here */}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
      <Toaster />
    </AppLayout>
  )
}

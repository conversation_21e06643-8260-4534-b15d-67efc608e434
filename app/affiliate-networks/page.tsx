import { Button } from "@/components/ui/button"
import { AffiliateNetworkDirectory } from "@/components/affiliate-network-directory"
import { ApplicationQueue } from "@/components/application-queue"
import { ApplicationStatus } from "@/components/application-status"
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Toaster } from "@/components/toaster"
import { AppLayout } from "@/components/layouts/app-layout"

export default function AffiliateNetworksPage() {
  return (
    <AppLayout>
      <div className="space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Affiliate Network Application Hub</h2>
          <div className="flex items-center space-x-2">
            <Button variant="outline">View Documentation</Button>
            <Button>Add Network</Button>
          </div>
        </div>

        <Tabs defaultValue="directory" className="space-y-4">
          <TabsList>
            <TabsTrigger value="directory">Network Directory</TabsTrigger>
            <TabsTrigger value="queue">Application Queue</TabsTrigger>
            <TabsTrigger value="status">Application Status</TabsTrigger>
          </TabsList>

          <TabsContent value="directory">
            <AffiliateNetworkDirectory />
          </TabsContent>

          <TabsContent value="queue">
            <ApplicationQueue />
          </TabsContent>

          <TabsContent value="status">
            <ApplicationStatus />
          </TabsContent>
        </Tabs>
      </div>
      <Toaster />
    </AppLayout>
  )
}

import { But<PERSON> } from "@/components/ui/button"
import { TrendsTracker } from "@/components/trends-tracker"
import { AppLayout } from "@/components/layouts/app-layout"

export default function TrendsPage() {
  return (
    <AppLayout>
      <div className="space-y-4 p-8 pt-6">
        <div className="flex items-start md:flex-row flex-col md:items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Trends & Opportunities</h2>
          <div className="flex items-center space-x-2">
            <Button>Refresh Trends</Button>
          </div>
        </div>

        <TrendsTracker />
      </div>
    </AppLayout>
  )
}

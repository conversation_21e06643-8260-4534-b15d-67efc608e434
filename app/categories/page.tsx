'use client'

import { useState, useEffect } from "react"
import { AppLayout } from "@/components/layouts/app-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { Plus, Search, Filter, MoreHorizontal, Edit, Trash2, ChevronLeft, ChevronRight } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { CategoryCreateDialog } from "@/components/category-create-dialog"
import { CategoryEditDialog } from "@/components/category-edit-dialog"
import { categoryService, Category } from "@/lib/api/categories"

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [pagination, setPagination] = useState({
    currentPage: 1,
    itemsPerPage: 10,
    totalPages: 1,
    totalItems: 0
  })
  const { toast } = useToast()

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async (page: number = pagination.currentPage || 1) => {
    try {
      setLoading(true)
      const itemsPerPage = pagination.itemsPerPage || 10
      const response = await categoryService.getAllCategories(page, itemsPerPage)
      setCategories(response.data)

      if (response.pagination) {
        setPagination({
          currentPage: response.pagination.page,
          itemsPerPage: response.pagination.limit,
          totalPages: response.pagination.totalPages,
          totalItems: response.pagination.totalItems
        })
      } else {
        // Fallback if no pagination data from API
        setPagination(prev => ({
          ...prev,
          currentPage: page,
          totalItems: response.data.length,
          totalPages: Math.ceil(response.data.length / itemsPerPage)
        }))
      }
    } catch (error) {
      console.error('Failed to load categories:', error)
      toast({
        title: "Error",
        description: "Failed to load categories. Please check your API connection and authentication.",
        variant: "destructive",
      })
      setCategories([])
    } finally {
      setLoading(false)
    }
  }

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      loadCategories(newPage)
    }
  }

  const handleCreateCategory = async (data: any) => {
    try {
      await categoryService.createCategory(data)
      toast({
        title: "Success",
        description: "Category created successfully.",
      })
      setIsCreateDialogOpen(false)
      // After creating, go to first page to see the new category
      loadCategories(1)
    } catch (error) {
      console.error('Failed to create category:', error)
      toast({
        title: "Error",
        description: "Failed to create category. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleEditCategory = async (data: any) => {
    if (!selectedCategory) return

    try {
      await categoryService.updateCategory(selectedCategory.id, data)
      toast({
        title: "Success",
        description: "Category updated successfully.",
      })
      setIsEditDialogOpen(false)
      setSelectedCategory(null)
      // Preserve current page when reloading after update
      loadCategories(pagination.currentPage)
    } catch (error) {
      console.error('Failed to update category:', error)
      toast({
        title: "Error",
        description: "Failed to update category. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleDeleteCategory = async (category: Category) => {
    if (!confirm(`Are you sure you want to delete "${category.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      await categoryService.deleteCategory(category.id)
      toast({
        title: "Success",
        description: "Category deleted successfully.",
      })
      // After delete, check if we need to go to previous page
      const currentPageItemCount = filteredCategories.length
      const shouldGoToPrevPage = currentPageItemCount === 1 && pagination.currentPage > 1
      const targetPage = shouldGoToPrevPage ? pagination.currentPage - 1 : pagination.currentPage
      loadCategories(targetPage)
    } catch (error) {
      console.error('Failed to delete category:', error)
      toast({
        title: "Error",
        description: "Failed to delete category. Please try again.",
        variant: "destructive",
      })
    }
  }

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading categories...</p>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 lg:p-8 pt-4 sm:pt-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">Category Management</h2>
            <p className="text-muted-foreground text-sm sm:text-base">
              Manage product and content categories for your platform
            </p>
          </div>
          <Button onClick={() => setIsCreateDialogOpen(true)} className="w-full sm:w-auto">
            <Plus className="mr-2 h-4 w-4" />
            Add Category
          </Button>
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:space-x-2">
          <div className="relative flex-1 sm:max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search categories..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
          <Button variant="outline" size="sm" className="w-full sm:w-auto">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg sm:text-xl">Categories ({pagination.totalItems})</CardTitle>
            <CardDescription className="text-sm">
              Manage your categories and their settings
            </CardDescription>
          </CardHeader>
          <CardContent className="p-3 sm:p-6">
            {filteredCategories.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-muted-foreground mb-4 text-sm sm:text-base">No categories found</div>
                <Button onClick={() => setIsCreateDialogOpen(true)} size="sm" className="w-full sm:w-auto">
                  <Plus className="mr-2 h-4 w-4" />
                  Create your first category
                </Button>
              </div>
            ) : (
              <div className="rounded-md border overflow-hidden">
                {/* Mobile Card View */}
                <div className="block sm:hidden">
                  {filteredCategories.map((category) => (
                    <div key={category.id} className="border-b last:border-b-0 p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3 flex-1 min-w-0">
                          <div
                            className="w-4 h-4 rounded-full border border-gray-200 flex-shrink-0"
                            style={{ backgroundColor: category.color }}
                          />
                          <div className="min-w-0 flex-1">
                            <div className="font-medium text-sm truncate">{category.name}</div>
                            {category.slug && (
                              <div className="text-xs text-muted-foreground">/{category.slug}</div>
                            )}
                          </div>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0 flex-shrink-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedCategory(category)
                                setIsEditDialogOpen(true)
                              }}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteCategory(category)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="text-muted-foreground line-clamp-2">
                          {category.description}
                        </div>

                        <div className="flex items-center justify-between">
                          <Badge variant={category.is_active ? "default" : "secondary"} className="text-xs">
                            {category.is_active ? "Active" : "Inactive"}
                          </Badge>
                          <div className="text-xs text-muted-foreground">
                            {new Date(category.created_at).toLocaleDateString()}
                          </div>
                        </div>

                        <div className="flex justify-between text-xs">
                          <span>{(category.products_count || 0)} products</span>
                          <span className="text-muted-foreground">{(category.avatars_count || 0)} avatars</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Desktop Table View */}
                <div className="hidden sm:block overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="min-w-[200px]">Name</TableHead>
                        <TableHead className="min-w-[250px] hidden md:table-cell">Description</TableHead>
                        <TableHead className="min-w-[80px]">Status</TableHead>
                        <TableHead className="min-w-[100px] hidden lg:table-cell">Items</TableHead>
                        <TableHead className="min-w-[100px] hidden lg:table-cell">Created</TableHead>
                        <TableHead className="w-[50px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredCategories.map((category) => (
                        <TableRow key={category.id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center space-x-3">
                              <div
                                className="w-4 h-4 rounded-full border border-gray-200 flex-shrink-0"
                                style={{ backgroundColor: category.color }}
                              />
                              <div className="min-w-0">
                                <div className="font-medium truncate">{category.name}</div>
                                {category.slug && (
                                  <div className="text-xs text-muted-foreground truncate">/{category.slug}</div>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">
                            <div className="max-w-[200px] lg:max-w-[300px] truncate" title={category.description}>
                              {category.description}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={category.is_active ? "default" : "secondary"} className="text-xs">
                              {category.is_active ? "Active" : "Inactive"}
                            </Badge>
                          </TableCell>
                          <TableCell className="hidden lg:table-cell">
                            <div className="text-sm">
                              <div>{(category.products_count || 0)} products</div>
                              <div className="text-muted-foreground">{(category.avatars_count || 0)} avatars</div>
                            </div>
                          </TableCell>
                          <TableCell className="hidden lg:table-cell text-sm text-muted-foreground">
                            {new Date(category.created_at).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() => {
                                    setSelectedCategory(category)
                                    setIsEditDialogOpen(true)
                                  }}
                                >
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleDeleteCategory(category)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <Card>
            <CardContent className="pt-4 sm:pt-6 p-3 sm:p-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="text-xs sm:text-sm text-muted-foreground text-center sm:text-left">
                  {(() => {
                    const currentPage = pagination.currentPage || 1
                    const itemsPerPage = pagination.itemsPerPage || 10
                    const totalItems = pagination.totalItems || 0
                    const startItem = ((currentPage - 1) * itemsPerPage) + 1
                    const endItem = Math.min(currentPage * itemsPerPage, totalItems)

                    return `Showing ${startItem} to ${endItem} of ${totalItems} results`
                  })()}
                </div>
                <div className="flex items-center justify-center space-x-1 sm:space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                    disabled={pagination.currentPage <= 1}
                    className="px-2 sm:px-3"
                  >
                    <ChevronLeft className="h-4 w-4 sm:mr-1" />
                    <span className="hidden sm:inline">Previous</span>
                  </Button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(3, pagination.totalPages) }, (_, i) => {
                      const pageNumber = i + 1
                      return (
                        <Button
                          key={pageNumber}
                          variant={pagination.currentPage === pageNumber ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(pageNumber)}
                          className="w-8 h-8 p-0 text-xs sm:text-sm"
                        >
                          {pageNumber}
                        </Button>
                      )
                    })}
                    {pagination.totalPages > 3 && (
                      <>
                        <span className="text-muted-foreground text-xs sm:text-sm">...</span>
                        <Button
                          variant={pagination.currentPage === pagination.totalPages ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(pagination.totalPages)}
                          className="w-8 h-8 p-0 text-xs sm:text-sm"
                        >
                          {pagination.totalPages}
                        </Button>
                      </>
                    )}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                    disabled={pagination.currentPage >= pagination.totalPages}
                    className="px-2 sm:px-3"
                  >
                    <span className="hidden sm:inline">Next</span>
                    <ChevronRight className="h-4 w-4 sm:ml-1" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <CategoryCreateDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          onSubmit={handleCreateCategory}
        />

        <CategoryEditDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          category={selectedCategory}
          onSubmit={handleEditCategory}
        />
      </div>
    </AppLayout>
  )
}

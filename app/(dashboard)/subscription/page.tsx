"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { toast } from "@/hooks/use-toast";
import { Crown, RefreshCw, Settings } from "lucide-react";
import {
  getUserSubscriptionStatus,
  getAvailablePlans,
  updateUserSubscriptionWithCurrentInterval,
  checkoutUserSubscriptionWithInterval,
  getUserUsageData,
  type SubscriptionStatusData,
  type SubscriptionPlan,
  type UsageApiResponse,
} from "@/lib/api/user-subscriptions";
import { SubscriptionStatus } from "@/components/subscription/subscription-status";
import { PlansDisplay } from "@/components/subscription/plans-display";
import { PlanComparisonModal } from "@/components/subscription/plan-comparison-modal";
import { SubscriptionManagement } from "@/components/subscription/subscription-management";
import { UsageAnalytics } from "@/components/subscription/usage-analytics";
import { BillingHistory } from "@/components/subscription/billing-history";
import {
  SubscriptionNotifications,
  ActivePlanStatus,
} from "@/components/subscription/subscription-notifications";

export default function SubscriptionPage() {
  const { user, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [subscriptionData, setSubscriptionData] =
    useState<SubscriptionStatusData | null>(null);
  const [usageData, setUsageData] = useState<UsageApiResponse | null>(null);
  const [availablePlans, setAvailablePlans] = useState<SubscriptionPlan[]>([]);
  const [activeTab, setActiveTab] = useState("overview");
  const [showComparisonModal, setShowComparisonModal] = useState(false);
  const [showManagementModal, setShowManagementModal] = useState(false);
  const [comparisonPlans, setComparisonPlans] = useState<SubscriptionPlan[]>(
    []
  );
  const [isUpdating, setIsUpdating] = useState(false);
  const [subscriptionChangeData, setSubscriptionChangeData] =
    useState<any>(null);
  const [showNotification, setShowNotification] = useState(false);
  const [showActivePlanStatus, setShowActivePlanStatus] = useState(false);

  // Load usage data
  const loadUsageData = async () => {
    if (!user) return;

    try {
      const usageResponse = await getUserUsageData();
      if (usageResponse.success) {
        setUsageData(usageResponse.data);
      } else {
        setUsageData(null);
        console.error("Failed to load usage data:", usageResponse.message);
      }
    } catch (error) {
      console.error("Error loading usage data:", error);
      setUsageData(null);
    }
  };

  // Load subscription data
  const loadSubscriptionData = async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      // Load current subscription status, available plans, and usage data
      const [statusResponse, plansResponse] = await Promise.all([
        getUserSubscriptionStatus(),
        getAvailablePlans(),
      ]);

      if (statusResponse.success) {
        // Handle case where API returns success but data is null (no active subscription)
        setSubscriptionData(statusResponse.data);
      } else {
        setSubscriptionData(null);
      }
      if (plansResponse.success) {
        setAvailablePlans(plansResponse.data);
      } else {
      }

      // Load usage data separately
      await loadUsageData();
    } catch (error) {
      console.error("Error loading subscription data:", error);
      setSubscriptionData(null);
      toast({
        title: "Error",
        description:
          "Failed to load subscription information. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!authLoading && user) {
      loadSubscriptionData();
    }
  }, [user, authLoading]);

  // Listen for focus events to refresh data when user returns from payment
  useEffect(() => {
    const handleFocus = () => {
      // Refresh subscription data when user returns to the tab
      if (!authLoading && user) {
        loadSubscriptionData();
      }
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [user, authLoading]);

  // Show active plan status when subscription data loads
  useEffect(() => {
    if (subscriptionData && subscriptionData.subscription.status === "active") {
      setShowActivePlanStatus(true);
      // Auto-hide after 5 seconds
      const timer = setTimeout(() => {
        setShowActivePlanStatus(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [subscriptionData]);

  // Handle plan selection
  const handleSelectPlan = async (
    plan: SubscriptionPlan,
    billingCycle: "monthly" | "annual"
  ) => {
    setIsUpdating(true);
    try {
      let response;
      // Check for active subscription but exclude Trial Plan
      const hasActiveSubscription =
        subscriptionData !== null &&
        subscriptionData?.subscription?.status === "active" &&
        subscriptionData?.plan?.slug !== "trial";

      if (hasActiveSubscription) {
        // Use update API for existing active subscriptions
        response = await updateUserSubscriptionWithCurrentInterval(plan.id, billingCycle);

        if (response.success && response.data) {
          const { change_type, message } = response.data;

          // Store change data for notification
          setSubscriptionChangeData(response.data);

          // Enhanced success message based on change type
          const successTitle =
            change_type === "upgrade" ? "Plan Upgraded!" : "Plan Updated!";
          const successDescription =
            message || `Successfully ${change_type}d to ${plan.name} plan.`;

          toast({
            title: successTitle,
            description: successDescription,
          });

          // Show notification
          setShowNotification(true);

          // Show additional information for downgrades
          if (change_type === "downgrade") {
            setTimeout(() => {
              toast({
                title: "Important Notice",
                description:
                  "Some features may no longer be available. You'll retain access to current usage until the next billing cycle.",
                variant: "default",
              });
            }, 2000);
          }

          // If payment is required, show payment notice
          if (response.requires_payment) {
            toast({
              title: "Payment Required",
              description:
                "Please complete the payment to activate your new plan.",
              variant: "default",
            });
          }

          await loadSubscriptionData(); // Refresh data
          setShowActivePlanStatus(true); // Show active plan status
        } else {
          throw new Error(response.message || "Failed to update subscription");
        }
      } else {
        // Use checkout API for new subscriptions (no active subscription)
        response = await checkoutUserSubscriptionWithInterval(plan.id, billingCycle);

        if (response.success && response.url) {
          // Open Stripe checkout URL in new tab
          window.open(response.url, "_blank");

          toast({
            title: "Redirecting to Payment",
            description: `Opening checkout for ${plan.name} plan...`,
          });

          // Don't refresh data here as payment is not completed yet
          // Data will be refreshed when user returns from successful payment
        } else {
          throw new Error(
            response.message || "Failed to create checkout session"
          );
        }
      }
    } catch (error) {
      console.error("Error processing subscription:", error);
      toast({
        title: "Subscription Failed",
        description:
          error instanceof Error
            ? error.message
            : "Failed to process subscription. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle plan comparison
  const handleComparePlans = (plans: SubscriptionPlan[]) => {
    setComparisonPlans(plans);
    setShowComparisonModal(true);
  };

  // Show loading while checking authentication
  if (authLoading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading subscription information...</p>
        </div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (!user) {
    router.push("/auth/login");
    return null;
  }

  const currentPlan = subscriptionData?.plan;

  return (
    <div className="space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">My Subscription</h1>
          <p className="text-muted-foreground">
            Manage your subscription plan and monitor usage
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {currentPlan && (
            <Badge variant="outline" className="flex items-center space-x-1">
              <Crown className="h-3 w-3" />
              <span>{currentPlan.name}</span>
            </Badge>
          )}
          {subscriptionData && (
            <Button
              variant="outline"
              onClick={() => setShowManagementModal(true)}
              disabled={isUpdating}
            >
              <Settings className="h-4 w-4 mr-2" />
              Manage
            </Button>
          )}
        </div>
      </div>

      {/* Active Plan Status */}
      {subscriptionData &&
        subscriptionData.subscription.status === "active" && (
          <ActivePlanStatus
            planName={currentPlan?.name || "Current Plan"}
            status={subscriptionData.subscription.status}
            currentPeriodEnd={
              subscriptionData.subscription.current_period_end?.toString() || ""
            }
            isVisible={showActivePlanStatus}
          />
        )}

      {/* Subscription Change Notifications */}
      <SubscriptionNotifications
        changeData={subscriptionChangeData}
        planName={currentPlan?.name}
        isVisible={showNotification}
        onDismiss={() => setShowNotification(false)}
      />

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="plans">Plans</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <SubscriptionStatus
            data={subscriptionData}
            usageData={usageData}
            onManageSubscription={() => setShowManagementModal(true)}
            onViewBilling={() => setActiveTab("billing")}
            onUpgrade={() => setActiveTab("plans")}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="usage">
          <UsageAnalytics
            data={subscriptionData}
            usageData={usageData}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="plans">
          {(() => {
            // Check for active subscription but exclude Trial Plan
            const hasActiveSubscription =
              subscriptionData !== null &&
              subscriptionData?.subscription?.status === "active" &&
              subscriptionData?.plan?.slug !== "trial";
            return (
              <PlansDisplay
                plans={availablePlans.filter((plan) => plan.slug !== "trial")}
                currentPlanId={currentPlan?.id}
                hasActiveSubscription={hasActiveSubscription}
                onSelectPlan={handleSelectPlan}
                onComparePlans={handleComparePlans}
                isLoading={isLoading || isUpdating}
              />
            );
          })()}
        </TabsContent>

        <TabsContent value="billing">
          <BillingHistory isLoading={isLoading} />
        </TabsContent>
      </Tabs>

      {/* Modals */}
      <PlanComparisonModal
        open={showComparisonModal}
        onOpenChange={setShowComparisonModal}
        plans={comparisonPlans.filter((plan) => plan.slug !== "trial")}
        currentPlanId={currentPlan?.id}
        hasActiveSubscription={
          subscriptionData !== null &&
          subscriptionData?.subscription?.status === "active" &&
          subscriptionData?.plan?.slug !== "trial"
        }
        isLoading={isUpdating}
        onSelectPlan={handleSelectPlan}
      />

      <SubscriptionManagement
        open={showManagementModal}
        onOpenChange={setShowManagementModal}
        currentSubscription={subscriptionData}
        availablePlans={availablePlans.filter((plan) => plan.slug !== "trial")}
        onSubscriptionUpdated={loadSubscriptionData}
      />
    </div>
  );
}

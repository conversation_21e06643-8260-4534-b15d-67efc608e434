"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { useToast } from "@/hooks/use-toast"
import { adminService, User } from "@/lib/api/admin"
import { useAuth } from "@/contexts/auth-context"
import {
  Search,
  UserCheck,
  Trash2,
  RefreshCw,
  Users,
  Shield,
  CheckCircle,
  Clock,
  Eye,
  MoreHorizontal,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export default function UserManagementPage() {
  const { toast } = useToast()
  const { user, isLoading: authLoading } = useAuth()
  const router = useRouter()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [verifyingUserId, setVerifyingUserId] = useState<string | null>(null)
  const [deletingUserId, setDeletingUserId] = useState<string | null>(null)

  // Check if user has admin access
  useEffect(() => {
    if (!authLoading && user) {
      if (user.role !== 'admin') {
        toast({
          title: "Access Denied",
          description: "You don't have permission to access this page.",
          variant: "destructive",
        })
        router.push('/dashboard')
        return
      }
    }
  }, [user, authLoading, router, toast])

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Checking permissions...</p>
        </div>
      </div>
    )
  }

  // Don't render if user is not admin (will be redirected)
  if (!user || user.role !== 'admin') {
    return null
  }

  const itemsPerPage = 10

  // Calculate statistics
  const stats = {
    total: users.length,
    verified: users.filter(u => u.is_verified).length,
    unverified: users.filter(u => !u.is_verified).length,
    admins: users.filter(u => u.role === 'admin').length,
    moderators: users.filter(u => u.role === 'moderator').length,
    regularUsers: users.filter(u => u.role === 'user').length,
  }

  // Fetch users
  const fetchUsers = async (page: number = 1, search: string = "") => {
    setLoading(true)
    try {
      const response = await adminService.getUsers({
        page,
        limit: itemsPerPage,
        search: search.trim() || undefined,
      })

      setUsers(response.data)
      setCurrentPage(response.pagination.page)
      setTotalPages(response.pagination.totalPages)
      setTotalItems(response.pagination.totalItems)
    } catch (error) {
      console.error("Failed to fetch users:", error)
      toast({
        title: "Error",
        description: "Failed to fetch users. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
    fetchUsers(1, query)
  }

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchUsers(page, searchQuery)
  }

  // Verify user
  const handleVerifyUser = async (userId: string, userName: string) => {
    setVerifyingUserId(userId)
    try {
      await adminService.verifyUser(userId)

      toast({
        title: "User Verified",
        description: `${userName} has been successfully verified.`,
      })

      // Refresh the users list
      fetchUsers(currentPage, searchQuery)
    } catch (error) {
      console.error("Failed to verify user:", error)
      toast({
        title: "Error",
        description: "Failed to verify user. Please try again.",
        variant: "destructive",
      })
    } finally {
      setVerifyingUserId(null)
    }
  }

  // Delete user
  const handleDeleteUser = async (userId: string, userName: string) => {
    setDeletingUserId(userId)
    try {
      await adminService.deleteUser(userId)

      toast({
        title: "User Deleted",
        description: `${userName} has been successfully deleted.`,
      })

      // Refresh the users list
      fetchUsers(currentPage, searchQuery)
    } catch (error) {
      console.error("Failed to delete user:", error)
      toast({
        title: "Error",
        description: "Failed to delete user. Please try again.",
        variant: "destructive",
      })
    } finally {
      setDeletingUserId(null)
    }
  }

  // Get status badge
  const getStatusBadge = (user: User) => {
    if (user.is_verified) {
      return <Badge variant="default">Verified</Badge>
    }
    return <Badge variant="secondary">Unverified</Badge>
  }

  // Get user initials safely
  const getUserInitials = (user: User) => {
    if (user?.first_name && user?.last_name) {
      return (user.first_name.charAt(0) + user.last_name.charAt(0)).toUpperCase()
    }
    return user?.email?.charAt(0).toUpperCase() || 'U'
  }

  // Get full name safely
  const getFullName = (user: User) => {
    if (user?.first_name && user?.last_name) {
      return `${user.first_name} ${user.last_name}`
    }
    return user?.email || 'User'
  }

  // Get role badge
  const getRoleBadge = (role: string) => {
    const variants = {
      admin: "default",
      staff: "secondary",
      user: "outline",
    } as const

    return (
      <Badge variant={variants[role as keyof typeof variants] || "outline"}>
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </Badge>
    )
  }

  useEffect(() => {
    fetchUsers()
  }, [])

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 lg:p-8 pt-4 sm:pt-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">User Management</h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            Manage users, verify accounts, and control access permissions.
          </p>
        </div>
        <Button onClick={() => fetchUsers(currentPage, searchQuery)} disabled={loading} className="w-full sm:w-auto">
          <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
          Refresh
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalItems}</div>
            <p className="text-xs text-muted-foreground">
              {stats.verified} verified, {stats.unverified} unverified
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Verified Users</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.verified}</div>
            <p className="text-xs text-muted-foreground">
              {totalItems > 0 ? Math.round((stats.verified / totalItems) * 100) : 0}% of total users
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Verification</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.unverified}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting manual verification
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Administrators</CardTitle>
            <Shield className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.admins}</div>
            <p className="text-xs text-muted-foreground">
              {stats.moderators} moderators, {stats.regularUsers} users
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Users ({totalItems})</span>
          </CardTitle>
          <CardDescription className="text-sm">
            View and manage all registered users in the system.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-3 sm:p-6">
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users by name or email..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading users...</span>
            </div>
          ) : (
            <>
              {users.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="mx-auto h-12 w-12 text-muted-foreground" />
                  <p className="mt-2 text-muted-foreground">
                    {searchQuery ? "No users found matching your search." : "No users found."}
                  </p>
                </div>
              ) : (
                <div className="rounded-md border overflow-hidden">
                  {/* Mobile Card View */}
                  <div className="block sm:hidden">
                    {users.map((user) => (
                      <div key={user.id} className="border-b last:border-b-0 p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center space-x-3 flex-1 min-w-0">
                            <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center flex-shrink-0">
                              <span className="text-sm font-medium">
                                {getUserInitials(user)}
                              </span>
                            </div>
                            <div className="min-w-0 flex-1">
                              <div className="font-medium text-sm truncate">{getFullName(user)}</div>
                              <div className="text-xs text-muted-foreground truncate">{user.email}</div>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0 flex-shrink-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {!user.is_verified && (
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                      <UserCheck className="mr-2 h-4 w-4" />
                                      Verify User
                                    </DropdownMenuItem>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Manually Verify User</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Are you sure you want to verify {getFullName(user)}?
                                        This will mark their account as verified and grant them full access.
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() => handleVerifyUser(user.id, getFullName(user))}
                                        disabled={verifyingUserId === user.id}
                                      >
                                        {verifyingUserId === user.id ? (
                                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                        ) : (
                                          <UserCheck className="mr-2 h-4 w-4" />
                                        )}
                                        Verify User
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              )}
                              {user.role !== 'admin' && (
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <DropdownMenuItem
                                      onSelect={(e) => e.preventDefault()}
                                      className="text-destructive focus:text-destructive"
                                    >
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      Delete User
                                    </DropdownMenuItem>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Delete User</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Are you sure you want to delete {getFullName(user)}?
                                        This action cannot be undone and will permanently remove all user data.
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() => handleDeleteUser(user.id, getFullName(user))}
                                        disabled={deletingUserId === user.id}
                                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                      >
                                        {deletingUserId === user.id ? (
                                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                        ) : (
                                          <Trash2 className="mr-2 h-4 w-4" />
                                        )}
                                        Delete User
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        <div className="space-y-2 text-sm">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              {getRoleBadge(user.role)}
                              {getStatusBadge(user)}
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="text-xs">
                              {user.role === 'admin' || user.role === 'staff' ? (
                                <div className="flex items-center space-x-1">
                                  <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                                  <span className="font-medium text-blue-600">
                                    {user.role === 'admin' ? 'Admin' : 'Staff'}
                                  </span>
                                </div>
                              ) : user.subscriptionPlan ? (
                                <div className="flex items-center space-x-1">
                                  <div className="h-2 w-2 rounded-full bg-green-500"></div>
                                  <span className="font-medium">{user.subscriptionPlan.name}</span>
                                </div>
                              ) : (
                                <div className="flex items-center space-x-1">
                                  <div className="h-2 w-2 rounded-full bg-gray-400"></div>
                                  <span className="text-muted-foreground">No Plan</span>
                                </div>
                              )}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Joined: {new Date(user.created_at).toLocaleDateString()}
                            </div>
                          </div>

                          <div className="flex justify-between text-xs">
                            <span className="text-muted-foreground">ID: {user.id.slice(0, 8)}...</span>
                            <span className="text-muted-foreground">
                              Last login: {user.last_login ? new Date(user.last_login).toLocaleDateString() : "Never"}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Desktop Table View */}
                  <div className="hidden sm:block overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="min-w-[200px]">User</TableHead>
                          <TableHead className="min-w-[200px] hidden md:table-cell">Email</TableHead>
                          <TableHead className="min-w-[80px]">Role</TableHead>
                          <TableHead className="min-w-[120px] hidden lg:table-cell">Plan</TableHead>
                          <TableHead className="min-w-[80px]">Status</TableHead>
                          <TableHead className="min-w-[100px] hidden lg:table-cell">Joined</TableHead>
                          <TableHead className="min-w-[100px] hidden xl:table-cell">Last Login</TableHead>
                          <TableHead className="w-[50px]">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {users.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center space-x-3">
                                <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                                  <span className="text-sm font-medium">
                                    {getUserInitials(user)}
                                  </span>
                                </div>
                                <div>
                                  <div className="font-medium">
                                    {getFullName(user)}
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    ID: {user.id.slice(0, 8)}...
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="hidden md:table-cell">
                              <div className="max-w-[200px] truncate" title={user.email}>
                                {user.email}
                              </div>
                            </TableCell>
                            <TableCell>{getRoleBadge(user.role)}</TableCell>
                            <TableCell className="hidden lg:table-cell">
                              {user.role === 'admin' || user.role === 'staff' ? (
                                <div className="flex items-center space-x-2">
                                  <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                                  <span className="text-sm font-medium text-blue-600">
                                    {user.role === 'admin' ? 'Admin' : 'Staff'}
                                  </span>
                                </div>
                              ) : user.subscriptionPlan ? (
                                <div className="flex items-center space-x-2">
                                  <div className="h-2 w-2 rounded-full bg-green-500"></div>
                                  <span className="text-sm font-medium">{user.subscriptionPlan.name}</span>
                                </div>
                              ) : (
                                <div className="flex items-center space-x-2">
                                  <div className="h-2 w-2 rounded-full bg-gray-400"></div>
                                  <span className="text-sm text-muted-foreground">No Plan</span>
                                </div>
                              )}
                            </TableCell>
                            <TableCell>{getStatusBadge(user)}</TableCell>
                            <TableCell className="hidden lg:table-cell text-sm text-muted-foreground">
                              {new Date(user.created_at).toLocaleDateString()}
                            </TableCell>
                            <TableCell className="hidden xl:table-cell text-sm text-muted-foreground">
                              {user.last_login
                                ? new Date(user.last_login).toLocaleDateString()
                                : "Never"
                              }
                            </TableCell>
                            <TableCell>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" className="h-8 w-8 p-0">
                                    <span className="sr-only">Open menu</span>
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  {!user.is_verified && (
                                    <AlertDialog>
                                      <AlertDialogTrigger asChild>
                                        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                          <UserCheck className="mr-2 h-4 w-4" />
                                          Verify User
                                        </DropdownMenuItem>
                                      </AlertDialogTrigger>
                                      <AlertDialogContent>
                                        <AlertDialogHeader>
                                          <AlertDialogTitle>Manually Verify User</AlertDialogTitle>
                                          <AlertDialogDescription>
                                            Are you sure you want to verify {getFullName(user)}?
                                            This will mark their account as verified and grant them full access.
                                          </AlertDialogDescription>
                                        </AlertDialogHeader>
                                        <AlertDialogFooter>
                                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                                          <AlertDialogAction
                                            onClick={() => handleVerifyUser(user.id, getFullName(user))}
                                            disabled={verifyingUserId === user.id}
                                          >
                                            {verifyingUserId === user.id ? (
                                              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                            ) : (
                                              <UserCheck className="mr-2 h-4 w-4" />
                                            )}
                                            Verify User
                                          </AlertDialogAction>
                                        </AlertDialogFooter>
                                      </AlertDialogContent>
                                    </AlertDialog>
                                  )}
                                  {user.role !== 'admin' && (
                                    <AlertDialog>
                                      <AlertDialogTrigger asChild>
                                        <DropdownMenuItem
                                          onSelect={(e) => e.preventDefault()}
                                          className="text-destructive focus:text-destructive"
                                        >
                                          <Trash2 className="mr-2 h-4 w-4" />
                                          Delete User
                                        </DropdownMenuItem>
                                      </AlertDialogTrigger>
                                      <AlertDialogContent>
                                        <AlertDialogHeader>
                                          <AlertDialogTitle>Delete User</AlertDialogTitle>
                                          <AlertDialogDescription>
                                            Are you sure you want to delete {getFullName(user)}?
                                            This action cannot be undone and will permanently remove all user data.
                                          </AlertDialogDescription>
                                        </AlertDialogHeader>
                                        <AlertDialogFooter>
                                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                                          <AlertDialogAction
                                            onClick={() => handleDeleteUser(user.id, getFullName(user))}
                                            disabled={deletingUserId === user.id}
                                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                          >
                                            {deletingUserId === user.id ? (
                                              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                            ) : (
                                              <Trash2 className="mr-2 h-4 w-4" />
                                            )}
                                            Delete User
                                          </AlertDialogAction>
                                        </AlertDialogFooter>
                                      </AlertDialogContent>
                                    </AlertDialog>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}

              {totalPages > 1 && (
                <div className="mt-4 flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} users
                  </div>
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => handlePageChange(currentPage - 1)}
                          className={currentPage <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>

                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const page = i + 1
                        return (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => handlePageChange(page)}
                              isActive={currentPage === page}
                              className="cursor-pointer"
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        )
                      })}

                      {totalPages > 5 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => handlePageChange(currentPage + 1)}
                          className={currentPage >= totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

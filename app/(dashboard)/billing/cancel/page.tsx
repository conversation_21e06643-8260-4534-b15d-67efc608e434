"use client"

import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  XCircle,
  ArrowLeft,
  RefreshCw,
  Crown,
} from "lucide-react"

export default function BillingCancelPage() {
  const router = useRouter()

  return (
    <div className="container mx-auto p-6">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader className="text-center">
            <XCircle className="h-16 w-16 mx-auto mb-4 text-orange-600" />
            <CardTitle className="text-2xl">Payment Cancelled</CardTitle>
            <CardDescription>
              Your payment was cancelled. No charges were made to your account.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <Alert>
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                You can try again anytime or choose a different plan that better suits your needs.
              </AlertDescription>
            </Alert>
            
            <div className="flex space-x-4 justify-center">
              <Button onClick={() => router.push('/subscription')} className="flex-1">
                <Crown className="h-4 w-4 mr-2" />
                View Plans
              </Button>
              <Button variant="outline" onClick={() => router.push('/dashboard')} className="flex-1">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  CheckCircle,
  RefreshCw,
  AlertTriangle,
  Crown,
  ArrowLeft,
  CreditCard,
  XCircle,
  Clock,
  Zap,
  Shield,
  Mail,
  Calendar,
  User,
  Receipt,
  ExternalLink,
  Copy,
  CheckCheck,
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { checkPaymentStatus } from "@/lib/api/user-subscriptions";

interface PaymentStatusResponse {
  success: boolean;
  message: string;
  data: {
    order: {
      id: string;
      status: string;
      amount_total: number;
      amount_total_formatted: string;
      currency: string;
      created_at: string;
      updated_at: string;
      plan: {
        id: string;
        name: string;
        slug: string;
      };
      user: {
        id: string;
        email: string;
        first_name: string;
        last_name: string;
      };
      transactions: Array<{
        id: string;
        type: string;
        status: string;
        amount: number;
        amount_formatted: string;
        processed_at: string;
      }>;
    };
    stripe_data: {
      session: {
        id: string;
        payment_status: string;
        status: string;
        mode: string;
        url: string | null;
      };
      payment_intent: any;
      subscription: {
        id: string;
        status: string;
        current_period_start: number;
        current_period_end: number;
      };
    };
    sync_results: {
      order_updated: boolean;
      transaction_created: boolean;
      subscription_created: boolean;
      email_sent: boolean;
      previous_status: string;
      new_status: string;
      stripe_status: string;
    };
  };
}

// Payment state types for better UX
type PaymentState =
  | "paid"
  | "unpaid"
  | "canceled"
  | "failed"
  | "processing"
  | "pending";

// Helper function to determine payment state
const getPaymentState = (data: PaymentStatusResponse["data"]): PaymentState => {
  const orderStatus = data.order.status.toLowerCase();
  const sessionStatus = data.stripe_data.session.status.toLowerCase();
  const paymentStatus = data.stripe_data.session.payment_status.toLowerCase();
  const subscriptionStatus = data.stripe_data.subscription.status.toLowerCase();

  // Check for explicit failure states
  if (orderStatus === "failed" || paymentStatus === "failed") return "failed";
  if (orderStatus === "canceled" || sessionStatus === "canceled")
    return "canceled";

  // Check for success state
  if (
    orderStatus === "paid" &&
    paymentStatus === "paid" &&
    sessionStatus === "complete"
  ) {
    return "paid";
  }

  // Check for processing states
  if (paymentStatus === "processing" || sessionStatus === "open")
    return "processing";
  if (paymentStatus === "unpaid" || orderStatus === "pending") return "unpaid";

  // Default to processing for unknown states
  return "processing";
};

// State configuration for different payment states
const getStateConfig = (state: PaymentState) => {
  const configs = {
    paid: {
      icon: CheckCircle,
      iconColor: "text-green-500",
      bgColor: "bg-gradient-to-br from-green-50 to-emerald-50",
      borderColor: "border-green-200",
      title: "Payment Successful!",
      subtitle: "Your subscription has been activated",
      primaryAction: "View Subscription",
      showCelebration: true,
    },
    unpaid: {
      icon: Clock,
      iconColor: "text-amber-500",
      bgColor: "bg-gradient-to-br from-amber-50 to-yellow-50",
      borderColor: "border-amber-200",
      title: "Payment Pending",
      subtitle: "We're processing your payment",
      primaryAction: "Check Status",
      showCelebration: false,
    },
    canceled: {
      icon: XCircle,
      iconColor: "text-gray-500",
      bgColor: "bg-gradient-to-br from-gray-50 to-slate-50",
      borderColor: "border-gray-200",
      title: "Payment Canceled",
      subtitle: "Your payment was canceled",
      primaryAction: "Try Again",
      showCelebration: false,
    },
    failed: {
      icon: AlertTriangle,
      iconColor: "text-red-500",
      bgColor: "bg-gradient-to-br from-red-50 to-rose-50",
      borderColor: "border-red-200",
      title: "Payment Failed",
      subtitle: "There was an issue processing your payment",
      primaryAction: "Try Again",
      showCelebration: false,
    },
    processing: {
      icon: RefreshCw,
      iconColor: "text-blue-500",
      bgColor: "bg-gradient-to-br from-blue-50 to-indigo-50",
      borderColor: "border-blue-200",
      title: "Processing Payment",
      subtitle: "Please wait while we confirm your payment",
      primaryAction: "Refresh Status",
      showCelebration: false,
    },
    pending: {
      icon: Clock,
      iconColor: "text-orange-500",
      bgColor: "bg-gradient-to-br from-orange-50 to-amber-50",
      borderColor: "border-orange-200",
      title: "Payment Pending",
      subtitle: "Your payment is being reviewed",
      primaryAction: "Check Status",
      showCelebration: false,
    },
  };

  return configs[state];
};

export default function BillingSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get("order_id");

  const [isLoading, setIsLoading] = useState(true);
  const [paymentData, setPaymentData] = useState<PaymentStatusResponse | null>(
    null
  );
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  // Copy to clipboard function
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      toast({
        title: "Copied!",
        description: "Order ID copied to clipboard",
      });
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy: ", err);
    }
  };

  useEffect(() => {
    const verifyPayment = async () => {
      if (!orderId) {
        setError("No order ID provided");
        setIsLoading(false);
        return;
      }

      try {
        const response = await checkPaymentStatus(orderId);

        if (response.success) {
          setPaymentData(response as PaymentStatusResponse);

          // Check if payment is actually successful
          const isSuccess =
            response.data &&
            response.data.order.status === "paid" &&
            response.data.stripe_data.session.payment_status === "paid" &&
            response.data.stripe_data.session.status === "complete";

          if (isSuccess) {
            toast({
              title: "Payment Successful!",
              description: "Your subscription has been activated successfully!",
            });
          } else {
            toast({
              title: "Payment Processing",
              description: "Your payment is being processed. Please wait...",
              variant: "default",
            });
          }
        } else {
          setError(response.message || "Failed to verify payment status");
        }
      } catch (err) {
        console.error("Error checking payment status:", err);
        setError("Failed to verify payment status");
      } finally {
        setIsLoading(false);
      }
    };

    verifyPayment();
  }, [orderId]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-lg mx-auto p-8">
          <Card className="border-0 shadow-2xl bg-white/95 backdrop-blur-xl rounded-3xl">
            <CardHeader className="text-center pb-8 pt-12">
              <div className="flex justify-center mb-6">
                <div className="relative">
                  <div className="w-20 h-20 border-4 border-blue-200 rounded-full"></div>
                  <div className="absolute top-0 left-0 w-20 h-20 border-4 border-blue-600 rounded-full animate-spin border-t-transparent"></div>
                </div>
              </div>
              <CardTitle className="text-3xl font-bold text-gray-900 mb-4">
                Verifying Payment
              </CardTitle>
              <CardDescription className="text-lg text-gray-600 leading-relaxed">
                Please wait while we confirm your payment and activate your
                subscription...
              </CardDescription>
            </CardHeader>
            <CardContent className="pb-12">
              <div className="flex justify-center">
                <div className="flex space-x-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce delay-100"></div>
                  <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce delay-200"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-red-50 to-rose-100 flex items-center justify-center">
        <div className="max-w-2xl mx-auto p-8">
          <Card className="border-0 shadow-2xl bg-white/95 backdrop-blur-xl rounded-3xl">
            <CardHeader className="text-center pb-6 pt-12">
              <div className="flex justify-center mb-6">
                <div className="p-6 rounded-full bg-red-100 shadow-lg">
                  <AlertTriangle className="h-16 w-16 text-red-600" />
                </div>
              </div>
              <CardTitle className="text-3xl font-bold text-gray-900 mb-4">
                Payment Verification Failed
              </CardTitle>
              <CardDescription className="text-lg text-gray-600 leading-relaxed">
                We encountered an issue while verifying your payment. This might
                be temporary.
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-8 px-8 pb-12">
              <Alert variant="destructive" className="border-red-200 bg-red-50">
                <AlertTriangle className="h-5 w-5" />
                <AlertDescription className="text-left font-medium">
                  {error}
                </AlertDescription>
              </Alert>
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 justify-center">
                <Button
                  onClick={() => router.push("/subscription")}
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <ArrowLeft className="h-5 w-5 mr-2" />
                  Back to Subscription
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                  className="border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <RefreshCw className="h-5 w-5 mr-2" />
                  Try Again
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!paymentData) {
    return null;
  }

  const { order, stripe_data, sync_results } = paymentData.data;
  const paymentState = getPaymentState(paymentData.data);
  const stateConfig = getStateConfig(paymentState);
  const IconComponent = stateConfig.icon;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div
          className="absolute top-0 left-0 w-full h-full"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        ></div>
      </div>

      <div className="container mx-auto px-4 py-12 relative z-10">
        <div className="max-w-5xl mx-auto">
          {/* Hero Section with Enhanced Design */}
          <div
            className={`relative overflow-hidden rounded-3xl border-2 ${stateConfig.borderColor} ${stateConfig.bgColor} p-12 mb-12 shadow-2xl backdrop-blur-sm`}
          >
            {/* Enhanced Celebration Animation for Success */}
            {stateConfig.showCelebration && (
              <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute -top-8 -left-8 w-32 h-32 bg-gradient-to-br from-green-200 to-emerald-200 rounded-full opacity-20 animate-pulse"></div>
                <div className="absolute -bottom-8 -right-8 w-40 h-40 bg-gradient-to-br from-emerald-200 to-green-200 rounded-full opacity-20 animate-pulse delay-300"></div>
                <div className="absolute top-1/2 left-1/4 w-20 h-20 bg-gradient-to-br from-green-300 to-emerald-300 rounded-full opacity-15 animate-bounce delay-500"></div>
                <div className="absolute bottom-1/4 right-1/3 w-16 h-16 bg-gradient-to-br from-emerald-300 to-green-400 rounded-full opacity-10 animate-pulse delay-700"></div>

                {/* Floating particles */}
                <div className="absolute top-20 left-20 w-2 h-2 bg-green-400 rounded-full opacity-40 animate-ping"></div>
                <div className="absolute top-40 right-32 w-2 h-2 bg-emerald-400 rounded-full opacity-40 animate-ping delay-200"></div>
                <div className="absolute bottom-32 left-40 w-2 h-2 bg-green-500 rounded-full opacity-40 animate-ping delay-400"></div>
              </div>
            )}

            <div className="relative z-10 text-center">
              <div className="flex justify-center mb-8">
                <div
                  className={`relative p-6 rounded-full bg-white shadow-2xl ring-4 ring-white/50 ${
                    paymentState === "processing"
                      ? "animate-spin"
                      : paymentState === "paid"
                      ? "animate-bounce"
                      : "animate-pulse"
                  }`}
                >
                  <IconComponent
                    className={`h-16 w-16 ${stateConfig.iconColor}`}
                  />
                  {paymentState === "paid" && (
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <CheckCircle className="h-4 w-4 text-white" />
                    </div>
                  )}
                </div>
              </div>

              <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent mb-4 leading-tight">
                {stateConfig.title}
              </h1>

              <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                {stateConfig.subtitle}
              </p>

              {/* Enhanced Order Summary */}
              <div className="inline-flex items-center space-x-6 bg-white/90 backdrop-blur-lg rounded-3xl px-8 py-6 shadow-2xl border border-white/20">
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-full bg-gradient-to-br from-amber-100 to-amber-200">
                    <Crown className="h-6 w-6 text-amber-600" />
                  </div>
                  <div className="text-left">
                    <div className="text-sm text-gray-500 font-medium">
                      Plan
                    </div>
                    <div className="font-bold text-gray-900 text-lg">
                      {order.plan.name}
                    </div>
                  </div>
                </div>
                <div className="w-px h-12 bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-full bg-gradient-to-br from-blue-100 to-blue-200">
                    <CreditCard className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="text-left">
                    <div className="text-sm text-gray-500 font-medium">
                      Amount
                    </div>
                    <div className="font-bold text-gray-900 text-2xl">
                      {order.amount_total_formatted}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Status Progress Indicator */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12 justify-center items-stretch">
            <div
              className={`group relative p-8 rounded-3xl border-2 w-full max-w-2xl mx-auto transition-all duration-500 hover:scale-105 transform ${
                paymentState === "paid"
                  ? "border-green-300 bg-gradient-to-br from-green-50 to-emerald-50 shadow-lg shadow-green-100"
                  : "border-gray-200 bg-white shadow-md hover:shadow-lg"
              }`}
            >
              <div className="flex items-center space-x-4">
                <div
                  className={`relative p-4 rounded-2xl transition-all duration-300 ${
                    paymentState === "paid"
                      ? "bg-green-100 shadow-lg shadow-green-200/50"
                      : "bg-gray-100 group-hover:bg-gray-200"
                  }`}
                >
                  <CheckCircle
                    className={`h-8 w-8 transition-colors duration-300 ${
                      paymentState === "paid"
                        ? "text-green-600"
                        : "text-gray-400"
                    }`}
                  />
                  {paymentState === "paid" && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  )}
                </div>
                <div>
                  <div className="font-bold text-lg text-gray-900">Payment</div>
                  <div
                    className={`text-sm font-medium capitalize ${
                      paymentState === "paid"
                        ? "text-green-600"
                        : "text-gray-500"
                    }`}
                  >
                    {order.status}
                  </div>
                </div>
              </div>
              {paymentState === "paid" && (
                <div className="absolute top-4 right-4">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-ping"></div>
                </div>
              )}
            </div>

            <div
              className={`group relative p-8 rounded-3xl border-2 w-full max-w-2xl mx-auto transition-all duration-500 hover:scale-105 transform ${
                stripe_data.subscription.status === "active"
                  ? "border-blue-300 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-lg shadow-blue-100"
                  : "border-gray-200 bg-white shadow-md hover:shadow-lg"
              }`}
            >
              <div className="flex items-center space-x-4">
                <div
                  className={`relative p-4 rounded-2xl transition-all duration-300 ${
                    stripe_data.subscription.status === "active"
                      ? "bg-blue-100 shadow-lg shadow-blue-200/50"
                      : "bg-gray-100 group-hover:bg-gray-200"
                  }`}
                >
                  <Zap
                    className={`h-8 w-8 transition-colors duration-300 ${
                      stripe_data.subscription.status === "active"
                        ? "text-blue-600"
                        : "text-gray-400"
                    }`}
                  />
                  {stripe_data.subscription.status === "active" && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                  )}
                </div>
                <div>
                  <div className="font-bold text-lg text-gray-900">
                    Subscription
                  </div>
                  <div
                    className={`text-sm font-medium capitalize ${
                      stripe_data.subscription.status === "active"
                        ? "text-blue-600"
                        : "text-gray-500"
                    }`}
                  >
                    {stripe_data.subscription.status}
                  </div>
                </div>
              </div>
              {stripe_data.subscription.status === "active" && (
                <div className="absolute top-4 right-4">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-ping"></div>
                </div>
              )}
            </div>
          </div>

          {/* Enhanced Main Content Grid */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 mb-12">
            {/* Order Details Card */}
            <Card className="border-0 shadow-2xl bg-white/95 backdrop-blur-xl rounded-3xl overflow-hidden group hover:shadow-3xl transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <CardHeader className="relative pb-6 pt-8 px-8">
                <CardTitle className="flex items-center space-x-3 text-2xl">
                  <div className="p-3 rounded-2xl bg-gradient-to-br from-blue-100 to-indigo-100">
                    <Receipt className="h-7 w-7 text-blue-600" />
                  </div>
                  <span className="bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    Order Details
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="relative space-y-8 px-8 pb-8">
                {/* Order ID with Copy Function */}
                <div className="flex items-center justify-between p-6 bg-gradient-to-r from-gray-50 to-blue-50/50 rounded-2xl border border-gray-100 hover:shadow-md transition-all duration-300">
                  <div>
                    <div className="text-sm font-semibold text-gray-600 mb-1">
                      Order ID
                    </div>
                    <div className="font-mono text-lg font-bold text-gray-900 tracking-wider">
                      #{order.id.slice(-8)}
                    </div>
                    <div className="text-xs text-gray-500 font-mono mt-1">
                      {order.id}
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(order.id)}
                    className="h-12 w-12 p-0 rounded-xl hover:bg-white hover:shadow-lg transition-all duration-300"
                  >
                    {copied ? (
                      <CheckCheck className="h-5 w-5 text-green-600" />
                    ) : (
                      <Copy className="h-5 w-5 text-gray-500" />
                    )}
                  </Button>
                </div>

                {/* Customer Info */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-xl bg-gradient-to-br from-emerald-100 to-green-100">
                      <User className="h-5 w-5 text-emerald-600" />
                    </div>
                    <span className="text-lg font-semibold text-gray-700">
                      Customer Information
                    </span>
                  </div>
                  <div className="pl-12 space-y-2">
                    <div className="text-xl font-bold text-gray-900">
                      {order.user.first_name} {order.user.last_name}
                    </div>
                    <div className="text-gray-600 font-medium">
                      {order.user.email}
                    </div>
                  </div>
                </div>

                {/* Order Date */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-xl bg-gradient-to-br from-purple-100 to-pink-100">
                      <Calendar className="h-5 w-5 text-purple-600" />
                    </div>
                    <span className="text-lg font-semibold text-gray-700">
                      Order Date
                    </span>
                  </div>
                  <div className="pl-12 text-gray-900 font-medium text-lg">
                    {new Date(order.created_at).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Subscription Details Card */}
            <Card className="border-0 shadow-2xl bg-white/95 backdrop-blur-xl rounded-3xl overflow-hidden group hover:shadow-3xl transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-amber-50/50 to-orange-50/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <CardHeader className="relative pb-6 pt-8 px-8">
                <CardTitle className="flex items-center space-x-3 text-2xl">
                  <div className="p-3 rounded-2xl bg-gradient-to-br from-amber-100 to-orange-100">
                    <Crown className="h-7 w-7 text-amber-600" />
                  </div>
                  <span className="bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                    Subscription Details
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="relative space-y-8 px-8 pb-8">
                {/* Plan Info */}
                <div className="p-6 bg-gradient-to-r from-amber-50 via-yellow-50 to-orange-50 rounded-3xl border-2 border-amber-200/50 shadow-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-gray-900 mb-1">
                        {order.plan.name}
                      </div>
                      <div className="text-amber-700 font-semibold flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span>Active Subscription</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                        {order.amount_total_formatted}
                      </div>
                      <div className="text-gray-600 font-medium">per month</div>
                    </div>
                  </div>
                </div>

                {/* Billing Period */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-xl bg-gradient-to-br from-blue-100 to-indigo-100">
                      <Calendar className="h-5 w-5 text-blue-600" />
                    </div>
                    <span className="text-lg font-semibold text-gray-700">
                      Billing Period
                    </span>
                  </div>
                  <div className="pl-12 space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="text-sm font-medium text-gray-500">
                        Start Date:
                      </div>
                      <div className="font-semibold text-gray-900">
                        {new Date(
                          stripe_data.subscription.current_period_start * 1000
                        ).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="text-sm font-medium text-gray-500">
                        Next Billing:
                      </div>
                      <div className="font-semibold text-gray-900">
                        {new Date(
                          stripe_data.subscription.current_period_end * 1000
                        ).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Subscription ID */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-xl bg-gradient-to-br from-gray-100 to-slate-100">
                      <Shield className="h-5 w-5 text-gray-600" />
                    </div>
                    <span className="text-lg font-semibold text-gray-700">
                      Subscription ID
                    </span>
                  </div>
                  <div className="pl-12 font-mono text-sm text-gray-700 bg-gray-50 p-4 rounded-xl border">
                    {stripe_data.subscription.id}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Transaction History */}
          {/* {order.transactions && order.transactions.length > 0 && (
            <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm mb-8">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-xl">
                  <Receipt className="h-6 w-6 text-green-600" />
                  <span>Transaction History</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.transactions.map((transaction, index) => (
                    <div
                      key={transaction.id}
                      className="flex items-center justify-between p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div
                          className={`p-2 rounded-full ${
                            transaction.status === "succeeded"
                              ? "bg-green-100"
                              : "bg-yellow-100"
                          }`}
                        >
                          {transaction.status === "succeeded" ? (
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          ) : (
                            <Clock className="h-5 w-5 text-yellow-600" />
                          )}
                        </div>
                        <div>
                          <div className="font-semibold text-gray-900 capitalize">
                            {transaction.type} #{index + 1}
                          </div>
                          <div className="text-sm text-gray-600">
                            {new Date(
                              transaction.processed_at
                            ).toLocaleString()}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-lg text-gray-900">
                          {transaction.amount_formatted}
                        </div>
                        <Badge
                          variant="secondary"
                          className={
                            transaction.status === "succeeded"
                              ? "bg-green-100 text-green-800"
                              : "bg-yellow-100 text-yellow-800"
                          }
                        >
                          {transaction.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )} */}

          {/* Enhanced Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
            <Button
              onClick={() => router.push("/subscription")}
              size="lg"
              className="group bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 text-white px-10 py-4 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
            >
              <Crown className="h-6 w-6 mr-3 group-hover:animate-bounce" />
              <span className="font-semibold text-lg">
                {stateConfig.primaryAction}
              </span>
            </Button>

            <Button
              variant="outline"
              onClick={() => router.push("/dashboard")}
              size="lg"
              className="group border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 px-10 py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              <ArrowLeft className="h-6 w-6 mr-3 group-hover:-translate-x-1 transition-transform" />
              <span className="font-semibold text-lg">Back to Dashboard</span>
            </Button>

            {paymentState !== "paid" && (
              <Button
                variant="ghost"
                onClick={() => window.location.reload()}
                size="lg"
                className="group px-10 py-4 rounded-2xl hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-200 transition-all duration-300 transform hover:scale-105"
              >
                <RefreshCw className="h-6 w-6 mr-3 group-hover:rotate-180 transition-transform duration-500" />
                <span className="font-semibold text-lg">Refresh Status</span>
              </Button>
            )}
          </div>

          {/* Enhanced Additional Info for Non-Success States */}
          {paymentState !== "paid" && (
            <div className="mt-8 p-8 bg-white/80 backdrop-blur-xl rounded-3xl border border-gray-200 shadow-xl">
              <div className="text-center space-y-6">
                <div className="flex justify-center">
                  <div className="p-4 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl">
                    <Mail className="h-8 w-8 text-blue-600" />
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-gray-900">Need Help?</h3>
                <p className="text-gray-600 text-lg max-w-2xl mx-auto leading-relaxed">
                  If you're experiencing issues with your payment or have any
                  questions about your subscription, our support team is here to
                  help.
                </p>
                <div className="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-4">
                  <Button
                    variant="outline"
                    size="lg"
                    className="group border-2 border-blue-300 hover:border-blue-400 hover:bg-blue-50 rounded-xl px-8 py-3 transition-all duration-300"
                  >
                    <Mail className="h-5 w-5 mr-2 group-hover:animate-pulse" />
                    Contact Support
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    className="group border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 rounded-xl px-8 py-3 transition-all duration-300"
                  >
                    <ExternalLink className="h-5 w-5 mr-2 group-hover:rotate-12 transition-transform" />
                    View FAQ
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Success celebration message */}
          {paymentState === "paid" && (
            <div className="mt-8 text-center">
              <div className="inline-flex items-center space-x-2 px-6 py-3 bg-green-100 text-green-800 rounded-full">
                <CheckCircle className="h-5 w-5" />
                <span className="font-semibold">
                  Welcome to your new subscription! 🎉
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

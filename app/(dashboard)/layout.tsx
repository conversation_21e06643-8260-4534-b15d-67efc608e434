import type React from "react"
import {
  <PERSON>bar<PERSON>rov<PERSON>,
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  SidebarHeader,
  SidebarInset
} from "@/components/ui/sidebar"
import { SidebarNav, sidebarNavItems } from "@/components/sidebar-nav"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { DashboardHeader } from "@/components/dashboard-header"
import { Toaster } from "@/components/ui/toaster"

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ProtectedRoute requireAuth={true}>
      <SidebarProvider>
        <div className="flex min-h-screen w-full">
          <Sidebar>
            <SidebarHeader className="p-4">
              <h2 className="text-lg font-semibold">Command Center</h2>
            </SidebarHeader>
            <SidebarContent className="p-4">
              <SidebarNav items={sidebarNavItems} />
            </SidebarContent>
          </Sidebar>

          <SidebarInset className="flex-1">
            <DashboardHeader />

            <div className="flex-1">
              {children}
            </div>
          </SidebarInset>
        </div>
        <Toaster />
      </SidebarProvider>
    </ProtectedRoute>
  )
}

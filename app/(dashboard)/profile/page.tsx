'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Eye, EyeOff, Loader2, Upload, Mail, Shield, CreditCard, Check, X } from 'lucide-react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { authAPI } from '@/lib/api'
import { subscriptionPlanService } from '@/lib/api/subscription-plans'
import { getProfileImageUrl } from '@/lib/url-utils'

// Profile update schema
const profileSchema = z.object({
  first_name: z.string().min(2, 'First name must be at least 2 characters'),
  last_name: z.string().min(2, 'Last name must be at least 2 characters'),
  gender: z.string().optional(),
  phone_number: z.string().optional(),
  country: z.string().optional(),
  city: z.string().optional(),
  address: z.string().optional(),
})

// Change password schema
const passwordSchema = z.object({
  currentPassword: z.string().min(6, 'Current password is required'),
  newPassword: z.string().min(6, 'New password must be at least 6 characters'),
  confirmPassword: z.string().min(6, 'Password confirmation is required'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type ProfileFormData = z.infer<typeof profileSchema>
type PasswordFormData = z.infer<typeof passwordSchema>

export default function ProfilePage() {
  const { user, updateProfile, changePassword, sendVerification, isLoading, refreshProfile, isAuthenticated } = useAuth()
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [profileImage, setProfileImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [profileLoading, setProfileLoading] = useState(true)
  const [profileData, setProfileData] = useState<any>(null)
  const [planData, setPlanData] = useState<any>(null)
  const [planLoading, setPlanLoading] = useState(false)

  const profileForm = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      first_name: '',
      last_name: '',
      gender: '',
      phone_number: '',
      country: '',
      city: '',
      address: '',
    }
  })

  const passwordForm = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
  })

  // Load profile data on component mount
  useEffect(() => {
    const loadProfile = async () => {
      try {
        setProfileLoading(true)
        console.log('Loading profile data...')
        console.log('Is authenticated:', isAuthenticated)
        console.log('Current user:', user)

        // Check if we have a token
        const token = document.cookie.split('; ').find(row => row.startsWith('auth_token='))?.split('=')[1]
        console.log('Auth token:', token ? 'Present' : 'Missing')

        const profile = await authAPI.getProfile()
        console.log('Profile data received:', profile)
        setProfileData(profile)
      } catch (error) {
        console.error('Failed to load profile:', error)
      } finally {
        setProfileLoading(false)
      }
    }

    if (isAuthenticated) {
      loadProfile()
    } else {
      console.log('User not authenticated, skipping profile load')
      setProfileLoading(false)
    }
  }, [isAuthenticated, user])

  // Load profile data into form when profileData changes
  useEffect(() => {
    if (profileData) {
      console.log('Profile data in form:', profileData)
      profileForm.reset({
        first_name: profileData.first_name || '',
        last_name: profileData.last_name || '',
        gender: profileData.gender || '',
        phone_number: profileData.phone_number || '',
        country: profileData.country || '',
        city: profileData.city || '',
        address: profileData.address || '',
      })
      setImagePreview(profileData.profile_image || null)
      console.log('Profile image path:', profileData.profile_image)
      console.log('Generated image URL:', getProfileImageUrl(profileData.profile_image))

      // Load plan data if user has a plan
      if (profileData.plan_id && profileData.role === 'user') {
        loadPlanData(profileData.plan_id)
      }
    }
  }, [profileData, profileForm])

  // Load plan data
  const loadPlanData = async (planId: string) => {
    try {
      setPlanLoading(true)
      console.log('Loading plan data for plan ID:', planId)

      // Use the subscription plan service
      const response = await subscriptionPlanService.getMyPlan()
      console.log('Plan data received:', response.data)
      setPlanData(response.data)
    } catch (error) {
      console.error('Failed to load plan data:', error)
    } finally {
      setPlanLoading(false)
    }
  }

  const handleProfileSubmit = async (data: ProfileFormData) => {
    const updateData: any = { ...data }
    if (profileImage) {
      updateData.profile_image = profileImage
    }

    const success = await updateProfile(updateData)
    if (success) {
      setProfileImage(null)
      // Reload profile data
      try {
        const updatedProfile = await authAPI.getProfile()
        setProfileData(updatedProfile)
      } catch (error) {
        console.error('Failed to reload profile:', error)
      }
    }
  }

  const handlePasswordSubmit = async (data: PasswordFormData) => {
    const success = await changePassword({
      currentPassword: data.currentPassword,
      newPassword: data.newPassword,
    })
    
    if (success) {
      passwordForm.reset()
    }
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setProfileImage(file)
      const reader = new FileReader()
      reader.onloadend = () => {
        setImagePreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleSendVerification = async () => {
    await sendVerification()
  }

  const getUserInitials = () => {
    const userData = profileData || user
    if (userData?.first_name && userData?.last_name) {
      return (userData.first_name.charAt(0) + userData.last_name.charAt(0)).toUpperCase()
    }
    return userData?.email?.charAt(0).toUpperCase() || 'U'
  }

  const getFullName = () => {
    const userData = profileData || user
    if (userData?.first_name && userData?.last_name) {
      return `${userData.first_name} ${userData.last_name}`
    }
    return userData?.email || 'User'
  }



  if (profileLoading || (!user && !profileData)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  const currentUserData = profileData || user

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Profile</h2>
      </div>

      <div className="grid gap-6">
        {/* Profile Overview Card */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-4">
              <Avatar className="h-20 w-20 border">
                <AvatarImage
                  src={imagePreview || getProfileImageUrl(currentUserData?.profile_image)}
                  alt={getFullName()}
                  onError={(e) => {
                    console.log('Avatar image failed to load:', e.currentTarget.src)
                    e.currentTarget.style.display = 'none'
                  }}
                />
                <AvatarFallback className="text-lg">{getUserInitials()}</AvatarFallback>
              </Avatar>
              <div className="space-y-1">
                <h3 className="text-2xl font-semibold">{getFullName()}</h3>
                <p className="text-muted-foreground">{currentUserData?.email}</p>
                <div className="flex items-center space-x-2">
                  <Badge variant={currentUserData?.role === 'admin' ? 'default' : 'secondary'}>
                    <Shield className="mr-1 h-3 w-3" />
                    {currentUserData?.role}
                  </Badge>
                  {currentUserData?.is_verified ? (
                    <Badge variant="outline" className="text-green-600 border-green-600">
                      <Mail className="mr-1 h-3 w-3" />
                      Verified
                    </Badge>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-orange-600 border-orange-600">
                        <Mail className="mr-1 h-3 w-3" />
                        Unverified
                      </Badge>
                      <Button size="sm" variant="outline" onClick={handleSendVerification} disabled={isLoading}>
                        Send Verification
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Profile Tabs */}
        <Tabs defaultValue="profile" className="space-y-4">
          <TabsList>
            <TabsTrigger value="profile">Profile Information</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            {currentUserData?.role === 'user' && (
              <TabsTrigger value="plan">Plan Details</TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="profile" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
                <CardDescription>Update your personal information and profile picture.</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={profileForm.handleSubmit(handleProfileSubmit)} className="space-y-6">
                  {/* Profile Image Upload */}
                  <div className="space-y-2">
                    <Label>Profile Picture</Label>
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-16 w-16 border">
                        <AvatarImage
                          src={imagePreview || getProfileImageUrl(currentUserData?.profile_image)}
                          alt={getFullName()}
                          onError={(e) => {
                            console.log('Profile form image failed to load:', e.currentTarget.src)
                            e.currentTarget.style.display = 'none'
                          }}
                        />
                        <AvatarFallback>{getUserInitials()}</AvatarFallback>
                      </Avatar>
                      <div>
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={handleImageChange}
                          className="hidden"
                          id="profile-image"
                        />
                        <Label htmlFor="profile-image" className="cursor-pointer">
                          <Button type="button" variant="outline" size="sm" asChild>
                            <span>
                              <Upload className="mr-2 h-4 w-4" />
                              Upload Image
                            </span>
                          </Button>
                        </Label>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Name Fields */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="first_name">First Name</Label>
                      <Input
                        id="first_name"
                        {...profileForm.register('first_name')}
                        disabled={isLoading}
                      />
                      {profileForm.formState.errors.first_name && (
                        <p className="text-sm text-red-600">{profileForm.formState.errors.first_name.message}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="last_name">Last Name</Label>
                      <Input
                        id="last_name"
                        {...profileForm.register('last_name')}
                        disabled={isLoading}
                      />
                      {profileForm.formState.errors.last_name && (
                        <p className="text-sm text-red-600">{profileForm.formState.errors.last_name.message}</p>
                      )}
                    </div>
                  </div>

                  {/* Contact Information */}
                  <div className="grid md:grid-cols-2 grid-cols-1 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone_number">Phone Number</Label>
                      <Input
                        id="phone_number"
                        {...profileForm.register('phone_number')}
                        disabled={isLoading}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="gender">Gender</Label>
                      <Select
                        value={profileForm.watch('gender')}
                        onValueChange={(value) => profileForm.setValue('gender', value)}
                        disabled={isLoading}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select gender" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="male">Male</SelectItem>
                          <SelectItem value="female">Female</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Location Information */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="country">Country</Label>
                      <Input
                        id="country"
                        {...profileForm.register('country')}
                        disabled={isLoading}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        {...profileForm.register('city')}
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      {...profileForm.register('address')}
                      disabled={isLoading}
                    />
                  </div>

                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Updating...
                      </>
                    ) : (
                      'Update Profile'
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Change Password</CardTitle>
                <CardDescription>Update your password to keep your account secure.</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={passwordForm.handleSubmit(handlePasswordSubmit)} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">Current Password</Label>
                    <div className="relative">
                      <Input
                        id="currentPassword"
                        type={showCurrentPassword ? 'text' : 'password'}
                        {...passwordForm.register('currentPassword')}
                        disabled={isLoading}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        disabled={isLoading}
                      >
                        {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                    {passwordForm.formState.errors.currentPassword && (
                      <p className="text-sm text-red-600">{passwordForm.formState.errors.currentPassword.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="newPassword">New Password</Label>
                    <div className="relative">
                      <Input
                        id="newPassword"
                        type={showNewPassword ? 'text' : 'password'}
                        {...passwordForm.register('newPassword')}
                        disabled={isLoading}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        disabled={isLoading}
                      >
                        {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                    {passwordForm.formState.errors.newPassword && (
                      <p className="text-sm text-red-600">{passwordForm.formState.errors.newPassword.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm New Password</Label>
                    <div className="relative">
                      <Input
                        id="confirmPassword"
                        type={showConfirmPassword ? 'text' : 'password'}
                        {...passwordForm.register('confirmPassword')}
                        disabled={isLoading}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        disabled={isLoading}
                      >
                        {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                    {passwordForm.formState.errors.confirmPassword && (
                      <p className="text-sm text-red-600">{passwordForm.formState.errors.confirmPassword.message}</p>
                    )}
                  </div>

                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Changing Password...
                      </>
                    ) : (
                      'Change Password'
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Plan Details Tab - Only for regular users */}
          {currentUserData?.role === 'user' && (
            <TabsContent value="plan" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CreditCard className="h-5 w-5" />
                    <span>Subscription Plan</span>
                  </CardTitle>
                  <CardDescription>
                    View your current subscription plan details and features.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {planLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin" />
                      <span className="ml-2">Loading plan details...</span>
                    </div>
                  ) : planData ? (
                    <div className="space-y-6">
                      {/* Plan Overview */}
                      <div className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <h3 className="text-lg font-semibold">{planData.name}</h3>
                          <p className="text-muted-foreground">{planData.description}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold">
                            ${planData.price_monthly}
                            <span className="text-sm font-normal text-muted-foreground">/month</span>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            ${planData.price_annual}/year
                          </div>
                        </div>
                      </div>

                      {/* Plan Features */}
                      <div>
                        <h4 className="text-md font-semibold mb-4">Plan Features</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {/* API Access */}
                          <div className="flex items-center space-x-2">
                            {planData.features.api_access ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <X className="h-4 w-4 text-red-500" />
                            )}
                            <span className="text-sm">API Access</span>
                          </div>

                          {/* Max Products */}
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-500" />
                            <span className="text-sm">
                              {planData.features.max_products === -1
                                ? 'Unlimited Products'
                                : `${planData.features.max_products} Products`}
                            </span>
                          </div>

                          {/* Custom Branding */}
                          <div className="flex items-center space-x-2">
                            {planData.features.custom_branding ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <X className="h-4 w-4 text-red-500" />
                            )}
                            <span className="text-sm">Custom Branding</span>
                          </div>

                          {/* Product Editing */}
                          <div className="flex items-center space-x-2">
                            {planData.features.product_editing ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <X className="h-4 w-4 text-red-500" />
                            )}
                            <span className="text-sm">Product Editing</span>
                          </div>

                          {/* AI Agents Access */}
                          <div className="flex items-center space-x-2">
                            {planData.features.ai_agents_access ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <X className="h-4 w-4 text-red-500" />
                            )}
                            <span className="text-sm">AI Agents Access</span>
                          </div>

                          {/* Analytics Access */}
                          <div className="flex items-center space-x-2">
                            {planData.features.analytics_access ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <X className="h-4 w-4 text-red-500" />
                            )}
                            <span className="text-sm">Analytics Access</span>
                          </div>

                          {/* Priority Support */}
                          <div className="flex items-center space-x-2">
                            {planData.features.priority_support ? (
                              <Check className="h-4 w-4 text-green-500" />
                            ) : (
                              <X className="h-4 w-4 text-red-500" />
                            )}
                            <span className="text-sm">Priority Support</span>
                          </div>

                          {/* Team Members */}
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-500" />
                            <span className="text-sm">
                              {planData.features.max_team_members === -1
                                ? 'Unlimited Team Members'
                                : `${planData.features.max_team_members} Team Members`}
                            </span>
                          </div>

                          {/* Monthly Analysis Requests */}
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-500" />
                            <span className="text-sm">
                              {planData.features.monthly_analysis_requests === -1
                                ? 'Unlimited Analysis Requests'
                                : `${planData.features.monthly_analysis_requests} Analysis Requests/month`}
                            </span>
                          </div>

                          {/* Monthly Media Generations */}
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-500" />
                            <span className="text-sm">
                              {planData.features.monthly_media_generations === -1
                                ? 'Unlimited Media Generations'
                                : `${planData.features.monthly_media_generations} Media Generations/month`}
                            </span>
                          </div>

                          {/* Monthly Video Exports */}
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-500" />
                            <span className="text-sm">
                              {planData.features.monthly_video_exports === -1
                                ? 'Unlimited Video Exports'
                                : `${planData.features.monthly_video_exports} Video Exports/month`}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Plan Status */}
                      <div className="p-4 bg-muted rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium">Plan Status</h4>
                            <p className="text-sm text-muted-foreground">
                              Your subscription is currently active
                            </p>
                          </div>
                          <Badge variant="outline" className="text-green-600 border-green-600">
                            Active
                          </Badge>
                        </div>
                      </div>

                      {/* Plan Actions */}
                      <div className="flex gap-4 pt-4 border-t">
                        <Button
                          variant="default"
                          className="flex-1"
                          onClick={() => {
                            console.log('Upgrade plan clicked')
                            // TODO: Implement plan upgrade functionality
                          }}
                        >
                          <CreditCard className="h-4 w-4 mr-2" />
                          Upgrade Plan
                        </Button>
                        <Button
                          variant="outline"
                          className="flex-1"
                          onClick={() => {
                            console.log('Change plan clicked')
                            // TODO: Implement plan change functionality
                          }}
                        >
                          Change Plan
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => {
                            console.log('Billing history clicked')
                            // TODO: Implement billing history functionality
                          }}
                        >
                          Billing History
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <CreditCard className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">No Active Plan</h3>
                      <p className="text-muted-foreground mb-4">
                        You don't have an active subscription plan.
                      </p>
                      <Button variant="outline">
                        Browse Plans
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </div>
  )
}

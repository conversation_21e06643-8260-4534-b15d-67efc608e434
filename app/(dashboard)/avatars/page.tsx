'use client'

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar as AvatarUI, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Plus, User, Edit, Instagram, Youtube, Twitter, Facebook, Linkedin } from "lucide-react"

import { AvatarCreateDialog } from "@/components/avatar-create-dialog"
import { AvatarEditDialog } from "@/components/avatar-edit-dialog"
import { useToast } from "@/hooks/use-toast"
import { avatarService, type Avatar, type CreateAvatarData } from "@/lib/api/avatars"


export default function AvatarsPage() {
  const [avatars, setAvatars] = useState<Avatar[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedAvatar, setSelectedAvatar] = useState<Avatar | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const { toast } = useToast()

  // Helper function to get category name from avatar
  const getCategoryName = (avatar: Avatar): string => {
    return avatar.category?.name || 'Unknown Category'
  }

  // Helper function to get category color from avatar
  const getCategoryColor = (avatar: Avatar): string => {
    return 'text-white'
  }

  // Helper function to get category background color
  const getCategoryBgColor = (avatar: Avatar): string => {
    return avatar.category?.color || '#6b7280'
  }

  // Load avatars from API
  useEffect(() => {
    const loadAvatars = async () => {
      try {
        console.log('Attempting to load avatars from API...')
        const data = await avatarService.getAllAvatars()
        console.log('Successfully loaded avatars:', data)
        setAvatars(data)
      } catch (error) {
        console.error('Failed to load avatars:', error)
        toast({
          title: "API Connection Failed",
          description: "Unable to connect to the backend API. Please check if the server is running.",
          variant: "destructive",
        })
        setAvatars([])

      } finally {
        setLoading(false)
      }
    }

    loadAvatars()
  }, [])

  const handleCreateAvatar = async (avatarData: CreateAvatarData) => {
    try {
      console.log('Creating avatar with data:', avatarData)
      const newAvatar = await avatarService.createAvatar(avatarData)
      setAvatars(prev => [...prev, newAvatar])
      setIsCreateDialogOpen(false)
      toast({
        title: "Avatar Created",
        description: `${newAvatar.name} has been created successfully.`,
      })
    } catch (error) {
      console.error('Failed to create avatar:', error)

      // Fallback: Add to local state with mock ID when API is unavailable
      const mockAvatar: Avatar = {
        id: Date.now().toString(),
        ...avatarData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      setAvatars(prev => [...prev, mockAvatar])
      setIsCreateDialogOpen(false)
      toast({
        title: "Avatar Created (Demo Mode)",
        description: `${mockAvatar.name} has been created locally. Connect to backend to persist.`,
      })
    }
  }

  const handleUpdateAvatar = async (avatarData: any) => {
    if (!selectedAvatar) return

    try {
      console.log('Updating avatar with data:', avatarData)
      const updatedAvatar = await avatarService.updateAvatar(selectedAvatar.id, avatarData)
      setAvatars(prev => prev.map(avatar =>
        avatar.id === selectedAvatar.id ? updatedAvatar : avatar
      ))
      setIsEditDialogOpen(false)
      setSelectedAvatar(null)
      toast({
        title: "Avatar Updated",
        description: `${updatedAvatar.name} has been updated successfully.`,
      })
    } catch (error) {
      console.error('Failed to update avatar:', error)

      // Fallback: Update local state when API is unavailable
      const updatedAvatar = {
        ...selectedAvatar,
        ...avatarData,
        updated_at: new Date().toISOString()
      }

      setAvatars(prev => prev.map(avatar =>
        avatar.id === selectedAvatar.id ? updatedAvatar : avatar
      ))
      setIsEditDialogOpen(false)
      setSelectedAvatar(null)
      toast({
        title: "Avatar Updated (Demo Mode)",
        description: `${updatedAvatar.name} has been updated locally. Connect to backend to persist.`,
      })
    }
  }



  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'instagram': return <Instagram className="h-4 w-4" />
      case 'youtube': return <Youtube className="h-4 w-4" />
      case 'twitter': return <Twitter className="h-4 w-4" />
      case 'facebook': return <Facebook className="h-4 w-4" />
      case 'linkedin': return <Linkedin className="h-4 w-4" />
      default: return <User className="h-4 w-4" />
    }
  }



  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading avatars...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-8 pt-6">
        <div className="flex md:flex-row flex-col items-start gap-2 md:items-center  justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Avatar Management</h2>
            <p className="text-muted-foreground">
              Create and manage your content avatars for different niches and platforms.
            </p>
          </div>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Avatar
          </Button>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {avatars.map((avatar) => (
            <Card key={avatar.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <AvatarUI className="h-12 w-12 border">
                      <AvatarImage src={`/placeholder-user.jpg`} alt={avatar.name} />
                      <AvatarFallback>
                        <User className="h-6 w-6" />
                      </AvatarFallback>
                    </AvatarUI>
                    <div>
                      <CardTitle className="text-lg">{avatar.name}</CardTitle>
                      <Badge
                        className={getCategoryColor(avatar)}
                        style={{ backgroundColor: getCategoryBgColor(avatar) }}
                      >
                        {getCategoryName(avatar)}
                      </Badge>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedAvatar(avatar)
                      setIsEditDialogOpen(true)
                    }}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="mb-4">
                  {avatar.description}
                </CardDescription>
                
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium mb-2">Platforms</p>
                    <div className="flex space-x-2">
                      {avatar.content_preferences.primaryPlatforms.map((platform) => (
                        <div key={platform} className="flex items-center space-x-1">
                          {getPlatformIcon(platform)}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium mb-2">Expertise</p>
                    <div className="flex flex-wrap gap-1">
                      {avatar.personality_traits.expertise.slice(0, 3).map((skill) => (
                        <Badge key={skill} variant="outline" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                      {avatar.personality_traits.expertise.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{avatar.personality_traits.expertise.length - 3}
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between pt-2">
                    <Badge variant={avatar.is_active ? "default" : "secondary"}>
                      {avatar.is_active ? "Active" : "Inactive"}
                    </Badge>
                    <Button variant="outline" size="sm">
                      Manage Content
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <AvatarCreateDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          onSubmit={handleCreateAvatar}
        />

        <AvatarEditDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          avatar={selectedAvatar}
          onSubmit={handleUpdateAvatar}
        />
      </div>
  )
}

"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { toast } from '@/hooks/use-toast'
import { AdminOrderHistory } from '@/components/admin/admin-order-history'
import { RefreshCw } from 'lucide-react'

export default function OrderHistoryPage() {
  const { user, isLoading: authLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!authLoading && user) {
      if (user.role !== 'admin') {
        toast({
          title: "Access Denied",
          description: "You don't have permission to access this page.",
          variant: "destructive",
        })
        router.push('/dashboard')
        return
      }
    }
  }, [user, authLoading, router])

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Checking permissions...</p>
        </div>
      </div>
    )
  }

  // Don't render if user is not admin (will be redirected)
  if (!user || user.role !== 'admin') {
    return null
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <AdminOrderHistory />
    </div>
  )
}

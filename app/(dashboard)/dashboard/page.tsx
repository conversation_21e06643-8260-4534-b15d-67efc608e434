"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  ArrowUpRight,
  BarChart3,
  FileText,
  TrendingUp,
  LayoutList,
  LayoutGrid,
  Calendar,
  Users,
  Package,
  Activity,
  Loader2,
} from "lucide-react"
import { Overview } from "@/components/overview"
import { RecentSales } from "@/components/recent-sales"
import Link from "next/link"
import { dashboardService, type AdminDashboardData, type UserDashboardData, type RecentUser } from "@/lib/api/dashboard"
import { getCurrentUserRole } from "@/lib/api"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { DashboardAIWorkflowsCard } from "@/components/ai-workflows/dashboard-ai-workflows-card"

// Recent Users Component for Admin Dashboard
function RecentUsers({ users }: { users: RecentUser[] }) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })
  }

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
  }

  if (!users || users.length === 0) {
    return (
      <div className="text-center py-4">
        <p className="text-sm text-muted-foreground">No recent users found</p>
      </div>
    )
  }

  return (
    <div className="space-y-6 sm:space-y-8">
      {users.map((user) => (
        <div key={user.id} className="flex items-center">
          <Avatar className="h-8 w-8 sm:h-9 sm:w-9 flex-shrink-0">
            <AvatarImage src="/placeholder-user.jpg" alt="Avatar" />
            <AvatarFallback>{getInitials(user.first_name, user.last_name)}</AvatarFallback>
          </Avatar>
          <div className="ml-3 sm:ml-4 space-y-1 min-w-0 flex-1">
            <p className="text-sm font-medium leading-none truncate">
              {user.first_name} {user.last_name}
            </p>
            <p className="text-xs sm:text-sm text-muted-foreground truncate">{user.email}</p>
          </div>
          <div className="ml-2 sm:ml-auto text-right flex-shrink-0">
            <div className="text-xs sm:text-sm font-medium">{formatDate(user.created_at)}</div>
            <div className="text-xs text-muted-foreground capitalize">{user.role}</div>
          </div>
        </div>
      ))}
    </div>
  )
}

export default function DashboardPage() {
  const [dashboardData, setDashboardData] = useState<AdminDashboardData | UserDashboardData | null>(null)
  const [userRole, setUserRole] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true)
        setError(null)

        // Get user role first
        const role = await getCurrentUserRole()
        console.log('Current user role:', role)
        setUserRole(role)

        if (!role) {
          throw new Error('Unable to determine user role')
        }

        // Fetch dashboard data based on role
        const response = await dashboardService.getDashboardData(role)
        console.log('Dashboard data response:', response)

        if (response.success && response.data) {
          setDashboardData(response.data)
        } else {
          throw new Error(response.message || 'Failed to fetch dashboard data')
        }
      } catch (err) {
        console.error('Dashboard data fetch error:', err)
        setError(err instanceof Error ? err.message : 'Failed to load dashboard data')
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  // Helper function to get metric cards based on role and data
  const getMetricCards = () => {
    if (!dashboardData) return []

    const isAdmin = userRole === 'admin'
    const adminData = dashboardData as AdminDashboardData
    const userData = dashboardData as UserDashboardData

    if (isAdmin) {
      return [
        {
          title: "Total Users",
          value: adminData.counts.users?.toString() || "0",
          change: "+12% from last month",
          icon: Users,
        },
        {
          title: "Total Products",
          value: adminData.counts.products.toString(),
          change: "+8% from last month",
          icon: Package,
        },
        {
          title: "Analysis Requests",
          value: adminData.counts.analysisRequests.toString(),
          change: "+15% from last month",
          icon: BarChart3,
        },
        {
          title: "Media Creations",
          value: adminData.counts.productMediaCreations.toString(),
          change: "+23% from last month",
          icon: Activity,
        },
      ]
    } else {
      return [
        {
          title: "My Products",
          value: userData.counts.products.toString(),
          change: "+2 this month",
          icon: Package,
        },
        {
          title: "Analysis Requests",
          value: userData.counts.analysisRequests.toString(),
          change: "This month",
          icon: BarChart3,
        },
        {
          title: "Media Creations",
          value: userData.counts.productMediaCreations.toString(),
          change: "This month",
          icon: Activity,
        },
        {
          title: "Success Rate",
          value: "95%",
          change: "+5% from last month",
          icon: TrendingUp,
        },
      ]
    }
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin" />
            <p className="text-muted-foreground">Loading dashboard...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h3 className="text-lg font-medium text-red-600 mb-2">Error Loading Dashboard</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-4 p-4 sm:p-6 lg:p-8 pt-4 sm:pt-6">
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div>
          <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">Command Center</h2>
          <p className="text-muted-foreground">
            {userRole === 'admin' ? 'Admin Dashboard' : 'User Dashboard'}
          </p>
        </div>
        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2">
          <div className="flex items-center border rounded-md p-1 w-fit">
            <Button variant="ghost" size="sm" className="h-8 px-2 flex items-center gap-1">
              <LayoutList className="h-4 w-4" />
              <span className="sr-only sm:not-sr-only sm:inline">List View</span>
            </Button>
            <Button variant="ghost" size="sm" className="h-8 px-2 flex items-center gap-1">
              <LayoutGrid className="h-4 w-4" />
              <span className="sr-only sm:not-sr-only sm:inline">Grid View</span>
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            <Link href="/calendar">
              <Button variant="outline"  className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span className="hidden sm:inline">Calendar</span>
              </Button>
            </Link>
            <Link href="/trends">
              <Button variant="outline" className=" flex items-center gap-1">
                <TrendingUp className="h-4 w-4" />
                <span className="hidden sm:inline">Trends</span>
              </Button>
            </Link>
            <Link href="/content-creator">
              <Button className="w-full sm:w-auto">
                <span className="sm:hidden">Create Content</span>
                <span className="hidden sm:inline">Create New Content</span>
              </Button>
            </Link>
          </div>
        </div>
      </div>
      <div className="space-y-4">
        <div className="mb-4 p-3 sm:p-4 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="text-base sm:text-lg font-semibold text-green-800 mb-2">🎉 Successfully Authenticated!</h3>
          <p className="text-green-700 text-sm">
            You are now logged in to the Command Center dashboard via API authentication.
          </p>
        </div>

        <div>
          <DashboardAIWorkflowsCard />
        </div>

        {/* Dynamic Metric Cards */}
        <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
          {getMetricCards().map((metric, index) => {
            const IconComponent = metric.icon
            return (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
                  <IconComponent className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-xl sm:text-2xl font-bold">{metric.value}</div>
                  <p className="text-xs text-muted-foreground">{metric.change}</p>
                </CardContent>
              </Card>
            )
          })}
        </div>

        <div className="grid gap-4 grid-cols-1 lg:grid-cols-7">
          <Card className="lg:col-span-4">
            <CardHeader>
              <CardTitle className="text-base sm:text-lg">Performance Overview</CardTitle>
              <CardDescription className="text-sm">
                {userRole === 'admin' ? 'System-wide activity' : 'Your activity over time'}
              </CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              <Overview performanceData={dashboardData?.performanceOverview} />
            </CardContent>
          </Card>
          <Card className="lg:col-span-3">
            <CardHeader>
              <CardTitle className="text-base sm:text-lg">
                {userRole === 'admin' ? 'Recent Users' : 'Recent Activity'}
              </CardTitle>
              <CardDescription className="text-sm">
                {userRole === 'admin'
                  ? `${(dashboardData as AdminDashboardData)?.recentUsers?.length || 0} new users this month`
                  : 'Your recent activities and achievements'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              {userRole === 'admin' ? (
                <RecentUsers users={(dashboardData as AdminDashboardData)?.recentUsers || []} />
              ) : (
                <RecentSales />
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { AppLayout } from '@/components/layouts/app-layout'
import { useAuth } from '@/contexts/auth-context'
import { blogPostService, BlogPost, BlogPostFilters, CreateBlogPostRequest, UpdateBlogPostRequest } from '@/lib/api/blog-posts'
import { blogCategoryService, BlogCategory } from '@/lib/api/blog-categories'
import { useToast } from '@/hooks/use-toast'
import { BlogPostDialog } from '@/components/blog/blog-post-dialog'
import { BlogPostViewDialog } from '@/components/blog/blog-post-view-dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  FileText,
  Calendar,
  User,
  Tag,
  ChevronLeft,
  ChevronRight,
  RefreshCw,
  Download,
  Archive,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react'

export default function BlogManagementPage() {
  const { user, isLoading: authLoading } = useAuth()
  const { toast } = useToast()

  // State management
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [categories, setCategories] = useState<BlogCategory[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedPosts, setSelectedPosts] = useState<string[]>([])

  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [selectedPost, setSelectedPost] = useState<BlogPost | null>(null)
  const [dialogLoading, setDialogLoading] = useState(false)

  // Filters
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [categoryFilter, setCategoryFilter] = useState('all')
  
  // Pagination
  const [pagination, setPagination] = useState({
    currentPage: 1,
    itemsPerPage: 10,
    totalPages: 1,
    totalItems: 0
  })

  // Check if user is admin or staff
  const isAdminOrStaff = user?.role === 'admin' || user?.role === 'staff'

  useEffect(() => {
    if (!authLoading) {
      loadPosts()
      loadCategories()
    }
  }, [authLoading, pagination.currentPage, pagination.itemsPerPage, searchTerm, statusFilter, categoryFilter])

  const loadPosts = async () => {
    try {
      setLoading(true)

      const filters: BlogPostFilters = {
        page: pagination.currentPage,
        limit: pagination.itemsPerPage,
      }

      if (searchTerm.trim()) filters.search = searchTerm.trim()
      if (statusFilter !== "all") filters.status = statusFilter
      if (categoryFilter !== "all") filters.category_id = categoryFilter

      const response = isAdminOrStaff
        ? await blogPostService.getAllBlogPosts(filters)
        : await blogPostService.getMyBlogPosts(filters)

      setPosts(response.posts || [])
      setPagination({
        currentPage: response.currentPage || 1,
        itemsPerPage: pagination.itemsPerPage,
        totalPages: response.totalPages || 1,
        totalItems: response.totalPosts || 0
      })
    } catch (error) {
      console.error('Failed to load blog posts:', error)
      toast({
        title: "Error",
        description: "Failed to load blog posts. Please try again.",
        variant: "destructive",
      })
      setPosts([])
    } finally {
      setLoading(false)
    }
  }

  const loadCategories = async () => {
    try {
      const response = await blogCategoryService.getAllBlogCategories({ limit: 100 })
      setCategories(response.categories || [])
    } catch (error) {
      console.error('Failed to load categories:', error)
    }
  }

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }))
  }

  const handleLimitChange = (newLimit: string) => {
    setPagination(prev => ({ ...prev, itemsPerPage: parseInt(newLimit), currentPage: 1 }))
  }

  const handleSelectPost = (postId: string, checked: boolean) => {
    if (checked) {
      setSelectedPosts(prev => [...prev, postId])
    } else {
      setSelectedPosts(prev => prev.filter(id => id !== postId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedPosts(posts.map(post => post.id))
    } else {
      setSelectedPosts([])
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'draft':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'archived':
        return <Archive className="h-4 w-4 text-gray-500" />
      default:
        return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      published: 'default',
      draft: 'secondary',
      archived: 'outline'
    } as const
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {getStatusIcon(status)}
        <span className="ml-1 capitalize">{status}</span>
      </Badge>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  // CRUD Operations
  const handleCreatePost = async (data: CreateBlogPostRequest) => {
    try {
      setDialogLoading(true)
      await blogPostService.createBlogPost(data)
      toast({
        title: "Success",
        description: "Blog post created successfully.",
      })
      loadPosts()
    } catch (error) {
      console.error('Failed to create post:', error)
      toast({
        title: "Error",
        description: "Failed to create blog post. Please try again.",
        variant: "destructive",
      })
    } finally {
      setDialogLoading(false)
    }
  }

  const handleUpdatePost = async (data: UpdateBlogPostRequest) => {
    if (!selectedPost) return

    try {
      setDialogLoading(true)
      await blogPostService.updateBlogPost(selectedPost.id, data)
      toast({
        title: "Success",
        description: "Blog post updated successfully.",
      })
      loadPosts()
    } catch (error) {
      console.error('Failed to update post:', error)
      toast({
        title: "Error",
        description: "Failed to update blog post. Please try again.",
        variant: "destructive",
      })
    } finally {
      setDialogLoading(false)
    }
  }

  const handleDeletePost = async (postId: string) => {
    if (!confirm('Are you sure you want to delete this post?')) return

    try {
      await blogPostService.deleteBlogPost(postId)
      toast({
        title: "Success",
        description: "Blog post deleted successfully.",
      })
      loadPosts()
    } catch (error) {
      console.error('Failed to delete post:', error)
      toast({
        title: "Error",
        description: "Failed to delete blog post. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleEditPost = (post: BlogPost) => {
    setSelectedPost(post)
    setIsEditDialogOpen(true)
  }

  const handleViewPost = (post: BlogPost) => {
    setSelectedPost(post)
    setIsViewDialogOpen(true)
  }

  const handleViewToEdit = () => {
    setIsViewDialogOpen(false)
    setIsEditDialogOpen(true)
  }

  const handleBulkStatusUpdate = async (status: 'draft' | 'published' | 'archived') => {
    if (selectedPosts.length === 0) return

    try {
      await blogPostService.bulkUpdateStatus(selectedPosts, status)
      toast({
        title: "Success",
        description: `${selectedPosts.length} post(s) updated to ${status}.`,
      })
      setSelectedPosts([])
      loadPosts()
    } catch (error) {
      console.error('Failed to update posts:', error)
      toast({
        title: "Error",
        description: "Failed to update posts. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleBulkDelete = async () => {
    if (selectedPosts.length === 0) return
    if (!confirm(`Are you sure you want to delete ${selectedPosts.length} post(s)?`)) return

    try {
      await blogPostService.bulkDelete(selectedPosts)
      toast({
        title: "Success",
        description: `${selectedPosts.length} post(s) deleted successfully.`,
      })
      setSelectedPosts([])
      loadPosts()
    } catch (error) {
      console.error('Failed to delete posts:', error)
      toast({
        title: "Error",
        description: "Failed to delete posts. Please try again.",
        variant: "destructive",
      })
    }
  }

  if (authLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading...</p>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 lg:p-8 pt-4 sm:pt-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Blog Management</h1>
            <p className="text-muted-foreground text-sm sm:text-base">
              {isAdminOrStaff ? "Manage all blog posts in the system" : "Manage your blog posts"}
            </p>
          </div>
          <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2">
            <Button variant="outline" size="sm" onClick={loadPosts} disabled={loading} className="w-full sm:w-auto">
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>

            <Button onClick={() => setIsCreateDialogOpen(true)} className="w-full sm:w-auto">
              <Plus className="mr-2 h-4 w-4" />
              Create Post
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-3 sm:gap-4 grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium">Total Posts</CardTitle>
              <FileText className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-lg sm:text-2xl font-bold">{pagination.totalItems}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium">Published</CardTitle>
              <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-lg sm:text-2xl font-bold">
                {posts.filter(p => p.status === 'published').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium">Drafts</CardTitle>
              <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-lg sm:text-2xl font-bold">
                {posts.filter(p => p.status === 'draft').length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-xs sm:text-sm font-medium">Archived</CardTitle>
              <Archive className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-lg sm:text-2xl font-bold">
                {posts.filter(p => p.status === 'archived').length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base sm:text-lg">Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-3 sm:space-y-4">
              {/* Search bar - full width on all screens */}
              <div className="w-full">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search posts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>

              {/* Filter selects - responsive grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={pagination.itemsPerPage.toString()} onValueChange={handleLimitChange}>
                  <SelectTrigger className="w-full sm:w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 per page</SelectItem>
                    <SelectItem value="10">10 per page</SelectItem>
                    <SelectItem value="20">20 per page</SelectItem>
                    <SelectItem value="50">50 per page</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bulk Actions */}
        {selectedPosts.length > 0 && (
          <Card>
            <CardContent className="pt-4 sm:pt-6">
              <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                <span className="text-sm text-muted-foreground">
                  {selectedPosts.length} post{selectedPosts.length !== 1 ? 's' : ''} selected
                </span>
                <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2">
                  <div className="grid grid-cols-2 gap-2 sm:flex sm:space-x-2">
                    <Button variant="outline" size="sm" onClick={() => handleBulkStatusUpdate('published')} className="text-xs sm:text-sm">
                      <CheckCircle className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">Publish Selected</span>
                      <span className="sm:hidden">Publish</span>
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleBulkStatusUpdate('draft')} className="text-xs sm:text-sm">
                      <Clock className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">Draft Selected</span>
                      <span className="sm:hidden">Draft</span>
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleBulkStatusUpdate('archived')} className="text-xs sm:text-sm">
                      <Archive className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">Archive Selected</span>
                      <span className="sm:hidden">Archive</span>
                    </Button>
                    <Button variant="destructive" size="sm" onClick={handleBulkDelete} className="text-xs sm:text-sm">
                      <Trash2 className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">Delete Selected</span>
                      <span className="sm:hidden">Delete</span>
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Posts Table */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base sm:text-lg">Blog Posts</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center h-32 sm:h-64">
                <div className="text-center">
                  <RefreshCw className="h-6 w-6 sm:h-8 sm:w-8 animate-spin mx-auto mb-4" />
                  <p className="text-sm sm:text-base">Loading posts...</p>
                </div>
              </div>
            ) : posts.length === 0 ? (
              <div className="text-center py-8 sm:py-12">
                <FileText className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-base sm:text-lg font-semibold mb-2">No blog posts found</h3>
                <p className="text-muted-foreground mb-4 text-sm sm:text-base">
                  {searchTerm || statusFilter !== 'all' || categoryFilter !== 'all'
                    ? "No posts match your current filters."
                    : "Get started by creating your first blog post."}
                </p>
                <Button onClick={() => setIsCreateDialogOpen(true)} className="w-full sm:w-auto">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Your First Post
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Desktop Table - Hidden on mobile */}
                <div className="hidden lg:block">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedPosts.length === posts.length}
                            onCheckedChange={handleSelectAll}
                          />
                        </TableHead>
                        <TableHead>Title</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Author</TableHead>
                        <TableHead>Published</TableHead>
                        <TableHead>Updated</TableHead>
                        <TableHead className="w-12"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {posts.map((post) => (
                        <TableRow key={post.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedPosts.includes(post.id)}
                              onCheckedChange={(checked) => handleSelectPost(post.id, checked as boolean)}
                            />
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="font-medium">{post.title}</div>
                              <div className="text-sm text-muted-foreground">
                                {truncateText(post.excerpt)}
                              </div>
                              {post.tags && post.tags.length > 0 && (
                                <div className="flex flex-wrap gap-1">
                                  {post.tags.slice(0, 3).map((tag, index) => (
                                    <Badge key={index} variant="outline" className="text-xs">
                                      <Tag className="mr-1 h-3 w-3" />
                                      {tag}
                                    </Badge>
                                  ))}
                                  {post.tags.length > 3 && (
                                    <Badge variant="outline" className="text-xs">
                                      +{post.tags.length - 3} more
                                    </Badge>
                                  )}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {post.category ? (
                              <Badge variant="secondary">{post.category.title}</Badge>
                            ) : (
                              <span className="text-muted-foreground">No category</span>
                            )}
                          </TableCell>
                          <TableCell>{getStatusBadge(post.status)}</TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Avatar className="h-6 w-6">
                                <AvatarFallback className="text-xs">
                                  {post.created_by?.substring(0, 2).toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-sm">{post.created_by}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {post.published_at ? (
                              <div className="text-sm">
                                <Calendar className="inline mr-1 h-3 w-3" />
                                {formatDate(post.published_at)}
                              </div>
                            ) : (
                              <span className="text-muted-foreground">Not published</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="text-sm text-muted-foreground">
                              {formatDate(post.updated_at)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => handleViewPost(post)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleEditPost(post)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleBulkStatusUpdate('published')}>
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  Publish
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleBulkStatusUpdate('draft')}>
                                  <Clock className="mr-2 h-4 w-4" />
                                  Draft
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleBulkStatusUpdate('archived')}>
                                  <Archive className="mr-2 h-4 w-4" />
                                  Archive
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-destructive"
                                  onClick={() => handleDeletePost(post.id)}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Mobile Card Layout - Visible on mobile and tablet */}
                <div className="lg:hidden space-y-4">
                  {posts.map((post) => (
                    <Card key={post.id} className="p-4">
                      <div className="space-y-3">
                        {/* Header with checkbox and actions */}
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3 flex-1">
                            <Checkbox
                              checked={selectedPosts.includes(post.id)}
                              onCheckedChange={(checked) => handleSelectPost(post.id, checked as boolean)}
                              className="mt-1"
                            />
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-sm sm:text-base leading-tight">{post.title}</h3>
                              <p className="text-xs sm:text-sm text-muted-foreground mt-1 line-clamp-2">
                                {truncateText(post.excerpt, 80)}
                              </p>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0 flex-shrink-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewPost(post)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEditPost(post)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleBulkStatusUpdate('published')}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Publish
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleBulkStatusUpdate('draft')}>
                                <Clock className="mr-2 h-4 w-4" />
                                Draft
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleBulkStatusUpdate('archived')}>
                                <Archive className="mr-2 h-4 w-4" />
                                Archive
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-destructive"
                                onClick={() => handleDeletePost(post.id)}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        {/* Status and Category */}
                        <div className="flex flex-wrap items-center gap-2">
                          {getStatusBadge(post.status)}
                          {post.category ? (
                            <Badge variant="secondary" className="text-xs">{post.category.title}</Badge>
                          ) : (
                            <span className="text-xs text-muted-foreground">No category</span>
                          )}
                        </div>

                        {/* Tags */}
                        {post.tags && post.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {post.tags.slice(0, 2).map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                <Tag className="mr-1 h-3 w-3" />
                                {tag}
                              </Badge>
                            ))}
                            {post.tags.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{post.tags.length - 2} more
                              </Badge>
                            )}
                          </div>
                        )}

                        {/* Author and Dates */}
                        <div className="flex flex-col space-y-2 text-xs text-muted-foreground">
                          <div className="flex items-center space-x-2">
                            <Avatar className="h-5 w-5">
                              <AvatarFallback className="text-xs">
                                {post.created_by?.substring(0, 2).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <span>By {post.created_by}</span>
                          </div>
                          <div className="flex flex-col space-y-1">
                            {post.published_at ? (
                              <div className="flex items-center">
                                <Calendar className="inline mr-1 h-3 w-3" />
                                Published: {formatDate(post.published_at)}
                              </div>
                            ) : (
                              <span>Not published</span>
                            )}
                            <div>Updated: {formatDate(post.updated_at)}</div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <Card>
            <CardContent className="pt-4 sm:pt-6">
              <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                <div className="text-xs sm:text-sm text-muted-foreground text-center sm:text-left">
                  {(() => {
                    const currentPage = pagination.currentPage || 1
                    const itemsPerPage = pagination.itemsPerPage || 10
                    const totalItems = pagination.totalItems || 0
                    const startItem = ((currentPage - 1) * itemsPerPage) + 1
                    const endItem = Math.min(currentPage * itemsPerPage, totalItems)

                    return `Showing ${startItem} to ${endItem} of ${totalItems} results`
                  })()}
                </div>
                <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2">
                  {/* Previous/Next buttons for mobile */}
                  <div className="flex items-center justify-center space-x-2 sm:hidden">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.currentPage - 1)}
                      disabled={pagination.currentPage <= 1}
                      className="flex-1"
                    >
                      <ChevronLeft className="h-4 w-4 mr-1" />
                      Previous
                    </Button>
                    <span className="text-sm text-muted-foreground px-2">
                      {pagination.currentPage} / {pagination.totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.currentPage + 1)}
                      disabled={pagination.currentPage >= pagination.totalPages}
                      className="flex-1"
                    >
                      Next
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>

                  {/* Full pagination for desktop */}
                  <div className="hidden sm:flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.currentPage - 1)}
                      disabled={pagination.currentPage <= 1}
                    >
                      <ChevronLeft className="h-4 w-4 mr-1" />
                      Previous
                    </Button>

                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        const pageNumber = i + 1
                        return (
                          <Button
                            key={pageNumber}
                            variant={pagination.currentPage === pageNumber ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(pageNumber)}
                            className="w-8 h-8 p-0"
                          >
                            {pageNumber}
                          </Button>
                        )
                      })}
                      {pagination.totalPages > 5 && (
                        <>
                          <span className="text-muted-foreground">...</span>
                          <Button
                            variant={pagination.currentPage === pagination.totalPages ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(pagination.totalPages)}
                            className="w-8 h-8 p-0"
                          >
                            {pagination.totalPages}
                          </Button>
                        </>
                      )}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.currentPage + 1)}
                      disabled={pagination.currentPage >= pagination.totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Create Dialog */}
        <BlogPostDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          categories={categories}
          onSave={handleCreatePost}
          loading={dialogLoading}
        />

        {/* Edit Dialog */}
        <BlogPostDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          post={selectedPost}
          categories={categories}
          onSave={handleUpdatePost}
          loading={dialogLoading}
        />

        {/* View Dialog */}
        <BlogPostViewDialog
          open={isViewDialogOpen}
          onOpenChange={setIsViewDialogOpen}
          post={selectedPost}
          onEdit={handleViewToEdit}
        />
      </div>
    </AppLayout>
  )
}

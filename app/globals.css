@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sage-50: #f8faf8;
    --sage-100: #f0f4f0;
    --sage-200: #dce7dc;
    --sage-300: #c0d4c0;
    --sage-400: #9cba9c;
    --sage-500: #77a077;
    --sage-600: #5c8a5c;
    --sage-700: #4a714a;
    --sage-800: #3e5e3e;
    --sage-900: #344e34;
    --sage-950: #172617;

    --chart-1: 142 76% 36%;
    --chart-2: 172 66% 50%;
    --chart-3: 204 70% 53%;
    --chart-4: 271 81% 56%;
    --chart-5: 346 77% 49.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom styles for sage and gold theme */
.bg-sage-50 {
  background-color: hsl(var(--sage-50));
}
.bg-sage-100 {
  background-color: hsl(var(--sage-100));
}
.bg-sage-200 {
  background-color: hsl(var(--sage-200));
}
.bg-sage-700 {
  background-color: hsl(var(--sage-700));
}
.bg-sage-800 {
  background-color: hsl(var(--sage-800));
}

.text-sage-600 {
  color: hsl(var(--sage-600));
}
.text-sage-700 {
  color: hsl(var(--sage-700));
}
.text-sage-800 {
  color: hsl(var(--sage-800));
}
.text-sage-900 {
  color: hsl(var(--sage-900));
}

.border-sage-200 {
  border-color: hsl(var(--sage-200));
}

.hover\:bg-sage-200:hover {
  background-color: hsl(var(--sage-200));
}
.hover\:bg-sage-800:hover {
  background-color: hsl(var(--sage-800));
}

/* Custom grid for heatmap */
.grid-cols-24 {
  grid-template-columns: repeat(24, minmax(0, 1fr));
}

/* Custom animations for billing success page */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(34, 197, 94, 0.5);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200px 100%;
  animation: shimmer 2s infinite linear;
}

/* Enhanced shadow utilities */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Custom delay classes */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

.delay-700 {
  animation-delay: 0.7s;
}

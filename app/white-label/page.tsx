import type { Metadata } from "next"

import { WhiteLabelScoreboard } from "@/components/white-label-scoreboard"
import { ManufacturerContactLog } from "@/components/manufacturer-contact-log"
import { PrivateLabelTracker } from "@/components/private-label-tracker"
import { RoiCalculator } from "@/components/roi-calculator"
import { AppLayout } from "@/components/layouts/app-layout"

export const metadata: Metadata = {
  title: "White Label Opportunity Engine",
  description: "Track and manage white label product opportunities.",
}

export default function WhiteLabelPage() {
  return (
    <AppLayout>
      <div className="flex flex-col gap-8 p-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">White Label Opportunity Engine</h1>
            <p className="text-muted-foreground">
              Track potential products by sales, margin, competition, and manage your white label journey.
            </p>
          </div>
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          <WhiteLabelScoreboard />
          <RoiCalculator />
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          <ManufacturerContactLog />
          <PrivateLabelTracker />
        </div>
      </div>
    </AppLayout>
  )
}

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, Facebook, Instagram, Linkedin, Plus, Twitter, Youtube } from "lucide-react"
import { AppLayout } from "@/components/layouts/app-layout"

export default function ContentPage() {
  return (
    <AppLayout>
      <div className="space-y-4 p-4 sm:p-6 lg:p-8 pt-4 sm:pt-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">Content Management</h2>
          <div className="flex items-center space-x-2">
            <Button className="w-full sm:w-auto">
              <Plus className="mr-2 h-4 w-4" />
              <span className="">Create New Content</span>
            </Button>
          </div>
        </div>

        <Tabs defaultValue="all" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 h-auto">
            <TabsTrigger value="all" className="text-xs sm:text-sm">All Content</TabsTrigger>
            <TabsTrigger value="scheduled" className="text-xs sm:text-sm">Scheduled</TabsTrigger>
            <TabsTrigger value="published" className="text-xs sm:text-sm">Published</TabsTrigger>
            <TabsTrigger value="drafts" className="text-xs sm:text-sm">Drafts</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            <div className="grid gap-4">
              <Card>
                <CardHeader className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0">
                  <div className="grid gap-1 flex-1">
                    <CardTitle className="text-lg sm:text-xl">Top 10 Travel Destinations for 2023</CardTitle>
                    <CardDescription className="text-sm">Video + Social Posts + Blog Article</CardDescription>
                  </div>
                  <div className="flex items-center gap-2 flex-wrap">
                    <Badge>Scheduled</Badge>
                    <Badge variant="outline">
                      Travel
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <Calendar className="mr-1 h-4 w-4" />
                      <span>May 6, 2023</span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="mr-1 h-4 w-4" />
                      <span>9:00 AM</span>
                    </div>
                    <div className="flex items-center">
                      <Avatar className="h-6 w-6 mr-1">
                        <AvatarImage src="/placeholder-user.jpg" alt="Avatar" />
                        <AvatarFallback>TR</AvatarFallback>
                      </Avatar>
                      <span>Travel Avatar</span>
                    </div>
                  </div>
                  <div className="mt-4 flex flex-wrap items-center gap-2">
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <Youtube className="h-3 w-3" />
                      <span>Video</span>
                    </Badge>
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <Instagram className="h-3 w-3" />
                      <span>Instagram</span>
                    </Badge>
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <Twitter className="h-3 w-3" />
                      <span>Twitter</span>
                    </Badge>
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <Facebook className="h-3 w-3" />
                      <span>Facebook</span>
                    </Badge>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col sm:flex-row gap-2 sm:justify-between">
                  <Button variant="outline" className="w-full sm:w-auto">Preview</Button>
                  <Button className="w-full sm:w-auto">Edit Content</Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0">
                  <div className="grid gap-1 flex-1">
                    <CardTitle className="text-lg sm:text-xl">Minimalist Home Office Setup Guide</CardTitle>
                    <CardDescription className="text-sm">Video + Social Posts + Blog Article</CardDescription>
                  </div>
                  <div className="flex items-center gap-2 flex-wrap">
                    <Badge>Scheduled</Badge>
                    <Badge variant="outline">
                      Home
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <Calendar className="mr-1 h-4 w-4" />
                      <span>May 7, 2023</span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="mr-1 h-4 w-4" />
                      <span>10:30 AM</span>
                    </div>
                    <div className="flex items-center">
                      <Avatar className="h-6 w-6 mr-1">
                        <AvatarImage src="/placeholder-user.jpg" alt="Avatar" />
                        <AvatarFallback>HM</AvatarFallback>
                      </Avatar>
                      <span>Home Avatar</span>
                    </div>
                  </div>
                  <div className="mt-4 flex flex-wrap items-center gap-2">
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <Youtube className="h-3 w-3" />
                      <span>Video</span>
                    </Badge>
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <Instagram className="h-3 w-3" />
                      <span>Instagram</span>
                    </Badge>
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <Facebook className="h-3 w-3" />
                      <span>Facebook</span>
                    </Badge>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col sm:flex-row gap-2 sm:justify-between">
                  <Button variant="outline" className="w-full sm:w-auto">Preview</Button>
                  <Button className="w-full sm:w-auto">Edit Content</Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0">
                  <div className="grid gap-1 flex-1">
                    <CardTitle className="text-lg sm:text-xl">5 Superfoods You Should Eat Daily</CardTitle>
                    <CardDescription className="text-sm">Video + Social Posts + Blog Article</CardDescription>
                  </div>
                  <div className="flex items-center gap-2 flex-wrap">
                    <Badge>Scheduled</Badge>
                    <Badge variant="outline">
                      Health
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <Calendar className="mr-1 h-4 w-4" />
                      <span>May 8, 2023</span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="mr-1 h-4 w-4" />
                      <span>8:15 AM</span>
                    </div>
                    <div className="flex items-center">
                      <Avatar className="h-6 w-6 mr-1">
                        <AvatarImage src="/placeholder-user.jpg" alt="Avatar" />
                        <AvatarFallback>HE</AvatarFallback>
                      </Avatar>
                      <span>Health Avatar</span>
                    </div>
                  </div>
                  <div className="mt-4 flex flex-wrap items-center gap-2">
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <Youtube className="h-3 w-3" />
                      <span>Video</span>
                    </Badge>
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <Instagram className="h-3 w-3" />
                      <span>Instagram</span>
                    </Badge>
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <Linkedin className="h-3 w-3" />
                      <span>LinkedIn</span>
                    </Badge>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col sm:flex-row gap-2 sm:justify-between">
                  <Button variant="outline" className="w-full sm:w-auto">Preview</Button>
                  <Button className="w-full sm:w-auto">Edit Content</Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  )
}

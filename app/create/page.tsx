'use client'

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Upload } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { AppLayout } from "@/components/layouts/app-layout"

export default function CreatePage() {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined)
  return (
    <AppLayout>
      <div className="space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Create New Content</h2>
        </div>

        <Tabs defaultValue="video" className="space-y-4">
          <TabsList>
            <TabsTrigger value="video">Video</TabsTrigger>
            <TabsTrigger value="social">Social Posts</TabsTrigger>
            <TabsTrigger value="blog">Blog Article</TabsTrigger>
          </TabsList>

          <TabsContent value="video" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Video Details</CardTitle>
                <CardDescription>Create a new video that will be distributed across platforms</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid w-full gap-1.5">
                  <Label htmlFor="video-title">Title</Label>
                  <Input id="video-title" placeholder="Enter video title" />
                </div>

                <div className="grid w-full gap-1.5">
                  <Label htmlFor="video-description">Description</Label>
                  <Textarea id="video-description" placeholder="Enter video description" />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="grid w-full gap-1.5">
                    <Label htmlFor="avatar">Avatar</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select avatar" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="travel">Travel Avatar</SelectItem>
                        <SelectItem value="home">Home Avatar</SelectItem>
                        <SelectItem value="health">Health Avatar</SelectItem>
                        <SelectItem value="fitness">Fitness Avatar</SelectItem>
                        <SelectItem value="entrepreneur">Entrepreneur Avatar</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid w-full gap-1.5">
                    <Label htmlFor="category">Category</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="travel">Travel</SelectItem>
                        <SelectItem value="home">Home</SelectItem>
                        <SelectItem value="health">Health</SelectItem>
                        <SelectItem value="fitness">Fitness</SelectItem>
                        <SelectItem value="entrepreneur">Entrepreneurship</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid w-full gap-1.5">
                  <Label>Upload Video</Label>
                  <div className="border-2 border-dashed rounded-md p-8 text-center">
                    <Upload className="mx-auto h-8 w-8 text-muted-foreground" />
                    <div className="mt-2">
                      <Button variant="secondary" size="sm">
                        Select File
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">MP4, MOV or AVI. Max 2GB.</p>
                  </div>
                </div>

                <div className="grid w-full gap-1.5">
                  <Label>Platforms</Label>
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="youtube" />
                      <label
                        htmlFor="youtube"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        YouTube
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="instagram" />
                      <label
                        htmlFor="instagram"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Instagram
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="tiktok" />
                      <label
                        htmlFor="tiktok"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        TikTok
                      </label>
                    </div>
                  </div>
                </div>

                <div className="grid w-full gap-1.5">
                  <Label>Schedule</Label>
                  <div className="grid grid-cols-2 gap-4">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn("w-full justify-start text-left font-normal", !selectedDate && "text-muted-foreground")}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {selectedDate ? format(selectedDate, "PPP") : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={selectedDate}
                          onSelect={setSelectedDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>

                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select time" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="9am">9:00 AM</SelectItem>
                        <SelectItem value="12pm">12:00 PM</SelectItem>
                        <SelectItem value="3pm">3:00 PM</SelectItem>
                        <SelectItem value="6pm">6:00 PM</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid w-full gap-1.5">
                  <Label>Generate Additional Content</Label>
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="social-posts" />
                      <label
                        htmlFor="social-posts"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Generate Social Media Posts
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="blog-article" />
                      <label
                        htmlFor="blog-article"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Generate Blog Article
                      </label>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline">Save as Draft</Button>
                <Button>Create & Schedule</Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  )
}

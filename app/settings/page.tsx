import { But<PERSON> } from "@/components/ui/button"
import { WebsiteIntegrationGuide } from "@/components/website-integration-guide"
import { WebhookManager } from "@/components/webhook-manager"
import { Toaster } from "@/components/ui/toaster"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { AppLayout } from "@/components/layouts/app-layout"

export default function SettingsPage() {
  return (
    <AppLayout>
      <div className="space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Settings</h2>
        </div>

        <Tabs defaultValue="integrations" className="space-y-4">
          <TabsList>
            <TabsTrigger value="integrations">Website Integration</TabsTrigger>
            <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
            <TabsTrigger value="api">API Keys</TabsTrigger>
          </TabsList>

          <TabsContent value="integrations">
            <WebsiteIntegrationGuide />
          </TabsContent>

          <TabsContent value="webhooks">
            <WebhookManager />
          </TabsContent>

          <TabsContent value="api">
            <div className="space-y-4">
              <div className="grid gap-4">
                <div className="border rounded-md p-4">
                  <h3 className="text-lg font-medium mb-2">API Access</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Use these API keys to access the Command Center API programmatically. Keep your API keys secure and
                    never share them publicly.
                  </p>

                  <div className="space-y-4">
                    <div className="border-b pb-4">
                      <div className="flex justify-between items-center mb-2">
                        <div>
                          <h4 className="font-medium">Production API Key</h4>
                          <p className="text-xs text-muted-foreground">Use this key for your production environment</p>
                        </div>
                        <Button variant="outline" size="sm">
                          Regenerate
                        </Button>
                      </div>
                      <div className="flex space-x-2">
                        <input
                          type="password"
                          value="ak_prod_7f8h3j2k1l0p9o8i7u6y5t4r3e2w1q"
                          readOnly
                          className="flex-1 px-3 py-2 border rounded-md text-sm"
                        />
                        <Button variant="outline" size="sm">
                          Copy
                        </Button>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <div>
                          <h4 className="font-medium">Development API Key</h4>
                          <p className="text-xs text-muted-foreground">Use this key for testing and development</p>
                        </div>
                        <Button variant="outline" size="sm">
                          Regenerate
                        </Button>
                      </div>
                      <div className="flex space-x-2">
                        <input
                          type="password"
                          value="ak_dev_9g8f7e6d5c4b3a2z1y2x3c4v5b6n7m8"
                          readOnly
                          className="flex-1 px-3 py-2 border rounded-md text-sm"
                        />
                        <Button variant="outline" size="sm">
                          Copy
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
      <Toaster />
    </AppLayout>
  )
}

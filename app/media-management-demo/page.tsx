'use client'

import React from "react"
import { AppLayout } from "@/components/layouts/app-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { 
  Image as ImageIcon, 
  Video, 
  Search, 
  Filter, 
  Eye, 
  Trash2, 
  Download,
  ArrowRight,
  CheckCircle,
  Database,
  Zap,
  Shield
} from "lucide-react"

export default function MediaManagementDemoPage() {
  return (
    <AppLayout>
      <div className="container mx-auto py-6 space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold">Media Management System</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Comprehensive media file management with advanced filtering, pagination, and CRUD operations
          </p>
          <div className="flex justify-center">
            <Link href="/media-management">
              <Button size="lg" className="text-lg px-8">
                <ImageIcon className="mr-2 h-5 w-5" />
                Open Media Management
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>

        {/* Features Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="text-center">
              <Database className="mx-auto h-8 w-8 text-blue-500 mb-2" />
              <CardTitle className="text-lg">Advanced Filtering</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-sm text-muted-foreground">
                Filter by media type, status, search terms, and product associations
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <Zap className="mx-auto h-8 w-8 text-green-500 mb-2" />
              <CardTitle className="text-lg">Smart Pagination</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-sm text-muted-foreground">
                Efficient pagination with customizable page sizes and navigation
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <Eye className="mx-auto h-8 w-8 text-purple-500 mb-2" />
              <CardTitle className="text-lg">Media Preview</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-sm text-muted-foreground">
                View detailed media information with inline previews and metadata
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <Shield className="mx-auto h-8 w-8 text-red-500 mb-2" />
              <CardTitle className="text-lg">CRUD Operations</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-sm text-muted-foreground">
                Complete Create, Read, Update, Delete operations with confirmation dialogs
              </p>
            </CardContent>
          </Card>
        </div>

        {/* API Endpoints */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">API Endpoints Implemented</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="space-y-3">
                <h3 className="font-semibold text-lg">Media List & Filtering</h3>
                <div className="space-y-2">
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center space-x-2 mb-1">
                      <Badge variant="outline" className="bg-green-100 text-green-800">GET</Badge>
                      <code className="text-sm">/api/media</code>
                    </div>
                    <p className="text-xs text-green-700">Basic media list with pagination</p>
                  </div>
                  
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center space-x-2 mb-1">
                      <Badge variant="outline" className="bg-blue-100 text-blue-800">GET</Badge>
                      <code className="text-sm">/api/media?page=1&limit=10&media_type=image&status=completed</code>
                    </div>
                    <p className="text-xs text-blue-700">Advanced filtering with multiple parameters</p>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="font-semibold text-lg">Individual Media Operations</h3>
                <div className="space-y-2">
                  <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
                    <div className="flex items-center space-x-2 mb-1">
                      <Badge variant="outline" className="bg-purple-100 text-purple-800">GET</Badge>
                      <code className="text-sm">/api/media/{'{mediaId}'}</code>
                    </div>
                    <p className="text-xs text-purple-700">Get detailed media information</p>
                  </div>
                  
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center space-x-2 mb-1">
                      <Badge variant="outline" className="bg-red-100 text-red-800">DELETE</Badge>
                      <code className="text-sm">/api/media/{'{mediaId}'}</code>
                    </div>
                    <p className="text-xs text-red-700">Delete media file with confirmation</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Features Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">Key Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="font-semibold text-lg flex items-center">
                  <Search className="mr-2 h-5 w-5 text-blue-500" />
                  Advanced Search & Filtering
                </h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Real-time search across media files</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Filter by media type (image, video)</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Filter by status (completed, processing, failed, pending)</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Product association filtering</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-4">
                <h3 className="font-semibold text-lg flex items-center">
                  <Eye className="mr-2 h-5 w-5 text-purple-500" />
                  Media Management
                </h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Detailed media preview with metadata</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>File size, dimensions, and duration display</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Status badges with color coding</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Safe delete operations with confirmation</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Technical Implementation */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">Technical Implementation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="space-y-3">
                <h3 className="font-semibold">Frontend Architecture</h3>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• React with TypeScript</li>
                  <li>• Tailwind CSS for styling</li>
                  <li>• Shadcn/ui components</li>
                  <li>• Custom hooks for state management</li>
                  <li>• Responsive design patterns</li>
                </ul>
              </div>

              <div className="space-y-3">
                <h3 className="font-semibold">API Integration</h3>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• RESTful API design</li>
                  <li>• JWT authentication</li>
                  <li>• Error handling & fallbacks</li>
                  <li>• Loading states & UX</li>
                  <li>• Optimistic updates</li>
                </ul>
              </div>

              <div className="space-y-3">
                <h3 className="font-semibold">User Experience</h3>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Intuitive table interface</li>
                  <li>• Modal dialogs for details</li>
                  <li>• Confirmation dialogs</li>
                  <li>• Toast notifications</li>
                  <li>• Keyboard navigation</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="text-center py-8">
            <h2 className="text-2xl font-bold mb-4">Ready to Manage Your Media?</h2>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Experience the full power of our media management system with advanced filtering, 
              real-time search, and comprehensive CRUD operations.
            </p>
            <div className="flex justify-center space-x-4">
              <Link href="/media-management">
                <Button size="lg">
                  <ImageIcon className="mr-2 h-5 w-5" />
                  Open Media Management
                </Button>
              </Link>
              <Link href="/products">
                <Button variant="outline" size="lg">
                  <ArrowRight className="mr-2 h-5 w-5" />
                  View Products
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}

"use client"

import React, { useState } from 'react'
import { AppLayout } from '@/components/layouts/app-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { subscriptionPlanService } from '@/lib/api/subscription-plans'
import { Code, Play, RefreshCw } from 'lucide-react'

export default function PlanManagementTestPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState('')
  const { toast } = useToast()

  const testEndpoints = [
    {
      name: 'Get All Plans',
      description: 'Test fetching all subscription plans',
      action: async () => {
        const response = await subscriptionPlanService.getPlans({
          page: 1,
          limit: 10,
          sort_by: 'sort_order',
          sort_order: 'ASC',
        })
        return response
      }
    },
    {
      name: 'Create Test Plan',
      description: 'Test creating a new subscription plan',
      action: async () => {
        const testPlan = {
          name: 'Test Plan',
          slug: 'test-plan',
          description: 'A test subscription plan created via API',
          price_monthly: 19.99,
          price_annual: 199.99,
          status: 'draft' as const,
          features: {
            api_access: true,
            max_products: 50,
            custom_branding: false,
            product_editing: true,
            ai_agents_access: true,
            analytics_access: true,
            custom_workflows: false,
            max_team_members: 3,
            priority_support: false,
            product_analysis: true,
            product_creation: true,
            advanced_analytics: false,
            market_surveillance: false,
            monthly_video_exports: 25,
            basic_media_generation: true,
            monthly_analysis_requests: 25,
            monthly_media_generations: 50,
          },
          sort_order: 99,
          is_popular: false,
        }
        const response = await subscriptionPlanService.createPlan(testPlan)
        return response
      }
    }
  ]

  const runTest = async (test: typeof testEndpoints[0]) => {
    setLoading(true)
    setResult('')
    
    try {
      const startTime = Date.now()
      const response = await test.action()
      const endTime = Date.now()
      
      const resultData = {
        test: test.name,
        success: true,
        duration: `${endTime - startTime}ms`,
        response: response,
        timestamp: new Date().toISOString()
      }
      
      setResult(JSON.stringify(resultData, null, 2))
      
      toast({
        title: "Test Successful",
        description: `${test.name} completed in ${endTime - startTime}ms`,
      })
    } catch (error: any) {
      const resultData = {
        test: test.name,
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
      
      setResult(JSON.stringify(resultData, null, 2))
      
      toast({
        title: "Test Failed",
        description: error.message,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const testAllEndpoints = async () => {
    setLoading(true)
    setResult('')
    
    const results = []
    
    for (const test of testEndpoints) {
      try {
        const startTime = Date.now()
        const response = await test.action()
        const endTime = Date.now()
        
        results.push({
          test: test.name,
          success: true,
          duration: `${endTime - startTime}ms`,
          response: response,
        })
      } catch (error: any) {
        results.push({
          test: test.name,
          success: false,
          error: error.message,
        })
      }
    }
    
    const finalResult = {
      summary: {
        total: testEndpoints.length,
        passed: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        timestamp: new Date().toISOString()
      },
      results: results
    }
    
    setResult(JSON.stringify(finalResult, null, 2))
    setLoading(false)
    
    toast({
      title: "All Tests Completed",
      description: `${finalResult.summary.passed}/${finalResult.summary.total} tests passed`,
      variant: finalResult.summary.failed > 0 ? "destructive" : "default",
    })
  }

  return (
    <AppLayout>
      <div className="space-y-6 p-8 pt-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Plan Management API Test</h2>
            <p className="text-muted-foreground">
              Test the subscription plan API endpoints
            </p>
          </div>
          <Button onClick={testAllEndpoints} disabled={loading}>
            <Play className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
            Run All Tests
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Code className="h-5 w-5" />
                <span>API Tests</span>
              </CardTitle>
              <CardDescription>
                Individual endpoint tests for subscription plan management
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {testEndpoints.map((test, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">{test.name}</h4>
                    <p className="text-sm text-muted-foreground">{test.description}</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => runTest(test)}
                    disabled={loading}
                  >
                    {loading ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Play className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <CardDescription>
                API response data and error information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea
                value={result}
                readOnly
                placeholder="Test results will appear here..."
                className="min-h-[400px] font-mono text-xs"
              />
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>API Endpoints</CardTitle>
            <CardDescription>
              Available subscription plan management endpoints
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium">GET /api/subscription-plans</h4>
                  <p className="text-sm text-muted-foreground">Get all plans with pagination</p>
                  <code className="text-xs bg-muted p-2 rounded block">
                    ?page=1&limit=10&sort_by=sort_order&sort_order=ASC
                  </code>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">GET /api/subscription-plans/:id</h4>
                  <p className="text-sm text-muted-foreground">Get plan by ID</p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">POST /api/subscription-plans</h4>
                  <p className="text-sm text-muted-foreground">Create new plan</p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">PUT /api/subscription-plans/:id</h4>
                  <p className="text-sm text-muted-foreground">Update existing plan</p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">DELETE /api/subscription-plans/:id</h4>
                  <p className="text-sm text-muted-foreground">Delete plan</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}

"use client"

import React, { useState, useEffect } from 'react'
import { AppLayout } from '@/components/layouts/app-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { useToast } from '@/hooks/use-toast'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import {
  Crown,
  Plus,
  Search,
  RefreshCw,
  MoreHorizontal,
  Edit,
  Trash2,
  Copy,
  Eye,
  Star,
  DollarSign,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react'
import { subscriptionPlanService, type SubscriptionPlan } from '@/lib/api/subscription-plans'
import { PlanFormDialog } from '@/components/plan-management/plan-form-dialog'
import { PlanDetailsDialog } from '@/components/plan-management/plan-details-dialog'

export default function PlanManagementPage() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null)
  const [deletingPlanId, setDeletingPlanId] = useState<string | null>(null)
  const { toast } = useToast()

  const fetchPlans = async (page = 1, search = '') => {
    try {
      setLoading(true)
      const response = await subscriptionPlanService.getPlans({
        page,
        limit: 10,
        sort_by: 'sort_order',
        sort_order: 'ASC',
      })

      if (response.success) {
        let filteredPlans = response.data

        // Apply client-side search filter if search query exists
        if (search.trim()) {
          filteredPlans = response.data.filter(plan =>
            plan.name.toLowerCase().includes(search.toLowerCase()) ||
            plan.description.toLowerCase().includes(search.toLowerCase()) ||
            plan.slug.toLowerCase().includes(search.toLowerCase())
          )
        }

        setPlans(filteredPlans)
        setTotalPages(response.pagination.totalPages)
        setTotalItems(response.pagination.totalItems)
        setCurrentPage(page)
      }
    } catch (error: any) {
      console.error('Error fetching plans:', error)

      // Show demo data when API is not available
      if (error.message.includes('Backend service is not available') || error.message.includes('Failed to fetch')) {
        const demoPlans: SubscriptionPlan[] = [
          {
            id: 'demo-1',
            name: 'Starter Plan',
            slug: 'starter',
            description: 'Perfect for beginners getting started with Avatar AI',
            price_monthly: 9.99,
            price_annual: 99.99,
            stripe_price_monthly_id: null,
            stripe_price_annual_id: null,
            status: 'active',
            features: {
              api_access: false,
              product_manage: true,
              ai_agents_access: false,
              analytics_access: true,
              image_generation: true,
              video_generation: true,
              priority_support: true,
              product_analysis: true,
              advanced_analytics: true,
              prototype_pipeline: true,
              production_pipeline: true,
              brog_management: true,
              limits: {
                products_created: 500,
                analysis_requests: 100,
                image_generations: 200,
                video_generations: 100,
                prototype_image_generations: 50,
                prototype_video_generations: 25,
                brogs_created: 50,
              },
            },
            sort_order: 0,
            is_popular: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
          {
            id: 'demo-2',
            name: 'Professional Plan',
            slug: 'professional',
            description: 'For growing businesses with advanced needs',
            price_monthly: 29.99,
            price_annual: 299.99,
            stripe_price_monthly_id: null,
            stripe_price_annual_id: null,
            status: 'active',
            features: {
              api_access: true,
              product_manage: true,
              ai_agents_access: true,
              analytics_access: true,
              image_generation: true,
              video_generation: true,
              priority_support: true,
              product_analysis: true,
              advanced_analytics: true,
              prototype_pipeline: true,
              production_pipeline: true,
              brog_management: true,
              limits: {
                products_created: 1000,
                analysis_requests: 500,
                image_generations: 1000,
                video_generations: 500,
                prototype_image_generations: 200,
                prototype_video_generations: 100,
                brogs_created: 200,
              },
            },
            sort_order: 1,
            is_popular: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        ]

        setPlans(demoPlans)
        setTotalPages(1)
        setTotalItems(demoPlans.length)
        setCurrentPage(1)

        toast({
          title: "Demo Mode",
          description: "Showing demo data. Connect to backend to manage real plans.",
          variant: "default",
        })
      } else {
        toast({
          title: "Error",
          description: error.message || "Failed to fetch subscription plans",
          variant: "destructive",
        })
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPlans(currentPage, searchQuery)
  }, [currentPage])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
    fetchPlans(1, query)
  }

  const handleCreatePlan = async (planData: any) => {
    try {
      const response = await subscriptionPlanService.createPlan(planData)
      if (response.success) {
        toast({
          title: "Success",
          description: response.message || "Subscription plan created successfully",
        })
        setIsCreateDialogOpen(false)
        fetchPlans(currentPage, searchQuery)
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to create subscription plan",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      // Handle API error responses
      let errorMessage = "Failed to create subscription plan"

      if (error.message) {
        // Check if it's a structured API error
        if (error.message.includes('HTTP 400:')) {
          try {
            const errorData = JSON.parse(error.message.replace('HTTP 400: ', ''))
            errorMessage = errorData.message || errorMessage
          } catch {
            errorMessage = error.message
          }
        } else {
          errorMessage = error.message
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  const handleUpdatePlan = async (planData: any) => {
    if (!selectedPlan) return

    try {
      const response = await subscriptionPlanService.updatePlan(selectedPlan.id, planData)
      if (response.success) {
        toast({
          title: "Success",
          description: response.message || "Subscription plan updated successfully",
        })
        setIsEditDialogOpen(false)
        setSelectedPlan(null)
        fetchPlans(currentPage, searchQuery)
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to update subscription plan",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      // Handle API error responses
      let errorMessage = "Failed to update subscription plan"

      if (error.message) {
        // Check if it's a structured API error
        if (error.message.includes('HTTP 400:')) {
          try {
            const errorData = JSON.parse(error.message.replace('HTTP 400: ', ''))
            errorMessage = errorData.message || errorMessage
          } catch {
            errorMessage = error.message
          }
        } else {
          errorMessage = error.message
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  const handleDeletePlan = async (planId: string) => {
    try {
      setDeletingPlanId(planId)
      const response = await subscriptionPlanService.deletePlan(planId)
      if (response.success) {
        toast({
          title: "Success",
          description: "Subscription plan deleted successfully",
        })
        fetchPlans(currentPage, searchQuery)
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete subscription plan",
        variant: "destructive",
      })
    } finally {
      setDeletingPlanId(null)
    }
  }

  const handleDuplicatePlan = async (plan: SubscriptionPlan) => {
    try {
      const timestamp = Date.now()
      const newName = `${plan.name} (Copy)`
      // Make slug unique by adding timestamp
      const baseSlug = newName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
      const newSlug = `${baseSlug}-${timestamp}`

      // Check if it's demo mode
      if (plan.id.startsWith('demo-')) {
        // Handle demo mode locally
        const duplicatedPlan: SubscriptionPlan = {
          ...plan,
          id: `demo-${timestamp}`,
          name: newName,
          slug: newSlug,
          status: 'inactive',
          is_popular: false,
          sort_order: plan.sort_order + 1,
          stripe_price_monthly_id: null,
          stripe_price_annual_id: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }

        setPlans(prev => [...prev, duplicatedPlan])

        toast({
          title: "Success",
          description: "Subscription plan duplicated successfully (Demo Mode)",
        })
        return
      }

      // For real plans, create new plan via API
      // Ensure limits exist with default values if not present
      const originalLimits = plan.features.limits || {
        products_created: 10,
        analysis_requests: 5,
        image_generations: 20,
        video_generations: 5,
        prototype_image_generations: 10,
        prototype_video_generations: 3,
        brogs_created: 5,
      }

      const duplicatedPlanData = {
        name: newName,
        slug: newSlug, // This will be unique now
        description: plan.description || '',
        price_monthly: Number(plan.price_monthly) || 0,
        price_annual: Number(plan.price_annual) || 0,
        stripe_price_monthly_id: null,
        stripe_price_annual_id: null,
        status: 'inactive' as const, // Backend only accepts: 'active' | 'inactive' | 'deprecated'
        features: {
          api_access: Boolean(plan.features.api_access),
          product_manage: Boolean(plan.features.product_manage),
          ai_agents_access: Boolean(plan.features.ai_agents_access),
          analytics_access: Boolean(plan.features.analytics_access),
          image_generation: Boolean(plan.features.image_generation),
          video_generation: Boolean(plan.features.video_generation),
          priority_support: Boolean(plan.features.priority_support),
          product_analysis: Boolean(plan.features.product_analysis),
          advanced_analytics: Boolean(plan.features.advanced_analytics),
          prototype_pipeline: Boolean(plan.features.prototype_pipeline),
          production_pipeline: Boolean(plan.features.production_pipeline),
          brog_management: Boolean(plan.features.brog_management),
          limits: {
            products_created: Number(originalLimits.products_created) || 0,
            analysis_requests: Number(originalLimits.analysis_requests) || 0,
            image_generations: Number(originalLimits.image_generations) || 0,
            video_generations: Number(originalLimits.video_generations) || 0,
            prototype_image_generations: Number(originalLimits.prototype_image_generations) || 0,
            prototype_video_generations: Number(originalLimits.prototype_video_generations) || 0,
            brogs_created: Number(originalLimits.brogs_created) || 0,
          }
        },
        sort_order: Number(plan.sort_order) + 1 || 0,
        is_popular: false,
      }



      const response = await subscriptionPlanService.createPlan(duplicatedPlanData)

      if (response.success) {
        toast({
          title: "Success",
          description: response.message || "Subscription plan duplicated successfully",
        })
        fetchPlans(currentPage, searchQuery)
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to duplicate subscription plan",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error('Duplicate plan error:', error)

      let errorMessage = "Failed to duplicate subscription plan"

      if (error.message) {
        if (error.message.includes('HTTP 400:')) {
          try {
            const errorData = JSON.parse(error.message.replace('HTTP 400: ', ''))
            errorMessage = errorData.message || errorMessage
          } catch {
            errorMessage = error.message
          }
        } else if (error.message.includes('slug already exists')) {
          errorMessage = "A plan with this name already exists. Please try a different name."
        } else if (error.message.includes('Backend service is not available') || error.message.includes('Failed to fetch')) {
          errorMessage = "Backend service is not available. Please try again later."
        } else {
          errorMessage = error.message
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default">Active</Badge>
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>
      case 'draft':
        return <Badge variant="outline">Draft</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <AppLayout>
      <div className="space-y-6 p-8 pt-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Plan Management</h2>
            <p className="text-muted-foreground">
              Manage subscription plans, pricing, and features
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button onClick={() => fetchPlans(currentPage, searchQuery)} disabled={loading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
              Refresh
            </Button>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Plan
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Crown className="h-5 w-5" />
              <span>Subscription Plans ({totalItems})</span>
            </CardTitle>
            <CardDescription>
              Manage all subscription plans and their features
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search plans by name, description, or slug..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading plans...</span>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Plan</TableHead>
                      <TableHead>Pricing</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Features</TableHead>
                      <TableHead>Order</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {plans.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          <Crown className="mx-auto h-12 w-12 text-muted-foreground" />
                          <p className="mt-2 text-muted-foreground">
                            {searchQuery ? 'No plans found matching your search.' : 'No subscription plans found.'}
                          </p>
                        </TableCell>
                      </TableRow>
                    ) : (
                      plans.map((plan) => (
                        <TableRow key={plan.id}>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <div>
                                <div className="flex items-center space-x-2">
                                  <p className="font-medium">{plan.name}</p>
                                  {plan.is_popular && (
                                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                  )}
                                </div>
                                <p className="text-sm text-muted-foreground">{plan.slug}</p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="flex items-center space-x-1">
                                <DollarSign className="h-3 w-3" />
                                <span className="text-sm">{formatPrice(plan.price_monthly)}/mo</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <DollarSign className="h-3 w-3" />
                                <span className="text-sm text-muted-foreground">{formatPrice(plan.price_annual)}/yr</span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{getStatusBadge(plan.status)}</TableCell>
                          <TableCell>
                            <div className="text-sm text-muted-foreground">
                              {plan.features.limits.products_created} products, {plan.features.limits.brogs_created} blogs
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{plan.sort_order}</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem
                                  onClick={() => {
                                    setSelectedPlan(plan)
                                    setIsDetailsDialogOpen(true)
                                  }}
                                >
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Details
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => {
                                    setSelectedPlan(plan)
                                    setIsEditDialogOpen(true)
                                  }}
                                >
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit Plan
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleDuplicatePlan(plan)}>
                                  <Copy className="mr-2 h-4 w-4" />
                                  Duplicate
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      Delete Plan
                                    </DropdownMenuItem>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Delete Subscription Plan</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Are you sure you want to delete "{plan.name}"? This action cannot be undone.
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() => handleDeletePlan(plan.id)}
                                        disabled={deletingPlanId === plan.id}
                                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                      >
                                        {deletingPlanId === plan.id ? "Deleting..." : "Delete"}
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <p className="text-sm text-muted-foreground">
                  Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalItems)} of {totalItems} plans
                </p>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Button>
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + 1
                      return (
                        <Button
                          key={page}
                          variant={currentPage === page ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentPage(page)}
                          className="w-8 h-8 p-0"
                        >
                          {page}
                        </Button>
                      )
                    })}
                    {totalPages > 5 && (
                      <>
                        <span className="text-muted-foreground">...</span>
                        <Button
                          variant={currentPage === totalPages ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentPage(totalPages)}
                          className="w-8 h-8 p-0"
                        >
                          {totalPages}
                        </Button>
                      </>
                    )}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <PlanFormDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          onSubmit={handleCreatePlan}
          title="Create New Plan"
        />

        <PlanFormDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onSubmit={handleUpdatePlan}
          plan={selectedPlan}
          title="Edit Plan"
        />

        <PlanDetailsDialog
          open={isDetailsDialogOpen}
          onOpenChange={setIsDetailsDialogOpen}
          plan={selectedPlan}
        />
      </div>
    </AppLayout>
  )
}

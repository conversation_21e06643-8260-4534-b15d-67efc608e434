'use client'

import React, { useState, useEffect } from "react"
import Link from "next/link"
import { useAuth } from "@/contexts/auth-context"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Plus,
  Search,
  Filter,
  Edit,
  BarChart3,
  Eye,
  DollarSign,
  Package,
  TrendingUp,
  ExternalLink,
  Image as ImageIcon,
  Play,
  Video
} from "lucide-react"
import { AppLayout } from "@/components/layouts/app-layout"
import { ProductCreateDialog } from "@/components/product-create-dialog"
import { ProductEditDialog } from "@/components/product-edit-dialog"
import { ProductAnalysisDialog } from "@/components/product-analysis-dialog"
import { ProductMediaDialog } from "@/components/product-media-dialog"
import { MediaDetailDialog } from "@/components/media-detail-dialog"
import { PrototypeVideoDialog } from "@/components/prototype-video-dialog"
import { useToast } from "@/hooks/use-toast"
import { productService, type Product, type CreateProductData, type PaginationInfo } from "@/lib/api/products"
import { categoryService, type Category } from "@/lib/api/categories"
import { type Media } from "@/lib/api/media"

// Helper function to ensure product has all required properties
const normalizeProduct = (product: any): Product => {

  const normalized = {
    ...product,
    name: product.name || "",
    description: product.description || "",
    category: product.category || { id: "", name: "", slug: "", description: "", type: "", is_active: true, is_featured: false, icon: null, color: null },
    analyses: product.analyses || []
  }

  return normalized
}

export default function ProductsPage() {
  const { isAuthenticated, user, isLoading: authLoading } = useAuth()
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [usingLocalProducts, setUsingLocalProducts] = useState(false) // Track if we're managing products locally
  const [forceRender, setForceRender] = useState(0) // Force re-render when products change
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 12,
    totalPages: 1,
    totalItems: 0,
    hasNextPage: false,
    hasPrevPage: false
  })
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isAnalysisDialogOpen, setIsAnalysisDialogOpen] = useState(false)
  const [isMediaDialogOpen, setIsMediaDialogOpen] = useState(false)
  const [isMediaDetailDialogOpen, setIsMediaDetailDialogOpen] = useState(false)
  const [selectedMedia, setSelectedMedia] = useState<Media | null>(null)
  const [isPrototypeVideoDialogOpen, setIsPrototypeVideoDialogOpen] = useState(false)
  const [newlyCreatedProduct, setNewlyCreatedProduct] = useState<Product | null>(null)
  const [newlyCreatedProductData, setNewlyCreatedProductData] = useState<CreateProductData | null>(null)
  const [categories, setCategories] = useState<Category[]>([])
  const { toast } = useToast()

  // Check if user is admin or staff
  const isAdmin = user?.role === 'admin' || user?.role === 'staff'

  useEffect(() => {
    // Wait for auth to be checked before loading products
    if (!authLoading) {
      loadProducts()
      loadCategories()
    }
  }, [authLoading, isAuthenticated])

  // Debug effect to track products state changes
  useEffect(() => {

  }, [products, usingLocalProducts, pagination])

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      loadProducts(newPage, pagination.limit)
    }
  }

  const handleLimitChange = (newLimit: number) => {
    loadProducts(1, newLimit) // Reset to page 1 when changing limit
  }

  const loadProducts = async (page: number = 1, limit: number = 12) => {
    // Check if user is authenticated
    if (!isAuthenticated) {

      toast({
        title: "Authentication Required",
        description: "Please log in to see your products.",
        variant: "destructive",
      })
      setProducts([])
      setUsingLocalProducts(false)
      setPagination({
        page: 1,
        limit: 12,
        totalPages: 1,
        totalItems: 0,
        hasNextPage: false,
        hasPrevPage: false
      })
      setLoading(false)
      return
    }

    try {
      // Role-based API calls:
      // - Admin/Staff: Get all products (no userId parameter)
      // - Regular users: Get only their products (with userId parameter)
      const response = (user?.role === 'admin' || user?.role === 'staff')
        ? await productService.getAllProducts(undefined, page, limit)
        : await productService.getAllProducts(user?.id, page, limit)

      if (response.data && Array.isArray(response.data)) {
        const normalizedProducts = response.data.map(normalizeProduct)
        setProducts(normalizedProducts)

        // Handle pagination data
        if (response.pagination) {
          setPagination(response.pagination)
        } else {
          // Fallback pagination calculation if API doesn't provide it
          const totalItems = response.data.length
          const totalPages = Math.ceil(totalItems / limit)
          setPagination({
            page: page,
            limit: limit,
            totalPages: totalPages,
            totalItems: totalItems,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
          })
        }

        setUsingLocalProducts(true) // We're now managing products from API

      } else {
        throw new Error('API returned invalid data format')
      }
    } catch (error) {
      console.error('Failed to load products:', error)
      toast({
        title: "Error",
        description: "Failed to load products.",
        variant: "destructive",
      })

      setProducts([])
      setUsingLocalProducts(false)
      setPagination({
        page: 1,
        limit: 12,
        totalPages: 1,
        totalItems: 0,
        hasNextPage: false,
        hasPrevPage: false
      })
    } finally {
      setLoading(false)
    }
  }

  const loadCategories = async () => {
    try {
      const publicCategories = await categoryService.getPublicCategories(1, 100)
      setCategories(publicCategories || [])
    } catch (error) {
      console.error('Failed to load categories:', error)
      setCategories([])
    }
  }

  const handleCreateProduct = async (productData: CreateProductData) => {
    // Validate required fields
    if (!productData.name?.trim() || !productData.description?.trim() || !productData.category_id || !productData.affiliate_link?.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    try {
      const newProduct = await productService.createProduct(productData)
      const normalizedProduct = normalizeProduct(newProduct)
      // After successful creation, reload the first page to show the new product
      await loadProducts(1, pagination.limit)
      setIsCreateDialogOpen(false)

      // Store the newly created product and original data, then open prototype video dialog
      setNewlyCreatedProduct(normalizedProduct)
      setNewlyCreatedProductData(productData)
      // setIsPrototypeVideoDialogOpen(true)

      toast({
        title: "Product Created",
        description: `${normalizedProduct.name || productData.name || 'Product'} has been created successfully.`,
      })
    } catch (error) {
      console.error('Failed to create product:', error)
      toast({
        title: "Error",
        description: "Failed to create product.",
        variant: "destructive",
      })
    }
  }

  const handleUpdateProduct = async (productData: any) => {
    if (!selectedProduct) return

    // Validate required fields
    if (!productData.name?.trim() || !productData.description?.trim() || !productData.category_id || !productData.affiliate_link?.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    try {
      const updatedProduct = await productService.updateProduct(selectedProduct.id, productData)
      const normalizedProduct = normalizeProduct(updatedProduct)
      // After successful update, reload the current page to show the updated product
      await loadProducts(pagination.page, pagination.limit)
      setIsEditDialogOpen(false)
      setSelectedProduct(null)
      toast({
        title: "Product Updated",
        description: `${normalizedProduct.name || productData.name || 'Product'} has been updated successfully.`,
      })
    } catch (error) {
      console.error('Failed to update product:', error)

      // Fallback: Update local state when API is unavailable
      const updatedProduct = normalizeProduct({
        ...selectedProduct,
        ...productData,
        updated_at: new Date().toISOString()
      })

      setProducts(prev => {
        const updated = prev.map(product =>
          product.id === selectedProduct.id ? updatedProduct : product
        )
        return updated
      })
      setUsingLocalProducts(true) // We're now managing products locally
      setForceRender(prev => prev + 1) // Force re-render
      setIsEditDialogOpen(false)
      setSelectedProduct(null)
      toast({
        title: "Product Updated (Demo Mode)",
        description: `${updatedProduct.name || productData.name || 'Product'} has been updated locally. Connect to backend to persist.`,
      })
    }
  }

  const handlePrototypeMediaSubmit = async (mediaData: any) => {
    if (!newlyCreatedProduct) return

    try {

      // Prepare media data for API
      const requestData = {
        content_type: mediaData.media_type, // 'image' or 'video'
        media_type: 'prototype',
        ai_generation_type: `prototype_${mediaData.media_type}`,
        ask_ai_first: mediaData.ask_ai_first,
        content_description: mediaData.content_description,
        custom_ai_prompt: mediaData.custom_ai_prompt
      }

      // Call the API to create product media
      const response = await productService.createProductMedia(newlyCreatedProduct.id, requestData)

      setIsPrototypeVideoDialogOpen(false)
      setNewlyCreatedProduct(null)
      setNewlyCreatedProductData(null)

      const mediaType = mediaData.media_type === 'image' ? 'image' : 'video'
      toast({
        title: "Prototype Content Requested",
        description: `Prototype ${mediaType} generation has been started for ${newlyCreatedProduct.name}. You'll be notified when it's ready.`,
      })
    } catch (error) {
      console.error('Failed to create prototype media:', error)
      toast({
        title: "Error",
        description: "Failed to request prototype content generation.",
        variant: "destructive",
      })
    }
  }

  const productsToFilter = products

  // Check for duplicate IDs
  const productIds = productsToFilter.map(p => p.id)
  const uniqueIds = new Set(productIds)

  const filteredProducts = productsToFilter.filter(product => {
    // Safely handle potentially undefined properties
    const productName = product.name || ""
    const productDescription = product.description || ""
    const productCategory = product.category?.name || product.category?.slug || ""

    const matchesSearch = productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         productDescription.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCategory = selectedCategory === "all" || productCategory.toLowerCase() === selectedCategory
    const matchesType = selectedType === "all" || selectedType === "affiliate" // Default to affiliate for now
    const matchesStatus = selectedStatus === "all" ||
                         (selectedStatus === "active" && product.is_active) ||
                         (selectedStatus === "inactive" && !product.is_active)

    return matchesSearch && matchesCategory && matchesType && matchesStatus
  })

  const safeProducts = productsToFilter
  const totalRevenue = safeProducts.reduce((sum, product) => sum + parseFloat(product.price), 0)
  const totalProducts = safeProducts.length
  const avgAnalysisScore = safeProducts.length > 0
    ? safeProducts.reduce((sum, product) => {
        const analyses = product.analyses || []
        const latestAnalysis = analyses.length > 0 ? analyses[0] : null
        return sum + (latestAnalysis?.analysis_result?.analysis_score || 0)
      }, 0) / safeProducts.length
    : 0

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">
              {authLoading ? 'Checking authentication...' : 'Loading products...'}
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <AppLayout>
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 lg:p-8 pt-4 sm:pt-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">Product Management</h2>
            <p className="text-sm sm:text-base text-muted-foreground">
              {isAdmin ? "Manage all products in the system" : "Manage your products"}
            </p>
          </div>
          <Button onClick={() => setIsCreateDialogOpen(true)} className="w-full sm:w-auto">
            <Plus className="mr-2 h-4 w-4" />
            <span className="sm:inline">Add New Product</span>
          </Button>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full ">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.name.toLowerCase()}>
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color || '#3b82f6' }}
                      />
                      <span>{category.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-full ">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="Affiliate">Affiliate</SelectItem>
                <SelectItem value="Dropship">Dropship</SelectItem>
                <SelectItem value="Digital">Digital</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full ">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Products Grid */}
        <div key={forceRender} className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3">
          {filteredProducts.map((product, index) => (
            <Card key={`${product.id}-${forceRender}-${index}`} className="relative hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3 p-4 sm:p-6">
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1 min-w-0">
                    <Link href={`/products/${product.id}`}>
                      <CardTitle className="text-base sm:text-lg line-clamp-2 hover:text-primary cursor-pointer">
                        {product.name}
                      </CardTitle>
                    </Link>
                    <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2 mt-1">
                      {product.description}
                    </p>
                  </div>
                  <div className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-1 ml-2 shrink-0">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedProduct(product)
                        setIsEditDialogOpen(true)
                      }}
                      title="Edit Product"
                      className="h-8 w-8 p-0"
                    >
                      <Edit className="h-3 w-3 sm:h-4 sm:w-4" />
                    </Button>
                    {/* <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedProduct(product)
                        setIsAnalysisDialogOpen(true)
                      }}
                      title="View Analysis"
                      className="h-8 w-8 p-0"
                    >
                      <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4" />
                    </Button> */}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm border-t pt-2">
                    <div>
                      <p className="text-muted-foreground">Price</p>
                      <p className="font-medium truncate">{product.currency} {product.price}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Category</p>
                      <p className="font-medium capitalize truncate">{product.category?.name || 'Unknown'}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-xs sm:text-sm pt-2 border-t">
                    <div>
                      <p className="text-muted-foreground">Country</p>
                      <p className="font-medium truncate">{product.country || 'Global'}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Analysis Status</p>
                      {(product.analyses || []).length > 0 ? (
                        <div className="flex flex-wrap items-center gap-1">
                          {(() => {
                            const statusCounts = (product.analyses || []).reduce((acc, analysis) => {
                              acc[analysis.status] = (acc[analysis.status] || 0) + 1;
                              return acc;
                            }, {} as Record<string, number>);

                            return Object.entries(statusCounts).map(([status, count]) => (
                              <Badge
                                key={status}
                                variant={
                                  status === 'completed' ? 'default' :
                                  status === 'processing' ? 'secondary' :
                                  status === 'pending' ? 'outline' :
                                  'destructive'
                                }
                                className="text-xs"
                              >
                                {status} ({count})
                              </Badge>
                            ));
                          })()}
                        </div>
                      ) : (
                        <p className="font-medium text-muted-foreground">No analysis</p>
                      )}
                    </div>
                  </div>

                  {/* Owner Information */}
                  {product.user && (
                    <div className="pt-2 border-t">
                      <div className="flex items-center justify-between text-sm">
                        <div>
                          <p className="text-muted-foreground">Owner</p>
                          <p className="font-medium truncate">
                            {`${product.user.first_name} ${product.user.last_name}`.trim()}
                          </p>
                        </div>
                        <Badge variant={product.is_active ? 'default' : 'destructive'}>
                          {product.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </div>
                  )}

                  {/* Media Thumbnails */}
                  {product.media && product.media.length > 0 && (
                    <div className="pt-3 border-t">
                      <div className="flex items-center justify-between mb-2 sm:mb-3">
                        <p className="text-xs sm:text-sm font-medium text-foreground">Media Files</p>
                        <span className="text-xs text-muted-foreground">{product.media.length} file{product.media.length !== 1 ? 's' : ''}</span>
                      </div>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-1.5 sm:gap-2">
                        {product.media.slice(-3).map((mediaItem, index) => (
                          <div key={mediaItem.id} className="relative group">
                            {mediaItem.media_type === 'image' ? (
                              <div
                                className="aspect-square rounded-md sm:rounded-lg overflow-hidden cursor-pointer hover:ring-2 hover:ring-primary transition-all shadow-sm hover:shadow-md"
                                onClick={() => mediaItem.media_url && window.open(mediaItem.media_url, '_blank')}
                                title="Click to view full image"
                              >
                                <img
                                  src={mediaItem.thumbnail_url || mediaItem.media_url || '/placeholder.jpg'}
                                  alt={`Media ${index + 1}`}
                                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                                  onError={(e) => {
                                    (e.target as HTMLImageElement).src = '/placeholder.jpg'
                                  }}
                                />
                              </div>
                            ) : mediaItem.media_type === 'video' ? (
                              <div
                                className="aspect-square rounded-md sm:rounded-lg overflow-hidden cursor-pointer hover:ring-2 hover:ring-primary transition-all relative bg-gray-900 shadow-sm hover:shadow-md"
                                onClick={() => {
                                  const video = document.createElement('video')
                                  video.src = mediaItem.media_url
                                  video.controls = true
                                  video.autoplay = true
                                  video.style.width = '100%'
                                  video.style.height = '100%'
                                  const newWindow = window.open('', '_blank')
                                  if (newWindow) {
                                    newWindow.document.body.appendChild(video)
                                    newWindow.document.title = `Video - ${product.name}`
                                  }
                                }}
                                title="Click to play video"
                              >
                                {/* Use video element as thumbnail if available */}
                                {mediaItem.media_url ? (
                                  <video
                                    src={mediaItem.media_url}
                                    className="w-full h-full object-cover"
                                    muted
                                    preload="metadata"
                                    poster={mediaItem.thumbnail_url}
                                  />
                                ) : (
                                  /* Simple placeholder for videos without URL */
                                  <div className="w-full h-full bg-gray-800 flex items-center justify-center">
                                    <div className="text-center">
                                      <Video className="h-6 w-6 sm:h-8 sm:w-8 text-gray-400 mx-auto mb-1 sm:mb-2" />
                                      <span className="text-gray-400 text-xs">Video</span>
                                    </div>
                                  </div>
                                )}

                                {/* Simple play button overlay */}
                                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 group-hover:bg-opacity-20 transition-all">
                                  <div className="bg-white bg-opacity-90 rounded-full p-1.5 sm:p-2 group-hover:scale-110 transition-transform">
                                    <Play className="h-3 w-3 sm:h-4 sm:w-4 text-black fill-black" />
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <div className="aspect-square rounded-md sm:rounded-lg bg-muted flex items-center justify-center shadow-sm">
                                <ImageIcon className="h-4 w-4 sm:h-6 sm:w-6 text-muted-foreground" />
                              </div>
                            )}
                          </div>
                        ))}
                        {/* Fill empty slots if less than 3 media files */}
                        {Array.from({ length: 3 - Math.min(product.media.length, 3) }).map((_, index) => (
                          <div key={`empty-${index}`} className="aspect-square rounded-md sm:rounded-lg bg-muted/30 border-2 border-dashed border-muted-foreground/20 flex items-center justify-center">
                            <ImageIcon className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground/40" />
                          </div>
                        ))}
                      </div>
                      {product.media.length > 3 && (
                        <div className="mt-2">
                          <button
                            className="w-full text-xs text-primary hover:text-primary/80 font-medium py-1 px-2 rounded-md hover:bg-primary/5 transition-colors"
                            onClick={() => {
                              setSelectedProduct(product)
                              setIsMediaDialogOpen(true)
                            }}
                            title={`View all ${product.media.length} media files`}
                          >
                            View all {product.media.length} media files
                          </button>
                        </div>
                      )}
                    </div>
                  )}

                  <div className="flex flex-col flex-wrap sm:flex-row justify-between items-stretch sm:items-center pt-3 sm:pt-4 border-t gap-2 ">
                    <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                      <Link href={`/products/${product.id}`} className="flex-1 sm:flex-none">
                        <Button variant="outline" size="sm" className="w-full sm:w-auto text-xs sm:text-sm">
                          <Eye className="mr-1 h-3 w-3" />
                          <span className="sm:inline">Details</span>
                        </Button>
                      </Link>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedProduct(product)
                          setIsMediaDialogOpen(true)
                        }}
                        className="w-full sm:w-auto text-xs sm:text-sm"
                      >
                        <ImageIcon className="mr-1 h-3 w-3" />
                        <span className="sm:inline">Media</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedProduct(product)
                          setIsAnalysisDialogOpen(true)
                        }}
                        title="View Analysis"
                        className="w-full sm:w-auto text-xs sm:text-sm"
                      >
                        <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4" />
                        <span className="sm:inline">Analysis</span>
                      </Button>
                    </div>
                    <Button asChild size="sm" className="w-full sm:w-auto text-xs sm:text-sm">
                      <a href={product.affiliate_link} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="mr-1 h-3 w-3" />
                        <span className="sm:inline">Visit</span>
                      </a>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-8 sm:py-12 px-4">
            <Package className="mx-auto h-10 w-10 sm:h-12 sm:w-12 text-muted-foreground" />
            <h3 className="mt-4 text-base sm:text-lg font-semibold">No products found</h3>
            <p className="text-sm sm:text-base text-muted-foreground max-w-md mx-auto">
              {searchTerm || selectedCategory !== "all" || selectedType !== "all" || selectedStatus !== "all"
                ? "Try adjusting your search or filters"
                : "Get started by creating your first product"}
            </p>
            {!searchTerm && selectedCategory === "all" && selectedType === "all" && selectedStatus === "all" && (
              <Button className="mt-4 w-full sm:w-auto" onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add New Product
              </Button>
            )}
          </div>
        )}

        {/* Pagination Controls */}
        {usingLocalProducts && pagination.totalItems > 0 && (
          <div className="flex flex-col sm:flex-row items-center justify-between mt-6 sm:mt-8 gap-4 sm:gap-0">
            <div className="flex items-center space-x-2 order-2 sm:order-1">
              <span className="text-xs sm:text-sm text-muted-foreground text-center sm:text-left">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.totalItems)} of {pagination.totalItems} products
              </span>
            </div>

            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-2 order-1 sm:order-2 w-full sm:w-auto">
              <div className="flex items-center space-x-2 w-full sm:w-auto">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={!pagination.hasPrevPage}
                  className="flex-1 sm:flex-none text-xs sm:text-sm"
                >
                  Previous
                </Button>

                <div className="hidden sm:flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(pagination.totalPages - 4, pagination.page - 2)) + i
                    if (pageNum > pagination.totalPages) return null

                    return (
                      <Button
                        key={pageNum}
                        variant={pageNum === pagination.page ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(pageNum)}
                        className="w-8 h-8 p-0 text-xs"
                      >
                        {pageNum}
                      </Button>
                    )
                  })}
                </div>

                <div className="sm:hidden flex items-center space-x-2">
                  <span className="text-xs text-muted-foreground">
                    Page {pagination.page} of {pagination.totalPages}
                  </span>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={!pagination.hasNextPage}
                  className="flex-1 sm:flex-none text-xs sm:text-sm"
                >
                  Next
                </Button>
              </div>

              <Select value={pagination.limit.toString()} onValueChange={(value) => handleLimitChange(parseInt(value))}>
                <SelectTrigger className="w-full sm:w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="12">12</SelectItem>
                  <SelectItem value="24">24</SelectItem>
                  <SelectItem value="36">36</SelectItem>
                  <SelectItem value="48">48</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        <ProductCreateDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          onSubmit={handleCreateProduct}
        />

        <ProductEditDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          product={selectedProduct}
          onSubmit={handleUpdateProduct}
        />

        <ProductAnalysisDialog
          open={isAnalysisDialogOpen}
          onOpenChange={setIsAnalysisDialogOpen}
          product={selectedProduct}
          onProductUpdate={loadProducts}
        />

        <ProductMediaDialog
          open={isMediaDialogOpen}
          onOpenChange={setIsMediaDialogOpen}
          product={selectedProduct}
          onMediaSelect={(media) => {
            setSelectedMedia(media)
            setIsMediaDetailDialogOpen(true)
          }}
        />

        <MediaDetailDialog
          open={isMediaDetailDialogOpen}
          onOpenChange={setIsMediaDetailDialogOpen}
          media={selectedMedia}
          onMediaUpdate={(updatedMedia) => {
            setSelectedMedia(updatedMedia)
            // You could also update the media in the parent list here if needed
          }}
        />

        <PrototypeVideoDialog
          open={isPrototypeVideoDialogOpen}
          onOpenChange={setIsPrototypeVideoDialogOpen}
          productName={newlyCreatedProduct?.name || newlyCreatedProductData?.name || "Product"}
          onSubmit={handlePrototypeMediaSubmit}
        />
      </div>
    </AppLayout>
  )
}

'use client'

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  ExternalLink,
  Calendar,
  DollarSign,
  Globe,
  User,
  BarChart3,
  TrendingUp,
  Target,
  Star,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Image as ImageIcon,
  Video,
  Play,
  Eye,
  Download,
  Copy
} from "lucide-react"
import { AppLayout } from "@/components/layouts/app-layout"
import { useToast } from "@/hooks/use-toast"
import { productService, type Product, type ProductAnalysis } from "@/lib/api/products"
import { mediaService, type Media } from "@/lib/api/media"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

export default function ProductDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [media, setMedia] = useState<Media[]>([])
  const [loadingMedia, setLoadingMedia] = useState(false)
  const [selectedMedia, setSelectedMedia] = useState<Media | null>(null)
  const [isMediaModalOpen, setIsMediaModalOpen] = useState(false)

  useEffect(() => {
    loadProduct()
    loadMedia()
  }, [params.id])

  const loadProduct = async () => {
    try {
      const data = await productService.getProductById(params.id as string)
      setProduct(data)
    } catch (error) {
      console.error('Failed to load product:', error)

      console.error('Failed to load product:', error)
      toast({
        title: "Error",
        description: "Failed to load product details.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const loadMedia = async () => {
    if (!params.id) return

    setLoadingMedia(true)
    try {
      console.log('Loading media for product:', params.id)
      const response = await mediaService.getProductMedia(params.id as string)
      console.log('Media API response:', response)

      if (response.data && Array.isArray(response.data)) {
        setMedia(response.data)
        console.log('Media loaded successfully:', response.data)
      } else {
        console.log('No media data in response')
        throw new Error('No media data')
      }
    } catch (error) {
      console.error('Failed to load media:', error)
      setMedia([])
    } finally {
      setLoadingMedia(false)
    }
  }

  useEffect(() => {
    loadProduct()
  }, [params.id])

  useEffect(() => {
    if (product) {
      loadMedia()
    }
  }, [product])

  const openMediaModal = (mediaItem: Media) => {
    setSelectedMedia(mediaItem)
    setIsMediaModalOpen(true)
  }

  const openInNewTab = (url: string) => {
    window.open(url, '_blank')
  }

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard.`,
      })
    } catch (error) {
      console.error('Failed to copy:', error)
      toast({
        title: "Copy Failed",
        description: "Failed to copy to clipboard.",
        variant: "destructive",
      })
    }
  }

  const getMediaIcon = (mediaType: string) => {
    switch (mediaType) {
      case 'image':
        return <ImageIcon className="h-4 w-4" />
      case 'video':
        return <Video className="h-4 w-4" />
      default:
        return <ImageIcon className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading product details...</p>
          </div>
        </div>
      </AppLayout>
    )
  }

  if (!product) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Product Not Found</h2>
            <p className="text-muted-foreground mb-4">The product you're looking for doesn't exist.</p>
            <Button onClick={() => window.history.back()}>Go Back</Button>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="space-y-4 sm:space-y-6 p-4 sm:p-6 lg:p-8 pt-4 sm:pt-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.history.back()}
              className="self-start"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <div className="min-w-0">
              <h1 className="text-xl sm:text-2xl font-bold tracking-tight break-words">{product.name}</h1>
              <p className="text-muted-foreground text-sm sm:text-base break-words">{product.description}</p>
            </div>
          </div>
        </div>

        {/* Product Details */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6">
          {/* Main Content */}
          <div className="xl:col-span-2 space-y-4 sm:space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Product Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Price</label>
                    <p className="text-base sm:text-lg font-semibold break-words">{product.currency} {product.price}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Country</label>
                    <p className="text-sm break-words">{product.country || 'Not specified'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Category</label>
                    <p className="text-sm capitalize break-words">{typeof product.category === 'string' ? product.category : product.category?.name || 'Unknown'}</p>
                  </div>
                  <div className="flex flex-col gap-1">
                    <label className="text-sm font-medium text-muted-foreground">Status</label>
                    <Badge className="w-max" variant={product.is_active ? 'default' : 'destructive'}>
                      {product.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>
                {product.affiliate_link && (
                  <div className="mt-4">
                    <label className="text-sm font-medium text-muted-foreground">Affiliate Link</label>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 mt-1">
                      <p className="text-sm truncate flex-1 min-w-0 break-all">{product.affiliate_link}</p>
                      <div className="flex space-x-2 shrink-0">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(product.affiliate_link!, 'Affiliate link')}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => openInNewTab(product.affiliate_link!)}
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Media Section */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Product Media</CardTitle>
                <CardDescription className="text-sm">
                  Generated media files for this product
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loadingMedia ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    <span className="ml-2 text-sm text-muted-foreground">Loading media...</span>
                  </div>
                ) : media.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-2 2xl:grid-cols-3 gap-4">
                    {media.map((mediaItem) => (
                      <div
                        key={mediaItem.id}
                        className="border rounded-lg p-3 sm:p-4 hover:shadow-md transition-shadow cursor-pointer"
                        onClick={() => openMediaModal(mediaItem)}
                      >
                        <div className="flex items-center space-x-2 mb-2">
                          {getMediaIcon(mediaItem.media_type)}
                          <span className="text-sm font-medium capitalize truncate">
                            {mediaItem.media_type}
                          </span>
                          <Badge variant="outline" className="text-xs shrink-0">
                            {mediaItem.status}
                          </Badge>
                        </div>
                        {mediaItem.media_url && (
                          <div className="aspect-video bg-muted rounded-md mb-2 overflow-hidden">
                            {mediaItem.media_type === 'image' ? (
                              <img
                                src={mediaItem.media_url}
                                alt="Product media"
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <video
                                src={mediaItem.media_url}
                                className="w-full h-full object-cover"
                                muted
                              />
                            )}
                          </div>
                        )}
                        <div className="text-xs text-muted-foreground break-words">
                          {mediaItem.format?.toUpperCase()}
                          {mediaItem.file_size && ` • ${Math.round(mediaItem.file_size / 1024)} KB`}
                          {mediaItem.duration && ` • ${mediaItem.duration}s`}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-2 text-sm font-semibold">No media files</h3>
                    <p className="mt-1 text-sm text-muted-foreground">
                      No media files have been generated for this product yet.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-4 sm:space-y-6">
            {/* Analysis Results */}
            {product.analyses && product.analyses.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg sm:text-xl">Latest Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  {product.analyses.map((analysis) => (
                    <div key={analysis.id} className="space-y-4">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                        <Badge variant={analysis.status === 'completed' ? 'default' : 'secondary'} className="self-start">
                          {analysis.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {new Date(analysis.created_at).toLocaleDateString()}
                        </span>
                      </div>

                      {analysis.analysis_result && (
                        <div className="space-y-3">
                          {analysis.analysis_result.analysis_score && (
                            <div>
                              <label className="text-sm font-medium text-muted-foreground">Analysis Score</label>
                              <div className="flex items-center space-x-2 mt-1">
                                <div className="flex-1 bg-muted rounded-full h-2 min-w-0">
                                  <div
                                    className="bg-primary h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${analysis.analysis_result.analysis_score}%` }}
                                  />
                                </div>
                                <span className="text-sm font-medium shrink-0">
                                  {analysis.analysis_result.analysis_score}%
                                </span>
                              </div>
                            </div>
                          )}

                          {analysis.analysis_result.market_potential && (
                            <div>
                              <label className="text-sm font-medium text-muted-foreground">Market Potential</label>
                              <p className="text-sm capitalize break-words">{analysis.analysis_result.market_potential}</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>

      {/* Media Modal */}
      <Dialog open={isMediaModalOpen} onOpenChange={setIsMediaModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">Media Details</DialogTitle>
            <DialogDescription className="text-sm">
              View and manage product media file
            </DialogDescription>
          </DialogHeader>
          {selectedMedia && (
            <div className="space-y-4">
              <div className="aspect-video bg-black rounded-lg overflow-hidden">
                {selectedMedia.media_type === 'image' ? (
                  <img
                    src={selectedMedia.media_url || ''}
                    alt="Product media"
                    className="w-full h-full object-contain"
                  />
                ) : (
                  <video
                    src={selectedMedia.media_url || ''}
                    controls
                    className="w-full h-full"
                  />
                )}
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                <div>
                  <label className="font-medium text-muted-foreground">Type</label>
                  <p className="capitalize break-words">{selectedMedia.media_type}</p>
                </div>
                <div>
                  <label className="font-medium text-muted-foreground">Format</label>
                  <p className="uppercase break-words">{selectedMedia.format}</p>
                </div>
                <div>
                  <label className="font-medium text-muted-foreground">Status</label>
                  <Badge variant="outline">{selectedMedia.status}</Badge>
                </div>
                {selectedMedia.file_size && (
                  <div>
                    <label className="font-medium text-muted-foreground">File Size</label>
                    <p>{Math.round(selectedMedia.file_size / 1024)} KB</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </AppLayout>
  )
}

import { But<PERSON> } from "@/components/ui/button"
import { ComplianceDashboard } from "@/components/compliance-dashboard"
import { CookieConsentLog } from "@/components/cookie-consent-log"
import { AffiliateDisclosureMonitor } from "@/components/affiliate-disclosure-monitor"
import { AgentActionLog } from "@/components/agent-action-log"
import { DataRetentionSettings } from "@/components/data-retention-settings"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Toaster } from "@/components/toaster"
import { AppLayout } from "@/components/layouts/app-layout"

export default function CompliancePage() {
  return (
    <AppLayout>
      <div className="space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Compliance & Audit Center</h2>
          <div className="flex items-center space-x-2">
            <Button variant="outline">Export Compliance Report</Button>
            <Button>Schedule Audit</Button>
          </div>
        </div>

        <Tabs defaultValue="dashboard" className="space-y-4">
          <TabsList>
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="cookies">Cookie & Consent Log</TabsTrigger>
            <TabsTrigger value="disclosures">Affiliate Disclosures</TabsTrigger>
            <TabsTrigger value="agents">Agent Action Log</TabsTrigger>
            <TabsTrigger value="retention">Data Retention</TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard">
            <ComplianceDashboard />
          </TabsContent>

          <TabsContent value="cookies">
            <CookieConsentLog />
          </TabsContent>

          <TabsContent value="disclosures">
            <AffiliateDisclosureMonitor />
          </TabsContent>

          <TabsContent value="agents">
            <AgentActionLog />
          </TabsContent>

          <TabsContent value="retention">
            <DataRetentionSettings />
          </TabsContent>
        </Tabs>
      </div>
      <Toaster />
    </AppLayout>
  )
}

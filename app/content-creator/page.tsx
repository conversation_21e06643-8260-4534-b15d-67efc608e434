"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { AppLayout } from "@/components/layouts/app-layout"
import { productService, type Product, type ProductAnalysis } from "@/lib/api/products"
import {
  Spark<PERSON>,
  Video,
  Image as ImageIcon,
  Wand2,
  Play,
  Camera,
  Palette,
  Loader2,
  Info
} from "lucide-react"
import { ContentTypeStyleCard, ContentTypeStyleValues } from "@/components/content-type-style-card"

// Content creation form data interface
export interface ContentCreationFormData {
  // Product Selection
  product_id: string

  // API Fields
  content_type?: string // Content Type *
  media_type?: 'video' | 'image' | 'text'
  video_type?: string // Video Type
  duration?: number // Duration in seconds
  style?: string // Style
  tone?: string // Tone
  voice_tone?: string // Voice Tone
  ai_generation_type?: string // AI Generation Type
  content_description?: string // Content Description *
  custom_ai_prompt?: string // Custom AI Prompt
  target_audience?: string // Target Audience
  content_options?: object // Content Options
  platform_distribution?: object // Platform & Distribution

  // Camera and visual controls for video
  camera_angle?: string // Camera positioning
  camera_movement?: string // Camera movement type
  lighting_style?: string // Lighting setup

  // Legacy fields for UI compatibility
  video_duration?: string // '8s', '15s', '30s', '60s', '90s', '3min', '5min', '10min+'
  image_type?: 'banner' | 'logo' | 'photo_card' | 'social_post' | 'thumbnail'
  image_dimensions?: string // 'square', 'portrait', 'landscape', 'story'
  ai_type?: string // 'realistic', 'animated', 'cartoon', 'artistic', 'photorealistic', 'stylized', 'abstract'
  custom_prompt: string
  include_product_info: boolean
  include_pricing: boolean
  include_call_to_action: boolean
  brand_colors?: string[]
  platforms: string[] // 'instagram', 'tiktok', 'youtube', 'facebook', 'twitter'
}

export default function ContentCreatorPage() {
  const { toast } = useToast()
  const [products, setProducts] = useState<Product[]>([])
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [productAnalyses, setProductAnalyses] = useState<ProductAnalysis[]>([])
  const [loading, setLoading] = useState(true)
  const [loadingAnalyses, setLoadingAnalyses] = useState(false)
  const [generating, setGenerating] = useState(false)
  const [productsError, setProductsError] = useState<string | null>(null)
  const [isHelpDialogOpen, setIsHelpDialogOpen] = useState(false)
  const [isSuccessDialogOpen, setIsSuccessDialogOpen] = useState(false)
  const [successJobId, setSuccessJobId] = useState<string>('')
  
  const [formData, setFormData] = useState<ContentCreationFormData>({
    product_id: "",
    content_type: 'product_advertisement',
    media_type: 'video',
    video_type: 'short',
    duration: 8,
    style: 'modern',
    tone: 'professional',
    voice_tone: 'friendly',
    ai_generation_type: 'realistic',
    content_description: '',
    custom_ai_prompt: '',
    target_audience: '',
    content_options: { include_captions: true },
    platform_distribution: {},
    camera_angle: 'three_quarter_view',
    camera_movement: 'static',
    lighting_style: 'studio',
    custom_prompt: "",
    include_product_info: true,
    include_pricing: false,
    include_call_to_action: true,
    platforms: ['instagram']
  })

  // Load products on component mount
  const fetchProducts = async () => {
    setLoading(true)
    setProductsError(null)
    try {
      const response = await productService.getAllProducts()
      setProducts(response.data)
    } catch (error) {
      console.error('Failed to fetch products:', error)
      const errorMessage = "Failed to load products. Please try again."
      setProductsError(errorMessage)
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProducts()
  }, [toast])

  // Handle product selection
  const handleProductSelect = async (productId: string) => {
    const product = products.find(p => p.id === productId)
    setSelectedProduct(product || null)
    setFormData(prev => ({ ...prev, product_id: productId }))

    // Fetch product analyses when a product is selected
    if (product) {
      setLoadingAnalyses(true)
      setProductAnalyses([])

      try {
        console.log('Fetching analyses for product:', productId)
        const response = await productService.getProductAnalyses(productId) as any
        console.log('Raw API response:', response)

        // Handle different response structures
        let analyses: ProductAnalysis[] = []
        if (Array.isArray(response)) {
          analyses = response
        } else if (response && response.data && Array.isArray(response.data)) {
          analyses = response.data
        } else if (response && Array.isArray(response.analyses)) {
          analyses = response.analyses
        } else if (response && response.success && response.data) {
          // Handle wrapped response with success flag
          if (Array.isArray(response.data)) {
            analyses = response.data
          } else {
            // Single analysis object
            analyses = [response.data]
          }
        } else if (response && response.analysis_result) {
          // Handle single analysis response
          analyses = [response]
        } else {
          console.log('Unexpected response structure:', response)
          analyses = []
        }

        console.log('Processed analyses:', analyses)
        console.log('Analysis count:', analyses.length)

        // Log each analysis for debugging
        analyses.forEach((analysis, index) => {
          console.log(`Analysis ${index + 1}:`, {
            id: analysis.id,
            status: analysis.status,
            external_job_id: analysis.external_job_id,
            has_analysis_result: !!analysis.analysis_result,
            analysis_result: analysis.analysis_result
          })
        })

        setProductAnalyses(analyses)

        if (analyses.length === 0) {
          toast({
            title: "No Analysis Data",
            description: "No analysis data found for this product. Consider running product analysis first.",
            variant: "default",
          })
        } else {
          toast({
            title: "Analysis Data Loaded",
            description: `Found ${analyses.length} analysis result(s) for this product.`,
            variant: "default",
          })
        }
      } catch (error) {
        console.error('Failed to fetch product analyses:', error)
        toast({
          title: "Error",
          description: "Failed to load product analysis data.",
          variant: "destructive",
        })
      } finally {
        setLoadingAnalyses(false)
      }
    } else {
      setProductAnalyses([])
    }
  }

  // Handle form field updates
  const updateFormData = (field: keyof ContentCreationFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Handle platform selection
  const togglePlatform = (platform: string) => {
    setFormData(prev => {
      const isSelected = prev.platforms.includes(platform)
      const newPlatforms = isSelected
        ? prev.platforms.filter(p => p !== platform)
        : [...prev.platforms, platform]

      // Update platform_distribution object
      const newPlatformDistribution = { ...prev.platform_distribution } as any

      if (isSelected) {
        // Remove platform from distribution
        delete newPlatformDistribution[platform]
      } else {
        // Add platform with default settings
        newPlatformDistribution[platform] = {
          optimize_for_retention: platform === 'youtube',
          include_hashtags: platform === 'instagram' || platform === 'tiktok',
          aspect_ratio: platform === 'instagram' ? 'square' : 'landscape'
        }
      }

      return {
        ...prev,
        platforms: newPlatforms,
        platform_distribution: newPlatformDistribution
      }
    })
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.product_id) {
      toast({
        title: "Product Required",
        description: "Please select a product to create content for.",
        variant: "destructive",
      })
      return
    }

    if (!formData.custom_prompt.trim() && (!formData.content_description || !formData.content_description.trim())) {
      toast({
        title: "Content Description Required",
        description: "Please provide either a custom prompt or content description.",
        variant: "destructive",
      })
      return
    }

    setGenerating(true)

    try {
      // Prepare API payload
      const apiPayload = {
        content_type: formData.content_type || 'product_advertisement',
        media_type: formData.media_type || 'video',
        video_type: formData.video_type || 'short',
        duration: formData.duration || 8,
        style: formData.style || 'modern',
        tone: formData.tone || 'professional',
        voice_tone: formData.voice_tone || 'friendly',
        ai_generation_type: formData.ai_generation_type || 'realistic',
        content_description: formData.content_description || formData.custom_prompt,
        custom_ai_prompt: formData.custom_ai_prompt || formData.custom_prompt,
        target_audience: formData.target_audience || '',
        content_options: formData.content_options || { include_captions: true },
        platform_distribution: formData.platform_distribution || {},
        camera_angle: formData.camera_angle || 'three_quarter_view',
        camera_movement: formData.camera_movement || 'static',
        lighting_style: formData.lighting_style || 'studio'
      }

      console.log('API Payload:', apiPayload)

      // Call the create media API
      const response = await productService.createProductMedia(formData.product_id, apiPayload)

      console.log('API Response:', response)

      // Show success message with job ID
      const jobId = response.data?.job_id || response.job_id || response.jobId || 'Unknown'

      // Set success data and show modal
      setSuccessJobId(jobId)
      setIsSuccessDialogOpen(true)

      // Reset form after successful submission
      setFormData({
        product_id: "",
        content_type: 'product_advertisement',
        media_type: 'video',
        video_type: 'short',
        duration: 8,
        style: 'modern',
        tone: 'professional',
        voice_tone: 'friendly',
        ai_generation_type: 'realistic',
        content_description: '',
        custom_ai_prompt: '',
        target_audience: '',
        content_options: { include_captions: true },
        platform_distribution: {},
        camera_angle: 'three_quarter_view',
        camera_movement: 'static',
        lighting_style: 'studio',
        custom_prompt: "",
        include_product_info: true,
        include_pricing: false,
        include_call_to_action: true,
        platforms: ['instagram']
      })
      setSelectedProduct(null)

    } catch (error) {
      console.error('Content generation error:', error)
      toast({
        title: "Generation Failed",
        description: "Failed to start content generation. Please try again.",
        variant: "destructive",
      })
    } finally {
      setGenerating(false)
    }
  }

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin" />
            <p className="text-muted-foreground">Loading products...</p>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="space-y-6 p-8 pt-6">
        <div className="flex md:flex-row flex-col items-start  md:items-center  justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create Content & AI Studio</h1>
            <p className="text-muted-foreground">
              Generate AI-powered content for your products across multiple platforms
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Dialog open={isHelpDialogOpen} onOpenChange={setIsHelpDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Info className="mr-2 h-4 w-4" />
                  Help & Tips
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="flex items-center space-x-2">
                    <Info className="h-5 w-5" />
                    <span>Content Creator Help & Tips</span>
                  </DialogTitle>
                  <DialogDescription>
                    Learn how to create amazing AI-generated content for your products
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                  {/* Getting Started Section */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-semibold flex items-center space-x-2">
                      <Sparkles className="h-5 w-5 text-blue-500" />
                      <span>Getting Started</span>
                    </h3>
                    <div className="space-y-2 text-sm">
                      <p><strong>1. Select a Product:</strong> Choose the product you want to create content for from the dropdown. Make sure your product has analysis data for better AI generation.</p>
                      <p><strong>2. Choose Content Type:</strong> Select what kind of content you want (advertisement, showcase, tutorial, etc.).</p>
                      <p><strong>3. Pick Media Type:</strong> Choose between video or image content based on your needs.</p>
                    </div>
                  </div>

                  {/* Video Content Tips */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-semibold flex items-center space-x-2">
                      <Video className="h-5 w-5 text-purple-500" />
                      <span>Video Content Tips</span>
                    </h3>
                    <div className="space-y-2 text-sm">
                      <p><strong>Duration:</strong> 8-15 seconds work best for social media reels. Use 30-60 seconds for detailed product showcases.</p>
                      <p><strong>Camera Angles:</strong> Front view works great for product features, three-quarter view for overall product appeal.</p>
                      <p><strong>Camera Movement:</strong> Static shots for detailed views, smooth glide for dynamic presentations.</p>
                      <p><strong>Lighting:</strong> Natural lighting for authentic feel, studio lighting for professional look.</p>
                    </div>
                  </div>

                  {/* Content Description Tips */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-semibold flex items-center space-x-2">
                      <Wand2 className="h-5 w-5 text-green-500" />
                      <span>Writing Effective Prompts</span>
                    </h3>
                    <div className="space-y-2 text-sm">
                      <p><strong>Be Specific:</strong> Instead of "show product", write "showcase the sleek design and key features of the AC unit".</p>
                      <p><strong>Include Context:</strong> Mention the setting, mood, or scenario (e.g., "modern living room", "summer cooling").</p>
                      <p><strong>Highlight Benefits:</strong> Focus on what makes your product special (energy efficient, quiet operation, etc.).</p>
                      <p><strong>Target Audience:</strong> Specify who you're targeting (families, professionals, tech enthusiasts).</p>
                    </div>
                  </div>

                  {/* Platform Optimization */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-semibold flex items-center space-x-2">
                      <Palette className="h-5 w-5 text-orange-500" />
                      <span>Platform Optimization</span>
                    </h3>
                    <div className="space-y-2 text-sm">
                      <p><strong>Instagram:</strong> Square format, vibrant colors, include hashtags for discovery.</p>
                      <p><strong>TikTok:</strong> Vertical format, trending music, quick cuts, engaging hooks.</p>
                      <p><strong>YouTube:</strong> Landscape format, longer duration, detailed descriptions.</p>
                      <p><strong>Facebook:</strong> Landscape format, clear captions, family-friendly content.</p>
                    </div>
                  </div>

                  {/* Best Practices */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-semibold flex items-center space-x-2">
                      <Camera className="h-5 w-5 text-red-500" />
                      <span>Best Practices</span>
                    </h3>
                    <div className="space-y-2 text-sm">
                      <p><strong>Quality First:</strong> Always review generated content before publishing.</p>
                      <p><strong>Brand Consistency:</strong> Use consistent style and tone across all content.</p>
                      <p><strong>A/B Testing:</strong> Create multiple versions to see what works best.</p>
                      <p><strong>Monitor Performance:</strong> Check your content performance in Media Management.</p>
                    </div>
                  </div>

                  {/* Example Prompts */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-semibold">Example Prompts</h3>
                    <div className="space-y-3 text-sm">
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <p><strong>Product Advertisement:</strong></p>
                        <p className="italic">"Create a dynamic video showcasing the Gree AC 2-ton inverter in a modern living room. Highlight its energy efficiency, quiet operation, and sleek design. Show the remote control features and cooling effect with a professional, trustworthy tone."</p>
                      </div>
                      <div className="p-3 bg-green-50 rounded-lg">
                        <p><strong>Product Tutorial:</strong></p>
                        <p className="italic">"Step-by-step guide on setting up and using the smart features of the AC unit. Show the mobile app connectivity, temperature control, and energy-saving modes in a helpful, educational style."</p>
                      </div>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-3">
            {/* Left Column - Product Selection & Basic Info */}
            <div className="lg:col-span-1 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Sparkles className="h-5 w-5" />
                    <span>Product Selection</span>
                  </CardTitle>
                  <CardDescription>
                    Choose the product you want to create content for
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="product">Product *</Label>
                    <Select value={formData.product_id} onValueChange={handleProductSelect} disabled={loading}>
                      <SelectTrigger className="w-full min-w-0">
                        <div className="flex items-center justify-between w-full min-w-0">
                          {selectedProduct ? (
                            <>
                              <span className="truncate flex-1 text-sm text-left">
                                {selectedProduct.name}
                              </span>
                              <Badge variant="outline" className="text-xs ml-2 flex-shrink-0">
                                {selectedProduct.category?.name || 'No Category'}
                              </Badge>
                            </>
                          ) : (
                            <span className="text-muted-foreground">
                              {loading ? "Loading products..." : "Select a product..."}
                            </span>
                          )}
                        </div>
                      </SelectTrigger>
                      <SelectContent>
                        {loading ? (
                          <SelectItem value="loading" disabled>
                            <div className="flex items-center space-x-2">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <span>Loading products...</span>
                            </div>
                          </SelectItem>
                        ) : products.length === 0 ? (
                          <SelectItem value="no-products" disabled>
                            <div className="flex items-center space-x-2">
                              <Info className="h-4 w-4" />
                              <span>No products available</span>
                            </div>
                          </SelectItem>
                        ) : (
                          products.map((product) => (
                            <SelectItem key={product.id} value={product.id}>
                              <div className="flex items-center justify-between w-full min-w-0">
                                <span className="truncate flex-1 mr-2 text-sm">
                                  {product.name}
                                </span>
                                <Badge variant="outline" className="text-xs flex-shrink-0">
                                  {product.category?.name || 'No Category'}
                                </Badge>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>

                    {productsError && (
                      <div className="flex items-center justify-between p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <Info className="h-4 w-4 text-destructive" />
                          <span className="text-sm text-destructive">{productsError}</span>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={fetchProducts}
                          disabled={loading}
                        >
                          {loading ? (
                            <>
                              <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                              Retrying...
                            </>
                          ) : (
                            'Retry'
                          )}
                        </Button>
                      </div>
                    )}
                  </div>

                  {selectedProduct && (
                    <div className="space-y-3">
                      <div className="p-3 bg-muted rounded-lg space-y-3">
                        <div className="space-y-2">
                          <h4 className="font-medium text-sm leading-tight break-words">
                            {selectedProduct.name}
                          </h4>
                          <p className="text-xs text-muted-foreground line-clamp-2 leading-relaxed">
                            {selectedProduct.description}
                          </p>
                        </div>
                        {selectedProduct.price && (
                          <div className="flex items-center gap-2 flex-wrap">
                            <Badge variant="secondary" className="text-xs">
                              {selectedProduct.currency} {selectedProduct.price}
                            </Badge>
                            <Badge variant={selectedProduct.is_active ? "default" : "secondary"} className="text-xs">
                              {selectedProduct.is_active ? "Active" : "Inactive"}
                            </Badge>
                          </div>
                        )}
                      </div>

                      {/* Product Analysis Data Display */}
                      {loadingAnalyses && (
                        <div className="p-3 bg-muted rounded-lg flex items-center space-x-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="text-sm">Loading analysis data...</span>
                        </div>
                      )}

                      {!loadingAnalyses && productAnalyses.length > 0 && (
                        <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg space-y-3">
                          <h5 className="font-medium text-sm flex items-center space-x-2">
                            <Info className="h-4 w-4" />
                            <span>Product Analysis Data ({productAnalyses.length} results)</span>
                          </h5>

                          {productAnalyses.map((analysis, index) => (
                            <div key={analysis.id || index} className="bg-white dark:bg-gray-800 rounded border p-3 space-y-2">
                              <div className="flex items-center justify-between">
                                <Badge variant={analysis.status === 'completed' ? 'default' : 'secondary'}>
                                  {analysis.status}
                                </Badge>
                                {analysis.external_job_id && (
                                  <span className="text-xs text-muted-foreground">
                                    Job: {analysis.external_job_id}
                                  </span>
                                )}
                              </div>

                              {analysis.analysis_result && (
                                <div className="grid grid-cols-2 gap-2 text-xs">
                                  {analysis.analysis_result.analysis_score && (
                                    <div>
                                      <span className="font-medium">Score:</span> {analysis.analysis_result.analysis_score}
                                    </div>
                                  )}
                                  {analysis.analysis_result.market_potential && (
                                    <div>
                                      <span className="font-medium">Market:</span> {analysis.analysis_result.market_potential.replace('_', ' ')}
                                    </div>
                                  )}
                                  {analysis.analysis_result.competition_level && (
                                    <div>
                                      <span className="font-medium">Competition:</span> {analysis.analysis_result.competition_level}
                                    </div>
                                  )}
                                  {analysis.analysis_result.price_recommendation?.optimal && (
                                    <div>
                                      <span className="font-medium">Price:</span> ${analysis.analysis_result.price_recommendation.optimal}
                                    </div>
                                  )}
                                </div>
                              )}

                              {/* Raw data toggle */}
                              <details className="mt-2">
                                <summary className="text-xs text-muted-foreground cursor-pointer hover:text-foreground">
                                  View raw data
                                </summary>
                                <pre className="text-xs whitespace-pre-wrap font-mono mt-2 p-2 bg-muted rounded max-h-32 overflow-auto">
                                  {JSON.stringify(analysis, null, 2)}
                                </pre>
                              </details>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* No Analysis Data Message */}
                      {!loadingAnalyses && productAnalyses.length === 0 && selectedProduct && (
                        <div className="p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg">
                          <p className="text-sm text-yellow-800 dark:text-yellow-200">
                            No analysis data available for this product. Consider running product analysis first for better AI content generation.
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Middle Column - Content Type & Specifications */}
            <div className="lg:col-span-1 space-y-6">
              <ContentTypeStyleCard
                values={{
                  content_type: formData.content_type,
                  media_type: (formData.media_type as any) || "",
                  video_type: formData.video_type,
                  duration: formData.duration,
                  camera_angle: formData.camera_angle,
                  camera_movement: formData.camera_movement,
                  lighting_style: formData.lighting_style,
                  image_type: formData.image_type,
                  image_dimensions: formData.image_dimensions,
                  style: formData.style,
                  tone: formData.tone,
                  voice_tone: formData.voice_tone,
                  ai_generation_type: formData.ai_generation_type,
                }}
                onChange={(key, value) => updateFormData(key as keyof ContentTypeStyleValues, value)}
              />
            </div>

            {/* Right Column - AI Prompt & Settings */}
            <div className="lg:col-span-1 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Sparkles className="h-5 w-5" />
                    <span>AI Content Prompt</span>
                  </CardTitle>
                  <CardDescription>
                    Describe what content you want the AI to create
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="content_description">Content Description *</Label>
                    <Textarea
                      id="content_description"
                      placeholder="Describe the content you want to create..."
                      value={formData.content_description}
                      onChange={(e) => updateFormData('content_description', e.target.value)}
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="custom_ai_prompt">Custom AI Prompt</Label>
                    <Textarea
                      id="custom_ai_prompt"
                      placeholder="Add specific instructions for the AI..."
                      value={formData.custom_ai_prompt || ''}
                      onChange={(e) => updateFormData('custom_ai_prompt', e.target.value)}
                      rows={4}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="target_audience">Target Audience</Label>
                    <Input
                      id="target_audience"
                      placeholder="e.g., Young professionals, fitness enthusiasts..."
                      value={formData.target_audience || ''}
                      onChange={(e) => updateFormData('target_audience', e.target.value)}
                    />
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    <Label>Content Options</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="include_captions"
                          checked={(formData.content_options as any)?.include_captions || false}
                          onCheckedChange={(checked) => {
                            updateFormData('content_options', {
                              ...formData.content_options,
                              include_captions: checked
                            })
                          }}
                        />
                        <Label htmlFor="include_captions" className="text-sm">
                          Include captions
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="include_product_info"
                          checked={formData.include_product_info}
                          onCheckedChange={(checked) => {
                            updateFormData('include_product_info', checked)
                            updateFormData('content_options', {
                              ...formData.content_options,
                              include_product_info: checked
                            })
                          }}
                        />
                        <Label htmlFor="include_product_info" className="text-sm">
                          Include product information
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="include_pricing"
                          checked={formData.include_pricing}
                          onCheckedChange={(checked) => {
                            updateFormData('include_pricing', checked)
                            updateFormData('content_options', {
                              ...formData.content_options,
                              include_pricing: checked
                            })
                          }}
                        />
                        <Label htmlFor="include_pricing" className="text-sm">
                          Include pricing information
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="include_call_to_action"
                          checked={formData.include_call_to_action}
                          onCheckedChange={(checked) => {
                            updateFormData('include_call_to_action', checked)
                            updateFormData('content_options', {
                              ...formData.content_options,
                              include_call_to_action: checked
                            })
                          }}
                        />
                        <Label htmlFor="include_call_to_action" className="text-sm">
                          Include call-to-action
                        </Label>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Platform Selection & Submit */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Palette className="h-5 w-5" />
                    <span>Platform & Distribution</span>
                  </CardTitle>
                  <CardDescription>
                    Choose where you want to publish this content
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <Label>Target Platforms</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {[
                        { id: 'instagram', name: 'Instagram', icon: '📷' },
                        { id: 'tiktok', name: 'TikTok', icon: '🎵' },
                        { id: 'youtube', name: 'YouTube', icon: '📺' },
                        { id: 'facebook', name: 'Facebook', icon: '👥' },
                        { id: 'twitter', name: 'Twitter/X', icon: '🐦' },
                        { id: 'linkedin', name: 'LinkedIn', icon: '💼' },
                      ].map((platform) => (
                        <div key={platform.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={platform.id}
                            checked={formData.platforms.includes(platform.id)}
                            onCheckedChange={() => togglePlatform(platform.id)}
                          />
                          <Label htmlFor={platform.id} className="text-sm flex items-center space-x-1">
                            <span>{platform.icon}</span>
                            <span>{platform.name}</span>
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {productAnalyses.length > 0 && (
                    <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                      <h4 className="font-medium text-sm mb-2">Product Analysis Available</h4>
                      <p className="text-xs text-muted-foreground">
                        AI will use {productAnalyses.length} analysis result{productAnalyses.length > 1 ? 's' : ''} to create more targeted content
                      </p>
                    </div>
                  )}

                  {formData.platforms.length === 0 && (
                    <p className="text-sm text-muted-foreground text-center">
                      Please select at least one platform
                    </p>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
          <div className="pt-4 flex justify-end">
            <Button
              type="submit"
              className=""
              disabled={generating || !formData.product_id}
            >
              {generating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating Content...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Generate AI Content
                </>
              )}
            </Button>
          </div>
        </form>
      </div>

      {/* Success Modal */}
      <Dialog open={isSuccessDialogOpen} onOpenChange={setIsSuccessDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2 text-green-600">
              <Sparkles className="h-5 w-5" />
              <span>Content Generation Queued</span>
            </DialogTitle>
            <DialogDescription>
              Your content generation request has been successfully submitted!
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="space-y-2">
                <p className="text-sm font-medium text-green-800">
                  ✅ Your request is in queue
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-green-700">Your Job ID:</span>
                  <code className="px-2 py-1 bg-green-100 text-green-800 rounded text-sm font-mono">
                    {successJobId}
                  </code>
                </div>
              </div>
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Next Steps:</strong>
              </p>
              <p className="text-sm text-blue-700 mt-1">
                Go to <strong>Media Management Menu</strong> and check the status of your content generation.
              </p>
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsSuccessDialogOpen(false)}
              >
                Close
              </Button>
              <Button
                onClick={() => {
                  setIsSuccessDialogOpen(false)
                  window.location.href = '/media-management'
                }}
              >
                Go to Media Management
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </AppLayout>
  )
}

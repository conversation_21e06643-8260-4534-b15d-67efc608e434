import { But<PERSON> } from "@/components/ui/button"
import { AIContentAssistant } from "@/components/ai-content-assistant"
import { Toaster } from "@/components/ui/toaster"
import { APIConnectionDialog } from "@/components/api-connection-dialog"
import { AppLayout } from "@/components/layouts/app-layout"

export default function AIStudioPage() {
  return (
    <AppLayout>
      <div className="space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">AI Content Studio</h2>
          <div className="flex items-center space-x-2">
            <Button variant="outline">View Tutorials</Button>
            <APIConnectionDialog>
              <Button>Connect APIs</Button>
            </APIConnectionDialog>
          </div>
        </div>

        <AIContentAssistant />
      </div>
      <Toaster />
    </AppLayout>
  )
}

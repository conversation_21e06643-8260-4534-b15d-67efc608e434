import { <PERSON><PERSON> } from "@/components/ui/button"
import { ContentCalendar } from "@/components/content-calendar"
import { CreateEventDialog } from "@/components/create-event-dialog"
import { Plus } from "lucide-react"
import { Toaster } from "@/components/ui/toaster"
import { AppLayout } from "@/components/layouts/app-layout"

export default function CalendarPage() {
  return (
    <AppLayout>
      <div className="space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Content Calendar</h2>
          <div className="flex items-center space-x-2">
            <CreateEventDialog>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Schedule New Content
              </Button>
            </CreateEventDialog>
          </div>
        </div>

        <ContentCalendar />
      </div>
      <Toaster />
    </AppLayout>
  )
}

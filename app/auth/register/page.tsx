'use client'

import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { AuthForm } from '@/components/auth/auth-form'
import { RegisterRequest } from '@/lib/api'

export default function RegisterPage() {
  const { register, isLoading } = useAuth()
  const router = useRouter()

  const handleRegister = async (data: RegisterRequest): Promise<boolean> => {
    const registrationSuccess = await register(data)

    if (registrationSuccess !== undefined) {
      // Registration successful, always redirect to login page
      console.log('Registration successful, redirecting to login page')
      setTimeout(() => {
        router.replace('/auth/login')
      }, 2000) // Give user time to read the success message
    }

    // Always return true if we get here (registration was successful)
    return true
  }

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        {/* AI Neural Network Background Pattern */}
        <div className="absolute inset-0 opacity-40" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='%234F46E5' stroke-width='1' opacity='0.4'%3E%3Ccircle cx='50' cy='50' r='4' fill='%234F46E5' fill-opacity='0.3'/%3E%3Ccircle cx='150' cy='50' r='4' fill='%234F46E5' fill-opacity='0.3'/%3E%3Ccircle cx='100' cy='100' r='5' fill='%234F46E5' fill-opacity='0.4'/%3E%3Ccircle cx='50' cy='150' r='4' fill='%234F46E5' fill-opacity='0.3'/%3E%3Ccircle cx='150' cy='150' r='4' fill='%234F46E5' fill-opacity='0.3'/%3E%3Cline x1='50' y1='50' x2='150' y2='50'/%3E%3Cline x1='50' y1='50' x2='100' y2='100'/%3E%3Cline x1='150' y1='50' x2='100' y2='100'/%3E%3Cline x1='100' y1='100' x2='50' y2='150'/%3E%3Cline x1='100' y1='100' x2='150' y2='150'/%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '200px 200px'
        }}></div>

        {/* AI Circuit Board Pattern */}
        <div className="absolute inset-0 opacity-30" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='%2306B6D4' stroke-width='0.8' opacity='0.5'%3E%3Cpath d='M20 20h80v80H20z'/%3E%3Cpath d='M30 30h60v60H30z'/%3E%3Cpath d='M40 40h40v40H40z'/%3E%3Ccircle cx='20' cy='20' r='2' fill='%2306B6D4' fill-opacity='0.6'/%3E%3Ccircle cx='100' cy='20' r='2' fill='%2306B6D4' fill-opacity='0.6'/%3E%3Ccircle cx='20' cy='100' r='2' fill='%2306B6D4' fill-opacity='0.6'/%3E%3Ccircle cx='100' cy='100' r='2' fill='%2306B6D4' fill-opacity='0.6'/%3E%3Ccircle cx='60' cy='60' r='3' fill='%2306B6D4' fill-opacity='0.7'/%3E%3Cline x1='20' y1='60' x2='40' y2='60'/%3E%3Cline x1='80' y1='60' x2='100' y2='60'/%3E%3Cline x1='60' y1='20' x2='60' y2='40'/%3E%3Cline x1='60' y1='80' x2='60' y2='100'/%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '120px 120px',
          transform: 'translate(60px, 60px)'
        }}></div>

        {/* Data Flow Animation */}
        <div className="absolute inset-0 opacity-50" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='300' height='300' viewBox='0 0 300 300' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='%237C3AED' stroke-width='1.5' opacity='0.4'%3E%3Cpath d='M0 150 Q75 100 150 150 T300 150' stroke-dasharray='8,4'%3E%3Canimate attributeName='stroke-dashoffset' values='0;12' dur='3s' repeatCount='indefinite'/%3E%3C/path%3E%3Cpath d='M150 0 Q200 75 150 150 T150 300' stroke-dasharray='6,6'%3E%3Canimate attributeName='stroke-dashoffset' values='0;12' dur='4s' repeatCount='indefinite'/%3E%3C/path%3E%3Cpath d='M0 0 Q150 150 300 0' stroke-dasharray='4,8'%3E%3Canimate attributeName='stroke-dashoffset' values='0;12' dur='5s' repeatCount='indefinite'/%3E%3C/path%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '300px 300px'
        }}></div>

        {/* Floating Orbs */}
        <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-blue-200/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-indigo-200/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-slate-200/30 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Content Container */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="text-center mt-8 animate-fade-in">
          <div className="relative inline-block ">
            {/* Animated AI/Video Creation Icon */}
            <div className="relative">
              {/* Outer rotating rings */}
              <div className="absolute inset-0 w-28 h-28 border-2 border-blue-300/40 rounded-full animate-spin-slow"></div>
              <div className="absolute inset-2 w-24 h-24 border border-indigo-300/30 rounded-full animate-spin-reverse"></div>

              {/* Main icon container */}
              <div className="relative w-28 h-28 bg-gradient-to-br from-white to-slate-100 rounded-3xl flex items-center justify-center shadow-2xl border border-slate-200">
                {/* AI Brain + Video Camera Hybrid Icon */}
                <div className="relative">
                  {/* AI Neural Network Background */}
                  <div className="absolute inset-0 opacity-15">
                    <svg className="w-14 h-14 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                    </svg>
                  </div>

                  {/* User Plus Icon for Registration */}
                  <svg className="w-10 h-10 text-slate-700 relative z-10 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>

                  {/* Animated dots for AI processing */}
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-ping"></div>
                  <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-500 rounded-full animate-ping delay-300"></div>
                  <div className="absolute top-1 -left-2 w-2 h-2 bg-purple-500 rounded-full animate-ping delay-700"></div>
                </div>
              </div>

              {/* Floating particles */}
              <div className="absolute top-2 left-2 w-1.5 h-1.5 bg-blue-400 rounded-full animate-float-1"></div>
              <div className="absolute top-4 right-2 w-1 h-1 bg-indigo-400 rounded-full animate-float-2"></div>
              <div className="absolute bottom-2 left-4 w-1 h-1 bg-slate-400 rounded-full animate-float-3"></div>
              <div className="absolute bottom-4 right-4 w-1.5 h-1.5 bg-green-400 rounded-full animate-float-1 delay-500"></div>
            </div>
          </div>

          <h1 className="text-3xl sm:text-4xl my-2 lg:text-5xl font-bold bg-gradient-to-r from-slate-800 via-slate-700 to-slate-600 bg-clip-text text-transparent">
            Command Center
          </h1>
          <p className="text-lg sm:text-xl text-slate-700 font-semibold tracking-wide mb-2">
            AI-Powered Avatar Creation Studio
          </p>
        </div>

        {/* Auth Form Container */}
        <div className="w-full max-w-lg animate-slide-up">
          <AuthForm type="register" onSubmit={handleRegister} isLoading={isLoading} />
        </div>

        {/* Footer */}
        <div className="mt-8 text-center animate-fade-in-delayed">
          <p className="text-slate-500 text-sm mb-4">
            © 2024 Avatar Command Center. Powered by Luminous Labs.
          </p>
        </div>
      </div>

      {/* Custom Styles */}
      <style jsx>{`
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: translateY(-20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes slide-up {
          from {
            opacity: 0;
            transform: translateY(40px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes fade-in-delayed {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        @keyframes spin-slow {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }

        @keyframes spin-reverse {
          from {
            transform: rotate(360deg);
          }
          to {
            transform: rotate(0deg);
          }
        }

        @keyframes float-1 {
          0%, 100% {
            transform: translateY(0px) translateX(0px);
            opacity: 0.7;
          }
          50% {
            transform: translateY(-10px) translateX(5px);
            opacity: 1;
          }
        }

        @keyframes float-2 {
          0%, 100% {
            transform: translateY(0px) translateX(0px);
            opacity: 0.5;
          }
          50% {
            transform: translateY(-8px) translateX(-3px);
            opacity: 1;
          }
        }

        @keyframes float-3 {
          0%, 100% {
            transform: translateY(0px) translateX(0px);
            opacity: 0.6;
          }
          50% {
            transform: translateY(-12px) translateX(4px);
            opacity: 1;
          }
        }

        .animate-fade-in {
          animation: fade-in 1s ease-out;
        }

        .animate-slide-up {
          animation: slide-up 1s ease-out 0.3s both;
        }

        .animate-fade-in-delayed {
          animation: fade-in-delayed 1s ease-out 0.6s both;
        }

        .animate-spin-slow {
          animation: spin-slow 8s linear infinite;
        }

        .animate-spin-reverse {
          animation: spin-reverse 6s linear infinite;
        }

        .animate-float-1 {
          animation: float-1 3s ease-in-out infinite;
        }

        .animate-float-2 {
          animation: float-2 4s ease-in-out infinite;
        }

        .animate-float-3 {
          animation: float-3 3.5s ease-in-out infinite;
        }
      `}</style>
    </div>
  )
}

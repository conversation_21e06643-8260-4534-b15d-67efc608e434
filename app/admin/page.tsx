'use client'

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  FolderTree, 
  Users, 
  ShoppingBag, 
  Settings, 
  BarChart3, 
  Shield,
  Database,
  Key,
  TestTube,
  Crown,
  CreditCard
} from "lucide-react"
import Link from "next/link"

export default function AdminDashboard() {
  const adminSections = [
    {
      title: "Category Management",
      description: "Manage product and content categories",
      icon: <FolderTree className="h-8 w-8" />,
      href: "/admin/categories",
      color: "bg-blue-500"
    },
    {
      title: "User Management",
      description: "Manage users and permissions",
      icon: <Users className="h-8 w-8" />,
      href: "/admin/users",
      color: "bg-green-500"
    },
    {
      title: "Product Management",
      description: "Advanced product administration",
      icon: <ShoppingBag className="h-8 w-8" />,
      href: "/admin/products",
      color: "bg-purple-500"
    },
    {
      title: "System Settings",
      description: "Configure system-wide settings",
      icon: <Settings className="h-8 w-8" />,
      href: "/admin/settings",
      color: "bg-orange-500"
    },
    {
      title: "Analytics & Reports",
      description: "View detailed analytics and reports",
      icon: <BarChart3 className="h-8 w-8" />,
      href: "/admin/analytics",
      color: "bg-indigo-500"
    },
    {
      title: "Security & Audit",
      description: "Security logs and audit trails",
      icon: <Shield className="h-8 w-8" />,
      href: "/admin/security",
      color: "bg-red-500"
    },
    {
      title: "Database Management",
      description: "Database operations and maintenance",
      icon: <Database className="h-8 w-8" />,
      href: "/admin/database",
      color: "bg-gray-500"
    },
    {
      title: "API Token Management",
      description: "Manage API tokens and access",
      icon: <Key className="h-8 w-8" />,
      href: "/admin/token",
      color: "bg-yellow-500"
    },
    {
      title: "Order History",
      description: "View and manage all customer orders",
      icon: <CreditCard className="h-8 w-8" />,
      href: "/order-history",
      color: "bg-blue-600"
    },
    {
      title: "API Testing",
      description: "Test API connections and endpoints",
      icon: <TestTube className="h-8 w-8" />,
      href: "/admin/test-api",
      color: "bg-cyan-500"
    }
  ]

  return (
    <div className="container mx-auto py-8">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Administrative tools and system management
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {adminSections.map((section) => (
            <Link key={section.href} href={section.href}>
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader className="pb-3">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${section.color} text-white`}>
                      {section.icon}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{section.title}</CardTitle>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription>{section.description}</CardDescription>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common administrative tasks
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Button variant="outline" asChild>
                <Link href="/admin/categories">
                  <FolderTree className="mr-2 h-4 w-4" />
                  Manage Categories
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/admin/test-api">
                  <TestTube className="mr-2 h-4 w-4" />
                  Test API
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/admin/token">
                  <Key className="mr-2 h-4 w-4" />
                  API Tokens
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/settings">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Status</CardTitle>
            <CardDescription>
              Current system health and status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">Online</div>
                <div className="text-sm text-green-700">API Status</div>
              </div>
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">Normal</div>
                <div className="text-sm text-blue-700">Performance</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">Active</div>
                <div className="text-sm text-purple-700">Services</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

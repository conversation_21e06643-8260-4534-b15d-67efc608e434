'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { setAuthToken, clearAuthToken } from "@/lib/api/products"

export default function TokenManagementPage() {
  const [token, setToken] = useState('')
  const { toast } = useToast()

  const handleSetToken = () => {
    if (!token.trim()) {
      toast({
        title: "Error",
        description: "Please enter a valid token",
        variant: "destructive",
      })
      return
    }

    try {
      setAuthToken(token.trim())
      toast({
        title: "Success",
        description: "Authentication token has been set successfully",
        variant: "default",
      })
      setToken('')
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to set token",
        variant: "destructive",
      })
    }
  }

  const handleClearToken = () => {
    clearAuthToken()
    toast({
      title: "Success",
      description: "Authentication token has been cleared",
      variant: "default",
    })
  }

  const getCurrentToken = () => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_token') || 'No token set'
    }
    return 'No token set'
  }

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Authentication Token Management</CardTitle>
          <CardDescription>
            Set your API authentication token to connect to the backend
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="token">Authentication Token</Label>
            <Input
              id="token"
              type="password"
              placeholder="Paste your JWT token here..."
              value={token}
              onChange={(e) => setToken(e.target.value)}
            />
          </div>

          <div className="flex gap-4">
            <Button onClick={handleSetToken}>
              Set Token
            </Button>
            <Button variant="outline" onClick={handleClearToken}>
              Clear Token
            </Button>
          </div>

          <div className="space-y-2">
            <Label>Current Token Status</Label>
            <div className="p-3 bg-muted rounded-md text-sm font-mono break-all">
              {getCurrentToken().substring(0, 50)}...
            </div>
          </div>

          <div className="space-y-2">
            <Label>Instructions</Label>
            <div className="text-sm text-muted-foreground space-y-2">
              <p>1. Get a fresh token from your API</p>
              <p>2. Paste the token in the field above</p>
              <p>3. Click Set Token to save it</p>
              <p>4. Navigate to the products page to see your real data</p>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Example API Call</Label>
            <div className="p-3 bg-muted rounded-md text-sm font-mono">
              <div>curl -X POST http://localhost:5002/api/auth/login</div>
              <div>-H Content-Type: application/json</div>
              <div>-d email and password</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

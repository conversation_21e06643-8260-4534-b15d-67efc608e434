'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { productService } from "@/lib/api/products"

export default function TestApiPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const { toast } = useToast()

  const testConnection = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      console.log('Testing API connection...')
      const products = await productService.getAllProducts()
      console.log('API test successful:', products)
      
      setResult({
        success: true,
        data: products,
        message: `Successfully loaded ${products.length} products`
      })
      
      toast({
        title: "Success",
        description: `API connection successful! Loaded ${products.length} products.`,
        variant: "default",
      })
    } catch (error) {
      console.error('API test failed:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      
      setResult({
        success: false,
        error: errorMessage,
        message: 'API connection failed'
      })
      
      toast({
        title: "Error",
        description: `API test failed: ${errorMessage}`,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const testProductDetails = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      console.log('Testing product details API...')
      const product = await productService.getProductById('f024f409-86bf-4316-a840-676edb13ad58')
      console.log('Product details test successful:', product)
      
      setResult({
        success: true,
        data: product,
        message: `Successfully loaded product: ${product.name}`
      })
      
      toast({
        title: "Success",
        description: `Product details loaded: ${product.name}`,
        variant: "default",
      })
    } catch (error) {
      console.error('Product details test failed:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      
      setResult({
        success: false,
        error: errorMessage,
        message: 'Product details API failed'
      })
      
      toast({
        title: "Error",
        description: `Product details test failed: ${errorMessage}`,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>API Connection Test</CardTitle>
          <CardDescription>
            Test the connection to your backend API
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex gap-4">
            <Button 
              onClick={testConnection} 
              disabled={loading}
            >
              {loading ? 'Testing...' : 'Test Products List API'}
            </Button>
            
            <Button 
              onClick={testProductDetails} 
              disabled={loading}
              variant="outline"
            >
              {loading ? 'Testing...' : 'Test Product Details API'}
            </Button>
          </div>

          {result && (
            <div className="space-y-4">
              <div className={`p-4 rounded-md ${result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                <h3 className={`font-semibold ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                  {result.success ? '✅ Success' : '❌ Error'}
                </h3>
                <p className={`text-sm ${result.success ? 'text-green-700' : 'text-red-700'}`}>
                  {result.message}
                </p>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold">Response Data:</h4>
                <pre className="bg-muted p-4 rounded-md text-sm overflow-auto max-h-96">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            </div>
          )}

          <div className="space-y-2">
            <h4 className="font-semibold">Instructions:</h4>
            <div className="text-sm text-muted-foreground space-y-2">
              <p>1. Make sure you have set a valid authentication token in the Token Management page</p>
              <p>2. Click "Test Products List API" to test the main products endpoint</p>
              <p>3. Click "Test Product Details API" to test the product details endpoint</p>
              <p>4. Check the browser console for detailed logs</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

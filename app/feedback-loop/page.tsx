import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { RecommendationLog } from "@/components/recommendation-log"
import { ABTestingBoard } from "@/components/ab-testing-board"
import { AppLayout } from "@/components/layouts/app-layout"
// import { StrategyAdjustmentTracker } from "@/components/strategy-adjustment-tracker"
// import { InsightsFeed } from "@/components/insights-feed"

export default function FeedbackLoopPage() {
  return (
    <AppLayout>
      <div className="space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Feedback & Learning Loop Engine</h2>
          <div className="flex items-center space-x-2">
            <Button variant="outline">Export Data</Button>
            <Button>Generate Insights</Button>
          </div>
        </div>

        <Tabs defaultValue="recommendations" className="space-y-4">
          <TabsList>
            <TabsTrigger value="recommendations">Recommendation Log</TabsTrigger>
            <TabsTrigger value="ab-testing">A/B Testing</TabsTrigger>
            <TabsTrigger value="strategy">Strategy Adjustments</TabsTrigger>
            <TabsTrigger value="insights">Insights Feed</TabsTrigger>
          </TabsList>

          <TabsContent value="recommendations" className="space-y-4">
            <RecommendationLog />
          </TabsContent>

          <TabsContent value="ab-testing" className="space-y-4">
            <ABTestingBoard />
          </TabsContent>

          <TabsContent value="strategy" className="space-y-4">
            {/* <StrategyAdjustmentTracker /> */}
            <div className="p-8 text-center text-muted-foreground">
              Strategy Adjustment Tracker temporarily disabled
            </div>
          </TabsContent>

          <TabsContent value="insights" className="space-y-4">
            {/* <InsightsFeed /> */}
            <div className="p-8 text-center text-muted-foreground">
              Insights Feed temporarily disabled
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  )
}

'use client'

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Skeleton } from "@/components/ui/skeleton"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"
import {
  <PERSON>,
  Filter,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Pause,
  Activity,
  Eye,
  BarChart3,
  Copy,
  ExternalLink,
  Info
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { productService } from "@/lib/api/products"
import { useAuth } from "@/contexts/auth-context"
import { AppLayout } from "@/components/layouts/app-layout"

interface AnalysisItem {
  id: string
  external_job_id: string
  product_id: string
  product_name: string
  owner_name: string
  status: string
  submitted_at: string
  completed_at: string | null
  error_message: string | null
}

interface AnalysisResponse {
  success: boolean
  data: AnalysisItem[]
  pagination: {
    currentPage: number
    totalPages: number
    totalItems: number
    itemsPerPage: number
  }
  filters: {
    status: string
    owner_id: string
    search: string | null
  }
}

export default function AnalysisQueuePage() {
  const [analyses, setAnalyses] = useState<AnalysisItem[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [checkingStatus, setCheckingStatus] = useState<string | null>(null)
  const [isProgressModalOpen, setIsProgressModalOpen] = useState(false)
  const [selectedAnalysis, setSelectedAnalysis] = useState<AnalysisItem | null>(null)
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10
  })
  const [filters, setFilters] = useState({
    status: 'all',
    search: '',
    limit: 10
  })
  const { toast } = useToast()
  const { user } = useAuth()

  const fetchAnalyses = async (params?: {
    page?: number
    limit?: number
    status?: string
    search?: string
  }) => {
    try {
      setLoading(true)
      const response: AnalysisResponse = await productService.getAnalyses(params)
      
      if (response.success) {
        setAnalyses(response.data)
        setPagination(response.pagination)
      } else {
        throw new Error('Failed to fetch analyses')
      }
    } catch (error) {
      console.error('Failed to fetch analyses:', error)
      toast({
        title: "Error",
        description: "Failed to load analysis queue data.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchAnalyses({
      page: pagination.currentPage,
      limit: filters.limit,
      status: filters.status !== 'all' ? filters.status : undefined,
      search: filters.search || undefined
    })
    setRefreshing(false)
  }

  const handleSearch = (searchTerm: string) => {
    setFilters(prev => ({ ...prev, search: searchTerm }))
    fetchAnalyses({
      page: 1,
      limit: filters.limit,
      status: filters.status || undefined,
      search: searchTerm || undefined
    })
  }

  const handleStatusFilter = (status: string) => {
    setFilters(prev => ({ ...prev, status }))
    fetchAnalyses({
      page: 1,
      limit: filters.limit,
      status: status !== 'all' ? status : undefined,
      search: filters.search || undefined
    })
  }

  const handlePageChange = (page: number) => {
    fetchAnalyses({
      page,
      limit: filters.limit,
      status: filters.status !== 'all' ? filters.status : undefined,
      search: filters.search || undefined
    })
  }

  const handleLimitChange = (limit: number) => {
    setFilters(prev => ({ ...prev, limit }))
    fetchAnalyses({
      page: 1,
      limit,
      status: filters.status !== 'all' ? filters.status : undefined,
      search: filters.search || undefined
    })
  }

  useEffect(() => {
    fetchAnalyses({
      page: 1,
      limit: filters.limit,
      status: filters.status !== 'all' ? filters.status : undefined,
      search: filters.search || undefined
    })
  }, [])

  const handleCheckStatus = async (analysis: AnalysisItem) => {
    if (!analysis.external_job_id) {
      toast({
        title: "Error",
        description: "No external job ID available for this analysis.",
        variant: "destructive",
      })
      return
    }

    setCheckingStatus(analysis.id)

    try {
      console.log('Checking status for analysis:', analysis.external_job_id)
      console.log('Analysis object:', analysis)

      const statusResponse = await productService.checkAnalysisStatus(analysis.external_job_id)

      console.log('Status response received:', statusResponse)

      // Handle different response formats
      let newStatus = analysis.status // Default to current status

      if (statusResponse) {
        // Check if response has status directly
        if (statusResponse.status) {
          newStatus = statusResponse.status
        }
        // Check if response has data.status
        else if (statusResponse.data && statusResponse.data.status) {
          newStatus = statusResponse.data.status
        }
        // Check if response itself is the status string
        else if (typeof statusResponse === 'string') {
          newStatus = statusResponse
        }
      }

      console.log('New status determined:', newStatus)

      // Format status for display
      const formatStatus = (status: string) => {
        return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()
      }

      const formattedStatus = formatStatus(newStatus)

      // Update the analysis status in the local state
      setAnalyses(prev => prev.map(a =>
        a.id === analysis.id
          ? { ...a, status: newStatus }
          : a
      ))

      // Also update selected analysis if it's the same one
      if (selectedAnalysis && selectedAnalysis.id === analysis.id) {
        setSelectedAnalysis(prev => prev ? { ...prev, status: newStatus } : null)
      }

      // Show appropriate toast message based on status
      const statusMessages = {
        'pending': 'Analysis is queued for processing',
        'processing': 'Analysis is currently being processed',
        'completed': 'Analysis has been completed successfully',
        'failed': 'Analysis processing has failed',
        'cancelled': 'Analysis has been cancelled'
      }

      const statusMessage = statusMessages[newStatus.toLowerCase() as keyof typeof statusMessages] || `Status updated to: ${formattedStatus}`

      toast({
        title: "Status Updated",
        description: statusMessage,
        variant: newStatus.toLowerCase() === 'failed' ? 'destructive' : 'default'
      })

      // Refresh the data to get latest information
      await fetchAnalyses({
        page: pagination.currentPage,
        limit: filters.limit,
        status: filters.status !== 'all' ? filters.status : undefined,
        search: filters.search || undefined
      })

    } catch (error) {
      console.error('Failed to check analysis status:', error)

      // More detailed error handling
      let errorMessage = "Failed to check analysis status."
      if (error instanceof Error) {
        errorMessage = error.message
      } else if (typeof error === 'object' && error !== null && 'message' in error) {
        errorMessage = (error as any).message
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setCheckingStatus(null)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'processing':
        return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'cancelled':
        return <Pause className="h-4 w-4 text-gray-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'default',
      processing: 'secondary',
      pending: 'outline',
      failed: 'destructive',
      cancelled: 'secondary'
    } as const

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status}
      </Badge>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const getProgressInfo = (status: string) => {
    const steps = [
      {
        key: 'pending',
        label: 'Queued',
        description: 'Analysis queued for processing',
        icon: '📋',
        shortDesc: 'Queued'
      },
      {
        key: 'processing',
        label: 'Analyzing',
        description: 'AI analyzing product data',
        icon: '🔍',
        shortDesc: 'Analyzing'
      },
      {
        key: 'completed',
        label: 'Completed',
        description: 'Analysis completed successfully',
        icon: '✅',
        shortDesc: 'Complete'
      },
      {
        key: 'failed',
        label: 'Failed',
        description: 'Analysis failed',
        icon: '❌',
        shortDesc: 'Failed'
      }
    ]

    const statusMapping: Record<string, { progress: number, stepIndex: number }> = {
      'pending': { progress: 25, stepIndex: 0 },
      'processing': { progress: 66, stepIndex: 1 },
      'completed': { progress: 100, stepIndex: 2 },
      'failed': { progress: 100, stepIndex: 3 },
      'cancelled': { progress: 100, stepIndex: 3 }
    }

    const mapping = statusMapping[status] || { progress: 0, stepIndex: 0 }

    return {
      steps,
      currentStepIndex: mapping.stepIndex,
      progress: mapping.progress,
      currentStep: steps[mapping.stepIndex] || steps[0]
    }
  }

  const handleViewAnalysisInfo = (analysis: AnalysisItem) => {
    setSelectedAnalysis(analysis)
    setIsProgressModalOpen(true)
  }

  return (
    <AppLayout>
      <div className="space-y-4 sm:space-y-6 p-3 sm:p-4 md:p-6 lg:p-8 pt-3 sm:pt-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4">
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight truncate">Analysis Queue Report</h1>
            <p className="text-muted-foreground mt-1 sm:mt-2 text-sm sm:text-base">
              Monitor and manage product analysis jobs
            </p>
          </div>
          <Button onClick={handleRefresh} disabled={refreshing} className="w-full sm:w-fit shrink-0">
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader className="pb-3 sm:pb-4">
          <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
            <Filter className="h-4 w-4 sm:h-5 sm:w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-3 sm:p-4 md:p-6">
          <div className="flex flex-col gap-3 sm:gap-4">
            <div className="flex-1 min-w-0">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by product name or job ID..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch(filters.search)}
                  className="pl-10 text-sm sm:text-base"
                />
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
              <Select value={filters.status} onValueChange={handleStatusFilter}>
                <SelectTrigger className="w-full sm:w-[160px] md:w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={() => handleSearch(filters.search)} className="w-full sm:w-auto shrink-0">
                <Search className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Search</span>
                <span className="sm:hidden">Go</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Analysis Table */}
      <Card>
        <CardHeader className="pb-3 sm:pb-4">
          <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between sm:gap-4">
            <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
              <Activity className="h-4 w-4 sm:h-5 sm:w-5" />
              <span>Analysis Queue</span>
            </CardTitle>
            <div className="flex items-center flex-reverse gap-2 sm:gap-3">
              <span className="text-xs sm:text-sm text-muted-foreground whitespace-nowrap order-2 xs:order-1">
                {pagination.totalItems} total items
              </span>
              <Select value={filters.limit.toString()} onValueChange={(value) => handleLimitChange(parseInt(value))}>
                <SelectTrigger className="w-full xs:w-[80px] sm:w-[100px] ">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="p-3 sm:p-4 md:p-6 space-y-3 sm:space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-2 sm:space-x-4">
                  <Skeleton className="h-4 w-[80px] sm:w-[100px]" />
                  <Skeleton className="h-4 w-[120px] sm:w-[200px]" />
                  <Skeleton className="h-4 w-[100px] sm:w-[150px]" />
                  <Skeleton className="h-4 w-[80px] sm:w-[100px]" />
                  <Skeleton className="h-4 w-[100px] sm:w-[150px]" />
                </div>
              ))}
            </div>
          ) : analyses.length > 0 ? (
            <div className="rounded-md border overflow-hidden">
              {/* Mobile Card View */}
              <div className="block sm:hidden">
                {analyses.map((analysis) => (
                  <div key={analysis.id} className="p-4 border-b last:border-b-0">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2 min-w-0 flex-1">
                        {getStatusIcon(analysis.status)}
                        <div className="min-w-0 flex-1">
                          <h3 className="font-medium text-sm truncate md:max-w-[400px] max-w-[250px]" title={analysis.product_name}>
                            {analysis.product_name}
                          </h3>
                          <div className="flex items-center space-x-2 mt-1">
                            {getStatusBadge(analysis.status)}
                          </div>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0 shrink-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuLabel className="text-xs">Actions</DropdownMenuLabel>
                          <DropdownMenuItem
                            onClick={() => navigator.clipboard.writeText(analysis.external_job_id)}
                            className="text-xs"
                          >
                            <Copy className="mr-2 h-3 w-3" />
                            Copy Job ID
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => navigator.clipboard.writeText(analysis.product_id)}
                            className="text-xs"
                          >
                            <Copy className="mr-2 h-3 w-3" />
                            Copy Product ID
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild className="text-xs">
                            <Link href={`/products/${analysis.product_id}`}>
                              <Eye className="mr-2 h-3 w-3" />
                              View Product
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleCheckStatus(analysis)}
                            disabled={checkingStatus === analysis.id}
                            className="text-xs"
                          >
                            {checkingStatus === analysis.id ? (
                              <RefreshCw className="mr-2 h-3 w-3 animate-spin" />
                            ) : (
                              <BarChart3 className="mr-2 h-3 w-3" />
                            )}
                            Check Status
                          </DropdownMenuItem>
                          {analysis.status === 'completed' && (
                            <DropdownMenuItem className="text-xs">
                              <ExternalLink className="mr-2 h-3 w-3" />
                              View Results
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    <div className="space-y-2 text-xs text-muted-foreground">
                      <div className="flex justify-between">
                        <span>Owner:</span>
                        <span className="font-medium text-foreground">{analysis.owner_name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Job ID:</span>
                        <code
                          className="text-xs bg-muted px-2 py-1 rounded cursor-pointer hover:bg-blue-100 transition-colors"
                          title={analysis.external_job_id}
                          onClick={() => handleViewAnalysisInfo(analysis)}
                        >
                          ...{analysis.external_job_id.slice(-6)}
                        </code>
                      </div>
                      <div className="flex justify-between">
                        <span>Submitted:</span>
                        <span>{formatDate(analysis.submitted_at)}</span>
                      </div>
                      {analysis.completed_at && (
                        <div className="flex justify-between">
                          <span>Completed:</span>
                          <span>{formatDate(analysis.completed_at)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Desktop Table View */}
              <div className="hidden sm:block overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[140px] font-medium">Status</TableHead>
                      <TableHead className="min-w-[200px] font-medium">Product Name</TableHead>
                      <TableHead className="w-[150px] font-medium hidden md:table-cell">Owner</TableHead>
                      <TableHead className="w-[120px] font-medium">
                        <Button
                          variant="ghost"
                          className="h-auto p-0 font-medium hover:bg-transparent"
                          onClick={() => setIsProgressModalOpen(true)}
                        >
                          Job ID
                        </Button>
                      </TableHead>
                      <TableHead className="w-[160px] font-medium hidden lg:table-cell">Submitted</TableHead>
                      <TableHead className="w-[160px] font-medium hidden xl:table-cell">Completed</TableHead>
                      <TableHead className="text-right w-[100px] font-medium">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {analyses.map((analysis) => (
                      <TableRow key={analysis.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(analysis.status)}
                            {getStatusBadge(analysis.status)}
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">
                          <div className="truncate max-w-[600px]" title={analysis.product_name}>
                            {analysis.product_name}
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <div className="truncate" title={analysis.owner_name}>
                            {analysis.owner_name}
                          </div>
                        </TableCell>
                        <TableCell>
                          <code
                            className="text-xs bg-muted px-2 py-1 rounded cursor-pointer hover:bg-blue-100 transition-colors"
                            title={analysis.external_job_id}
                            onClick={() => handleViewAnalysisInfo(analysis)}
                          >
                            ...{analysis.external_job_id.slice(-6)}
                          </code>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground hidden lg:table-cell">
                          <div className="truncate" title={formatDate(analysis.submitted_at)}>
                            {formatDate(analysis.submitted_at)}
                          </div>
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground hidden xl:table-cell">
                          <div className="truncate" title={analysis.completed_at ? formatDate(analysis.completed_at) : '-'}>
                            {analysis.completed_at ? formatDate(analysis.completed_at) : '-'}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-48">
                              <DropdownMenuLabel className="text-sm">Actions</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => navigator.clipboard.writeText(analysis.external_job_id)}
                                className="text-sm"
                              >
                                <Copy className="mr-2 h-4 w-4" />
                                Copy Job ID
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => navigator.clipboard.writeText(analysis.product_id)}
                                className="text-sm"
                              >
                                <Copy className="mr-2 h-4 w-4" />
                                Copy Product ID
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem asChild className="text-sm">
                                <Link href={`/products/${analysis.product_id}`}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Product
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleCheckStatus(analysis)}
                                disabled={checkingStatus === analysis.id}
                                className="text-sm"
                              >
                                {checkingStatus === analysis.id ? (
                                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                                ) : (
                                  <BarChart3 className="mr-2 h-4 w-4" />
                                )}
                                Check Status
                              </DropdownMenuItem>
                              {analysis.status === 'completed' && (
                                <DropdownMenuItem className="text-sm">
                                  <ExternalLink className="mr-2 h-4 w-4" />
                                  View Results
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          ) : (
            <div className="text-center py-12 sm:py-16 px-4 sm:px-6">
              <Activity className="mx-auto h-10 w-10 sm:h-12 sm:w-12 text-muted-foreground" />
              <h3 className="mt-3 sm:mt-4 text-base sm:text-lg font-semibold">No analyses found</h3>
              <p className="text-muted-foreground mt-2 max-w-md mx-auto text-sm sm:text-base">
                {filters.search || filters.status !== 'all'
                  ? "Try adjusting your filters or search terms"
                  : "No analysis jobs have been submitted yet"
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <Card>
            <CardContent className="p-3 sm:p-4 md:p-6">
              <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between sm:gap-4">
                <div className="text-xs sm:text-sm text-muted-foreground text-center sm:text-left">
                  Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to{' '}
                  {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{' '}
                  {pagination.totalItems} results
                </div>
                <div className="flex items-center justify-center space-x-1 sm:space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                    disabled={pagination.currentPage <= 1}
                    className="hidden sm:flex text-xs sm:text-sm"
                  >
                    <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                    disabled={pagination.currentPage <= 1}
                    className="sm:hidden h-8 w-8 p-0"
                  >
                    <ChevronLeft className="h-3 w-3" />
                  </Button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(3, pagination.totalPages) }, (_, i) => {
                      const pageNumber = i + 1
                      return (
                        <Button
                          key={pageNumber}
                          variant={pagination.currentPage === pageNumber ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(pageNumber)}
                          className="w-7 h-7 sm:w-8 sm:h-8 p-0 text-xs sm:text-sm"
                        >
                          {pageNumber}
                        </Button>
                      )
                    })}
                    {pagination.totalPages > 3 && (
                      <>
                        <span className="text-muted-foreground px-1 text-xs sm:text-sm">...</span>
                        <Button
                          variant={pagination.currentPage === pagination.totalPages ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(pagination.totalPages)}
                          className="w-7 h-7 sm:w-8 sm:h-8 p-0 text-xs sm:text-sm"
                        >
                          {pagination.totalPages}
                        </Button>
                      </>
                    )}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                    disabled={pagination.currentPage >= pagination.totalPages}
                    className="hidden sm:flex text-xs sm:text-sm"
                  >
                    Next
                    <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 ml-1" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                    disabled={pagination.currentPage >= pagination.totalPages}
                    className="sm:hidden h-8 w-8 p-0"
                  >
                    <ChevronRight className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Analysis Progress Modal */}
      <Dialog open={isProgressModalOpen} onOpenChange={setIsProgressModalOpen}>
        <DialogContent className="max-w-[95vw] sm:max-w-2xl md:max-w-3xl lg:max-w-4xl max-h-[90vh] overflow-y-auto mx-4">
          <DialogHeader>
            <DialogTitle className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg w-fit">
                <BarChart3 className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
              </div>
              <div className="min-w-0">
                <span className="text-lg sm:text-xl block">Analysis Processing Information</span>
                <p className="text-xs sm:text-sm text-muted-foreground font-normal mt-1">
                  Understanding how your product analysis is processed
                </p>
              </div>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6 sm:space-y-8">
            {/* Processing Workflow */}
            <div className="space-y-3 sm:space-y-4">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900">Analysis Workflow</h3>
              <p className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                Every product analysis goes through a structured pipeline to extract valuable insights.
                Here's how your analysis is processed from submission to completion.
              </p>

              {/* Horizontal Workflow Visualization */}
              <div className="bg-gray-50 p-3 sm:p-4 md:p-6 rounded-xl border">
                <div className="relative">
                  <div className="flex items-center justify-between">
                    {[
                      { icon: '📋', title: 'Queued', desc: 'Analysis queued for processing' },
                      { icon: '🔍', title: 'Analyzing', desc: 'AI analyzing product data' },
                      { icon: '✅', title: 'Completed', desc: 'Analysis ready' }
                    ].map((step, index) => (
                      <div key={index} className="flex flex-col items-center relative z-10">
                        {/* Step Circle */}
                        <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full border-3 border-blue-500 bg-blue-100 flex items-center justify-center text-lg sm:text-xl md:text-2xl transition-all duration-300 hover:scale-110">
                          <span className="text-blue-600">{step.icon}</span>
                        </div>

                        {/* Step Label */}
                        <div className="mt-2 sm:mt-3 text-center">
                          <div className="text-xs sm:text-sm font-semibold text-gray-900">{step.title}</div>
                          <div className="text-xs text-gray-600 mt-1 hidden sm:block">{step.desc}</div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Connection Lines */}
                  <div className="absolute top-6 sm:top-7 md:top-8 left-6 sm:left-7 md:left-8 right-6 sm:right-7 md:right-8 h-1 bg-gray-300 -z-0 rounded-full">
                    <div className="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-1000"></div>
                  </div>
                </div>

                {/* Failed State Example */}
                <div className="mt-6 sm:mt-8 pt-4 sm:pt-6 border-t border-gray-200">
                  <div className="text-center">
                    <h4 className="text-xs sm:text-sm font-semibold text-gray-900 mb-2">If Analysis Fails:</h4>
                    <div className="inline-flex items-center space-x-2 px-3 sm:px-4 py-2 bg-red-50 border border-red-200 rounded-lg">
                      <span className="text-base sm:text-lg">❌</span>
                      <span className="text-xs sm:text-sm font-medium text-red-700">Failed</span>
                      <span className="text-xs text-red-600 hidden sm:inline">- Error details will be shown in actions menu</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Analysis Information Guide */}
            <div className="space-y-3 sm:space-y-4">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900">Understanding Analysis Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                <div className="space-y-3 sm:space-y-4">
                  <div className="p-3 sm:p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h4 className="font-semibold text-blue-900 mb-2 text-sm sm:text-base">📊 Analysis Details</h4>
                    <ul className="text-xs sm:text-sm text-blue-800 space-y-1">
                      <li>• <strong>Product Name:</strong> The product being analyzed</li>
                      <li>• <strong>Owner:</strong> User who submitted the analysis</li>
                      <li>• <strong>Job ID:</strong> Unique identifier (last 6 digits shown)</li>
                      <li>• <strong>Timestamps:</strong> Submission and completion times</li>
                    </ul>
                  </div>

                  <div className="p-3 sm:p-4 bg-purple-50 rounded-lg border border-purple-200">
                    <h4 className="font-semibold text-purple-900 mb-2 text-sm sm:text-base">🎯 Status Tracking</h4>
                    <ul className="text-xs sm:text-sm text-purple-800 space-y-1">
                      <li>• <strong>Real-time updates:</strong> Status changes automatically</li>
                      <li>• <strong>Progress indicators:</strong> Visual workflow progress</li>
                      <li>• <strong>Error handling:</strong> Clear error messages if issues occur</li>
                      <li>• <strong>Completion alerts:</strong> Notifications when ready</li>
                    </ul>
                  </div>
                </div>

                <div className="space-y-3 sm:space-y-4">
                  <div className="p-3 sm:p-4 bg-green-50 rounded-lg border border-green-200">
                    <h4 className="font-semibold text-green-900 mb-2 text-sm sm:text-base">⚡ Analysis Features</h4>
                    <ul className="text-xs sm:text-sm text-green-800 space-y-1">
                      <li>• <strong>AI-Powered:</strong> Advanced machine learning analysis</li>
                      <li>• <strong>Comprehensive:</strong> Multiple data points analyzed</li>
                      <li>• <strong>Fast Processing:</strong> Optimized for quick results</li>
                      <li>• <strong>Detailed Reports:</strong> In-depth insights and recommendations</li>
                    </ul>
                  </div>

                  <div className="p-3 sm:p-4 bg-orange-50 rounded-lg border border-orange-200">
                    <h4 className="font-semibold text-orange-900 mb-2 text-sm sm:text-base">💡 Tips & Best Practices</h4>
                    <ul className="text-xs sm:text-sm text-orange-800 space-y-1">
                      <li>• <strong>Click on Job IDs</strong> to view detailed progress</li>
                      <li>• <strong>Use filters</strong> to find specific analysis jobs</li>
                      <li>• <strong>Check status regularly</strong> for processing updates</li>
                      <li>• <strong>Review completed analyses</strong> for insights</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="p-4 sm:p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3">Quick Actions</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4">
                <Button variant="outline" className="justify-start h-auto p-3 sm:p-4">
                  <Activity className="h-4 w-4 sm:h-5 sm:w-5 mr-2 sm:mr-3 text-blue-600 shrink-0" />
                  <div className="text-left min-w-0">
                    <div className="font-medium text-xs sm:text-sm">Check Status</div>
                    <div className="text-xs text-muted-foreground hidden sm:block">Get real-time analysis updates</div>
                  </div>
                </Button>
                <Button variant="outline" className="justify-start h-auto p-3 sm:p-4">
                  <Copy className="h-4 w-4 sm:h-5 sm:w-5 mr-2 sm:mr-3 text-green-600 shrink-0" />
                  <div className="text-left min-w-0">
                    <div className="font-medium text-xs sm:text-sm">Copy Job ID</div>
                    <div className="text-xs text-muted-foreground hidden sm:block">Copy full job identifier</div>
                  </div>
                </Button>
                <Button variant="outline" className="justify-start h-auto p-3 sm:p-4 sm:col-span-2 md:col-span-1">
                  <Eye className="h-4 w-4 sm:h-5 sm:w-5 mr-2 sm:mr-3 text-purple-600 shrink-0" />
                  <div className="text-left min-w-0">
                    <div className="font-medium text-xs sm:text-sm">View Results</div>
                    <div className="text-xs text-muted-foreground hidden sm:block">See analysis results when ready</div>
                  </div>
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Individual Analysis Progress Modal */}
      {selectedAnalysis && (
        <Dialog open={!!selectedAnalysis && isProgressModalOpen} onOpenChange={(open) => {
          if (!open) setSelectedAnalysis(null)
        }}>
          <DialogContent className="max-w-[95vw] sm:max-w-2xl md:max-w-3xl max-h-[90vh] overflow-y-auto mx-4">
            <DialogHeader>
              <DialogTitle className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg w-fit">
                  <BarChart3 className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                </div>
                <div className="min-w-0">
                  <span className="text-lg sm:text-xl block">Analysis Progress</span>
                  <p className="text-xs sm:text-sm text-muted-foreground font-normal mt-1">
                    Detailed progress for this analysis job
                  </p>
                </div>
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4 sm:space-y-6">
              {/* Analysis Status Progress */}
              <div className="p-3 sm:p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 mb-3 sm:mb-4">
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900">Processing Status</h3>
                  {getStatusBadge(selectedAnalysis.status)}
                </div>

                {(() => {
                  const progressInfo = getProgressInfo(selectedAnalysis.status)
                  return (
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Progress</span>
                          <span className="font-medium">{Math.round(progressInfo.progress)}%</span>
                        </div>
                        <Progress
                          value={progressInfo.progress}
                          className={`h-3 ${
                            selectedAnalysis.status === 'failed' ? '[&>div]:bg-red-500' :
                            selectedAnalysis.status === 'completed' ? '[&>div]:bg-green-500' :
                            selectedAnalysis.status === 'processing' ? '[&>div]:bg-blue-500' :
                            '[&>div]:bg-yellow-500'
                          }`}
                        />
                        <p className="text-sm text-gray-600">
                          {progressInfo.currentStep.description}
                        </p>
                      </div>

                      {/* Horizontal Workflow Steps */}
                      <div className="relative">
                        <div className="flex items-center justify-between">
                          {progressInfo.steps.slice(0, 3).map((step, index) => {
                            const isActive = index <= progressInfo.currentStepIndex && selectedAnalysis.status !== 'failed'
                            const isCurrent = index === progressInfo.currentStepIndex
                            const isFailed = selectedAnalysis.status === 'failed' && index === progressInfo.currentStepIndex

                            return (
                              <div key={step.key} className="flex flex-col items-center relative z-10">
                                {/* Step Circle */}
                                <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-full border-3 flex items-center justify-center text-base sm:text-lg transition-all duration-300 ${
                                  isFailed ? 'border-red-500 bg-red-100' :
                                  isActive ? 'border-blue-500 bg-blue-100' :
                                  'border-gray-300 bg-gray-100'
                                }`}>
                                  {isCurrent && !isFailed && (
                                    <div className="absolute inset-0 rounded-full border-3 border-blue-500 animate-pulse"></div>
                                  )}
                                  <span className={`${
                                    isFailed ? 'text-red-600' :
                                    isActive ? 'text-blue-600' : 'text-gray-400'
                                  }`}>
                                    {step.icon}
                                  </span>
                                </div>

                                {/* Step Label */}
                                <div className="mt-2 text-center">
                                  <div className={`text-xs font-medium ${
                                    isFailed ? 'text-red-700' :
                                    isActive ? 'text-blue-700' : 'text-gray-500'
                                  }`}>
                                    {step.shortDesc}
                                  </div>
                                  {isCurrent && (
                                    <div className="text-xs text-gray-500 mt-1 hidden sm:block">
                                      {step.description}
                                    </div>
                                  )}
                                </div>
                              </div>
                            )
                          })}

                          {/* Failed Step - Show separately if failed */}
                          {selectedAnalysis.status === 'failed' && (
                            <div className="flex flex-col items-center relative z-10">
                              <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full border-3 border-red-500 bg-red-100 flex items-center justify-center text-base sm:text-lg">
                                <span className="text-red-600">❌</span>
                              </div>
                              <div className="mt-2 text-center">
                                <div className="text-xs font-medium text-red-700">Failed</div>
                                <div className="text-xs text-gray-500 mt-1 hidden sm:block">Analysis failed</div>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Connection Lines */}
                        <div className="absolute top-5 sm:top-6 left-5 sm:left-6 right-5 sm:right-6 h-0.5 bg-gray-200 -z-0">
                          <div
                            className={`h-full transition-all duration-500 ${
                              selectedAnalysis.status === 'failed' ? 'bg-red-500' :
                              selectedAnalysis.status === 'completed' ? 'bg-green-500' :
                              'bg-blue-500'
                            }`}
                            style={{
                              width: selectedAnalysis.status === 'failed' ? '100%' :
                                     selectedAnalysis.status === 'completed' ? '100%' :
                                     selectedAnalysis.status === 'processing' ? '50%' : '25%'
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  )
                })()}
              </div>

              {/* Analysis Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                <div className="space-y-3 sm:space-y-4">
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900">Analysis Details</h3>
                  <div className="space-y-2 sm:space-y-3">
                    <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                        <span className="text-xs sm:text-sm font-medium text-gray-600">Product</span>
                        <span className="text-xs sm:text-sm font-mono text-gray-900 max-w-full sm:max-w-48 truncate">
                          {selectedAnalysis.product_name}
                        </span>
                      </div>
                    </div>
                    <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                        <span className="text-xs sm:text-sm font-medium text-gray-600">Owner</span>
                        <span className="text-xs sm:text-sm text-gray-900">
                          {selectedAnalysis.owner_name}
                        </span>
                      </div>
                    </div>
                    <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                      <div className="flex flex-col gap-1">
                        <span className="text-xs sm:text-sm font-medium text-gray-600">Full Job ID</span>
                        <span className="text-xs sm:text-sm font-mono text-gray-900 break-all">
                          {selectedAnalysis.external_job_id}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-3 sm:space-y-4">
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900">Timestamps</h3>
                  <div className="space-y-2 sm:space-y-3">
                    <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                        <span className="text-xs sm:text-sm font-medium text-gray-600">Submitted</span>
                        <span className="text-xs sm:text-sm text-gray-900">
                          {formatDate(selectedAnalysis.submitted_at)}
                        </span>
                      </div>
                    </div>
                    <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                        <span className="text-xs sm:text-sm font-medium text-gray-600">Completed</span>
                        <span className="text-xs sm:text-sm text-gray-900">
                          {selectedAnalysis.completed_at ? formatDate(selectedAnalysis.completed_at) : 'Not completed'}
                        </span>
                      </div>
                    </div>
                    <div className="p-2 sm:p-3 bg-gray-50 rounded-lg">
                      <div className="flex flex-col gap-1">
                        <span className="text-xs sm:text-sm font-medium text-gray-600">Product ID</span>
                        <span className="text-xs sm:text-sm font-mono text-gray-900 break-all">
                          {selectedAnalysis.product_id}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Error Message */}
              {selectedAnalysis.error_message && (
                <div className="p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg">
                  <h4 className="text-xs sm:text-sm font-medium text-red-800 mb-2 flex items-center">
                    <XCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                    Error Details
                  </h4>
                  <p className="text-xs sm:text-sm text-red-700 break-words">{selectedAnalysis.error_message}</p>
                </div>
              )}

              {/* Actions */}
              <div className="flex flex-col sm:flex-row justify-end gap-2 sm:gap-3 pt-3 sm:pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => handleCheckStatus(selectedAnalysis)}
                  disabled={checkingStatus === selectedAnalysis.id}
                  className="w-full sm:w-auto text-xs sm:text-sm"
                >
                  {checkingStatus === selectedAnalysis.id ? (
                    <RefreshCw className="h-3 w-3 sm:h-4 sm:w-4 mr-2 animate-spin" />
                  ) : (
                    <Activity className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                  )}
                  Check Status
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigator.clipboard.writeText(selectedAnalysis.external_job_id)}
                  className="w-full sm:w-auto text-xs sm:text-sm"
                >
                  <Copy className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                  Copy Job ID
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </AppLayout>
  )
}

import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl

  // Handle dashboard billing success redirect to correct route
  if (pathname === '/dashboard/billing/success') {
    const orderId = searchParams.get('order_id')
    const redirectUrl = new URL('/billing/success', request.url)

    if (orderId) {
      redirectUrl.searchParams.set('order_id', orderId)
    }

    return NextResponse.redirect(redirectUrl)
  }

  // Handle dashboard billing cancel redirect to correct route
  if (pathname === '/dashboard/billing/cancel') {
    const redirectUrl = new URL('/billing/cancel', request.url)
    return NextResponse.redirect(redirectUrl)
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    '/dashboard/billing/success',
    '/dashboard/billing/cancel'
  ]
}

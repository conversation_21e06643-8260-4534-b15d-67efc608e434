'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { getCurrentSubscriptionInterval } from '@/lib/api/user-subscriptions'

export function useSubscriptionInterval() {
  const [currentInterval, setCurrentInterval] = useState<'monthly' | 'annual'>('monthly')
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const detectInterval = async () => {
      try {
        setIsLoading(true)
        
        // First check URL params for interval preference
        const urlInterval = searchParams.get('interval') as 'monthly' | 'annual' | null
        if (urlInterval && ['monthly', 'annual'].includes(urlInterval)) {
          setCurrentInterval(urlInterval)
          setIsLoading(false)
          return
        }

        // Then check current subscription interval
        const intervalData = await getCurrentSubscriptionInterval()
        if (intervalData.success && intervalData.data) {
          setCurrentInterval(intervalData.data.interval)
        }
      } catch (error) {
        console.error('Error detecting subscription interval:', error)
        // Default to monthly if detection fails
        setCurrentInterval('monthly')
      } finally {
        setIsLoading(false)
      }
    }

    detectInterval()
  }, [searchParams])

  const switchInterval = (newInterval: 'monthly' | 'annual') => {
    setCurrentInterval(newInterval)
    
    // Update URL to reflect the interval change
    const current = new URLSearchParams(Array.from(searchParams.entries()))
    current.set('interval', newInterval)
    const search = current.toString()
    const query = search ? `?${search}` : ''
    
    router.replace(`/subscription${query}`, { scroll: false })
  }

  return {
    currentInterval,
    isLoading,
    switchInterval,
    isAnnual: currentInterval === 'annual',
    isMonthly: currentInterval === 'monthly'
  }
}

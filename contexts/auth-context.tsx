'use client'

import React, { create<PERSON>ontext, useContext, useEffect, useState, useC<PERSON>back, ReactNode } from 'react'
import Cookies from 'js-cookie'
import { authAPI, User, LoginRequest, RegisterRequest, ForgotPasswordRequest, ResetPasswordRequest, ChangePasswordRequest, ProfileUpdateRequest } from '@/lib/api'
import { toast } from '@/hooks/use-toast'

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (credentials: LoginRequest) => Promise<boolean>
  register: (userData: RegisterRequest) => Promise<boolean>
  logout: () => Promise<void>
  forgotPassword: (data: ForgotPasswordRequest) => Promise<boolean>
  resetPassword: (data: ResetPasswordRequest) => Promise<boolean>
  changePassword: (data: ChangePasswordRequest) => Promise<boolean>
  sendVerification: () => Promise<boolean>
  verifyEmail: (token: string) => Promise<boolean>
  updateProfile: (data: ProfileUpdateRequest) => Promise<boolean>
  refreshProfile: () => Promise<void>
  checkAuth: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  // Check if user is authenticated on mount
  useEffect(() => {
    checkAuth()
  }, [])

  // Token expiration checker - runs every minute
  useEffect(() => {
    const checkTokenExpiration = () => {
      const token = Cookies.get('auth_token')
      const tokenTimestamp = Cookies.get('token_timestamp')

      if (token && tokenTimestamp) {
        const tokenTime = parseInt(tokenTimestamp)
        const currentTime = Date.now()
        const tokenAge = (currentTime - tokenTime) / (1000 * 60) // in minutes

        // JWT_ACCESS_EXPIRATION_MINUTES is 30 minutes from backend
        const expirationMinutes = 30

        if (tokenAge >= expirationMinutes) {
          console.log('Token has expired, logging out user')
          handleTokenExpiration()
        } else if (tokenAge >= expirationMinutes - 5) {
          // Show warning 5 minutes before expiration
          console.log('Token will expire soon')
          toast({
            title: "Session Expiring Soon",
            description: "Your session will expire in a few minutes. Please save your work.",
            variant: "default",
          })
        }
      }
    }

    // Check immediately
    checkTokenExpiration()

    // Then check every minute
    const interval = setInterval(checkTokenExpiration, 60000)

    return () => clearInterval(interval)
  }, [isAuthenticated])

  // Show session expired message if redirected with query param
  useEffect(() => {
    if (typeof window === 'undefined') return
    const params = new URLSearchParams(window.location.search)
    if (params.get('sessionExpired') === '1') {
      toast({
        title: 'Session expired',
        description: 'Please log in again to continue.',
        variant: 'destructive',
      })
      // Remove the param so the toast won't repeat on further renders
      const url = new URL(window.location.href)
      url.searchParams.delete('sessionExpired')
      window.history.replaceState({}, '', url.toString())
    }
  }, [])

  const checkAuth = async () => {
    try {
      const token = Cookies.get('auth_token')
      const userData = Cookies.get('user_data')
      const tokenTimestamp = Cookies.get('token_timestamp')

      if (token && userData) {
        // Check if token has expired
        if (tokenTimestamp) {
          const tokenTime = parseInt(tokenTimestamp)
          const currentTime = Date.now()
          const tokenAge = (currentTime - tokenTime) / (1000 * 60) // in minutes
          const expirationMinutes = 30 // JWT_ACCESS_EXPIRATION_MINUTES

          if (tokenAge >= expirationMinutes) {
            console.log('Token has expired during checkAuth')
            handleTokenExpiration()
            return
          }
        }

        // Use cached user data first
        try {
          const cachedUser = JSON.parse(userData)
          setUser(cachedUser)
          setIsAuthenticated(true)

          // Optionally try to refresh user data from API in background
          // This won't block the UI if the backend is not available
          authAPI.getProfile().then((freshUser) => {
            setUser(freshUser)
          }).catch((error) => {
            // If API call fails, keep using cached data
            console.log('Failed to refresh user data from API:', error.message)
          })
        } catch (parseError) {
          // Clear invalid cached data
          clearAuthData()
        }
      } else {
        clearAuthData()
      }
    } catch (error) {
      clearAuthData()
    } finally {
      setIsLoading(false)
    }
  }

  const clearAuthData = () => {
    setUser(null)
    setIsAuthenticated(false)

    // Clear cookies with proper domain settings
    const cookieOptions = {
      domain: process.env.NODE_ENV === 'production' ? '.luminousdemo.com' : undefined
    }

    Cookies.remove('auth_token', cookieOptions)
    Cookies.remove('user_data', cookieOptions)
    Cookies.remove('token_timestamp', cookieOptions)

    // Also try removing without domain (fallback)
    Cookies.remove('auth_token')
    Cookies.remove('user_data')
    Cookies.remove('token_timestamp')
  }

  const handleTokenExpiration = () => {
    clearAuthData()

    toast({
      title: "Session Expired",
      description: "Your session has expired. Please login again.",
      variant: "destructive",
    })

    // Redirect to login page with expired parameter
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login?expired=true'
    }
  }

  const login = async (credentials: LoginRequest): Promise<boolean> => {
    try {
      setIsLoading(true)

      console.log('Attempting login with:', credentials.email)

      // Call real API login
      const response = await authAPI.login(credentials)

      // console.log('Login response received:', response)

      if (!response.token || !response.user) {
        throw new Error('Invalid response format: missing token or user data')
      }

      // Store auth data
      const cookieOptions = {
        expires: response.expires_in / (24 * 60 * 60), // Convert seconds to days
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax' as const, // Changed from 'strict' to 'lax' for better cross-domain support
        domain: process.env.NODE_ENV === 'production' ? '.luminousdemo.com' : undefined
      }

      Cookies.set('auth_token', response.token, cookieOptions)
      Cookies.set('user_data', JSON.stringify(response.user), cookieOptions)

      // Store token timestamp for expiration tracking
      Cookies.set('token_timestamp', Date.now().toString(), cookieOptions)

      setUser(response.user)
      setIsAuthenticated(true)

      // console.log('Login successful, user set:', response.user)

      toast({
        title: "Login Successful",
        description: `Welcome back, ${response.user.first_name}!`,
      })

      return true
    } catch (error: any) {
      console.error('Login error:', error)

      let message = 'Login failed. Please try again.'

      if (error.message === 'Backend service is not available. Please try again later.') {
        message = 'Backend service is not available. Please check if the backend '
      } else if (error.response?.data?.message) {
        message = error.response.data.message
      } else if (error.message) {
        message = error.message
      }

      toast({
        title: "Login Failed",
        description: message,
        variant: "destructive",
      })
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (userData: RegisterRequest): Promise<boolean> => {
    try {
      setIsLoading(true)

      console.log('Attempting registration with:', userData.email)
      console.log('Environment:', process.env.NODE_ENV)
      console.log('API URL:', process.env.NEXT_PUBLIC_API_URL)

      // Test backend connectivity first
      try {
        const healthCheck = await fetch(`${process.env.NEXT_PUBLIC_API_URL?.replace('/api', '')}`)
        const healthData = await healthCheck.json()
        console.log('Backend health check:', healthData)
      } catch (healthError) {
        console.warn('Backend health check failed:', healthError)
      }

      // Call real API registration
      const response = await authAPI.register(userData)

      console.log('Registration response received:', response)

      // Registration successful - always redirect to login page (no auto-login)
      console.log('Registration successful, redirecting to login page')

      toast({
        title: "Registration Successful",
        description: `Account created successfully! Please login with your credentials.`,
      })

      // Always return false to redirect to login page
      return false
    } catch (error: any) {
      console.error('Registration error:', error)

      let message = 'Registration failed. Please try again.'

      if (error.message === 'Backend service is not available. Please try again later.') {
        message = 'Backend service is not available. Please check your internet connection and try again.'
      } else if (error.message === 'Network error: Unable to connect to the backend. This could be a CORS issue or the backend server is not available.') {
        message = 'Unable to connect to the server. Please check your internet connection and try again.'
      } else if (error.message && error.message.includes('Email already exists')) {
        message = 'This email is already registered. Please use a different email or try logging in.'
      } else if (error.message && error.message.includes('Validation failed')) {
        message = 'Please check your input and try again.'
      } else if (error.response?.data?.message) {
        message = error.response.data.message
      } else if (error.message) {
        message = error.message
      }

      toast({
        title: "Registration Failed",
        description: message,
        variant: "destructive",
      })
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true)
      await authAPI.logout()
    } catch (error) {
      // Continue with logout even if API call fails
      console.error('Logout API call failed:', error)
    } finally {
      clearAuthData()
      setIsLoading(false)

      toast({
        title: "Logged Out",
        description: "You have been successfully logged out.",
      })

      // Redirect to login page after logout
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login'
      }
    }
  }

  const forgotPassword = async (data: ForgotPasswordRequest): Promise<boolean> => {
    try {
      setIsLoading(true)
      await authAPI.forgotPassword(data)
      
      toast({
        title: "Password Reset Email Sent",
        description: "Please check your email for password reset instructions.",
      })

      return true
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to send password reset email.'
      toast({
        title: "Password Reset Failed",
        description: message,
        variant: "destructive",
      })
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const resetPassword = async (data: ResetPasswordRequest): Promise<boolean> => {
    try {
      setIsLoading(true)
      await authAPI.resetPassword(data)
      
      toast({
        title: "Password Reset Successful",
        description: "Your password has been reset. Please login with your new password.",
      })

      return true
    } catch (error: any) {
      const message = error.response?.data?.message || 'Password reset failed.'
      toast({
        title: "Password Reset Failed",
        description: message,
        variant: "destructive",
      })
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const changePassword = async (data: ChangePasswordRequest): Promise<boolean> => {
    try {
      setIsLoading(true)
      await authAPI.changePassword(data)

      toast({
        title: "Password Changed",
        description: "Your password has been successfully changed.",
      })

      return true
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to change password.'
      toast({
        title: "Password Change Failed",
        description: message,
        variant: "destructive",
      })
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const sendVerification = async (): Promise<boolean> => {
    try {
      setIsLoading(true)
      await authAPI.sendVerification()

      toast({
        title: "Verification Email Sent",
        description: "Please check your email for verification instructions.",
      })

      return true
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to send verification email.'
      toast({
        title: "Verification Email Failed",
        description: message,
        variant: "destructive",
      })
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const verifyEmail = async (token: string): Promise<boolean> => {
    try {
      setIsLoading(true)
      await authAPI.verifyEmail(token)

      // Refresh user data to update verification status
      await refreshProfile()

      toast({
        title: "Email Verified",
        description: "Your email has been successfully verified.",
      })

      return true
    } catch (error: any) {
      const message = error.response?.data?.message || 'Email verification failed.'
      toast({
        title: "Email Verification Failed",
        description: message,
        variant: "destructive",
      })
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const updateProfile = async (data: ProfileUpdateRequest): Promise<boolean> => {
    try {
      setIsLoading(true)
      const updatedUser = await authAPI.updateProfile(data)

      // Update user state and cookies
      setUser(updatedUser)
      Cookies.set('user_data', JSON.stringify(updatedUser), {
        expires: 7,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax', // Changed from 'strict' to 'lax' for better cross-domain support
        domain: process.env.NODE_ENV === 'production' ? '.luminousdemo.com' : undefined
      })

      toast({
        title: "Profile Updated",
        description: "Your profile has been successfully updated.",
      })

      return true
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to update profile.'
      toast({
        title: "Profile Update Failed",
        description: message,
        variant: "destructive",
      })
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const refreshProfile = useCallback(async (): Promise<void> => {
    try {
      console.log('Refreshing profile...')
      const updatedUser = await authAPI.getProfile()
      console.log('Profile data received:', updatedUser)
      setUser(updatedUser)
      Cookies.set('user_data', JSON.stringify(updatedUser), {
        expires: 7,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax', // Changed from 'strict' to 'lax' for better cross-domain support
        domain: process.env.NODE_ENV === 'production' ? '.luminousdemo.com' : undefined
      })
    } catch (error) {
      console.error('Failed to refresh profile:', error)
    }
  }, [])

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    forgotPassword,
    resetPassword,
    changePassword,
    sendVerification,
    verifyEmail,
    updateProfile,
    refreshProfile,
    checkAuth,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

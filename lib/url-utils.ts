/**
 * URL utility functions for handling backend URLs
 */

/**
 * Get the backend base URL from environment variable
 * Removes /api suffix to get the domain
 */
export const getBackendBaseUrl = (): string => {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5002/api'
  return apiUrl.replace('/api', '')
}

/**
 * Get the full backend API URL
 */
export const getBackendApiUrl = (): string => {
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5002/api'
}

/**
 * Convert a relative image path to a full backend URL
 * @param imagePath - Relative path like 'uploads/profiles/image.jpg' or '/uploads/profiles/image.jpg'
 * @returns Full URL like 'http://localhost:5002/uploads/profiles/image.jpg'
 */
export const getImageUrl = (imagePath: string | null): string => {
  if (!imagePath) return "/placeholder-user.jpg"
  if (imagePath.startsWith('http')) return imagePath

  const baseUrl = getBackendBaseUrl()
  // Ensure the path starts with / for proper URL construction
  const normalizedPath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`
  return `${baseUrl}${normalizedPath}`
}

/**
 * Convert a relative file path to a full backend URL
 * @param filePath - Relative path like '/uploads/documents/file.pdf'
 * @returns Full URL like 'http://localhost:5002/uploads/documents/file.pdf'
 */
export const getFileUrl = (filePath: string | null): string => {
  if (!filePath) return ""
  if (filePath.startsWith('http')) return filePath
  
  const baseUrl = getBackendBaseUrl()
  return `${baseUrl}${filePath}`
}

/**
 * Get profile image URL with fallback to placeholder
 * @param profileImage - Profile image path from user data
 * @returns Full URL or placeholder
 */
export const getProfileImageUrl = (profileImage: string | null): string => {
  if (!profileImage) {
    return "/placeholder-user.jpg"
  }

  const imageUrl = getImageUrl(profileImage)
  return imageUrl
}

import Cookies from 'js-cookie'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5002/api'

// Helper function to get auth token (consistent with main API service)
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return Cookies.get('auth_token') || localStorage.getItem('auth_token')
  }
  return null
}

// Helper function to clear auth token
export const clearAuthToken = () => {
  if (typeof window !== 'undefined') {
    Cookies.remove('auth_token')
    localStorage.removeItem('auth_token')
  }
}

export interface Category {
  id: string
  name: string
  slug: string
  description: string
  color: string
  icon?: string
  type?: string
  parent_id?: string | null
  is_active: boolean
  is_featured?: boolean
  sort_order: number
  meta_title?: string | null
  meta_description?: string | null
  image_url?: string | null
  created_at: string
  updated_at: string
  createdAt?: string
  updatedAt?: string
  children?: Category[]
  parent?: Category
  products_count?: number
  avatars_count?: number
}

export interface CreateCategoryData {
  name: string
  description: string
  color: string
  icon?: string
  parent_id?: string | null
  is_active?: boolean
  sort_order?: number
}

export interface UpdateCategoryData {
  name?: string
  description?: string
  color?: string
  icon?: string
  parent_id?: string | null
  is_active?: boolean
  sort_order?: number
}

export interface CategoriesResponse {
  success: boolean
  data: Category[]
  pagination?: {
    page: number
    limit: number
    totalPages: number
    totalItems: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

class CategoryService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`
    
    const token = getAuthToken()
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        const errorText = await response.text()

        // Handle authentication errors specifically
        if (response.status === 401) {
          clearAuthToken()
          throw new Error('Authentication required. Please provide a valid token.')
        }

        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
      }
      
      const data = await response.json()
      return data
    } catch (error) {
      console.error('Category API request failed:', error)
      throw error
    }
  }

  // Category CRUD operations
  async getAllCategories(page: number = 1, limit: number = 50): Promise<CategoriesResponse> {
    const endpoint = `/categories?page=${page}&limit=${limit}`
    return await this.request<CategoriesResponse>(endpoint)
  }



  async getCategoryById(id: string): Promise<Category> {
    const response = await this.request<{success: boolean, data: Category}>(`/categories/${id}`)
    return response.data
  }

  async createCategory(data: CreateCategoryData): Promise<Category> {
    const response = await this.request<{success: boolean, data: Category}>('/categories', {
      method: 'POST',
      body: JSON.stringify(data),
    })
    return response.data
  }

  async updateCategory(id: string, data: UpdateCategoryData): Promise<Category> {
    const response = await this.request<{success: boolean, data: Category}>(`/categories/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
    return response.data
  }

  async deleteCategory(id: string): Promise<void> {
    return this.request<void>(`/categories/${id}`, {
      method: 'DELETE',
    })
  }

  // Additional category operations
  async getActiveCategories(): Promise<Category[]> {
    const response = await this.request<{success: boolean, data: Category[]}>('/categories/active')
    return response.data
  }

  // Public categories endpoint (no authentication required)
  async getPublicCategories(page: number = 1, limit: number = 100): Promise<Category[]> {
    const endpoint = `/categories/public?page=${page}&limit=${limit}`
    // Make request without authentication for public endpoint
    const url = `${API_BASE_URL}${endpoint}`

    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
      }

      const data = await response.json()

      // Handle response structure - could be {success: true, data: [...]} or direct array
      if (data.success && data.data) {
        return data.data
      } else if (Array.isArray(data)) {
        return data
      } else {
        return []
      }
    } catch (error) {
      console.error('Public Category API request failed:', error)
      throw error
    }
  }

  async getCategoriesTree(): Promise<Category[]> {
    const response = await this.request<{success: boolean, data: Category[]}>('/categories/tree')
    return response.data
  }

  async searchCategories(query: string, filters?: {
    is_active?: boolean
    parent_id?: string
  }): Promise<Category[]> {
    const params = new URLSearchParams({ q: query })
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) params.append(key, String(value))
      })
    }
    const response = await this.request<{success: boolean, data: Category[]}>(`/categories/search?${params.toString()}`)
    return response.data
  }

  // Bulk operations
  async bulkUpdateCategories(updates: { id: string; data: UpdateCategoryData }[]): Promise<Category[]> {
    const response = await this.request<{success: boolean, data: Category[]}>('/categories/bulk-update', {
      method: 'PUT',
      body: JSON.stringify({ updates }),
    })
    return response.data
  }

  async reorderCategories(categoryIds: string[]): Promise<void> {
    return this.request<void>('/categories/reorder', {
      method: 'PUT',
      body: JSON.stringify({ category_ids: categoryIds }),
    })
  }
}

export const categoryService = new CategoryService()

import Cookies from 'js-cookie'
import { Order, OrderDetails, OrdersPagination, OrderUser, OrderPlan, OrderMeta } from './user-subscriptions'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5002/api'

// Helper function to get auth token
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return Cookies.get('auth_token') || localStorage.getItem('auth_token')
  }
  return null
}

// Helper function to clear auth token
export const clearAuthToken = () => {
  if (typeof window !== 'undefined') {
    Cookies.remove('auth_token')
    localStorage.removeItem('auth_token')
  }
}

// Extended Order interface for admin with additional user data
export interface AdminOrder extends Order {
  user: OrderUser & {
    phone?: string
    role?: string
    is_verified?: boolean
    subscription_status?: string
  }
}

export interface AdminOrdersApiResponse {
  success: boolean
  data: {
    orders: AdminOrder[]
    pagination: OrdersPagination
  }
}

export interface AdminOrderStats {
  totalOrders: number
  totalRevenue: number
  paidOrders: number
  pendingOrders: number
  failedOrders: number
  cancelledOrders: number
  monthlySubscriptions: number
  annualSubscriptions: number
  averageOrderValue: number
  recentOrders: AdminOrder[]
}

// Function to fetch all orders (admin only)
export const getAdminOrders = async (params?: {
  page?: number
  limit?: number
  status?: 'pending' | 'paid' | 'failed' | 'cancelled'
  type?: 'subscription' | 'one-time'
  user_id?: string
  start_date?: string
  end_date?: string
  search?: string
}): Promise<{
  success: boolean
  data: AdminOrdersApiResponse['data'] | null
  message: string
}> => {
  try {
    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token found')
    }

    const queryParams = new URLSearchParams()
    
    if (params?.page) {
      queryParams.append('page', params.page.toString())
    }
    if (params?.limit) {
      queryParams.append('limit', params.limit.toString())
    }
    if (params?.status) {
      queryParams.append('status', params.status)
    }
    if (params?.type) {
      queryParams.append('type', params.type)
    }
    if (params?.user_id) {
      queryParams.append('user_id', params.user_id)
    }
    if (params?.start_date) {
      queryParams.append('start_date', params.start_date)
    }
    if (params?.end_date) {
      queryParams.append('end_date', params.end_date)
    }
    if (params?.search) {
      queryParams.append('search', params.search)
    }

    // Use the same endpoint as regular user orders, but as admin user
    const url = `${API_BASE_URL}/subscriptions/orders${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      if (response.status === 401) {
        clearAuthToken()
        throw new Error('Authentication failed. Please log in again.')
      }
      if (response.status === 403) {
        throw new Error('Access denied. Admin privileges required.')
      }
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result: AdminOrdersApiResponse = await response.json()

    return {
      success: true,
      data: result.data,
      message: 'Admin orders retrieved successfully'
    }
  } catch (error) {
    console.error('Error fetching admin orders:', error)
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : 'Failed to fetch admin orders'
    }
  }
}

// Function to get order statistics for admin dashboard
export const getAdminOrderStats = async (): Promise<{
  success: boolean
  data: AdminOrderStats | null
  message: string
}> => {
  try {
    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token found')
    }

    // Fetch orders with pagination to get comprehensive stats
    let allOrders: AdminOrder[] = []
    let currentPage = 1
    let hasMorePages = true
    const maxPages = 10 // Limit to prevent infinite loops and get reasonable sample

    while (hasMorePages && currentPage <= maxPages) {
      const response = await getAdminOrders({
        page: currentPage,
        limit: 50, // Use safe limit
      })

      if (!response.success || !response.data) {
        throw new Error(response.message || 'Failed to fetch orders for statistics')
      }

      allOrders = [...allOrders, ...response.data.orders]
      hasMorePages = response.data.pagination.has_next
      currentPage++
    }
    
    // Calculate statistics
    const totalOrders = allOrders.length
    const paidOrders = allOrders.filter(o => o.status === 'paid').length
    const pendingOrders = allOrders.filter(o => o.status === 'pending').length
    const failedOrders = allOrders.filter(o => o.status === 'failed').length
    const cancelledOrders = allOrders.filter(o => o.status === 'cancelled').length
    
    const monthlySubscriptions = allOrders.filter(o => o.meta.interval === 'monthly').length
    const annualSubscriptions = allOrders.filter(o => o.meta.interval === 'annual').length
    
    // Calculate total revenue (from paid orders only)
    const totalRevenue = allOrders
      .filter(o => o.status === 'paid')
      .reduce((sum, o) => sum + o.amount_total, 0) / 100 // Convert from cents to dollars
    
    // Calculate average order value
    const averageOrderValue = paidOrders > 0 ? totalRevenue / paidOrders : 0
    
    // Get recent orders (last 10)
    const recentOrders = allOrders
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 10)

    const stats: AdminOrderStats = {
      totalOrders,
      totalRevenue,
      paidOrders,
      pendingOrders,
      failedOrders,
      cancelledOrders,
      monthlySubscriptions,
      annualSubscriptions,
      averageOrderValue,
      recentOrders,
    }

    return {
      success: true,
      data: stats,
      message: 'Admin order statistics calculated successfully'
    }
  } catch (error) {
    console.error('Error calculating admin order stats:', error)
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : 'Failed to calculate admin order statistics'
    }
  }
}

// Function to get order details by ID (admin version with full user data)
export const getAdminOrderDetails = async (orderId: string): Promise<{
  success: boolean
  data: OrderDetails | null
  message: string
}> => {
  try {
    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token found')
    }

    // Use the same endpoint as regular user order details
    const response = await fetch(`${API_BASE_URL}/subscriptions/orders/${orderId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      if (response.status === 401) {
        clearAuthToken()
        throw new Error('Authentication failed. Please log in again.')
      }
      if (response.status === 403) {
        throw new Error('Access denied. Admin privileges required.')
      }
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    return {
      success: true,
      data: result.data,
      message: 'Admin order details retrieved successfully'
    }
  } catch (error) {
    console.error('Error fetching admin order details:', error)
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : 'Failed to fetch admin order details'
    }
  }
}

// Function to export admin orders (with pagination support)
export const exportAdminOrders = async (params?: {
  status?: 'pending' | 'paid' | 'failed' | 'cancelled'
  type?: 'subscription' | 'one-time'
  start_date?: string
  end_date?: string
  format?: 'csv' | 'xlsx'
}): Promise<{
  success: boolean
  data: AdminOrder[] | null
  message: string
}> => {
  try {
    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token found')
    }

    // Fetch all orders with pagination
    const allOrders: AdminOrder[] = []
    let currentPage = 1
    let hasMorePages = true
    const maxPages = 20 // Safety limit to prevent infinite loops

    while (hasMorePages && currentPage <= maxPages) {
      const response = await getAdminOrders({
        page: currentPage,
        limit: 50,
        ...params
      })

      if (!response.success || !response.data) {
        throw new Error(response.message || 'Failed to fetch orders for export')
      }

      allOrders.push(...response.data.orders)
      hasMorePages = response.data.pagination.has_next
      currentPage++
    }

    if (allOrders.length === 0) {
      return {
        success: false,
        data: null,
        message: 'No orders found to export'
      }
    }

    return {
      success: true,
      data: allOrders,
      message: `Successfully fetched ${allOrders.length} orders for export`
    }
  } catch (error) {
    console.error('Error exporting admin orders:', error)
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : 'Failed to export admin orders'
    }
  }
}

// Function to update order status (admin only) - Note: This may not be available in current API
export const updateOrderStatus = async (orderId: string, status: 'pending' | 'paid' | 'failed' | 'cancelled'): Promise<{
  success: boolean
  message: string
}> => {
  try {
    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token found')
    }

    // This endpoint may not exist in current API - using generic approach
    const response = await fetch(`${API_BASE_URL}/subscriptions/orders/${orderId}/status`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status })
    })

    if (!response.ok) {
      if (response.status === 401) {
        clearAuthToken()
        throw new Error('Authentication failed. Please log in again.')
      }
      if (response.status === 403) {
        throw new Error('Access denied. Admin privileges required.')
      }
      if (response.status === 404) {
        throw new Error('Order status update is not available through this endpoint.')
      }
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return {
      success: true,
      message: 'Order status updated successfully'
    }
  } catch (error) {
    console.error('Error updating order status:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to update order status'
    }
  }
}

import api from '../api'

// Dashboard Types
export interface DashboardCounts {
  users?: number
  products: number
  analysisRequests: number
  productMediaCreations: number
  categories?: number
  blogs?: number
}

export interface PerformanceOverviewItem {
  month: string
  year: number
  count: number
}

export interface RecentUser {
  id: string
  email: string
  first_name: string
  last_name: string
  role: string
  created_at: string
  is_verified: boolean
}

export interface AdminDashboardData {
  counts: DashboardCounts
  performanceOverview: PerformanceOverviewItem[]
  recentUsers: RecentUser[]
}

export interface UserDashboardData {
  counts: DashboardCounts
  performanceOverview: PerformanceOverviewItem[]
}

export interface DashboardResponse<T> {
  success: boolean
  message: string
  data: T
}

// Dashboard API Service
class DashboardService {
  private baseUrl = '/dashboard'

  // Helper method to make authenticated requests
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    try {
      const response = await api.request({
        url: endpoint,
        method: options.method || 'GET',
        data: options.body ? JSON.parse(options.body as string) : undefined,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      })

      console.log(`Dashboard API Response (${endpoint}):`, response)

      // Handle backend response structure
      if (response.data) {
        if (response.data.success && response.data.data) {
          return response.data as T
        }
        // If the response data is directly the content
        else if (typeof response.data === 'object') {
          return response.data as T
        }
      }

      // Fallback to the response itself
      return response as T
    } catch (error: any) {
      console.error(`Dashboard API Error (${endpoint}):`, error)

      // Handle specific error cases
      if (error.response?.status === 404) {
        throw new Error('Dashboard data not found')
      } else if (error.response?.status === 403) {
        throw new Error('Access denied to dashboard')
      } else if (error.response?.status === 401) {
        throw new Error('Authentication required')
      } else if (!error.response) {
        throw new Error('Network error - backend service unavailable')
      } else {
        throw new Error(error.response?.data?.message || error.message || 'Unknown error occurred')
      }
    }
  }

  // Get admin dashboard data
  async getAdminDashboard(): Promise<DashboardResponse<AdminDashboardData>> {
    const endpoint = `${this.baseUrl}/admin`
    console.log('Fetching admin dashboard data from:', endpoint)
    
    try {
      const result = await this.request<DashboardResponse<AdminDashboardData>>(endpoint)
      console.log('Admin dashboard data:', result)
      return result
    } catch (error) {
      console.error('Admin dashboard API error:', error)
      throw error
    }
  }

  // Get user dashboard data
  async getUserDashboard(): Promise<DashboardResponse<UserDashboardData>> {
    const endpoint = `${this.baseUrl}/user`
    console.log('Fetching user dashboard data from:', endpoint)
    
    try {
      const result = await this.request<DashboardResponse<UserDashboardData>>(endpoint)
      console.log('User dashboard data:', result)
      return result
    } catch (error) {
      console.error('User dashboard API error:', error)
      throw error
    }
  }

  // Get dashboard data based on user role
  async getDashboardData(userRole: string): Promise<DashboardResponse<AdminDashboardData | UserDashboardData>> {
    if (userRole === 'admin') {
      return this.getAdminDashboard()
    } else {
      return this.getUserDashboard()
    }
  }
}

export const dashboardService = new DashboardService()

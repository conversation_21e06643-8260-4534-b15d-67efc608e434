const GEMINI_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_GEMINI_API_KEY || process.env.GOOGLE_GEMINI_API_KEY
const GEMINI_MODELS = [
  'gemini-1.5-flash',
  'gemini-1.5-pro',
  'gemini-pro'
]

export interface GeminiPromptRequest {
  product_title: string
  media_type: 'image' | 'video'
}

export interface GeminiPromptResponse {
  success: boolean
  generated_prompt: string
  error?: string
}

class GeminiService {
  private async request(prompt: string, modelIndex: number = 0): Promise<string> {
    if (!GEMINI_API_KEY) {
      throw new Error('Gemini API key not configured')
    }

    if (modelIndex >= GEMINI_MODELS.length) {
      throw new Error('All Gemini models failed')
    }

    const model = GEMINI_MODELS[modelIndex]
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`

    const requestBody = {
      contents: [{
        parts: [{
          text: prompt
        }]
      }]
    }

    try {
      const response = await fetch(`${apiUrl}?key=${GEMINI_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        const errorText = await response.text()

        let errorData
        try {
          errorData = JSON.parse(errorText)
        } catch {
          errorData = { message: errorText }
        }

        const errorMessage = errorData.error?.message || errorData.message || `HTTP error! status: ${response.status}`

        // Try next model if this one fails
        if (modelIndex < GEMINI_MODELS.length - 1) {
          return this.request(prompt, modelIndex + 1)
        }

        throw new Error(errorMessage)
      }

      const data = await response.json()

      if (data.candidates && data.candidates[0] && data.candidates[0].content) {
        return data.candidates[0].content.parts[0].text
      }

      throw new Error('Invalid response format from Gemini API')
    } catch (error) {
      // Try next model if this one fails
      if (modelIndex < GEMINI_MODELS.length - 1) {
        return this.request(prompt, modelIndex + 1)
      }

      throw error
    }
  }

  async generateContentPrompt(data: GeminiPromptRequest): Promise<GeminiPromptResponse> {
    try {
      // Handle empty product title
      const productTitle = data.product_title?.trim() || 'this product'
      const mediaType = data.media_type || 'video'
      const systemPrompt = `${productTitle} is my product name. Please prepare a prototype ${mediaType} for me. Write a content description on how to showcase this product.`

      const generatedText = await this.request(systemPrompt)

      return {
        success: true,
        generated_prompt: generatedText.trim()
      }
    } catch (error) {
      // Fallback response if API fails
      const productTitle = data.product_title?.trim() || 'this product'
      const fallbackPrompt = data.media_type === 'video'
        ? `Create an engaging ${data.media_type} showcasing ${productTitle} with dynamic visuals, compelling narration, and clear product benefits that resonate with your target audience.`
        : `Design a stunning ${data.media_type} featuring ${productTitle} with eye-catching visuals, clear product highlights, and compelling messaging that drives engagement.`

      return {
        success: false,
        generated_prompt: fallbackPrompt,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }
}

export const geminiService = new GeminiService()

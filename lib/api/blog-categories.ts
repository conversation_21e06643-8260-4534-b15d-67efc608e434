import Cookies from 'js-cookie'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5002/api'

// Helper function to get auth token
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return Cookies.get('auth_token') || localStorage.getItem('auth_token')
  }
  return null
}

// Helper function to clear auth token
export const clearAuthToken = () => {
  if (typeof window !== 'undefined') {
    Cookies.remove('auth_token')
    localStorage.removeItem('auth_token')
  }
}

export interface BlogCategory {
  id: string
  title: string
  slug: string
  is_active: boolean
  is_frontend_show: boolean
  created_at: string
  updated_at: string
  createdAt: string
  updatedAt: string
}

export interface CreateBlogCategoryData {
  title: string
  slug: string
  is_active?: boolean
  is_frontend_show?: boolean
}

export interface UpdateBlogCategoryData {
  title?: string
  slug?: string
  is_active?: boolean
  is_frontend_show?: boolean
}

export interface BlogCategoryFilters {
  page?: number
  limit?: number
  search?: string
  is_active?: boolean
  is_frontend_show?: boolean
}

export interface BlogCategoriesResponse {
  categories: BlogCategory[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

export interface BlogCategoryResponse {
  category: BlogCategory
}

class BlogCategoryService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`
    
    console.log('Making Blog Category API request to:', url)
    
    const token = getAuthToken()
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      
      console.log('Blog Category API response status:', response.status)
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('Blog Category API error response:', errorText)

        // Handle authentication errors specifically
        if (response.status === 401) {
          console.error('Authentication failed - token may be expired')
          clearAuthToken()
          throw new Error('Authentication required. Please provide a valid token.')
        }

        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
      }
      
      const data = await response.json()
      console.log('Blog Category API response data:', data)
      return data
    } catch (error) {
      console.error('Blog Category API request failed:', error)
      throw error
    }
  }

  // Get all blog categories with filters
  async getAllBlogCategories(filters: BlogCategoryFilters = {}): Promise<BlogCategoriesResponse> {
    const queryParams = new URLSearchParams()
    
    if (filters.page) queryParams.append('page', filters.page.toString())
    if (filters.limit) queryParams.append('limit', filters.limit.toString())
    if (filters.search) queryParams.append('search', filters.search)
    if (filters.is_active !== undefined) queryParams.append('is_active', filters.is_active.toString())
    if (filters.is_frontend_show !== undefined) queryParams.append('is_frontend_show', filters.is_frontend_show.toString())

    const endpoint = `/blog-categories${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return await this.request<BlogCategoriesResponse>(endpoint)
  }

  // Get blog category by ID
  async getBlogCategoryById(id: string): Promise<BlogCategory> {
    const response = await this.request<BlogCategoryResponse>(`/blog-categories/${id}`)
    return response.category
  }

  // Create new blog category
  async createBlogCategory(data: CreateBlogCategoryData): Promise<BlogCategory> {
    const response = await this.request<BlogCategoryResponse>('/blog-categories', {
      method: 'POST',
      body: JSON.stringify(data),
    })
    return response.category
  }

  // Update blog category
  async updateBlogCategory(id: string, data: UpdateBlogCategoryData): Promise<BlogCategory> {
    const response = await this.request<BlogCategoryResponse>(`/blog-categories/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
    return response.category
  }

  // Toggle active status
  async toggleActiveStatus(id: string): Promise<BlogCategory> {
    const response = await this.request<BlogCategoryResponse>(`/blog-categories/${id}/toggle-active`, {
      method: 'PATCH',
    })
    return response.category
  }

  // Toggle frontend show status
  async toggleFrontendShow(id: string): Promise<BlogCategory> {
    const response = await this.request<BlogCategoryResponse>(`/blog-categories/${id}/toggle-frontend`, {
      method: 'PATCH',
    })
    return response.category
  }

  // Delete blog category
  async deleteBlogCategory(id: string): Promise<void> {
    await this.request<void>(`/blog-categories/${id}`, {
      method: 'DELETE',
    })
  }

  // Bulk operations
  async bulkToggleActive(ids: string[], isActive: boolean): Promise<BlogCategory[]> {
    const response = await this.request<{categories: BlogCategory[]}>('/blog-categories/bulk-toggle-active', {
      method: 'PATCH',
      body: JSON.stringify({ ids, is_active: isActive }),
    })
    return response.categories
  }

  async bulkToggleFrontend(ids: string[], isFrontendShow: boolean): Promise<BlogCategory[]> {
    const response = await this.request<{categories: BlogCategory[]}>('/blog-categories/bulk-toggle-frontend', {
      method: 'PATCH',
      body: JSON.stringify({ ids, is_frontend_show: isFrontendShow }),
    })
    return response.categories
  }

  async bulkDelete(ids: string[]): Promise<void> {
    await this.request<void>('/blog-categories/bulk-delete', {
      method: 'DELETE',
      body: JSON.stringify({ ids }),
    })
  }

  // Utility function to generate slug from title
  generateSlug(title: string): string {
    return title
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
  }
}

export const blogCategoryService = new BlogCategoryService()

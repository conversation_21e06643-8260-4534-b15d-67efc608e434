import Cookies from 'js-cookie'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5002/api'

// Helper function to get auth token (consistent with category service)
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return Cookies.get('auth_token') || localStorage.getItem('auth_token')
  }
  return null
}

// Helper function to clear auth token
export const clearAuthToken = () => {
  if (typeof window !== 'undefined') {
    Cookies.remove('auth_token')
    localStorage.removeItem('auth_token')
  }
}

export interface Category {
  id: string
  name: string
  slug: string
  type: string
  description: string
  icon: string
  color: string
}

export interface Avatar {
  id: string
  name: string
  category_id: string
  description: string
  personality_traits: {
    tone: string
    style: string
    expertise: string[]
  }
  content_preferences: {
    primaryPlatforms: string[]
    contentTypes: string[]
    hashtagStyle: string
    postingTimes: string[]
  }
  platform_settings?: any
  is_active: boolean
  created_at: string
  updated_at: string
  category?: Category
}

export interface CreateAvatarData {
  name: string
  category_id: string
  description: string
  personality_traits: {
    tone: string
    style: string
    expertise: string[]
  }
  content_preferences: {
    primaryPlatforms: string[]
    contentTypes: string[]
    hashtagStyle: string
    postingTimes: string[]
  }
  is_active: boolean
}

export interface UpdateAvatarData {
  name?: string
  description?: string
  personality_traits?: {
    tone?: string
    style?: string
    expertise?: string[]
  }
  is_active?: boolean
}

class AvatarService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`

    console.log('Making Avatar API request to:', url)

    const token = getAuthToken()
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)

      console.log('Avatar API response status:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Avatar API error response:', errorText)

        // Handle authentication errors specifically
        if (response.status === 401) {
          console.error('Authentication failed - token may be expired')
          clearAuthToken()
          throw new Error('Authentication required. Please provide a valid token.')
        }

        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
      }

      const data = await response.json()
      console.log('Avatar API response data:', data)
      return data
    } catch (error) {
      console.error('Avatar API request failed:', error)
      throw error
    }
  }

  async getAllAvatars(): Promise<Avatar[]> {
    const response = await this.request<{success: boolean, data: Avatar[]}>('/avatars')
    return response.data
  }

  async getAvatarById(id: string): Promise<Avatar> {
    const response = await this.request<{success: boolean, data: Avatar}>(`/avatars/${id}`)
    return response.data
  }

  async createAvatar(data: CreateAvatarData): Promise<Avatar> {
    const response = await this.request<{success: boolean, data: Avatar}>('/avatars', {
      method: 'POST',
      body: JSON.stringify(data),
    })
    return response.data
  }

  async updateAvatar(id: string, data: UpdateAvatarData): Promise<Avatar> {
    const response = await this.request<{success: boolean, data: Avatar}>(`/avatars/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
    return response.data
  }

  async deleteAvatar(id: string): Promise<void> {
    return this.request<void>(`/avatars/${id}`, {
      method: 'DELETE',
    })
  }
}

export const avatarService = new AvatarService()

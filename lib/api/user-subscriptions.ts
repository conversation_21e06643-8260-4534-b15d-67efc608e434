import Cookies from 'js-cookie'

// Updated SubscriptionPlan interface to match API response
export interface SubscriptionPlan {
  id: string
  name: string
  slug: string
  description: string
  price_monthly: string // API returns string
  price_annual: string // API returns string
  price_monthly_formatted?: string
  price_annual_formatted?: string
  stripe_price_monthly_id?: string | null
  stripe_price_annual_id?: string | null
  status: 'active' | 'inactive' | 'deprecated'
  features: {
    limits: {
      products_created: number
      analysis_requests: number
      image_generations: number
      video_generations: number
      prototype_image_generations: number
      prototype_video_generations: number
      brogs_created: number
    }
    api_access: boolean
    product_manage: boolean
    ai_agents_access: boolean
    analytics_access: boolean
    image_generation: boolean
    video_generation: boolean
    priority_support: boolean
    product_analysis: boolean
    advanced_analytics: boolean
    prototype_pipeline: boolean
    production_pipeline: boolean
    brog_management: boolean
    trial_duration_days?: number
  }
  sort_order?: number
  is_popular?: boolean
  created_at?: string
  updated_at?: string
  change_type?: string
  is_current?: boolean
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5002/api'

// Helper function to get auth token
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return Cookies.get('auth_token') || localStorage.getItem('auth_token')
  }
  return null
}

// Helper function to clear auth token
export const clearAuthToken = () => {
  if (typeof window !== 'undefined') {
    Cookies.remove('auth_token')
    localStorage.removeItem('auth_token')
  }
}

// New function to fetch usage data from the usage API
export const getUserUsageData = async (): Promise<{
  success: boolean
  data: UsageApiResponse | null
  message: string
}> => {
  try {
    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token found')
    }

    const response = await fetch(`${API_BASE_URL}/subscriptions/usage`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      if (response.status === 401) {
        clearAuthToken()
        throw new Error('Authentication failed. Please log in again.')
      }
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: UsageApiResponse = await response.json()

    return {
      success: true,
      data: data,
      message: 'Usage data retrieved successfully'
    }
  } catch (error) {
    console.error('Error fetching usage data:', error)
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : 'Failed to fetch usage data'
    }
  }
}

// Function to fetch orders/billing history
export const getUserOrders = async (params?: {
  page?: number
  limit?: number
  status?: 'pending' | 'paid' | 'failed' | 'cancelled'
  type?: 'subscription' | 'one-time'
}): Promise<{
  success: boolean
  data: OrdersApiResponse['data'] | null
  message: string
}> => {
  try {
    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token found')
    }

    // Build query parameters
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.status) queryParams.append('status', params.status)
    if (params?.type) queryParams.append('type', params.type)

    const url = `${API_BASE_URL}/subscriptions/orders${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      if (response.status === 401) {
        clearAuthToken()
        throw new Error('Authentication failed. Please log in again.')
      }
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result: OrdersApiResponse = await response.json()

    return {
      success: true,
      data: result.data,
      message: 'Orders retrieved successfully'
    }
  } catch (error) {
    console.error('Error fetching orders:', error)
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : 'Failed to fetch orders'
    }
  }
}

// Function to fetch order details by ID
export const getOrderDetails = async (orderId: string): Promise<{
  success: boolean
  data: OrderDetails | null
  message: string
}> => {
  try {
    const token = getAuthToken()
    if (!token) {
      throw new Error('No authentication token found')
    }

    const response = await fetch(`${API_BASE_URL}/subscriptions/orders/${orderId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      if (response.status === 401) {
        clearAuthToken()
        throw new Error('Authentication failed. Please log in again.')
      }
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result: OrderDetailsApiResponse = await response.json()

    return {
      success: true,
      data: result.data,
      message: 'Order details retrieved successfully'
    }
  } catch (error) {
    console.error('Error fetching order details:', error)
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : 'Failed to fetch order details'
    }
  }
}

export interface UserSubscription {
  id: string
  user_id: string
  plan_id: string
  stripe_subscription_id?: string | null
  status: 'active' | 'inactive' | 'canceled' | 'past_due' | 'trialing'
  current_period_start?: Date | null
  current_period_end?: Date | null
  cancel_at?: Date | null
  canceled_at?: Date | null
  created_at: string
  updated_at: string
  plan?: SubscriptionPlan
}

export interface UsageData {
  products_created: number
  analysis_requests: number
  image_generations: number
  video_generations: number
  prototype_image_generations: number
  prototype_video_generations: number
  brogs_created: number
}

// New interfaces for the usage API response
export interface UsageCounter {
  used: number
  limit: number
  remaining: number
  unlimited: boolean
  percentage_used: number
  status: 'available' | 'warning' | 'critical'
}

export interface UsagePeriod {
  id: string
  start: string
  end: string
  reason: string
}

export interface UsageSubscription {
  id: string
  status: string
  current_period_start: string
  current_period_end: string
  expired: boolean
}

export interface UsageSummary {
  total_features: number
  features_at_limit: number
  features_near_limit: number
}

// Orders API interfaces
export interface OrderUser {
  id: string
  email: string
  first_name: string
  last_name: string
}

export interface OrderPlan {
  id: string
  name: string
  slug: string
}

export interface OrderMeta {
  interval: 'monthly' | 'annual'
  price_id: string
  plan_slug: string
  // Additional fields found in actual API response
  upgrade_to?: string
  change_type?: string
  upgrade_from?: string
  upgrade_type?: string
  credits_preserved?: boolean
  proration_behavior?: string
  no_payment_required?: boolean
  free_plan?: boolean
}

export interface Order {
  id: string
  user_id: string
  subscription_id: string | null
  plan_id: string
  type: 'subscription' | 'one-time'
  status: 'pending' | 'paid' | 'failed' | 'cancelled'
  currency: string
  amount_subtotal: number
  amount_total: number
  stripe_checkout_session_id: string | null
  stripe_payment_intent_id: string | null
  stripe_invoice_id: string | null
  meta: OrderMeta
  created_at: string
  updated_at: string
  user: OrderUser
  plan: OrderPlan
  amount_total_formatted: string
  amount_subtotal_formatted: string
}

export interface OrdersPagination {
  current_page: number
  total_pages: number
  total_items: number
  items_per_page: number
  has_next: boolean
  has_prev: boolean
}

export interface OrdersApiResponse {
  success: boolean
  data: {
    orders: Order[]
    pagination: OrdersPagination
  }
}

// Detailed order interfaces
export interface OrderTransaction {
  id: string
  order_id: string
  user_id: string
  type: 'payment' | 'refund'
  status: 'succeeded' | 'failed' | 'pending'
  currency: string
  amount: number
  fee_amount: number | null
  net_amount: number | null
  stripe_payment_intent_id: string | null
  stripe_charge_id: string | null
  stripe_balance_transaction_id: string | null
  stripe_refund_id: string | null
  processed_at: string
  meta: any
  created_at: string
  updated_at: string
  amount_formatted: string
}

export interface OrderInvoice {
  id: string
  order_id: string
  stripe_invoice_id: string
  status: string
  amount: number
  currency: string
  created_at: string
  updated_at: string
}

export interface OrderItem {
  id: string
  order_id: string
  name: string
  description: string
  quantity: number
  unit_price: number
  total_price: number
  created_at: string
  updated_at: string
}

export interface DetailedOrderPlan {
  id: string
  name: string
  slug: string
  features: {
    limits: {
      brogs_created: number
      products_created: number
      analysis_requests: number
      image_generations: number
      video_generations: number
      prototype_image_generations: number
      prototype_video_generations: number
    }
    api_access: boolean
    product_manage: boolean
    brog_management: boolean
    ai_agents_access: boolean
    analytics_access: boolean
    image_generation: boolean
    priority_support: boolean
    product_analysis: boolean
    video_generation: boolean
    advanced_analytics: boolean
    prototype_pipeline: boolean
    production_pipeline: boolean
  }
}

export interface OrderDetails {
  id: string
  user_id: string
  subscription_id: string | null
  plan_id: string
  type: 'subscription' | 'one-time'
  status: 'pending' | 'paid' | 'failed' | 'cancelled'
  currency: string
  amount_subtotal: number
  amount_total: number
  stripe_checkout_session_id: string | null
  stripe_payment_intent_id: string | null
  stripe_invoice_id: string | null
  meta: OrderMeta
  created_at: string
  updated_at: string
  user: OrderUser
  plan: DetailedOrderPlan
  items: OrderItem[]
  transactions: OrderTransaction[]
  invoices: OrderInvoice[]
  amount_total_formatted: string
  amount_subtotal_formatted: string
}

export interface OrderDetailsApiResponse {
  success: boolean
  data: OrderDetails
}

export interface UsageApiResponse {
  success: boolean
  active: boolean
  unlimited_access: boolean
  user_role: string
  counters: {
    products_created: UsageCounter
    analysis_requests: UsageCounter
    image_generations: UsageCounter
    video_generations: UsageCounter
    prototype_image_generations: UsageCounter
    prototype_video_generations: UsageCounter
    brogs_created: UsageCounter
  }
  period: UsagePeriod
  subscription: UsageSubscription
  summary: UsageSummary
}

export interface SubscriptionStatusData {
  subscription: UserSubscription
  plan: SubscriptionPlan
  usage: UsageData
  limits: UsageData
  usage_percentage: {
    products_created: number
    analysis_requests: number
    image_generations: number
    video_generations: number
    prototype_image_generations: number
    prototype_video_generations: number
    brogs_created: number
  }
}

class UserSubscriptionService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`
    
    const token = getAuthToken()
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        if (response.status === 401) {
          clearAuthToken()
          throw new Error('Authentication failed. Please log in again.')
        }
        
        const errorText = await response.text()
        console.error('API Error Response:', errorText)
        console.error('Response Status:', response.status)
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const data = await response.json()
      return data
    } catch (error: any) {
      console.error('User Subscription API request failed:', error)
      throw error
    }
  }

  // Get user's current subscription plan
  async getMyPlan(): Promise<{ success: boolean; data: SubscriptionPlan | null; message: string }> {
    return this.request<{ success: boolean; data: SubscriptionPlan | null; message: string }>('/subscription-plans/my-plan')
  }

  // Get available subscription plans for users
  async getAvailablePlans(): Promise<{ success: boolean; data: SubscriptionPlan[]; message: string }> {
    const response = await this.request<{
      success: boolean;
      data: {
        plans: SubscriptionPlan[];
        current_subscription: any
      };
      message: string
    }>('/subscriptions/plans')

    // Transform the response to match our expected format
    return {
      success: response.success,
      data: response.data?.plans || [],
      message: response.message || ''
    }
  }

  // Get user's subscription status and usage
  async getSubscriptionStatus(): Promise<{
    success: boolean
    data: SubscriptionStatusData
    message: string
  }> {
    try {
      // First get the plans to find current plan details
      const plansResponse = await this.request<{
        success: boolean;
        data: {
          plans: SubscriptionPlan[];
          current_subscription: any
        };
        message: string
      }>('/subscriptions/plans')

      if (!plansResponse.success || !plansResponse.data) {
        throw new Error('Failed to fetch subscription data')
      }

      const { plans, current_subscription } = plansResponse.data

      // Find the current plan
      const currentPlan = plans.find(plan => plan.id === current_subscription?.plan_id)

      // If no current subscription, return null to indicate no active subscription
      if (!current_subscription || !currentPlan) {
        return {
          success: true,
          data: null as any,
          message: 'No active subscription found'
        }
      }

      // Create mock usage data for now (you can replace this with actual usage API call)
      const mockUsage = {
        products_created: 2,
        analysis_requests: 1,
        image_generations: 3,
        video_generations: 1,
        prototype_image_generations: 0,
        prototype_video_generations: 0,
        brogs_created: 0,
      }

      const limits = currentPlan.features.limits

      // Calculate usage percentages
      const usage_percentage = {
        products_created: limits.products_created > 0 ? (mockUsage.products_created / limits.products_created) * 100 : 0,
        analysis_requests: limits.analysis_requests > 0 ? (mockUsage.analysis_requests / limits.analysis_requests) * 100 : 0,
        image_generations: limits.image_generations > 0 ? (mockUsage.image_generations / limits.image_generations) * 100 : 0,
        video_generations: limits.video_generations > 0 ? (mockUsage.video_generations / limits.video_generations) * 100 : 0,
        prototype_image_generations: limits.prototype_image_generations > 0 ? (mockUsage.prototype_image_generations / limits.prototype_image_generations) * 100 : 0,
        prototype_video_generations: limits.prototype_video_generations > 0 ? (mockUsage.prototype_video_generations / limits.prototype_video_generations) * 100 : 0,
        brogs_created: limits.brogs_created > 0 ? (mockUsage.brogs_created / limits.brogs_created) * 100 : 0,
      }

      const subscriptionStatusData: SubscriptionStatusData = {
        subscription: {
          id: current_subscription.id,
          user_id: '', // Will be filled by backend
          plan_id: current_subscription.plan_id,
          status: current_subscription.status,
          current_period_start: current_subscription.current_period_start,
          current_period_end: current_subscription.current_period_end,
          cancel_at: current_subscription.cancel_at || null,
          canceled_at: current_subscription.canceled_at || null,
          created_at: current_subscription.created_at || new Date().toISOString(),
          updated_at: current_subscription.updated_at || new Date().toISOString(),
        },
        plan: currentPlan,
        usage: mockUsage,
        limits: limits,
        usage_percentage: usage_percentage,
      }

      return {
        success: true,
        data: subscriptionStatusData,
        message: 'Subscription status retrieved successfully'
      }
    } catch (error) {
      console.error('Error fetching subscription status:', error)
      throw error
    }
  }

  // Checkout/Subscribe to a new plan (for new subscriptions)
  async checkoutPlan(planId: string, interval: 'monthly' | 'annual' = 'monthly'): Promise<{
    success: boolean
    message: string
    data?: any
    url?: string
    session_id?: string
    order_id?: string
    plan?: any
  }> {
    return this.request<{
      success: boolean
      message: string
      data?: any
      url?: string
      session_id?: string
      order_id?: string
      plan?: any
    }>('/subscriptions/checkout', {
      method: 'POST',
      body: JSON.stringify({
        plan_id: planId,
        interval: interval
      })
    })
  }

  // Update existing subscription (upgrade/downgrade)
  async updateSubscription(planId: string, interval: 'monthly' | 'annual' = 'monthly'): Promise<{
    success: boolean
    message: string
    requires_payment?: boolean
    data?: {
      subscription: {
        id: string
        plan_id: string
        status: string
        current_period_start: string
        current_period_end: string
      }
      change_type: 'upgrade' | 'downgrade'
      message: string
      new_period?: {
        id: string
        period_start: string
        period_end: string
        reset_reason: string
      }
      scheduled_change?: any
    }
  }> {
    return this.request<{
      success: boolean
      message: string
      requires_payment?: boolean
      data?: {
        subscription: {
          id: string
          plan_id: string
          status: string
          current_period_start: string
          current_period_end: string
        }
        change_type: 'upgrade' | 'downgrade'
        message: string
        new_period?: {
          id: string
          period_start: string
          period_end: string
          reset_reason: string
        }
        scheduled_change?: any
      }
    }>('/subscriptions/update', {
      method: 'PUT',
      body: JSON.stringify({
        new_plan_id: planId,
        interval: interval,
        upgrade_type: 'immediate',
        proration_behavior: 'create_prorations'
      })
    })
  }

  // Cancel user subscription
  async cancelSubscription(options?: {
    reason?: string
    cancel_immediately?: boolean
  }): Promise<{
    success: boolean
    message: string
    data?: {
      subscription_id: string
      status: string
      cancel_at: string | null
      canceled_at: string | null
      refund_amount?: number
      effective_date: string
    }
  }> {
    return this.request<{
      success: boolean
      message: string
      data?: {
        subscription_id: string
        status: string
        cancel_at: string | null
        canceled_at: string | null
        refund_amount?: number
        effective_date: string
      }
    }>('/subscriptions/cancel', {
      method: 'POST',
      body: JSON.stringify({
        reason: options?.reason || '',
        cancel_immediately: options?.cancel_immediately || false
      })
    })
  }

  // Check payment status after Stripe checkout
  async checkPaymentStatus(orderId: string): Promise<{
    success: boolean
    message: string
    data?: any
  }> {
    return this.request<{
      success: boolean
      message: string
      data?: any
    }>('/stripe/check-payment-status', {
      method: 'POST',
      body: JSON.stringify({
        order_id: orderId
      })
    })
  }
}

// Export singleton instance
export const userSubscriptionService = new UserSubscriptionService()

// Function to detect current subscription interval
export const getCurrentSubscriptionInterval = async (): Promise<{
  success: boolean
  data: { interval: 'monthly' | 'annual'; planId: string } | null
  message: string
}> => {
  try {
    const subscriptionStatus = await getUserSubscriptionStatus()
    
    if (!subscriptionStatus.success || !subscriptionStatus.data) {
      return {
        success: false,
        data: null,
        message: 'No active subscription found'
      }
    }

    const subscription = subscriptionStatus.data.subscription
    const plan = subscriptionStatus.data.plan

    // Detect interval from subscription period duration
    let interval: 'monthly' | 'annual' = 'monthly' // default

    if (subscription.current_period_start && subscription.current_period_end) {
      const periodStart = new Date(subscription.current_period_start)
      const periodEnd = new Date(subscription.current_period_end)
      const diffInDays = Math.ceil((periodEnd.getTime() - periodStart.getTime()) / (1000 * 60 * 60 * 24))
      
      // If period is around 365 days (330-400), it's annual; if around 30 days (25-35), it's monthly
      interval = diffInDays > 300 ? 'annual' : 'monthly'
    }

    return {
      success: true,
      data: {
        interval,
        planId: subscription.plan_id
      },
      message: 'Current subscription interval detected successfully'
    }
  } catch (error) {
    console.error('Error detecting subscription interval:', error)
    return {
      success: false,
      data: null,
      message: error instanceof Error ? error.message : 'Failed to detect subscription interval'
    }
  }
}

// Enhanced update subscription function that auto-detects current interval
export const updateUserSubscriptionWithCurrentInterval = async (
  newPlanId: string,
  forceInterval?: 'monthly' | 'annual'
): Promise<{
  success: boolean
  message: string
  requires_payment?: boolean
  data?: any
}> => {
  try {
    let interval: 'monthly' | 'annual' = 'monthly'

    if (forceInterval) {
      interval = forceInterval
    } else {
      // Auto-detect current interval
      const currentInterval = await getCurrentSubscriptionInterval()
      if (currentInterval.success && currentInterval.data) {
        interval = currentInterval.data.interval
      }
    }

    console.log(`Updating subscription to plan ${newPlanId} with ${interval} interval`)

    return await userSubscriptionService.updateSubscription(newPlanId, interval)
  } catch (error) {
    console.error('Error updating subscription with current interval:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to update subscription'
    }
  }
}

// Enhanced checkout function that detects preferred interval
export const checkoutUserSubscriptionWithInterval = async (
  planId: string,
  preferredInterval?: 'monthly' | 'annual'
): Promise<{
  success: boolean
  message: string
  data?: any
  url?: string
  session_id?: string
  order_id?: string
  plan?: any
}> => {
  try {
    let interval: 'monthly' | 'annual' = 'monthly'

    if (preferredInterval) {
      interval = preferredInterval
    } else {
      // Try to detect from current subscription if upgrading
      const currentInterval = await getCurrentSubscriptionInterval()
      if (currentInterval.success && currentInterval.data) {
        interval = currentInterval.data.interval
      }
    }

    console.log(`Creating checkout for plan ${planId} with ${interval} interval`)

    return await userSubscriptionService.checkoutPlan(planId, interval)
  } catch (error) {
    console.error('Error creating checkout with interval:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to create checkout session'
    }
  }
}

// Convenience functions for easier imports
export const getUserSubscriptionPlan = () => userSubscriptionService.getMyPlan()
export const getAvailablePlans = () => userSubscriptionService.getAvailablePlans()
export const getUserSubscriptionStatus = () => userSubscriptionService.getSubscriptionStatus()
export const checkoutUserSubscription = (planId: string, interval: 'monthly' | 'annual' = 'monthly') =>
  userSubscriptionService.checkoutPlan(planId, interval)
export const updateUserSubscription = (planId: string, interval: 'monthly' | 'annual' = 'monthly') =>
  userSubscriptionService.updateSubscription(planId, interval)
export const cancelUserSubscription = (options?: {
  reason?: string
  cancel_immediately?: boolean
}) => userSubscriptionService.cancelSubscription(options)
export const checkPaymentStatus = (orderId: string) => userSubscriptionService.checkPaymentStatus(orderId)

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5002/api'

// Authentication token management
const getAuthToken = () => {
  // Get token from cookies (set by auth context)
  if (typeof window !== 'undefined') {
    // Try to get from cookies first (primary method)
    const cookieToken = document.cookie
      .split('; ')
      .find(row => row.startsWith('auth_token='))
      ?.split('=')[1]

    if (cookieToken) {
      return cookieToken
    }

    // Fallback to localStorage (for manual token setting)
    const storedToken = localStorage.getItem('auth_token')
    if (storedToken) {
      return storedToken
    }
  }

  // No token available
  return null
}

// Helper function to set auth token (for development/testing)
export const setAuthToken = (token: string) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('auth_token', token)
    localStorage.setItem('token_timestamp', Date.now().toString())
  }
}

// Helper function to clear auth token
export const clearAuthToken = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('token_timestamp')
  }
}

// Helper function to check if token is expired
export const isTokenExpired = (): boolean => {
  if (typeof window === 'undefined') return false

  const tokenTimestamp = localStorage.getItem('token_timestamp')
  if (!tokenTimestamp) return false

  const tokenTime = parseInt(tokenTimestamp)
  const currentTime = Date.now()
  const tokenAge = (currentTime - tokenTime) / (1000 * 60) // in minutes
  const expirationMinutes = 30 // JWT_ACCESS_EXPIRATION_MINUTES

  return tokenAge >= expirationMinutes
}

export interface Product {
  id: string
  name: string
  slug: string
  description: string
  price: string
  currency: string
  country: string | null
  category_id: string
  category: {
    id: string
    name: string
    slug: string
    description: string
    type: string
    is_active: boolean
    is_featured: boolean
    icon: string | null
    color: string | null
  }
  affiliate_link: string
  is_active: boolean
  user_id: string
  created_at: string
  updated_at: string
  user: {
    id: string
    first_name: string
    last_name: string
    email: string
  }
  analyses: ProductAnalysis[]
  media?: Array<{
    id: string
    media_type: string
    media_url: string
    thumbnail_url: string
    format: string
    status: string
  }>
}

export interface CreateProductData {
  name: string
  description: string
  category_id: string
  price: string
  currency: string
  country?: string
  affiliate_link: string
  is_active?: boolean
}

export interface UpdateProductData {
  name?: string
  description?: string
  category_id?: string
  price?: string
  currency?: string
  country?: string
  affiliate_link?: string
  is_active?: boolean
}

export interface ProductAnalysis {
  id: string
  product_id: string
  external_job_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  submitted_data: {
    geo: string
    sku: string
    asin: string
    timeframe: string
    product_name: string
    additional_keywords: string[]
  }
  analysis_result: {
    // Old format
    market_trends?: {
      demand: string
      seasonality: string
    }
    analysis_score?: number
    market_potential?: string
    competition_level?: string
    price_recommendation?: {
      max: number
      min: number
      optimal: number
    }
    // New format
    content_strategy?: {
      content_themes?: Array<{
        topic: string
        content_type: string
        search_intent: string
        opportunity_score: string
      }>
      target_audience?: string | null
      primary_keywords?: string[]
    }
    audience_insights?: {
      pain_points?: string[]
      demographics?: string | null
      buying_motivations?: string[]
    }
    market_validation?: {
      demand_score?: number | null
      competition_level?: string
      market_opportunity?: string | null
    }
    competitive_intelligence?: {
      price_range?: string
      top_competitors?: string[]
      key_differentiators?: string[]
    }
  } | null
  analyzed_data: {
    // Old format
    keywords?: string[]
    market_position?: string
    category_confidence?: number
    competitor_analysis?: {
      main_competitors: string[]
      price_comparison: string
    }
    seo_recommendations?: string[]
    // New format fields can be added here as needed
    analysis_metadata?: {
      ai_model?: string
      data_sources?: any
      product_name?: string
      analysis_timestamp?: string
    }
    audience_insights?: {
      customer_questions?: string[]
      customer_pain_points?: string[]
      demographic_indicators?: string[]
      purchase_intent_signals?: string[]
      search_behavior_patterns?: string[]
    }
    competitive_intelligence?: {
      market_gaps?: string[]
      main_competitors?: Array<{
        name: string
        domain: string
        key_strengths: string[]
        market_position: string
      }>
      pricing_insights?: any
      market_saturation?: string
    }
    product_market_validation?: {
      market_maturity?: string
      key_demand_indicators?: string[]
      market_demand_signals?: string[]
      demand_validation_score?: string
      search_volume_indicators?: string
    }
    content_strategy_gold_mine?: {
      seo_gaps?: string[]
      content_formats?: string[]
      trending_topics?: string[]
      high_value_keywords?: string[]
      content_opportunities?: Array<{
        topic: string
        content_type: string
        search_intent: string
        opportunity_score: string
      }>
    }
  } | null
  error_message: string | null
  submitted_at: string
  completed_at: string | null
  created_at: string
  updated_at: string
  data_summary?: any | null
  price_analysis?: any | null
  // New analyzed data fields
  organic_results?: any | null
  shopping_results?: any | null
  knowledge_graph?: any | null
  related_searches?: any | null
  related_questions?: any | null
  local_results?: any | null
  ai_overview?: any | null
  inline_videos?: any | null
  related_products?: any | null
}

export interface PaginationInfo {
  page: number
  limit: number
  totalPages: number
  totalItems: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

export interface ProductsResponse {
  success: boolean
  data: Product[]
  pagination: PaginationInfo
}

class ProductService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`

    // Check if token is expired before making the request
    if (isTokenExpired()) {
      clearAuthToken()
      // Create an auth error
      const authError = new Error('Your session has expired. Please login again.')
      ;(authError as any).isAuthError = true
      throw authError
    }

    const token = getAuthToken()
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        const errorText = await response.text()

        // Try to parse the error response as JSON
        let errorData: any = null
        try {
          errorData = JSON.parse(errorText)
        } catch (parseError) {
          // If parsing fails, use the raw text
          errorData = { message: errorText }
        }

        // Handle authentication errors specifically
        if (response.status === 401) {
          clearAuthToken() // Clear the invalid token

          // Create a more detailed error for authentication issues
          const authError = new Error(errorData.message || 'Authentication required. Please login again.')
          ;(authError as any).isAuthError = true
          ;(authError as any).hint = errorData.hint
          throw authError
        }

        // For other errors, return the parsed error data if available
        if (errorData && errorData.success === false) {
          const apiError = new Error(errorData.message || `HTTP error! status: ${response.status}`)
          ;(apiError as any).data = errorData.data
          ;(apiError as any).success = false
          ;(apiError as any).message = errorData.message
          throw apiError
        }

        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
      }
      
      const data = await response.json()
      return data
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // Product CRUD operations
  async getAllProducts(userId?: string, page: number = 1, limit: number = 12): Promise<ProductsResponse> {
    // All users use the same endpoint: /products/my-products
    // - Admin/Staff: No userId parameter (gets all products)
    // - Regular users: With userId parameter (gets user's own products)
    const baseEndpoint = '/products/my-products'

    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString()
    })

    // Only add userId parameter for regular users
    if (userId) {
      params.append('userId', userId)
    }

    const endpoint = `${baseEndpoint}?${params.toString()}`
    const response = await this.request<ProductsResponse>(endpoint)
    return response
  }

  async getProductById(id: string): Promise<Product> {
    try {
      const response = await this.request<{success: boolean, data: Product}>(`/products/details/${id}`)
      return response.data
    } catch (error) {
      // If authentication fails, try to get from the list of products
      console.log('Direct product fetch failed, trying to get from products list...')
      try {
        const productsResponse = await this.getAllProducts()
        const product = productsResponse.data.find(p => p.id === id)
        if (product) {
          return product
        }
      } catch (listError) {
        console.log('Products list also failed, will use fallback in component')
      }
      throw error
    }
  }

  async createProduct(data: CreateProductData): Promise<Product> {
    const response = await this.request<{success: boolean, data: Product}>('/products', {
      method: 'POST',
      body: JSON.stringify(data),
    })
    return response.data || response
  }

  async updateProduct(id: string, data: UpdateProductData): Promise<Product> {
    const response = await this.request<{success: boolean, data: Product}>(`/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
    return response.data || response
  }

  async deleteProduct(id: string): Promise<void> {
    return this.request<void>(`/products/${id}`, {
      method: 'DELETE',
    })
  }

  // Product Analysis operations
  async requestAnalysis(productId: string, analysisType: string): Promise<ProductAnalysis> {
    return this.request<ProductAnalysis>('/products/analysis', {
      method: 'POST',
      body: JSON.stringify({
        product_id: productId,
        analysis_type: analysisType
      }),
    })
  }

  async getAnalysisStatus(analysisId: string): Promise<ProductAnalysis> {
    return this.request<ProductAnalysis>(`/products/analysis/${analysisId}`)
  }

  async getProductAnalyses(productId: string): Promise<ProductAnalysis[]> {
    return this.request<ProductAnalysis[]>(`/products/${productId}/analyses`)
  }

  // Create Prototype for a product
  async createPrototype(productId: string, payload: any): Promise<any> {
    return this.request<any>(`/products/${productId}/prototypes`, {
      method: 'POST',
      body: JSON.stringify(payload),
    })
  }

  // Update existing Prototype
  async updatePrototype(prototypeId: string, payload: any): Promise<any> {
    return this.request<any>(`/products/prototypes/${prototypeId}`, {
      method: 'PUT',
      body: JSON.stringify(payload),
    })
  }

  // Get prototypes for a product
  async getProductPrototypes(productId: string, page: number = 1, limit: number = 5, analysesId?: string): Promise<any> {
    let url = `/products/${productId}/prototypes?page=${page}&limit=${limit}`
    if (analysesId) {
      url += `&analyses_id=${analysesId}`
    }
    return this.request<any>(url)
  }

  // Generate AI prompt
  async generateAIPrompt(payload: { product_title: string; media_type: string; analysis_summary?: string }): Promise<any> {
    // Remove undefined values from payload
    const cleanPayload = Object.fromEntries(
      Object.entries(payload).filter(([_, value]) => value !== undefined)
    )

    return this.request<any>('/ai/gemini/prompt', {
      method: 'POST',
      body: JSON.stringify(cleanPayload),
    })
  }

  // Delete prototype
  async deletePrototype(prototypeId: string): Promise<any> {
    return this.request<any>(`/products/prototypes/${prototypeId}`, {
      method: 'DELETE',
    })
  }

  // Mark prototype as production
  async markPrototypeAsProduction(prototypeId: string): Promise<any> {
    return this.request<any>(`/prototypes/${prototypeId}/mark-production`, {
      method: 'POST',
    })
  }

  // Get all prototypes with pagination and filtering
  async getAllPrototypes(page: number = 1, limit: number = 10, filters?: {
    product_id?: string
    status?: string
    search?: string
  }): Promise<any> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      include: 'product,analysis' // Include product and analysis relations
    })

    if (filters?.product_id) {
      params.append('product_id', filters.product_id)
    }
    if (filters?.status) {
      params.append('status', filters.status)
    }
    if (filters?.search) {
      params.append('search', filters.search)
    }

    return this.request<any>(`/products/prototypes?${params.toString()}`)
  }

  // New Analysis API endpoints
  async checkAnalysisStatus(externalJobId: string): Promise<any> {
    const response = await this.request<any>(`/analysis/${externalJobId}/status`)
    return response
  }

  async syncResultData(externalJobId: string): Promise<any> {
    const response = await this.request<any>(`/analysis/${externalJobId}/result`)
    return response
  }

  async syncAnalyzedData(externalJobId: string): Promise<any> {
    const response = await this.request<any>(`/analysis/${externalJobId}/analyzed-data`)
    return response
  }

  // Prototype status sync
  async syncPrototypeStatus(jobId: string): Promise<any> {
    const response = await this.request<any>(`/prototypes/status/${jobId}`)
    return response
  }

  // Product Analysis Request
  async analyzeProduct(productId: string, analysisData: {
    product_name: string
    sku: string
    asin: string
    timeframe: string
    geo: string
    additional_keywords?: string[]
  }): Promise<any> {
    const response = await this.request<any>(`/products/${productId}/analyze`, {
      method: 'POST',
      body: JSON.stringify(analysisData),
    })
    return response
  }

  // Create Product Media
  async createProductMedia(productId: string, mediaData: {
    content_type?: string
    media_type?: string
    video_type?: string
    duration?: number
    style?: string
    tone?: string
    voice_tone?: string
    ai_generation_type?: string
    content_description?: string
    custom_ai_prompt?: string
    target_audience?: string
    content_options?: object
    platform_distribution?: object
  }): Promise<any> {
    const response = await this.request<any>(`/products/${productId}/create-media`, {
      method: 'POST',
      body: JSON.stringify(mediaData),
    })
    return response
  }

  // Analysis Queue Report
  async getAnalyses(params?: {
    page?: number
    limit?: number
    status?: string
    owner_id?: string
    search?: string
  }): Promise<{
    success: boolean
    data: Array<{
      id: string
      external_job_id: string
      product_id: string
      product_name: string
      owner_name: string
      status: string
      submitted_at: string
      completed_at: string | null
      error_message: string | null
    }>
    pagination: {
      currentPage: number
      totalPages: number
      totalItems: number
      itemsPerPage: number
    }
    filters: {
      status: string
      owner_id: string
      search: string | null
    }
  }> {
    const queryParams = new URLSearchParams()

    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.status) queryParams.append('status', params.status)
    if (params?.owner_id) queryParams.append('owner_id', params.owner_id)
    if (params?.search) queryParams.append('search', params.search)

    const url = `/analyses${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request(url)
  }

  // Bulk operations
  async bulkUpdateProducts(updates: { id: string; data: UpdateProductData }[]): Promise<Product[]> {
    return this.request<Product[]>('/products/bulk-update', {
      method: 'PUT',
      body: JSON.stringify({ updates }),
    })
  }

  // Search and filter
  async searchProducts(query: string, filters?: {
    category?: string
    type?: string
    status?: string
    user_id?: string
  }): Promise<Product[]> {
    const params = new URLSearchParams({ q: query })
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value)
      })
    }
    return this.request<Product[]>(`/products/search?${params.toString()}`)
  }
}

export const productService = new ProductService()

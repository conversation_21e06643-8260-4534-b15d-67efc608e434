import Cookies from 'js-cookie'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5002/api'

// Helper function to get auth token
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return Cookies.get('auth_token') || localStorage.getItem('auth_token')
  }
  return null
}

// Helper function to clear auth token
export const clearAuthToken = () => {
  if (typeof window !== 'undefined') {
    Cookies.remove('auth_token')
    localStorage.removeItem('auth_token')
  }
}

export interface PlanLimits {
  products_created: number
  analysis_requests: number
  image_generations: number
  video_generations: number
  prototype_image_generations: number
  prototype_video_generations: number
  brogs_created: number
}

export interface PlanFeatures {
  api_access: boolean
  product_manage: boolean
  ai_agents_access: boolean
  analytics_access: boolean
  image_generation: boolean
  video_generation: boolean
  priority_support: boolean
  product_analysis: boolean
  advanced_analytics: boolean
  prototype_pipeline: boolean
  production_pipeline: boolean
  brog_management: boolean
  limits: PlanLimits
}

export interface SubscriptionPlan {
  id: string
  name: string
  slug: string
  description: string
  price_monthly: number
  price_annual: number
  stripe_price_monthly_id?: string | null
  stripe_price_annual_id?: string | null
  status: 'active' | 'inactive' | 'deprecated'
  features: PlanFeatures
  sort_order: number
  is_popular: boolean
  created_at: string
  updated_at: string
}

export interface CreatePlanData {
  name: string
  slug: string
  description: string
  price_monthly: number
  price_annual: number
  stripe_price_monthly_id?: string | null
  stripe_price_annual_id?: string | null
  status: 'active' | 'inactive' | 'deprecated'
  features: PlanFeatures
  sort_order: number
  is_popular: boolean
}

export interface UpdatePlanData {
  name?: string
  slug?: string
  description?: string
  price_monthly?: number
  price_annual?: number
  stripe_price_monthly_id?: string | null
  stripe_price_annual_id?: string | null
  status?: 'active' | 'inactive' | 'deprecated'
  features?: PlanFeatures  // Full features object for proper updates
  sort_order?: number
  is_popular?: boolean
}

export interface PaginationInfo {
  page: number
  limit: number
  totalPages: number
  totalItems: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

export interface PlansResponse {
  success: boolean
  data: SubscriptionPlan[]
  pagination: PaginationInfo
}

export interface PlanResponse {
  success: boolean
  data: SubscriptionPlan
  message: string
}

class SubscriptionPlanService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`
    
    const token = getAuthToken()
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        if (response.status === 401) {
          clearAuthToken()
          throw new Error('Authentication failed. Please log in again.')
        }
        
        const errorText = await response.text()
        console.error('API Error Response:', errorText)
        console.error('Response Status:', response.status)
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const data = await response.json()
      return data
    } catch (error: any) {
      console.error('Subscription Plan API request failed:', error)
      throw error
    }
  }

  // Get all plans with pagination and filtering
  async getPlans(params: {
    page?: number
    limit?: number
    sort_by?: string
    sort_order?: 'ASC' | 'DESC'
    status?: string
  } = {}): Promise<PlansResponse> {
    const queryParams = new URLSearchParams()
    
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.limit) queryParams.append('limit', params.limit.toString())
    if (params.sort_by) queryParams.append('sort_by', params.sort_by)
    if (params.sort_order) queryParams.append('sort_order', params.sort_order)
    if (params.status) queryParams.append('status', params.status)

    const endpoint = `/subscription-plans${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request<PlansResponse>(endpoint)
  }

  // Get plan by ID
  async getPlanById(id: string): Promise<PlanResponse> {
    return this.request<PlanResponse>(`/subscription-plans/${id}`)
  }

  // Create new plan
  async createPlan(planData: CreatePlanData): Promise<PlanResponse> {
    return this.request<PlanResponse>('/subscription-plans', {
      method: 'POST',
      body: JSON.stringify(planData),
    })
  }

  // Update plan
  async updatePlan(id: string, planData: UpdatePlanData): Promise<PlanResponse> {
    return this.request<PlanResponse>(`/subscription-plans/${id}`, {
      method: 'PUT',
      body: JSON.stringify(planData),
    })
  }

  // Delete plan
  async deletePlan(id: string): Promise<{ success: boolean; message: string }> {
    return this.request<{ success: boolean; message: string }>(`/subscription-plans/${id}`, {
      method: 'DELETE',
    })
  }

  // Duplicate plan (deprecated - use createPlan directly)
  async duplicatePlan(id: string, newName: string): Promise<PlanResponse> {
    throw new Error('Use createPlan method directly for duplication')
  }

  // Get user's current subscription plan
  async getMyPlan(): Promise<{ success: boolean; data: SubscriptionPlan; message: string }> {
    return this.request<{ success: boolean; data: SubscriptionPlan; message: string }>('/subscription-plans/my-plan', {
      method: 'GET'
    })
  }

  // Get available subscription plans for users
  async getAvailablePlans(): Promise<{ success: boolean; data: SubscriptionPlan[]; message: string }> {
    return this.request<{ success: boolean; data: SubscriptionPlan[]; message: string }>('/subscriptions/plans', {
      method: 'GET'
    })
  }

  // Get user's subscription status and usage
  async getSubscriptionStatus(): Promise<{
    success: boolean
    data: {
      subscription: any
      plan: SubscriptionPlan
      usage: any
      limits: any
    }
    message: string
  }> {
    return this.request<{
      success: boolean
      data: {
        subscription: any
        plan: SubscriptionPlan
        usage: any
        limits: any
      }
      message: string
    }>('/subscriptions/status/detailed', {
      method: 'GET'
    })
  }

  // Update user subscription (upgrade/downgrade)
  async updateSubscription(planId: string, billingCycle: 'monthly' | 'annual' = 'monthly'): Promise<{
    success: boolean
    message: string
    data?: any
  }> {
    return this.request<{
      success: boolean
      message: string
      data?: any
    }>('/subscriptions/update', {
      method: 'PUT',
      body: JSON.stringify({
        plan_id: planId,
        billing_cycle: billingCycle
      })
    })
  }

  // Cancel user subscription
  async cancelSubscription(): Promise<{
    success: boolean
    message: string
    data?: any
  }> {
    return this.request<{
      success: boolean
      message: string
      data?: any
    }>('/subscriptions/cancel', {
      method: 'POST'
    })
  }
}

// Export singleton instance
export const subscriptionPlanService = new SubscriptionPlanService()

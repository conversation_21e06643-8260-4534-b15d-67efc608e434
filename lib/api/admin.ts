const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5002/api'

// Authentication token management
const getAuthToken = () => {
  // Get token from cookies (set by auth context)
  if (typeof window !== 'undefined') {
    // Try to get from cookies first (primary method)
    const cookieToken = document.cookie
      .split('; ')
      .find(row => row.startsWith('auth_token='))
      ?.split('=')[1]

    if (cookieToken) {
      return cookieToken
    }

    // Fallback to localStorage (for manual token setting)
    const storedToken = localStorage.getItem('auth_token')
    if (storedToken) {
      return storedToken
    }
  }

  // No token available
  return null
}

// Helper function to set auth token (for development/testing)
export const setAuthToken = (token: string) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('auth_token', token)
  }
}

// Helper function to clear auth token
export const clearAuthToken = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth_token')
  }
}

export interface User {
  id: string
  first_name: string
  last_name: string
  email: string
  role: 'admin' | 'user' | 'moderator'
  is_verified: boolean
  created_at: string
  updated_at: string
  plan_id?: string | null
  subscriptionPlan?: {
    id: string
    name: string
    slug: string
    status: string
  } | null
  last_login: string | null
  profile_picture: string | null
}

export interface PaginationInfo {
  page: number
  limit: number
  totalPages: number
  totalItems: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

export interface UsersResponse {
  success: boolean
  data: User[]
  pagination: PaginationInfo
}



class AdminService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`
    
    console.log('Making Admin API request to:', url)
    
    const token = getAuthToken()
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      
      console.log('Admin API response status:', response.status)
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('Admin API error response:', errorText)

        // Handle authentication errors specifically
        if (response.status === 401) {
          console.error('Authentication failed - token may be expired')
          clearAuthToken() // Clear the invalid token
          throw new Error('Authentication required. Please provide a valid token.')
        }

        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
      }
      
      const data = await response.json()
      console.log('Admin API response data:', data)
      return data
    } catch (error) {
      console.error('Admin API request failed:', error)
      throw error
    }
  }

  // Get all users with pagination and optional search
  async getUsers(params?: {
    page?: number
    limit?: number
    search?: string
  }): Promise<UsersResponse> {
    const queryParams = new URLSearchParams()
    
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.search) queryParams.append('search', params.search)

    const endpoint = `/admin${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    console.log('Users API endpoint:', endpoint)
    
    const response = await this.request<UsersResponse>(endpoint)
    console.log('Users API response:', response)
    return response
  }

  // Manually verify a user
  async verifyUser(userId: string): Promise<User> {
    const response = await this.request<{success: boolean, data: User}>(`/admin/${userId}/verify`, {
      method: 'PUT',
      body: '',
    })
    return response.data || response
  }

  // Delete a user
  async deleteUser(userId: string): Promise<void> {
    return this.request<void>(`/admin/${userId}`, {
      method: 'DELETE',
    })
  }

  // Get user by ID
  async getUserById(userId: string): Promise<User> {
    const response = await this.request<{success: boolean, data: User}>(`/admin/${userId}`)
    return response.data || response
  }



  // Bulk operations
  async bulkVerifyUsers(userIds: string[]): Promise<User[]> {
    return this.request<User[]>('/admin/bulk-verify', {
      method: 'PUT',
      body: JSON.stringify({ user_ids: userIds }),
    })
  }

  async bulkDeleteUsers(userIds: string[]): Promise<void> {
    return this.request<void>('/admin/bulk-delete', {
      method: 'DELETE',
      body: JSON.stringify({ user_ids: userIds }),
    })
  }
}

export const adminService = new AdminService()

import api from '../api'

// Media Types - Updated to match backend API response
export interface Media {
  id: string
  product_id: string
  external_job_id: string
  media_type: string // 'image' | 'video'
  media_url: string | null
  thumbnail_url: string | null
  file_size: number
  duration?: number | null
  width?: number
  height?: number
  format: string | null
  status: string
  metadata: {
    fps?: number
    source?: string
    bitrate?: string
    quality_score?: number
    photographer?: string
  }
  error_message?: string | null
  created_at: string
  updated_at: string
  // New content-related fields
  content_type?: string
  video_type?: string
  style?: string
  tone?: string
  voice_tone?: string
  ai_generation_type?: string
  content_description?: string
  custom_ai_prompt?: string
  target_audience?: string
  content_options?: object
  platform_distribution?: object
}

// API Response structure (what the backend actually returns)
export interface ApiMediaStatsResponse {
  total: number
  images: number
  videos: number
  completed: number
  pending: number
  failed: number
  total_size?: number
  average_quality_score?: number
  last_uploaded?: string | null
}

// Normalized interface for the frontend
export interface ProductMediaStats {
  total_media: number
  total_size: number
  media_types: {
    image: number
    video: number
    [key: string]: number
  }
  total_duration?: number
  average_quality_score?: number
  last_uploaded: string | null
  formats: {
    [key: string]: number
  }
}

export interface ProductMediaResponse {
  success: boolean
  data: Media[]
  pagination?: {
    page: number
    limit: number
    totalPages: number
    totalItems: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

export interface MediaResponse {
  success: boolean
  data: Media
}

export interface MediaStatsResponse {
  success: boolean
  data: ApiMediaStatsResponse
  message?: string
}

// Media list response interface
export interface MediaListResponse {
  success: boolean
  data: Media[]
  pagination: {
    currentPage: number
    totalPages: number
    totalItems: number
    itemsPerPage: number
    hasNextPage: boolean
    hasPreviousPage: boolean
  }
  filters?: {
    product_id: string | null
    media_type: string
    status: string
    search: string | null
  }
}

// Media filters interface
export interface MediaFilters {
  page?: number
  limit?: number
  media_type?: string | undefined
  status?: string | undefined
  search?: string
  product_id?: string | undefined
}

// Base API service class for media
class MediaService {
  private baseUrl = '/media'
  private productMediaUrl = '/products'

  // Helper method to make authenticated requests
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    try {
      const response = await api.request({
        url: endpoint,
        method: options.method || 'GET',
        data: options.body ? JSON.parse(options.body as string) : undefined,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      })

      console.log(`Media API Response (${endpoint}):`, response)

      // Handle your backend response structure
      if (response.data) {
        // Your backend returns { success: true, data: [...], message: "..." }
        if (response.data.success && response.data.data) {
          return response.data as T
        }
        // If the response data is directly the content
        else if (Array.isArray(response.data) || typeof response.data === 'object') {
          // For status endpoints, return the data directly if it has status
          if (endpoint.includes('/status/') && response.data.status) {
            return response.data as T
          }
          return { success: true, data: response.data } as T
        }
      }

      // For status endpoints, check if response itself has status
      if (endpoint.includes('/status/') && response.status) {
        return response as T
      }

      // Fallback to the response itself
      return response as T
    } catch (error: any) {
      console.error(`Media API Error (${endpoint}):`, error)

      // Handle specific error cases
      if (error.response?.status === 404) {
        throw new Error('Media not found')
      } else if (error.response?.status === 403) {
        throw new Error('Access denied to media')
      } else if (error.response?.status === 401) {
        throw new Error('Authentication required')
      } else if (!error.response) {
        throw new Error('Network error - backend service unavailable')
      } else {
        throw new Error(error.response?.data?.message || error.message || 'Unknown error occurred')
      }
    }
  }

  // Get all media with filters and pagination
  async getAllMedia(filters: MediaFilters = {}): Promise<MediaListResponse> {
    const queryParams = new URLSearchParams()

    if (filters.page) queryParams.append('page', filters.page.toString())
    if (filters.limit) queryParams.append('limit', filters.limit.toString())
    if (filters.media_type && filters.media_type !== undefined) queryParams.append('media_type', filters.media_type)
    if (filters.status && filters.status !== undefined) queryParams.append('status', filters.status)
    if (filters.search && filters.search.trim() !== '') queryParams.append('search', filters.search)
    if (filters.product_id && filters.product_id !== undefined) queryParams.append('product_id', filters.product_id)

    const endpoint = `${this.baseUrl}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    console.log('Media list API endpoint:', endpoint)

    try {
      const result = await this.request<MediaListResponse>(endpoint)
      console.log('Media list API response:', result)
      return result
    } catch (error) {
      console.error('Media list API error:', error)
      throw error
    }
  }

  // Get all media for a specific product
  async getProductMedia(productId: string, page: number = 1, limit: number = 20): Promise<ProductMediaResponse> {
    const endpoint = `${this.productMediaUrl}/${productId}/media?page=${page}&limit=${limit}`
    return this.request<ProductMediaResponse>(endpoint)
  }

  // Get media by ID
  async getMediaById(mediaId: string): Promise<MediaResponse> {
    const endpoint = `${this.baseUrl}/${mediaId}`
    return this.request<MediaResponse>(endpoint)
  }

  // Delete media by ID
  async deleteMedia(mediaId: string): Promise<void> {
    const endpoint = `${this.baseUrl}/${mediaId}`
    await this.request<void>(endpoint, {
      method: 'DELETE',
    })
  }

  // Get product media statistics
  async getProductMediaStats(productId: string): Promise<MediaStatsResponse> {
    const endpoint = `${this.productMediaUrl}/${productId}/media/stats`
    console.log('Making media stats API call to:', endpoint)
    console.log('Full URL:', `http://localhost:5002/api${endpoint}`)

    try {
      const result = await this.request<MediaStatsResponse>(endpoint)
      console.log('Media stats API response:', result)
      return result
    } catch (error) {
      console.error('Media stats API error:', error)
      throw error
    }
  }

  // Upload media for a product (bonus functionality)
  async uploadProductMedia(productId: string, file: File, metadata?: {
    alt_text?: string
    description?: string
    is_primary?: boolean
  }): Promise<MediaResponse> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (metadata?.alt_text) {
      formData.append('alt_text', metadata.alt_text)
    }
    if (metadata?.description) {
      formData.append('description', metadata.description)
    }
    if (metadata?.is_primary !== undefined) {
      formData.append('is_primary', metadata.is_primary.toString())
    }

    try {
      const response = await api.request({
        url: `${this.productMediaUrl}/${productId}/media`,
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })

      return response.data?.data ? response.data : { success: true, data: response.data }
    } catch (error: any) {
      console.error('Media upload error:', error)
      throw new Error(error.response?.data?.message || 'Failed to upload media')
    }
  }

  // Update media metadata
  async updateMedia(mediaId: string, metadata: {
    alt_text?: string
    description?: string
    is_primary?: boolean
    sort_order?: number
  }): Promise<MediaResponse> {
    const endpoint = `${this.baseUrl}/${mediaId}`
    return this.request<MediaResponse>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(metadata),
    })
  }

  // Set media as primary for a product
  async setPrimaryMedia(mediaId: string): Promise<MediaResponse> {
    const endpoint = `${this.baseUrl}/${mediaId}/set-primary`
    return this.request<MediaResponse>(endpoint, {
      method: 'PATCH',
    })
  }

  // Reorder media for a product
  async reorderProductMedia(productId: string, mediaIds: string[]): Promise<void> {
    const endpoint = `${this.productMediaUrl}/${productId}/media/reorder`
    await this.request<void>(endpoint, {
      method: 'PATCH',
      body: JSON.stringify({ media_ids: mediaIds }),
    })
  }

  // Check media status by external job ID
  async checkMediaStatus(externalJobId: string): Promise<{ status: string; [key: string]: any }> {
    // Using the correct endpoint format as specified by user
    const endpoint = `/media/status/${externalJobId}`

    console.log('Checking media status for job ID:', externalJobId)
    console.log('Status check endpoint:', endpoint)

    try {
      const result = await this.request<{ status: string; [key: string]: any }>(endpoint)
      console.log('Status check result:', result)

      // Extract status from various possible response structures
      let status = 'Unknown'
      if (result) {
        if (typeof result === 'string') {
          status = result
        } else if (result.status) {
          status = result.status
        } else if (result.data?.status) {
          status = result.data.status
        } else if (result.data?.data?.status) {
          status = result.data.data.status
        }
      }

      return { status, ...result }
    } catch (error) {
      console.error('Status check error:', error)

      // If the primary endpoint fails, try alternative patterns
      const fallbackEndpoints = [
        `${this.baseUrl}/status/${externalJobId}`,
        `/api/v1/jobs/${externalJobId}/status`,
        `/jobs/${externalJobId}/status`
      ]

      for (const fallbackEndpoint of fallbackEndpoints) {
        try {
          console.log('Trying fallback endpoint:', fallbackEndpoint)
          const result = await this.request<{ status: string; [key: string]: any }>(fallbackEndpoint)
          console.log('Fallback result:', result)

          let status = 'Unknown'
          if (result) {
            if (typeof result === 'string') {
              status = result
            } else if (result.status) {
              status = result.status
            } else if (result.data?.status) {
              status = result.data.status
            } else if (result.data?.data?.status) {
              status = result.data.data.status
            }
          }

          return { status, ...result }
        } catch (fallbackError) {
          console.log(`Fallback endpoint ${fallbackEndpoint} failed:`, fallbackError)
          continue
        }
      }

      throw new Error('Unable to check status - all endpoints failed')
    }
  }

  // Sync media result by external job ID
  async syncMediaResult(externalJobId: string): Promise<Partial<Media>> {
    const endpoint = `${this.baseUrl}/result/${externalJobId}`
    return this.request<Partial<Media>>(endpoint)
  }
}

export const mediaService = new MediaService()

import Cookies from 'js-cookie'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5002/api'

// Helper function to get auth token
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return Cookies.get('auth_token') || localStorage.getItem('auth_token')
  }
  return null
}

export interface BlogPost {
  id: string
  title: string
  slug: string
  content: string
  excerpt: string
  category_id: string
  tags: string[]
  featured_image: string | null
  status: 'draft' | 'published' | 'archived'
  published_at: string | null
  created_by: string
  created_at: string
  updated_at: string
  category?: {
    id: string
    title: string
    slug: string
  }
}

export interface BlogPostFilters {
  page?: number
  limit?: number
  search?: string
  status?: string
  category_id?: string
}

export interface BlogPostsResponse {
  posts: BlogPost[]
  totalPosts: number
  currentPage: number
  totalPages: number
}

export interface CreateBlogPostRequest {
  title: string
  content: string
  excerpt: string
  category_id: string
  tags: string[]
  status: 'draft' | 'published'
  featured_image?: File | null
}

export interface UpdateBlogPostRequest {
  title?: string
  content?: string
  excerpt?: string
  category_id?: string
  tags?: string[]
  status?: 'draft' | 'published' | 'archived'
  featured_image?: File | null
}

class BlogPostService {
  private baseUrl = '/posts'

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const token = getAuthToken()
    const url = `${API_BASE_URL}${endpoint}`

    const headers: HeadersInit = {
      ...options.headers,
    }

    if (token) {
      headers['Authorization'] = `Bearer ${token}`
    }

    // Don't set Content-Type for FormData, let the browser set it
    if (options.body && !(options.body instanceof FormData)) {
      headers['Content-Type'] = 'application/json'
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const data = await response.json()
      return data
    } catch (error: any) {
      console.error('Blog Posts API request failed:', error)
      throw error
    }
  }

  // Get all blog posts (admin/staff)
  async getAllBlogPosts(filters: BlogPostFilters = {}): Promise<BlogPostsResponse> {
    const params = new URLSearchParams()
    
    if (filters.page) params.append('page', filters.page.toString())
    if (filters.limit) params.append('limit', filters.limit.toString())
    if (filters.search) params.append('search', filters.search)
    if (filters.status) params.append('status', filters.status)
    if (filters.category_id) params.append('category_id', filters.category_id)

    const queryString = params.toString()
    const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl

    return this.request<BlogPostsResponse>(url)
  }

  // Get user's blog posts
  async getMyBlogPosts(filters: BlogPostFilters = {}): Promise<BlogPostsResponse> {
    const params = new URLSearchParams()
    
    if (filters.page) params.append('page', filters.page.toString())
    if (filters.limit) params.append('limit', filters.limit.toString())
    if (filters.search) params.append('search', filters.search)
    if (filters.status) params.append('status', filters.status)
    if (filters.category_id) params.append('category_id', filters.category_id)

    const queryString = params.toString()
    const url = queryString ? `${this.baseUrl}/my-posts?${queryString}` : `${this.baseUrl}/my-posts`

    return this.request<BlogPostsResponse>(url)
  }

  // Get blog post by ID
  async getBlogPostById(id: string): Promise<BlogPost> {
    return this.request<BlogPost>(`${this.baseUrl}/${id}`)
  }

  // Create blog post
  async createBlogPost(data: CreateBlogPostRequest): Promise<BlogPost> {
    const formData = new FormData()

    formData.append('title', data.title)
    formData.append('content', data.content)
    formData.append('excerpt', data.excerpt)
    formData.append('category_id', data.category_id)
    formData.append('tags', JSON.stringify(data.tags))
    formData.append('status', data.status)

    if (data.featured_image) {
      formData.append('featured_image', data.featured_image)
    }

    return this.request<BlogPost>(this.baseUrl, {
      method: 'POST',
      body: formData,
    })
  }

  // Update blog post
  async updateBlogPost(id: string, data: UpdateBlogPostRequest): Promise<BlogPost> {
    const formData = new FormData()

    if (data.title !== undefined) formData.append('title', data.title)
    if (data.content !== undefined) formData.append('content', data.content)
    if (data.excerpt !== undefined) formData.append('excerpt', data.excerpt)
    if (data.category_id !== undefined) formData.append('category_id', data.category_id)
    if (data.tags !== undefined) formData.append('tags', JSON.stringify(data.tags))
    if (data.status !== undefined) formData.append('status', data.status)

    if (data.featured_image) {
      formData.append('featured_image', data.featured_image)
    }

    return this.request<BlogPost>(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      body: formData,
    })
  }

  // Delete blog post
  async deleteBlogPost(id: string): Promise<void> {
    await this.request<void>(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
    })
  }

  // Bulk operations
  async bulkUpdateStatus(ids: string[], status: 'draft' | 'published' | 'archived'): Promise<BlogPost[]> {
    const response = await this.request<{posts: BlogPost[]}>(`${this.baseUrl}/bulk-status`, {
      method: 'PATCH',
      body: JSON.stringify({ ids, status }),
    })
    return response.posts
  }

  async bulkDelete(ids: string[]): Promise<void> {
    await this.request<void>(`${this.baseUrl}/bulk-delete`, {
      method: 'DELETE',
      body: JSON.stringify({ ids }),
    })
  }
}

export const blogPostService = new BlogPostService()

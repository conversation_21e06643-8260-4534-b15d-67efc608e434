import axios, { AxiosInstance } from 'axios'
import Cookies from 'js-cookie'
import { getBackendApiUrl } from './url-utils'

// API Configuration
const API_BASE_URL = getBackendApiUrl()

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 30000,
  withCredentials: false,
})

// Single-flight guard to prevent multiple redirects on 401
let isHandling401 = false

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle network errors gracefully
    if (!error.response) {
      // Network error - backend might not be available
      console.warn('Network error - backend might not be available:', error.message)
      return Promise.reject(new Error('Backend service is not available. Please try again later.'))
    }

    if (error.response?.status === 401) {
      // Clear auth data on unauthorized
      Cookies.remove('auth_token')
      Cookies.remove('user_data')

      if (typeof window !== 'undefined') {
        const path = window.location.pathname
        const onAuthPage = path.startsWith('/auth') || path.includes('/logout')

        // Avoid redirect loops and only handle once
        if (!isHandling401 && !onAuthPage) {
          isHandling401 = true

          const returnTo = encodeURIComponent(path + window.location.search)
          // Frontend-only handling: always treat 401 as expired session for UX
          const params = new URLSearchParams({
            sessionExpired: '1',
            returnTo,
          })

          // Replace so back button doesn't bounce between pages
          window.location.replace(`/auth/login?${params.toString()}`)
        }
      }
    }
    return Promise.reject(error)
  }
)

// Auth API types
export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  first_name: string
  last_name: string
  email: string
  password: string
  password_confirmation: string
}

export interface ForgotPasswordRequest {
  email: string
}

export interface ResetPasswordRequest {
  email: string
  token: string
  password: string
  password_confirmation: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

export interface ProfileUpdateRequest {
  first_name?: string
  last_name?: string
  gender?: string
  phone_number?: string
  country?: string
  city?: string
  address?: string
  profile_image?: File | null
}

export interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  role: string
  is_verified: boolean
  last_login: string
  created_at: string
  gender?: string
  phone_number?: string
  country?: string
  city?: string
  address?: string
  profile_image?: string | null
  plan_id?: string | null
}

export interface AuthResponse {
  user: User
  token: string
  expires_in: number
}

export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  errors?: Record<string, string[]>
}

// Auth API functions
export const authAPI = {
  // Login user
  login: async (credentials: LoginRequest): Promise<AuthResponse> => {
    const response = await api.post('/auth/login', credentials)

    console.log('Raw API response:', response.data)

    // Handle the actual API response structure from your backend
    if (response.data.token && response.data.user) {
      return {
        token: response.data.token,
        user: response.data.user,
        expires_in: response.data.expires_in || 86400 // Default 24 hours
      }
    } else if (response.data.data) {
      // If response has data wrapper
      return response.data.data
    } else {
      throw new Error('Invalid API response structure')
    }
  },

  // Register user
  register: async (userData: RegisterRequest): Promise<AuthResponse> => {
    try {
      const registrationUrl = `${API_BASE_URL}/auth/register`
      console.log('Making registration request to:', registrationUrl)
      console.log('Registration data:', { ...userData, password: '[HIDDEN]', password_confirmation: '[HIDDEN]' })

      // Test backend connectivity first
      console.log('Testing backend connectivity...')
      const healthUrl = API_BASE_URL.replace('/api', '')
      const healthResponse = await fetch(healthUrl)
      if (healthResponse.ok) {
        const healthData = await healthResponse.json()
        console.log('Backend health check successful:', healthData)
      } else {
        console.warn('Backend health check failed:', healthResponse.status)
      }

      // Use fetch instead of axios to avoid potential CORS issues
      const response = await fetch(registrationUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(userData),
      })

      console.log('Registration response status:', response.status)
      console.log('Registration response ok:', response.ok)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Registration error response:', errorText)
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      const responseData = await response.json()

      console.log('Registration API response status:', response.status)
      console.log('Registration API response data:', responseData)

      // Check if registration was successful
      if (response.status === 200 || response.status === 201) {
        // Handle different response structures
        let data = responseData

        // If response has a data wrapper
        if (data.data) {
          data = data.data
        }

        // Check if backend provides auto-login (token and user)
        if (data.token && data.user) {
          console.log('Backend supports auto-login, found token and user')
          return {
            token: data.token,
            user: data.user,
            expires_in: data.expires_in || 86400
          }
        }
        // Check if user data exists without token (registration only)
        else if (data.user || data.id || data.email) {
          console.log('Registration successful but no auto-login token provided')
          // Create a mock response for successful registration without auto-login
          const user = data.user || data
          return {
            token: '', // Empty token indicates no auto-login
            user: {
              id: user.id || 'temp-id',
              email: user.email || userData.email,
              first_name: user.first_name || userData.first_name,
              last_name: user.last_name || userData.last_name,
              role: user.role || 'user',
              is_verified: user.is_verified || false,
              last_login: user.last_login || new Date().toISOString(),
              created_at: user.created_at || new Date().toISOString()
            },
            expires_in: 0 // 0 indicates no auto-login
          }
        }
        // If response indicates success but no user data
        else if (data.success || data.message) {
          console.log('Registration successful, basic response')
          return {
            token: '', // Empty token indicates no auto-login
            user: {
              id: 'temp-id',
              email: userData.email,
              first_name: userData.first_name,
              last_name: userData.last_name,
              role: 'user',
              is_verified: false,
              last_login: new Date().toISOString(),
              created_at: new Date().toISOString()
            },
            expires_in: 0 // 0 indicates no auto-login
          }
        }
      }

      throw new Error('Registration failed: Invalid response from server')

    } catch (error: any) {
      console.error('Registration API error:', error)

      // Handle fetch errors (different from axios errors)
      if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        console.error('Network error - likely CORS or connectivity issue')
        throw new Error('Network error: Unable to connect to the backend. This could be a CORS issue or the backend server is not available.')
      } else if (error.message.includes('HTTP 422')) {
        throw new Error('Validation failed. Please check your input and try again.')
      } else if (error.message.includes('HTTP 409')) {
        throw new Error('Email already exists. Please use a different email or try logging in.')
      } else if (error.message.includes('HTTP 5')) {
        throw new Error('Server error. Please try again later.')
      }

      throw error
    }
  },

  // Logout user
  logout: async (): Promise<void> => {
    await api.post('/auth/logout')
  },

  // Get current user
  me: async (): Promise<User> => {
    const response = await api.get('/auth/me')
    return response.data.data || response.data.user || response.data
  },

  // Forgot password
  forgotPassword: async (data: ForgotPasswordRequest): Promise<void> => {
    await api.post('/auth/forgot-password', data)
  },

  // Reset password
  resetPassword: async (data: ResetPasswordRequest): Promise<void> => {
    await api.post('/auth/reset-password', data)
  },

  // Refresh token
  refreshToken: async (): Promise<AuthResponse> => {
    const response = await api.post('/auth/refresh')

    if (response.data.data) {
      return response.data.data
    } else {
      return {
        token: response.data.token || response.data.access_token,
        user: response.data.user,
        expires_in: response.data.expires_in || 86400
      }
    }
  },

  // Change password
  changePassword: async (data: ChangePasswordRequest): Promise<void> => {
    await api.post('/auth/change-password', data)
  },

  // Send email verification
  sendVerification: async (): Promise<void> => {
    await api.post('/auth/send-verification')
  },

  // Verify email
  verifyEmail: async (token: string): Promise<void> => {
    await api.post('/auth/verify-email', { token })
  },

  // Get profile
  getProfile: async (): Promise<User> => {
    const response = await api.get('/auth/profile')
    return response.data.data || response.data
  },

  // Update profile
  updateProfile: async (data: ProfileUpdateRequest): Promise<User> => {
    // Handle file upload if profile_image is provided
    if (data.profile_image instanceof File) {
      const formData = new FormData()

      // Add all fields to FormData
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (key === 'profile_image' && value instanceof File) {
            formData.append(key, value)
          } else {
            formData.append(key, value as string)
          }
        }
      })

      const response = await api.put('/auth/profile', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      return response.data.data || response.data
    } else {
      // Regular JSON update
      const response = await api.put('/auth/profile', data)
      return response.data.data || response.data
    }
  },
}

// Utility functions
export const getCurrentUser = async (): Promise<User | null> => {
  try {
    const token = Cookies.get('auth_token')
    if (!token) {
      return null
    }

    // Try to get user data from cookie first
    const userData = Cookies.get('user_data')
    if (userData) {
      try {
        return JSON.parse(userData) as User
      } catch (e) {
        console.warn('Failed to parse user data from cookie')
      }
    }

    // Fallback to API call
    return await authAPI.getProfile()
  } catch (error) {
    console.error('Failed to get current user:', error)
    return null
  }
}

export const getCurrentUserRole = async (): Promise<string | null> => {
  try {
    const user = await getCurrentUser()
    return user?.role || null
  } catch (error) {
    console.error('Failed to get current user role:', error)
    return null
  }
}

export default api
